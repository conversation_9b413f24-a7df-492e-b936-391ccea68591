package com.yhd.admin.scada.webscoket;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@ServerEndpoint("/websocket")
@Component
@Slf4j
public class WsServerEndpoint {
  private static final Map<String, Session> livingSessions = new ConcurrentHashMap<>();
  private Session session; // 会话session

  /**
   * 连接成功
   *
   * @param session
   */
  @OnOpen
  public void onOpen(Session session) {
    this.session = session;
    livingSessions.put(session.getId(), session);
    log.info(">>>>>>>>>>连接成功");
  }

  /**
   * 连接关闭
   *
   * @param session
   */
  @OnClose
  public void onClose(Session session) {
    livingSessions.remove(session.getId());
    log.info(">>>>>>>>>>连接关闭");
  }

  /**
   * 接收到消息
   *
   * @param text
   */
  @OnMessage
  public String onMsg(String text, Session session) {
    log.info(">>>>>>>>>>接收消息,{}={}", session.getId(), text);

    return "servet 发送：" + text;
  }

  public void sendMessage(String message) {
    livingSessions.forEach(
        (k, session) -> {
          synchronized (session) {
            try {
              session.getBasicRemote().sendText(message);
            } catch (IOException e) {
              log.error(">>>>>>>>>>error:{}", e.getMessage());
            }
          }
        });
  }
}
