package com.yhd.admin.bms.service.flowable;

import com.yhd.admin.bms.domain.vo.flowable.*;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.flowable.task.api.Task;

import java.util.List;

/**
 * <AUTHOR> @Date 2022/5/23 10:33 @Description: @Version 1.0
 */
public interface IFlowableTaskService {
  /**
   * 驳回任意节点
   *
   * @param backTaskVo 参数
   * @return
   */
  RespJson<String> backToStepTask(BackTaskVo backTaskVo);

  List<FlowNodeVo> getBackNodesByProcessInstanceId(FlowTaskParam flowTaskParam);

  RespJson<String> complete(CompleteTaskVo params);

  List<String> getApprovers(String processInstanceId);

  PageRespJson<TaskVo> getApplyingTasks(TaskQueryVo params);

  PageRespJson<TaskVo> getApplyedTasks(TaskQueryVo params);

  Task getTask(String Assignee, String processInstanceId);

  Task getTaskByCandidate(String account, String processInstanceId);

  Task getTask(String taskId);

  void complete(FlowTaskParam taskParam);

  Task getCandidateTask(String account, String processInstanceId);

  List<Task> getMyTask(String account, List<String> key);
}
