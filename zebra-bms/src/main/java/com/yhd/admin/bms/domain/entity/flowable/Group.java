package com.yhd.admin.bms.domain.entity.flowable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/3/13 19:48
 * @Description:
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ACT_ID_GROUP")
public class Group implements Serializable {
    private static final long serialVersionUID = 161570817714448869L;

    /**
     * 组id
     */
    private String id_;

    /**
     * 版本号
     */
    private Long rev_;

    /**
     * 组名称
     */
    private String name_;

    /**
     * 类别
     */
    private String type_;
}
