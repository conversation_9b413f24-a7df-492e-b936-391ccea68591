package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.safe.MesSwWorkTicketTsdEquipmentDao;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketTsdEquipmentConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketTsdEquipmentDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketTsdEquipment;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketTsdEquipmentParam;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketTsdEquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/** 针对表【tb_mes_sw_work_ticket_tsd_equipment(外施工单位停送电设备信息)】的数据库操作Service实现 */
@Service
public class MesSwWorkTicketTsdEquipmentServiceImpl
    extends ServiceImpl<MesSwWorkTicketTsdEquipmentDao, MesSwWorkTicketTsdEquipment>
    implements MesSwWorkTicketTsdEquipmentService {
  @Autowired private MesSwWorkTicketTsdEquipmentConvert ticketTsdEquipmentConvert;

  @Override
  public List<MesSwWorkTicketTsdEquipmentDTO> queryList(MesSwWorkTicketTsdEquipmentParam param) {
    LambdaQueryWrapper<MesSwWorkTicketTsdEquipment> wrapper = new LambdaQueryWrapper<>();
    // 工作票主键id
    wrapper.eq(
        Objects.nonNull(param.getTicketId()),
        MesSwWorkTicketTsdEquipment::getTicketId,
        param.getTicketId());

    return baseMapper.selectList(wrapper).stream()
        .map(ticketTsdEquipmentConvert::toDTO)
        .collect(Collectors.toList());
  }

  @Override
  public List<MesSwWorkTicketTsdEquipmentDTO> queryList(Long ticketId) {
    if (Objects.isNull(ticketId)) {
      return null;
    }
    MesSwWorkTicketTsdEquipmentParam param = new MesSwWorkTicketTsdEquipmentParam();
    param.setTicketId(ticketId);

    return this.queryList(param);
  }
}
