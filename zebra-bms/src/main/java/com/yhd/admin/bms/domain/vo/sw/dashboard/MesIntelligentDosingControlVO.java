package com.yhd.admin.bms.domain.vo.sw.dashboard;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 浓缩池监控数据VO
 *
 * <AUTHOR>
 * @date 2024/6/10
 */
@Data
public class MesIntelligentDosingControlVO {
  // 3#浓缩池点位地址常量
  public static final String POINT_ADDRESS_THICKENER412_THICK_VALUE =
      "XMSW_Slurry_Thickener412_ThickValue";
  public static final String POINT_ADDRESS_TANK412_THICK_TORQUE = "XMSW_Slurry_Tank412_ThickTorque";
  public static final String POINT_ADDRESS_TANK412_CONCENTRATION_OUT =
      "XMSW_Slurry_Tank412_ConcentrationOut";
  public static final String POINT_ADDRESS_TANK412_INSTRUMENT_NUM =
      "XMSW_Slurry_Tank412_InstrumentNum";

  // 2#浓缩池点位地址常量
  public static final String POINT_ADDRESS_THICKENER602_THICK_VALUE =
      "XMSW_Slurry_Thickener602_ThickValue";
  public static final String POINT_ADDRESS_THICKENE602_THICK_TORQUE =
      "XMSW_Slurry_Thickene602_ThickTorque";
  public static final String POINT_ADDRESS_TANK602_CONCENTRATION_OUT =
      "XMSW_Slurry_Tank602_ConcentrationOut";

  // 1#浓缩池点位地址常量
  public static final String POINT_ADDRESS_THICKENER601_THICK_VALUE =
      "XMSW_Slurry_Thickener601_ThickValue";
  public static final String POINT_ADDRESS_TANK601_THICK_TORQUE = "XMSW_Slurry_Tank601_ThickTorque";
  public static final String POINT_ADDRESS_TANK601_CONCENTRATION_OUT =
      "XMSW_Slurry_Tank601_ConcentrationOut";
  public static final String POINT_ADDRESS_TANK601_INSTRUMENT_NUM =
      "XMSW_Slurry_Tank601_InstrumentNum";

  // 水质监测点位地址常量
  public static final String POINT_ADDRESS_TANK601_TURBIDITY1 = "XMSW_Slurry_Tank601_Turbidity1";
  public static final String POINT_ADDRESS_TANK601_TURBIDITY2 = "XMSW_Slurry_Tank601_Turbidity2";
  
  // 阳离子投加系统点位地址常量
  public static final String POINT_ADDRESS_CATIONIC_VIBRATION_MOT_RUN = 
      "XMSW_Dosing_CationicVibrationMOT_RunReturn";
  public static final String POINT_ADDRESS_CATIONIC_FEEDER_MOT_RUN = 
      "XMSW_Dosing_CationicFeederMOT_RunReturn";
  public static final String POINT_ADDRESS_CATIONIC_PUMP1_RUN = 
      "XMSW_Dosing_CationicPump1_RunReturn";
  public static final String POINT_ADDRESS_CATIONIC_PUMP2_RUN = 
      "XMSW_Dosing_CationicPump2_RunReturn";
  public static final String POINT_ADDRESS_CATIONIC_FEEDER_MOT_FREQUENCY = 
      "XMSW_Dosing_CationicFeederMOT_frequency";
  public static final String POINT_ADDRESS_CATIONIC_PUMP1_FREQUENCY = 
      "XMSW_Dosing_CationicPump1_Frequency";
  public static final String POINT_ADDRESS_CATIONIC_PUMP2_FREQUENCY = 
      "XMSW_Dosing_CationicPump2_Frequency";
  public static final String POINT_ADDRESS_SLURRY_DOSING_FLOW1 = 
      "XMSW_Slurry_Dosing_Flow1";
  public static final String POINT_ADDRESS_SLURRY_DOSING_FLOW2 = 
      "XMSW_Slurry_Dosing_Flow2";
      
  // 阴离子投加系统点位地址常量
  public static final String POINT_ADDRESS_ANION_VIBRATION_MOT_RUN = 
      "XMSW_Dosing_AnionVibrationMOT_RunReturn";
  public static final String POINT_ADDRESS_ANION_FEEDER_MOT_RUN = 
      "XMSW_Dosing_AnionFeederMOT_RunReturn";
  public static final String POINT_ADDRESS_ANION_PUMP1_RUN = 
      "XMSW_Dosing_AnionPump1_RunReturn";
  public static final String POINT_ADDRESS_ANION_PUMP2_RUN = 
      "XMSW_Dosing_AnionPump2_RunReturn";
  public static final String POINT_ADDRESS_ANION_FEEDER_MOT_FREQUENCY = 
      "XMSW_Dosing_AnionFeederMOT_Frequency";
  public static final String POINT_ADDRESS_ANION_PUMP1_FREQUENCY = 
      "XMSW_Dosing_AnionPump1_Frequency";
  public static final String POINT_ADDRESS_ANION_PUMP2_FREQUENCY = 
      "XMSW_Dosing_AnionPump2_Frequency";
  public static final String POINT_ADDRESS_SLURRY_DOSING_FLOW3 = 
      "XMSW_Slurry_Dosing_Flow3";

  // 3#浓缩池
  private BigDecimal thickener412ThickValue; // 耙位
  private BigDecimal tank412ThickTorque; // 扭矩
  private BigDecimal tank412ConcentrationOut; // 底流浓度
  private BigDecimal tank412InstrumentNum; // 界面仪

  // 2#浓缩池
  private BigDecimal thickener602ThickValue; // 耙位
  private BigDecimal thickene602ThickTorque; // 扭矩
  private BigDecimal tank602ConcentrationOut; // 底流浓度
  private BigDecimal tank601InstrumentNum; // 界面仪

  // 1#浓缩池
  private BigDecimal thickener601ThickValue; // 耙位
  private BigDecimal tank601ThickTorque; // 扭矩
  private BigDecimal tank601ConcentrationOut; // 底流浓度
  private BigDecimal tank601InstrumentNum1; // 界面仪

  // 水质监测
  private BigDecimal tank601Turbidity1; // 新主洗循环水池浊度(3#)
  private BigDecimal tank601Turbidity2; // 旧主洗循环水池浊度(1、2#)
  
  // 阳离子投加系统运行状态
  private Boolean cationicVibrationMotRunState; // 振动电机运行状态
  private Boolean cationicFeederMotRunState; // 给料电机运行状态
  private Boolean cationicPump1RunState; // 泵1运行状态
  private Boolean cationicPump2RunState; // 泵2运行状态
  
  // 阳离子投加系统工艺参数
  private BigDecimal cationicFeederMotFrequency; // 调浓度
  private BigDecimal cationicPump1Frequency; // 频率1
  private BigDecimal cationicPump2Frequency; // 频率2
  private BigDecimal slurryDosingFlow1; // 新主洗流量
  private BigDecimal slurryDosingFlow2; // 旧主洗流量
  
  // 阴离子投加系统运行状态
  private Boolean anionVibrationMotRunState; // 振动电机运行状态
  private Boolean anionFeederMotRunState; // 给料电机运行状态
  private Boolean anionBlowerRunState; // 鼓风机运行状态
  private Boolean anionPump1RunState; // 泵1运行状态
  private Boolean anionPump2RunState; // 泵2运行状态
  
  // 阴离子投加系统工艺参数
  private BigDecimal anionFeederMotFrequency; // 调浓度
  private BigDecimal anionPump1Frequency; // 频率1
  private BigDecimal anionPump2Frequency; // 频率2
  private BigDecimal slurryDosingFlow3; // 流量计
}
