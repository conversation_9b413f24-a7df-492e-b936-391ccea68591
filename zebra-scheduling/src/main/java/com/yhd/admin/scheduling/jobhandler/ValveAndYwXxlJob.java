package com.yhd.admin.scheduling.jobhandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class ValveAndYwXxlJob {

  private String bmsUrl;

  private final RestTemplate restTemplate;

  public ValveAndYwXxlJob(@Value("${bms.url}") String bmsUrl, RestTemplate restTemplate) {
    this.bmsUrl = bmsUrl;
    this.restTemplate = restTemplate;
  }

  @XxlJob("txValveAndYwTask")
  public void tx() {
    HttpHeaders headers = new HttpHeaders();
    headers.add("Content-Type", "application/json");

    HttpEntity<String> httpEntity = new HttpEntity<>("{}", headers);

    String retVal =
        restTemplate.postForObject(bmsUrl + "/api/jj/txValveAndYw", httpEntity, String.class);
    XxlJobHelper.log(retVal);
    if (StringUtils.contains(retVal, "ok")) {
      XxlJobHelper.handleSuccess(retVal);
    } else {
      XxlJobHelper.handleFail(retVal);
    }
  }
}
