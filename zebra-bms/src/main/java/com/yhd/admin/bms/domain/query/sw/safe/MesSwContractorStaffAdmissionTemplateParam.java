package com.yhd.admin.bms.domain.query.sw.safe;

import lombok.Data;

import java.io.Serializable;

/**
 * 承包商员工入场管理
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@Data
public class MesSwContractorStaffAdmissionTemplateParam implements Cloneable, Serializable {
  /** 序号 */
  private Integer index;
  /** 入厂工作流程步骤 */
  private String step;
  /** 管理类别 */
  private String category;
  /** 具体工作内容 */
  private String content;
  /** 分管厂领导-账号 */
  private String factoryLeaderAccount;
  /** 分管厂领导-名称 */
  private String factoryLeaderName;
  /** 部门主管 */
  private String departmentHead;
}
