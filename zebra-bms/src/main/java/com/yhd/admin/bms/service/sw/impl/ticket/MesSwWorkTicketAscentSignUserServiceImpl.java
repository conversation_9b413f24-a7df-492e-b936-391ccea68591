package com.yhd.admin.bms.service.sw.impl.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.ticket.MesSwWorkTicketAscentSignUserDao;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketAscentSignUserConvert;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketAscentSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketAscentSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketAscentSignUserParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketAscentSignUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 登高作业工作票签字用户信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
@Service
public class MesSwWorkTicketAscentSignUserServiceImpl extends ServiceImpl<MesSwWorkTicketAscentSignUserDao, MesSwWorkTicketAscentSignUser> implements MesSwWorkTicketAscentSignUserService {

    @Resource
    private MesSwWorkTicketAscentSignUserConvert convert;

    @Resource
    private MesSwWorkTicketAscentSignUserService service;

    @Override
    public IPage<MesSwWorkTicketAscentSignUserDTO> pagingQuery(MesSwWorkTicketAscentSignUserParam queryParam) {
        Page<MesSwWorkTicketAscentSignUser> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwWorkTicketAscentSignUser> queryChain = new LambdaQueryChainWrapper<>(baseMapper);

        queryChain.orderByDesc(MesSwWorkTicketAscentSignUser::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }


    @Override
    public Boolean add(MesSwWorkTicketAscentSignUserParam param) {
        MesSwWorkTicketAscentSignUser entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MesSwWorkTicketAscentSignUserParam param) {
        MesSwWorkTicketAscentSignUser entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MesSwWorkTicketAscentSignUserDTO getCurrentDetail(MesSwWorkTicketAscentSignUserParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
