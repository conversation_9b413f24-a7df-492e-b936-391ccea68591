package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 个人防护记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwProtectionParam extends QueryParam implements Serializable, Cloneable {

    private Long id;

    /**
     * 检查时间
     */
    private LocalDateTime checkTime;

    /**
     * 检查人
     */
    private String checkUser;

    /**
     * 检查人名称
     */
    private String checkName;

    /**
     * 被检查人
     */
    private String onCheck;

    /**
     * 被检查人名称
     */
    private String onCheckName;

    /**
     * 单位
     */
    private String department;

    /**
     * 单位code
     */
    private String departmentCode;

    /**
     * 车间
     */
    private String shop;

    /**
     * 车间code
     */
    private String shopCode;

    /**
     * 工作服
     */
    private String workClothes;
    /**
     * 防砸鞋
     */
    private String shoes;
    /**
     * 安全帽
     */
    private String safeHat;
    /**
     * 耳塞
     */
    private String earplug;
    /**
     * 防尘口罩
     */
    private String mask;
    /**
     * 存在问题
     */
    private String problem;

    /**
     * 查询日期区间
     */
    private String[] time;
}
