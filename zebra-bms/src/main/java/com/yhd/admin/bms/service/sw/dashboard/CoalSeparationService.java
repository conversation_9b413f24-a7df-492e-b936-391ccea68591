package com.yhd.admin.bms.service.sw.dashboard;

import com.yhd.admin.bms.domain.vo.sw.dashboard.CoalSeparationDataVO;
import com.yhd.admin.bms.domain.vo.sw.dashboard.MesIntelligentDosingVO;
import com.yhd.admin.bms.domain.vo.sw.dashboard.MesIntelligentSortControlVO;
import com.yhd.admin.bms.domain.vo.sw.dashboard.MesIntelligentSortPointVO;
import com.yhd.admin.bms.domain.vo.sw.dashboard.MesIntelligentBlendingVO;
import com.yhd.admin.bms.domain.vo.sw.dashboard.MesIntelligentDosingControlVO;
import com.yhd.admin.bms.domain.vo.sw.dashboard.TimeDataVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 智能分选(接点位数据)-业务层接口
 *
 * <AUTHOR>
 * @date 2024/9/29 15:24
 */
public interface CoalSeparationService {

  /**
   * 获取智能分选数据
   *
   * @return CoalSeparationDataVO
   */
  CoalSeparationDataVO coalSeparation();

  /**
   * 获取智能分选点位数据
   *
   * @return MesIntelligentSortPointVO
   */
  MesIntelligentSortPointVO getIntelligentSortPointData();

  /**
   * 获取智能分选控制逻辑点位数据
   *
   * @return MesIntelligentSortControlVO
   */
  MesIntelligentSortControlVO getIntelligentSortControlData();

  /**
   * 获取智能加药系统监控点位数据
   *
   * @return MesIntelligentDosingVO
   */
  MesIntelligentDosingVO getIntelligentDosingData();

  /**
   * 获取智能配煤系统监控点位数据
   *
   * @return MesIntelligentBlendingVO
   */
  MesIntelligentBlendingVO getIntelligentBlendingData();

  /**
   * 获取智能加药控制逻辑点位数据
   *
   * @return MesIntelligentDosingControlVO
   */
  MesIntelligentDosingControlVO getIntelligentDosingControlData();

  /**
   * 获取305密度曲线
   *
   * @return 305密度列表
   */
  List<TimeDataVO> getDensity305Curve();

  /**
   * 获取363密度曲线=374合介泵上密度
   *
   * @return 305密度列表
   */
  List<TimeDataVO> getDensity363Curve();

  /**
   * 获取原煤灰分301曲线
   *
   * @return 原煤灰分
   */
  List<TimeDataVO> getRawCoalAshCurve();

  /**
   * 获取精煤灰分701曲线
   *
   * @return 精煤灰分
   */
  List<TimeDataVO> getCleanCoalAshCurve();

  /**
   * 获取矸石产量曲线
   *
   * @return 矸石产量
   */
  List<TimeDataVO> getGangueCountCurve();

  /**
   * 原煤实时入洗量-取302数据(旧主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal rawCoalWash302();

  /**
   * 原煤当日入洗量-取302数据(旧主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal rawCoalWashToday302();

  /**
   * 原煤实时入洗量-取301数据(新主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal rawCoalWash301();

  /**
   * 原煤当日入洗量-取301数据(新主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal rawCoalWashToday301();

  /**
   * 精煤实时产量-取701数据(旧主洗=新主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal cleanCoalOutput701();

  /**
   * 精煤当日产量-(旧主洗=新主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal cleanCoalOutputToday();

  /**
   * 实际矸石产量-取802数据(旧主洗=新主洗)[涉及其他计算单独获取]
   *
   * @return 数值
   */
  BigDecimal gangueOutput802();
}
