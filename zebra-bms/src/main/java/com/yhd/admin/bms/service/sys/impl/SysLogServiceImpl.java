package com.yhd.admin.bms.service.sys.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sys.SysLogDao;
import com.yhd.admin.bms.domain.convert.sys.SysLogConvert;
import com.yhd.admin.bms.domain.dto.sys.SysLogDTO;
import com.yhd.admin.bms.domain.entity.sys.SysLog;
import com.yhd.admin.bms.domain.query.sys.SysLogParam;
import com.yhd.admin.bms.service.sys.SysLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogDao, SysLog> implements SysLogService {

  @Resource private SysLogConvert convert;

  @Override
  public IPage<SysLogDTO> pagingQuery(SysLogParam queryParam) {
    IPage<SysLog> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<SysLog> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
    // 默认查询当前月份到当前时间的操作数据
    LocalDate now = LocalDate.now();
    LocalDate first = LocalDate.of(now.getYear(), now.getMonth(), 1);
    LocalTime firstTime = LocalTime.of(0, 0, 0);
    LocalTime endTime = LocalTime.now();

    if (!StringUtils.isEmpty(queryParam.getStartDate())) {
      queryChain.ge(SysLog::getCreatedTime, LocalDateTime.of(queryParam.getStartDate(), firstTime));
    } else {
      queryChain.ge(SysLog::getCreatedTime, LocalDateTime.of(first, firstTime));
    }
    if (!StringUtils.isEmpty(queryParam.getEndDate())) {
      queryChain.le(SysLog::getCreatedTime, LocalDateTime.of(queryParam.getEndDate(), endTime));
    } else {
      queryChain.le(SysLog::getCreatedTime, LocalDateTime.of(now, endTime));
    }
    if (!StringUtils.isEmpty(queryParam.getOperName())) {
      queryChain.like(SysLog::getOperName, queryParam.getOperName());
    }
    queryChain.orderByDesc(SysLog::getCreatedTime);
    return queryChain.page(iPage).convert(convert::toDTO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(SysLogParam sysLogParam) {
    return save(convert.toEntity(sysLogParam));
  }
}
