package com.yhd.admin.bms.domain.entity.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SysAttachment extends BaseEntity {

    /**
     * 编号
     */
    private String attNo;
    /**
     * 附件名称
     */
    private String attName;
    /**
     * 附件类型;1图片，2文本，3
     */
    private String attTypes;
    /**
     * 地址
     */
    private String attAddress;
    /**
     * 状态;0不可用1可用
     */
    private Boolean attStatus;
}
