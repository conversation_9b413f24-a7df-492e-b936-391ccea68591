package com.yhd.admin.bms.service.sw.impl.consumption;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.consumption.MesConsumptionAntifreezeDao;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionAntifreezeDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionAntifreeze;
import com.yhd.admin.bms.domain.enums.produce.KHConsumptionPointEnum;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionAntifreezeParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionAntifreezeService;
import com.yhd.admin.bms.service.sw.dashboard.KHPointService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 能耗管理-防冻液
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
@Service
@Slf4j
public class MesConsumptionAntifreezeImpl
    extends ServiceImpl<MesConsumptionAntifreezeDao, MesConsumptionAntifreeze>
    implements MesConsumptionAntifreezeService {

  @Resource private KHPointService khPointService;

  @Override
  public List<MesConsumptionAntifreezeDTO> queryList(MesConsumptionAntifreezeParam param) {
    String year = param.getYear();
    if (StringUtils.isBlank(year)) {
      year = String.valueOf(LocalDate.now().getYear());
    }
    List<MesConsumptionAntifreezeDTO> result = Lists.newArrayList();
    // 防冻液数据
    LambdaQueryChainWrapper<MesConsumptionAntifreeze> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.like(MesConsumptionAntifreeze::getDate, year);
    List<MesConsumptionAntifreeze> waterList = wrapper.list();
    // 表里没有当天数据，需要去查询今天的数据
    MesConsumptionAntifreeze antifreezeToday = queryTodayData();
    if (antifreezeToday.getChute() != null || antifreezeToday.getWagons() != null) {
      // 只要有一项不为null，即为有效数据
      waterList.add(antifreezeToday);
    }

    if (CollectionUtil.isEmpty(waterList)) {
      return result;
    }

    // 分组累加
    Map<String, MesConsumptionAntifreeze> waterMap =
        waterList.stream()
            .collect(
                Collectors.groupingBy(
                    bean ->
                        bean.getDate().getYear()
                            + "-"
                            + String.format("%02d", bean.getDate().getMonthValue()),
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                          BigDecimal chute =
                              list.stream()
                                  .map(MesConsumptionAntifreeze::getChute)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          BigDecimal wagons =
                              list.stream()
                                  .map(MesConsumptionAntifreeze::getWagons)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          MesConsumptionAntifreeze sums = new MesConsumptionAntifreeze();
                          sums.setChute(chute);
                          sums.setWagons(wagons);
                          return sums;
                        })));

    for (Map.Entry<String, MesConsumptionAntifreeze> entry : waterMap.entrySet()) {
      String month = entry.getKey();
      MesConsumptionAntifreeze total = entry.getValue();
      // 生成返回数据，防冻液
      MesConsumptionAntifreezeDTO item = new MesConsumptionAntifreezeDTO();
      item.setMonth(month);
      BigDecimal chute = total.getChute();
      item.setChute(chute);
      BigDecimal wagons = total.getWagons();
      item.setWagons(wagons);
      // 总流量=溜槽流量+车皮流量
      BigDecimal totalSum = addBigDecimal(chute, wagons);
      item.setTotalSum(totalSum);
      result.add(item);
    }
    result.sort((b1, b2) -> b2.getMonth().compareTo(b1.getMonth()));
    return result;
  }

  /**
   * 查询防冻液当天的消耗量
   *
   * @return
   */
  private MesConsumptionAntifreeze queryTodayData() {
    // 补查当天数据，表里没有当天数据
    // 防冻液点位
    List<String> pointKeyList = new ArrayList<>();
    pointKeyList.add(KHConsumptionPointEnum.XMSW_Load_Antifreeze_Flux.getKey());
    pointKeyList.add(KHConsumptionPointEnum.XMSW_Load_Antifreeze_Production.getKey());
    Map<String, BigDecimal> pointDataMap =
        khPointService.getConsumptionByParams(LocalDate.now().toString(), pointKeyList);
    // 能耗管理-防冻液
    MesConsumptionAntifreeze antifreezePO = new MesConsumptionAntifreeze();
    antifreezePO.setDate(LocalDate.now());
    if (CollUtil.isNotEmpty(pointDataMap)) {
      pointDataMap.forEach(
          (key, value) -> {
            if (KHConsumptionPointEnum.XMSW_Load_Antifreeze_Flux.getKey().equals(key)) {
              antifreezePO.setChute(value);
            } else if (KHConsumptionPointEnum.XMSW_Load_Antifreeze_Production.getKey()
                .equals(key)) {
              antifreezePO.setWagons(value);
            }
          });
    }
    return antifreezePO;
  }

  @Override
  public List<MesConsumptionAntifreezeDTO> getCurrentDetail(MesConsumptionAntifreezeParam param) {
    String month = param.getMonth();
    if (StringUtils.isBlank(month)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询参数为空，请检查！");
    }
    List<MesConsumptionAntifreezeDTO> result = Lists.newArrayList();
    // 防冻液数据
    LambdaQueryChainWrapper<MesConsumptionAntifreeze> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.like(MesConsumptionAntifreeze::getDate, month);
    List<MesConsumptionAntifreeze> waterList = wrapper.list();
    // 表里没有当天数据，需要去查询今天的数据
    MesConsumptionAntifreeze antifreezeToday = queryTodayData();
    if (antifreezeToday.getChute() != null || antifreezeToday.getWagons() != null) {
      // 只要有一项不为null，即为有效数据
      waterList.add(antifreezeToday);
    }

    if (CollectionUtil.isEmpty(waterList)) {
      return result;
    }

    // 分组累加
    Map<LocalDate, List<MesConsumptionAntifreeze>> waterMap =
        CollStreamUtil.groupByKey(waterList, MesConsumptionAntifreeze::getDate);

    // 月合计
    BigDecimal monthChute = null;
    BigDecimal monthWagons = null;

    for (Map.Entry<LocalDate, List<MesConsumptionAntifreeze>> entry : waterMap.entrySet()) {
      LocalDate date = entry.getKey();
      List<MesConsumptionAntifreeze> dayConsumption = entry.getValue();
      // 生成返回数据，防冻液
      MesConsumptionAntifreezeDTO item = new MesConsumptionAntifreezeDTO();
      item.setDate(date);
      item.setDateStr(date.toString());
      if (CollectionUtil.isNotEmpty(dayConsumption)) {
        MesConsumptionAntifreeze totalItem = dayConsumption.get(0);
        BigDecimal chute = totalItem.getChute();
        item.setChute(chute);
        BigDecimal wagons = totalItem.getWagons();
        item.setWagons(wagons);
        // 总流量=溜槽流量+车皮流量
        BigDecimal totalSum = addBigDecimal(chute, wagons);
        item.setTotalSum(totalSum);

        // 月合计，累加防冻液
        monthChute = addBigDecimal(monthChute, chute);
        monthWagons = addBigDecimal(monthWagons, wagons);
      }
      result.add(item);
    }
    result.sort(Comparator.comparing(MesConsumptionAntifreezeDTO::getDate));
    if (CollectionUtil.isNotEmpty(result)) {
      // 结果不为空，将合计也放入
      MesConsumptionAntifreezeDTO monthTotal = new MesConsumptionAntifreezeDTO();
      monthTotal.setDateStr("合计");
      monthTotal.setChute(monthChute);
      monthTotal.setWagons(monthWagons);
      monthTotal.setTotalSum(addBigDecimal(monthChute, monthWagons));
      result.add(monthTotal);
    }

    return result;
  }

  @Override
  public List<MesConsumptionAntifreezeDTO> queryIntervalList(LocalDate start, LocalDate end) {
    List<MesConsumptionAntifreezeDTO> result = Lists.newArrayList();
    // 防冻液数据
    LambdaQueryChainWrapper<MesConsumptionAntifreeze> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.between(MesConsumptionAntifreeze::getDate, start, end);
    List<MesConsumptionAntifreeze> waterList = wrapper.list();
    // 核心逻辑：包含边界（>= start 且 <= end）
    LocalDate now = LocalDate.now();
    boolean isBetween = !now.isBefore(start) && !now.isAfter(end);
    if (isBetween) {
      // 表里没有当天数据，需要去查询今天的数据
      MesConsumptionAntifreeze antifreezeToday = queryTodayData();
      if (antifreezeToday.getChute() != null || antifreezeToday.getWagons() != null) {
        // 只要有一项不为null，即为有效数据
        waterList.add(antifreezeToday);
      }
    }
    // 分组累加
    Map<LocalDate, List<MesConsumptionAntifreeze>> waterMap =
        CollStreamUtil.groupByKey(waterList, MesConsumptionAntifreeze::getDate);

    for (Map.Entry<LocalDate, List<MesConsumptionAntifreeze>> entry : waterMap.entrySet()) {
      LocalDate date = entry.getKey();
      List<MesConsumptionAntifreeze> dayConsumption = entry.getValue();
      // 生成返回数据，防冻液
      MesConsumptionAntifreezeDTO item = new MesConsumptionAntifreezeDTO();
      item.setDate(date);
      item.setDateStr(date.toString());
      if (CollectionUtil.isNotEmpty(dayConsumption)) {
        MesConsumptionAntifreeze totalItem = dayConsumption.get(0);
        BigDecimal chute = totalItem.getChute();
        item.setChute(chute);
        BigDecimal wagons = totalItem.getWagons();
        item.setWagons(wagons);
        // 总流量=溜槽流量+车皮流量
        BigDecimal totalSum = addBigDecimal(chute, wagons);
        item.setTotalSum(totalSum);
      }
      result.add(item);
    }
    result.sort(Comparator.comparing(MesConsumptionAntifreezeDTO::getDate));
    return result;
  }

  private static BigDecimal addBigDecimal(BigDecimal... values) {
    if (values != null && values.length > 0 && values[0] == null) {
      values[0] = BigDecimal.ZERO;
    }
    return NumberUtil.add(values);
  }
}
