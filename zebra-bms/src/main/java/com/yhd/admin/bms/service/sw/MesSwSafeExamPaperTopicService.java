package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperTopicDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeExamPaperTopic;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperTopicParam;

/**
 * <AUTHOR>
 * @Date 2024/3/3 13:52
 * @Version 1.0
 */

public interface MesSwSafeExamPaperTopicService extends IService<MesSwSafeExamPaperTopic> {
    /**查看试卷试题*/
    MesSwSafeExamPaperTopicDTO getQuestion(MesSwSafeExamPaperTopicParam param);
    /**组卷时查看试卷试题*/
    MesSwSafeExamPaperTopicDTO getQuestionByAdd(MesSwSafeExamPaperTopicParam param);
}
