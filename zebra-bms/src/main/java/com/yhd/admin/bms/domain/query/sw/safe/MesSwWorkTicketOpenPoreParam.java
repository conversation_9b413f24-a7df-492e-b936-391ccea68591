package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 承包商员工入场管理
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketOpenPoreParam extends QueryParam implements Cloneable, Serializable {
  /** 申请单位 */
  private String applyUnit;
  /** 申请人账号 */
  private String applyAccount;
  /** 申请人名称 */
  private String applyName;
  /** 作业地点 */
  private String workLocation;
  /** 计划施工时间-开始 */
  private LocalDateTime sgStartTime;
  /** 计划施工时间-结束 */
  private LocalDateTime sgEndTime;
  /** 作业内容 */
  private String workContent;
  /** 开孔尺寸 */
  private String openPoreSize;
  /** 至基准面高度 */
  private String planeHeight;
  /** 作业负责人账号 */
  private String chargeAccount;
  /** 作业负责人名称 */
  private String chargeName;
  /** 监护人账号 */
  private String guardianAccount;
  /** 监护人名称 */
  private String guardianName;
  /** 工作票状态code */
  private String statusCode;
  /** 工作票状态name */
  private String statusName;
  /** 工作票提交人code */
  private String tjUserAccount;
  /** 工作票提交人姓名 */
  private String tjUserName;
  /** 工作票提交时间 */
  private LocalDateTime tjTime;

  /** 选煤厂分管负责人审批集合 */
  private List<MesSwWorkTicketOpenPoreSignParam> branchList;
  /** 作业负责人审批 */
  private List<MesSwWorkTicketOpenPoreSignParam> workList;

  /** 发起时间-开始 */
  private String startTime;
  /** 发起时间-开始 */
  private String endTime;
  /** 下拉框类型 0-申请单位，1-作业地点 */
  private String type;
}
