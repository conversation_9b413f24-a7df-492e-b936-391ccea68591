package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwProtectionDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwProtection;
import com.yhd.admin.bms.domain.query.sw.MesSwProtectionParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

/**
 * 交接班管理
 */
public interface MesSwProtectionService extends IService<MesSwProtection> {

    /**
     * 新增
     *
     * @param addParam
     * @return 【true 成功，false 失败】
     */
    Boolean add(MesSwProtectionParam addParam);

    /**
     * 修改
     *
     * @param modifyParam
     * @return 【true 成功，false 失败】
     */
    Boolean modify(MesSwProtectionParam modifyParam);

    /**
     * 批量删除
     *
     * @param removeParam
     * @return 【t 成功，false 失败】
     */
    Boolean removeBatch(BatchParam removeParam);

    /**
     * 列表分页查询
     *
     * @param queryParam
     * @return IPage<MesSwProtectionDTO>
     */
    IPage<MesSwProtectionDTO> pagingQuery(MesSwProtectionParam queryParam);

    /**
     * @param queryParam
     * @return
     */
    MesSwProtectionDTO currentDetail(MesSwProtectionParam queryParam);
}
