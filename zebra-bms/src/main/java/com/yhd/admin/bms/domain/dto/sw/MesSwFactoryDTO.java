package com.yhd.admin.bms.domain.dto.sw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工业厂区草坪
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesSwFactoryDTO extends BaseDTO implements Cloneable, Serializable {


    /**
     * 检查时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime checkTime;

    /**
     * 责任车间
     */
    private String workShop;

    /**
     * 责任车间
     */
    private String workShopCode;

    /**
     * 检查人
     */
    private String checker;

    /**
     * 检查人账号
     */
    private String checkerCode;

    /**
     * 消防设置是否完好
     */
    private String fireFacilities;

    /**
     * 是否有火灾隐患
     */
    private String fireHazard;

    /**
     * 隐患描述
     */
    private String dangerDesc;

    /**
     * 隐患消除情况(是、否)
     */
    private String dangerHidden;

    /**
     * 隐患消除时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime troubleTime;

    /**
     * 复查人
     */
    private String reviewer;

    /**
     * 复查人
     */
    private String reviewerCode;


}
