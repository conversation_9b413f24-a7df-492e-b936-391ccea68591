package com.yhd.admin.bms.domain.enums.ticket;

import com.yhd.admin.bms.domain.enums.safe.ExamStatusEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/17 9:39
 * @Version 1.0
 */

@Getter
public enum WorkTicketElectricSpEnum {
    ZZ_USER("ZZ_USER", "作业申请人"),
    DD_USER("DD_USER", "调度员"),
    LD_USER("LD_USER", "值班厂领导");

    private final String code;
    private final String desc;

    WorkTicketElectricSpEnum(String code, String description) {
        this.code = code;
        this.desc = description;
    }

    public static List<String> getCodeList() {
        WorkTicketElectricSpEnum[] values = WorkTicketElectricSpEnum.values();

        return Arrays.stream(values).map(WorkTicketElectricSpEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getValuesList() {
        WorkTicketElectricSpEnum[] values = WorkTicketElectricSpEnum.values();

        return Arrays.stream(values).map(WorkTicketElectricSpEnum::getDesc).collect(Collectors.toList());
    }

    // 根据状态获取状态枚举
    public static ExamStatusEnum getStatusEnumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        ExamStatusEnum[] values = ExamStatusEnum.values();
        for (ExamStatusEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
