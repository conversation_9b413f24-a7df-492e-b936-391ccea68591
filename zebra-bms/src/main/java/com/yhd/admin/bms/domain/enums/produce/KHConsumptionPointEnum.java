package com.yhd.admin.bms.domain.enums.produce;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** 能耗管理-亚控点位key枚举类 */
@Getter
public enum KHConsumptionPointEnum {
  /** 水耗 */
  XMSW_Lump_WTRmeter_WaterUsageQSBF1("XMSW_Lump_WTRmeter_WaterUsageQSBF1", "水表1累积量"),
  XMSW_Lump_WTRmeter_WaterUsageQSBF2("XMSW_Lump_WTRmeter_WaterUsageQSBF2", "水表2累积量"),
  XMSW_Lump_WTRmeter_WaterUsageQSBF3("XMSW_Lump_WTRmeter_WaterUsageQSBF3", "水表3累积量"),

  /** 防冻液 */
  XMSW_Load_Antifreeze_Flux("XMSW_Load_Antifreeze_Flux", "防冻液溜槽流量累积量"),
  XMSW_Load_Antifreeze_Production("XMSW_Load_Antifreeze_Production", "防冻液车皮流量累积量"),

  /** 电耗 */
  HighVoltageOld_6111Incomingline1_ForwardkWh("HighVoltageOld_6111Incomingline1_ForwardkWh", "旧高压1号进线"),
  HighVoltageOld_6205Incomingline2_ForwardkWh("HighVoltageOld_6205Incomingline2_ForwardkWh", "旧高压2号进线"),
  HighVoltageNewLump_6114Incomingline1_ForwardkWh(
      "HighVoltageNewLump_6114Incomingline1_ForwardkWh", "新高压配电室1#进线（6114）"),
  HighVoltageNewLump_6214Incomingline2_ForwardkWh(
      "HighVoltageNewLump_6214Incomingline2_ForwardkWh", "新高压配电室2#进线（6214）"),

  /** 原煤产量 */
  XMSW_Raw_CON201_Info_ProductToday("XMSW_Raw_CON201_Info_ProductToday", "201胶带机"),
  XMSW_Raw_CON218_Info_ProductToday("XMSW_Raw_CON218_Info_ProductToday", "218胶带机"),
  ;

  private final String key;
  private final String desc;

  KHConsumptionPointEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }

  public static List<String> getKeyList() {
    KHConsumptionPointEnum[] values = KHConsumptionPointEnum.values();

    return Arrays.stream(values).map(KHConsumptionPointEnum::getKey).collect(Collectors.toList());
  }

  public static KHConsumptionPointEnum getEnumByKey(String key) {
    if (StringUtils.isBlank(key)) {
      return null;
    }
    KHConsumptionPointEnum[] values = KHConsumptionPointEnum.values();
    for (KHConsumptionPointEnum value : values) {
      if (value.getKey().equals(key)) {
        return value;
      }
    }
    return null;
  }
}
