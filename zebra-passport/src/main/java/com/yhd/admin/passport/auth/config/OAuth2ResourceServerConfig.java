package com.yhd.admin.passport.auth.config;

import com.yhd.admin.passport.auth.handler.AuthAccessDeniedHandler;
import com.yhd.admin.passport.auth.handler.AuthExceptionEntryPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResourceServerConfig.java
 * @Description TODO
 * @createTime 2020年03月23日 15:23:00
 */
@Configuration
@EnableResourceServer
public class OAuth2ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Autowired
    private AuthAccessDeniedHandler authAccessDeniedHandler;

    @Autowired
    private AuthExceptionEntryPoint authExceptionEntryPoint;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
            .antMatchers(
                "/**/login",
                "/**/logout",
                "/oauth/**"
            )
            .permitAll().and()
            .exceptionHandling().
            accessDeniedHandler(authAccessDeniedHandler).and().authorizeRequests()
            .anyRequest()
            .authenticated();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {
        resources.authenticationEntryPoint(authExceptionEntryPoint);
    }
}
