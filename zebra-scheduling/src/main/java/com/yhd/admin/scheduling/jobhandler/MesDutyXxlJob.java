package com.yhd.admin.scheduling.jobhandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 值班管理数据 调度任务 Bean
 */
@Component
public class MesDutyXxlJob {
    private static Logger logger = LoggerFactory.getLogger(MesDutyXxlJob.class);

    // 初始换接口
    private final String initMesDuty = "/api/dutyAuto/initMesDuty";
    // 能耗管理的数据地址
    private final String ecmUrl = "/api/job/stopSafeHiddenDanger";
    // 超时关闭设备巡检的数据地址
    private final String eqiUrl = "/api/job/stopEquipmentInspection";
    // 自动创建设备巡检的数据地址
    private final String createUrl = "/api/job/saveCreateInspection";
    // 厂领导抽查复检定期推送数据地址
    private final String leaderCheckUrl = "/api/job/savePush";

    // 生成-保护试验台账的数据地址
    private final String saveLedgerUrl = "/api/job/saveAddProtectLedger";
    // 未完成的保护试验台账,检测是否通电的数据地址
    private final String ledgerPowerUrl = "/api/job/saveProtectLedgerToPower";
    // 未完成的保护试验台账,检测是否有信号的数据地址
    private final String ledgerFinishUrl = "/api/job/saveProtectLedgerToFinish";
    // 已完成的保护试验台账,检测是否有信号的数据地址
    private final String ledgerConfirmUrl = "/api/job/saveProtectLedgerToConfirm";
    // 已完成的保护试验台账,没有收到信号,需提交检修单的数据地址
    private final String ledgerErrorUrl = "/api/job/saveProtectLedgerToError";
    // 当天执行，定时生成昨天的能耗数据
    private final String saveConsumptionUrl = "/api/job/saveConsumption";
    // 定时任务，每天23点采集电厂皮带数据
    private final String collectSaveDcpdUrl = "/api/job/collectSaveDcpd";
    // 每天凌晨1点定时任务，采集商品煤日信息
    private final String saveCoalDailyUrl = "/api/job/saveCoalDaily";
    // 定时任务两小时执行一次，采集设备报警信息
    private final String saveEqptWarn = "/api/job/saveEqptWarn";

    @Resource
    private RestTemplate restTemplate;

    @Value("${bms.url}")
    private String bmsUrl;

    @XxlJob("initMesDuty")
    @Transactional(rollbackFor = Exception.class)
    public void initMesDuty() throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(initMesDuty);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(null, headers);

        ResponseEntity<Object> responseEntity =
            restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

        XxlJobHelper.log(responseEntity.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("stopSafeHiddenDanger")
    @Transactional(rollbackFor = Exception.class)
    public void stopSafeHiddenDanger(String param) throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(ecmUrl);
        if (StringUtils.isNotBlank(param)) {
            httpurl.append("?time=" + param);
        }
        String result = restTemplate.getForObject(httpurl.toString(), String.class);
        XxlJobHelper.log(result);
        XxlJobHelper.handleSuccess(result);
    }

    @XxlJob("stopEquipmentInspection")
    @Transactional(rollbackFor = Exception.class)
    public void stopEquipmentInspection(String param) throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(eqiUrl);
        if (StringUtils.isNotBlank(param)) {
            httpurl.append("?time=" + param);
        }
        String result = restTemplate.getForObject(httpurl.toString(), String.class);
        XxlJobHelper.log(result);
        XxlJobHelper.handleSuccess(result);
    }

    @XxlJob("saveCreateInspection")
    @Transactional(rollbackFor = Exception.class)
    public void saveCreateInspection() throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(createUrl);
        Map<String, LocalDate> params = new HashMap<>(1);
        params.put("startDate", LocalDate.now());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity = new HttpEntity<>(params, headers);

        ResponseEntity<Object> responseEntity =
            restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

        XxlJobHelper.log(responseEntity.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("leaderCheckUrl")
    @Transactional(rollbackFor = Exception.class)
    public void leaderCheckUrl() throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(leaderCheckUrl);
        Map<String, LocalDate> params = new HashMap<>(1);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity = new HttpEntity<>(params, headers);

        ResponseEntity<Object> responseEntity =
            restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

        XxlJobHelper.log(responseEntity.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("saveProtectLedgerToPowerOrFinish")
    @Transactional(rollbackFor = Exception.class)
    public void saveProtectLedgerToPowerOrFinish(String param) throws Exception {
        StringBuffer httpurl1 = new StringBuffer(bmsUrl).append(ledgerPowerUrl);
        Map<String, LocalDate> params1 = new HashMap<>(1);
        HttpHeaders headers1 = new HttpHeaders();
        headers1.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity1 = new HttpEntity<>(params1, headers1);
        ResponseEntity<Object> responseEntity1 =
            restTemplate.postForEntity(httpurl1.toString(), httpEntity1, Object.class);

        StringBuffer httpurl2 = new StringBuffer(bmsUrl).append(ledgerFinishUrl);
        Map<String, LocalDate> params2 = new HashMap<>(1);
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity2 = new HttpEntity<>(params2, headers2);
        ResponseEntity<Object> responseEntity2 =
            restTemplate.postForEntity(httpurl2.toString(), httpEntity2, Object.class);

        XxlJobHelper.log(
            responseEntity1.getBody().toString() + "/n" + responseEntity2.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("saveProtectLedgerToConfirmOrError")
    @Transactional(rollbackFor = Exception.class)
    public void saveProtectLedgerToConfirmOrError(String param) throws Exception {
        StringBuffer httpurl1 = new StringBuffer(bmsUrl).append(ledgerConfirmUrl);
        Map<String, LocalDate> params1 = new HashMap<>(1);
        HttpHeaders headers1 = new HttpHeaders();
        headers1.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity1 = new HttpEntity<>(params1, headers1);
        ResponseEntity<Object> responseEntity1 =
            restTemplate.postForEntity(httpurl1.toString(), httpEntity1, Object.class);

        StringBuffer httpurl2 = new StringBuffer(bmsUrl).append(ledgerErrorUrl);
        Map<String, LocalDate> params2 = new HashMap<>(1);
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity2 = new HttpEntity<>(params2, headers2);
        ResponseEntity<Object> responseEntity2 =
            restTemplate.postForEntity(httpurl2.toString(), httpEntity2, Object.class);

        XxlJobHelper.log(
            responseEntity1.getBody().toString() + "/n" + responseEntity2.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("saveLedgerUrl")
    @Transactional(rollbackFor = Exception.class)
    public void saveLedgerUrl(String param) throws Exception {
        StringBuffer httpurl2 = new StringBuffer(bmsUrl).append(saveLedgerUrl);
        Map<String, LocalDate> params2 = new HashMap<>(1);
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity2 = new HttpEntity<>(params2, headers2);
        ResponseEntity<Object> responseEntity2 =
            restTemplate.postForEntity(httpurl2.toString(), httpEntity2, Object.class);

        XxlJobHelper.log(responseEntity2.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("saveConsumption")
    @Transactional(rollbackFor = Exception.class)
    public void saveConsumption() throws Exception {
        StringBuffer httpurl2 = new StringBuffer(bmsUrl).append(saveConsumptionUrl);
        Map<String, LocalDate> params2 = new HashMap<>(1);
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity2 = new HttpEntity<>(params2, headers2);
        ResponseEntity<Object> responseEntity2 =
            restTemplate.postForEntity(httpurl2.toString(), httpEntity2, Object.class);

        XxlJobHelper.log(responseEntity2.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("collectSaveDcpd")
    @Transactional(rollbackFor = Exception.class)
    public void collectSaveDcpd() throws Exception {
        String result = restTemplate.getForObject(bmsUrl + collectSaveDcpdUrl, String.class);

        logger.debug("定时采集KIO电厂皮带数据结果：{}", result);
        XxlJobHelper.log(result);
        XxlJobHelper.handleSuccess(result);
    }

    @XxlJob("saveCoalDaily")
    @Transactional(rollbackFor = Exception.class)
    public void saveCoalDaily() throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(saveCoalDailyUrl);
        Map<String, LocalDate> params = new HashMap<>(1);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity = new HttpEntity<>(params, headers);
        ResponseEntity<Object> responseEntity =
            restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

        logger.debug("定时采集商品煤日信息");
        XxlJobHelper.log(responseEntity.getBody().toString());
        XxlJobHelper.handleSuccess();
    }

    @XxlJob("saveEqptWarn")
    @Transactional(rollbackFor = Exception.class)
    public void saveEqptWarn() throws Exception {
        StringBuffer httpurl = new StringBuffer(bmsUrl).append(saveEqptWarn);
        Map<String, LocalDate> params = new HashMap<>(1);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Map<String, LocalDate>> httpEntity = new HttpEntity<>(params, headers);
        ResponseEntity<Object> responseEntity =
            restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

        logger.debug("定时采集设备报警信息");
        XxlJobHelper.log(responseEntity.getBody().toString());
        XxlJobHelper.handleSuccess();
    }
}
