package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备健康中心-设备点位分类管理
 *
 * <AUTHOR>
 * @date 2025/4/23 10:22
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesEquipmentPointClassificationParam extends QueryParam implements Cloneable, Serializable {

    /** 父类id，一级分类为0 */
    private Long parentId;
    /** 父类名称 */
    private String parentName;
    /** 类型名称 */
    private String name;
    /** 分类级别 */
    private String level;
    /** 排序数，越小越靠前 */
    private Integer sort;
    /** 状态：0禁用，1启用 */
    private Boolean status;

}
