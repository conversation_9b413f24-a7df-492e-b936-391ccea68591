package com.yhd.admin.bms.service.sw.impl.consumption;

import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionDrugStatisticsDTO;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionWaterDTO;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionDrugParam;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionElectricParam;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionWaterParam;
import com.yhd.admin.bms.domain.vo.sw.consumption.MesConsumptionElectricVO;
import com.yhd.admin.bms.domain.vo.sw.consumption.MesEcmCountVO;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionDrugService;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionElectricService;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionWaterService;
import com.yhd.admin.bms.service.sw.consumption.MesEcmCountService;
import com.yhd.admin.common.utils.DateUtil;
import com.yhd.admin.common.utils.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 能源消耗统计-业务层接口
 *
 * <AUTHOR>
 * @date 2025/1/8 9:05
 */
@Service
public class MesEcmCountServiceImpl implements MesEcmCountService {
  private final MesConsumptionElectricService electricService;
  private final MesConsumptionWaterService waterService;
  private final MesConsumptionDrugService consumptionDrugService;

  public MesEcmCountServiceImpl(
    MesConsumptionElectricService electricService,
    MesConsumptionWaterService waterService,
    MesConsumptionDrugService consumptionDrugService) {
    this.electricService = electricService;
    this.waterService = waterService;
    this.consumptionDrugService = consumptionDrugService;
  }

  @Override
  public MesEcmCountVO getEcmMonthCount() {
    MesEcmCountVO result = new MesEcmCountVO();
    double electricalCount = 0d;
    double waterCount = 0d;
    // 查询电耗当月数据
    String month = DateUtil.getSysYearMonth();
    MesConsumptionElectricParam electricParam = new MesConsumptionElectricParam();
    electricParam.setStatsDate(month);
    List<MesConsumptionElectricVO> electricVOList = electricService.queryList(electricParam);
    if (CollectionUtils.isNotEmpty(electricVOList)) {
      MesConsumptionElectricVO electricVO = electricVOList.get(0);
      if (electricVO.getTotalCount() != null) {
        electricalCount = electricVO.getTotalCount().doubleValue();
      }
    }
    // 查询水耗数据
    MesConsumptionWaterParam waterParam = new MesConsumptionWaterParam();
    waterParam.setYear(month);
    List<MesConsumptionWaterDTO> waterDTOList = waterService.queryList(waterParam);
    if (CollectionUtils.isNotEmpty(waterDTOList)) {
      MesConsumptionWaterDTO waterDTO = waterDTOList.get(0);
      if (waterDTO.getTotalWater() != null) {
        waterCount = waterDTO.getTotalWater().doubleValue();
      }
    }
    // 查询吨煤消耗数据
    MesConsumptionDrugParam param = new MesConsumptionDrugParam();
    LocalDate now = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
    param.setMonth(formatter.format(now));
    MesConsumptionDrugStatisticsDTO mesConsumptionDrugStatisticsDTO = consumptionDrugService.queryStatistics(param).get(0);

    result.setElectricalCount(NumberUtil.round(electricalCount, 0));
    result.setWaterCount(NumberUtil.round(waterCount, 0));
    result.setAnionConsumptionDm(mesConsumptionDrugStatisticsDTO.getAnionConsumptionDm());
    result.setCationConsumptionDm(mesConsumptionDrugStatisticsDTO.getCationConsumptionDm());

    return result;
  }
}
