package com.yhd.admin.bms.service.sw.impl.load;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentData;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentPoint;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentPointData;
import com.yhd.admin.bms.domain.query.sw.load.LoadEqptHealthParam;
import com.yhd.admin.bms.domain.vo.api.MesThreeDOilAlarmVO;
import com.yhd.admin.bms.domain.vo.api.MesThreeDOilPointVO;
import com.yhd.admin.bms.domain.vo.sw.load.EqptHealthRespJson;
import com.yhd.admin.bms.domain.vo.sw.load.LoadEqptHealthVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentDataService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentPointDataService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentPointService;
import com.yhd.admin.bms.service.sw.load.LoadEqptHealthService;
import com.yhd.admin.common.utils.StringUtils;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/** 设备健康中心看板 <AUTHOR> @Date 2025/3/6 9:32 @Version 1.0 */
@Service
@Slf4j
public class LoadEqptHealthServiceImpl implements LoadEqptHealthService {
  private static final Logger logger = LoggerFactory.getLogger(LoadEqptHealthServiceImpl.class);

  @Resource private RestTemplate restTemplate;
  @Resource private MesSwEquipmentPointService mesSwEquipmentPointService;
  @Resource private MesSwEquipmentPointDataService mesSwEquipmentPointDataService;
  @Resource private MesSwEquipmentDataService equipmentDataService;

  // 获取主机列表
  private final String HOST_LIST = "/API/PLC/GetAllPLC";

  // 获取润滑主机状态接口
  private final String LUBRICATION_STATUS = "/API/PLC/GetDeviceState";

  // 获取近七日报警信息
  private final String ALARM_INFO = "/API/GasRecord/GetUnusualData";

  // 获取历史数据
  private final String HISTORY_INFO = "/API/GasRecord/GetHistoryData";

  // 获取近七日注油量
  private final String OIL_INFO = "/API/Census/GetCurPLCCensus";

  @Value("http://************:80")
  private String loadUrl;

  @Override
  public List<LoadEqptHealthVO> getEqptClassificationList(LoadEqptHealthParam param) {
    List<MesSwEquipmentData> equipmentData =
        equipmentDataService.list().stream().distinct().collect(Collectors.toList());
    // 将equipmentData根据equipmentNoType进行分组
    Map<String, List<MesSwEquipmentData>> equipmentNoTypeMap =
        CollStreamUtil.groupByKey(equipmentData, MesSwEquipmentData::getClassificationName);
    // 计算equipmentNoTypeMap每个key的数量
    Map<String, Integer> countMap =
        equipmentNoTypeMap.entrySet().stream()
            .collect(
                Collectors.toMap(
                    Map.Entry::getKey, // 键保持不变
                    entry -> entry.getValue().size()));
    List<LoadEqptHealthVO> list = new ArrayList<>();
    countMap.forEach(
        (k, v) -> {
          LoadEqptHealthVO vo = new LoadEqptHealthVO();
          vo.setClassificationName(k);
          vo.setClassificationNum(v);
          list.add(vo);
        });
    return list;
  }

  @Override
  public List<String> getEqptNameList(LoadEqptHealthParam param) {
    List<MesSwEquipmentPoint> points = mesSwEquipmentPointService.list();
    // 根据设备设备编码+类别进行分组
    Map<String, List<MesSwEquipmentPoint>> equipmentNoTypes =
        CollStreamUtil.groupByKey(points, MesSwEquipmentPoint::getEquipmentNoType);
    // 获取equipmentNoTypes所有的key
    List<String> equipmentNoTypeList =
        equipmentNoTypes.keySet().stream().distinct().collect(Collectors.toList());
    List<String> nameList = new ArrayList<>();
    equipmentNoTypeList.forEach(
        e -> {
          String[] split = e.split(" ");
          nameList.add(split[1]);
        });
    return nameList.stream().distinct().collect(Collectors.toList());
  }

  @Override
  public List<String> getEqptOrTypeList(LoadEqptHealthParam param) {
    if (StringUtils.isBlank(param.getEqptName())) {
      throw new BMSException("error", "缺少参数");
    }
    LambdaQueryWrapper<MesSwEquipmentPoint> wrapper = new LambdaQueryWrapper<>();
    wrapper.like(MesSwEquipmentPoint::getEquipmentNoType, param.getEqptName());
    List<MesSwEquipmentPoint> points = mesSwEquipmentPointService.list(wrapper);
    List<String> type = new ArrayList<>();
    points.forEach(
        e -> {
          String[] split = e.getEquipmentNoType().split(" ");
          type.add(split[0]);
        });
    return type.stream().sorted().distinct().collect(Collectors.toList());
  }

  @Override
  public List<LoadEqptHealthVO> getEqptOrTypeLoadList(LoadEqptHealthParam param) {
    if (StringUtils.isBlank(param.getEqptName()) || StringUtils.isBlank(param.getEqptPoint())) {
      throw new BMSException("error", "缺少参数");
    }
    // 拼接参数
    StringBuffer nameOrPoint = new StringBuffer();
    nameOrPoint.append(param.getEqptPoint()).append(" ").append(param.getEqptName());
    LambdaQueryWrapper<MesSwEquipmentPoint> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentPoint::getEquipmentNoType, String.valueOf(nameOrPoint));
    List<MesSwEquipmentPoint> points = mesSwEquipmentPointService.list(wrapper);
    // 查询该点位的所有数据
    List<Long> idList =
        points.stream().map(MesSwEquipmentPoint::getId).collect(Collectors.toList());
    if (CollUtil.isEmpty(idList)) {
      // 为空就任意塞个值，防止下面查询不带入;
      idList.add(-10000l);
    }
    LambdaQueryWrapper<MesSwEquipmentPointData> pointDataWrapper = new LambdaQueryWrapper<>();
    pointDataWrapper.in(MesSwEquipmentPointData::getPointId, idList);
    List<MesSwEquipmentPointData> pointData = mesSwEquipmentPointDataService.list(pointDataWrapper);
    // 将所有点位数据根据设备id进行分组
    Map<Long, List<MesSwEquipmentPointData>> dataList =
        CollStreamUtil.groupByKey(pointData, MesSwEquipmentPointData::getPointId);
    // 对分组数据进行处理，每组只保留一条最新的数据
    dataList.forEach(
        (k, v) -> {
          if (CollectionUtils.isNotEmpty(v)) {
            v.sort(Comparator.comparing(MesSwEquipmentPointData::getCreatedTime).reversed());
            dataList.put(k, v.subList(0, 1));
          }
        });
    List<LoadEqptHealthVO> pointOrValue = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(idList)) {
      idList.forEach(
          e -> {
            LoadEqptHealthVO vo = new LoadEqptHealthVO();
            if (CollectionUtils.isNotEmpty(dataList.get(e))) {
              // 主表取点位名称
              List<MesSwEquipmentPoint> pointList =
                  points.stream().filter(p -> p.getId().equals(e)).collect(Collectors.toList());
              if (CollectionUtils.isNotEmpty(pointList)) {
                vo.setPointName(pointList.get(0).getPointName());
              }
              // 副表取有效值
              List<MesSwEquipmentPointData> data = dataList.get(e);
              if (CollectionUtils.isNotEmpty(data)) {
                vo.setPointValue(data.get(0).getPointValue());
              }
              pointOrValue.add(vo);
            }
          });
    }
    return pointOrValue;
  }

  @Override
  public List<LoadEqptHealthVO> getCXList(LoadEqptHealthParam param) {
    LambdaQueryWrapper<MesSwEquipmentPointData> pointDataWrapper = new LambdaQueryWrapper<>();
    pointDataWrapper.like(MesSwEquipmentPointData::getValueTime, LocalDate.now());
    pointDataWrapper.ne(MesSwEquipmentPointData::getWarnStatus, "warn_no");
    pointDataWrapper.orderByDesc(MesSwEquipmentPointData::getValueTime);
    List<MesSwEquipmentPointData> pointData = mesSwEquipmentPointDataService.list(pointDataWrapper);
    List<LoadEqptHealthVO> loadEqptHealthVOS = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(pointData)) {
      pointData.forEach(
          e -> {
            LoadEqptHealthVO vo = new LoadEqptHealthVO();
            vo.setId(e.getPointId());
            vo.setPointValue(e.getPointValue());
            if ("warn_low".equals(e.getWarnStatus())) {
              vo.setWarnStatus("超警告下限");
            } else if ("risk_low".equals(e.getWarnStatus())) {
              vo.setWarnStatus("超危险下限");
            } else if ("warn_high".equals(e.getWarnStatus())) {
              vo.setWarnStatus("超警告上限");
            } else if ("risk_high".equals(e.getWarnStatus())) {
              vo.setWarnStatus("超危险上限");
            }
            vo.setValueTime(e.getValueTime());
            loadEqptHealthVOS.add(vo);
          });
    }
    loadEqptHealthVOS.forEach(
        e -> {
          e.setPointName(mesSwEquipmentPointService.getById(e.getId()).getPointName());
        });
    return loadEqptHealthVOS;
  }

  @Override
  public LoadEqptHealthVO getHosts(LoadEqptHealthParam param) {
    EqptHealthRespJson plcHosts = this.getPlcHosts();
    LoadEqptHealthVO healthVO = new LoadEqptHealthVO();
    if (plcHosts != null && plcHosts.getData() != null) {
      healthVO.setHostsList(plcHosts.getData());
    }
    return healthVO;
  }

  @Override
  public LoadEqptHealthVO getEqptRightLoad(LoadEqptHealthParam param) {
    if (param.getPlcid() == null) {
      throw new BMSException("error", "plcid不能为空");
    }
    LoadEqptHealthVO healthVO = new LoadEqptHealthVO();
    param.setStartDate(LocalDate.now().minusDays(6));
    param.setEndDate(LocalDate.now());
    healthVO.setHostStatusList(this.getLubricationStatus(param).getData());
    healthVO.setAlarmSituationList(this.getAlarmInfo(param).getData());
    healthVO.setOilingList(this.getOilInfo(param).getData());
    return healthVO;
  }

  /** 获取润滑主机状态接口 */
  @Override
  public EqptHealthRespJson getLubricationStatus(LoadEqptHealthParam param) {
    logger.debug("远程调用【主机状态】接口开始~~~");
    StringBuffer httpurl =
        new StringBuffer(loadUrl)
            .append(LUBRICATION_STATUS)
            .append("?plcid=")
            .append(param.getPlcid());
    ResponseEntity<EqptHealthRespJson> responseEntity;
    try {
      responseEntity = restTemplate.getForEntity(httpurl.toString(), EqptHealthRespJson.class);
    } catch (Exception e) {
      throw new BMSException("error", "远程调用【主机状态】接口异常");
    }
    //        Map<String, Object> params = new HashMap<>();
    //        params.put("plcid", param.getPlcid());
    //        HttpHeaders headers = new HttpHeaders();
    //        headers.add("Content-Type", "application/json");
    //        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, headers);
    //        ResponseEntity<EqptHealthRespJson> responseEntity =
    //            restTemplate.postForEntity(httpurl.toString(), httpEntity,
    // EqptHealthRespJson.class);
    return responseEntity.getBody();
  }

  /** 获取近七日报警信息 */
  @Override
  public EqptHealthRespJson getAlarmInfo(LoadEqptHealthParam param) {
    logger.debug("远程调用【报警信息】接口开始~~~");
    StringBuffer httpurl =
        new StringBuffer(loadUrl)
            .append(ALARM_INFO)
            .append("?plcid=")
            .append(param.getPlcid())
            .append("&startdate=")
            .append(param.getStartDate())
            .append("&enddate=")
            .append(param.getEndDate())
            .append("&deviceid=")
            .append(param.getDeviceid());
    ResponseEntity<EqptHealthRespJson> responseEntity;
    try {
      responseEntity = restTemplate.getForEntity(httpurl.toString(), EqptHealthRespJson.class);
    } catch (Exception e) {
      throw new BMSException("error", "获取报警信息失败,请重试");
    }
    EqptHealthRespJson entityBody = responseEntity.getBody();
    return entityBody;
    //        Map<String, Object> params = new HashMap<>();
    //        params.put("startdate", param.getStartDate());
    //        params.put("enddate", param.getEndDate());
    //        params.put("plcid", param.getPlcid());
    //        HttpHeaders headers = new HttpHeaders();
    //        headers.add("Content-Type", "application/json");
    //        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, headers);
    //        ResponseEntity<EqptHealthRespJson> responseEntity =
    //            restTemplate.postForEntity(httpurl.toString(), httpEntity,
    // EqptHealthRespJson.class);
    //        return responseEntity.getBody();
  }

  /** 获取近七日注油量 */
  @Override
  public EqptHealthRespJson getOilInfo(LoadEqptHealthParam param) {
    logger.debug("远程调用【注油量】接口开始~~~");
    StringBuffer httpurl =
        new StringBuffer(loadUrl)
            .append(OIL_INFO)
            .append("?plcid=")
            .append(param.getPlcid())
            .append("&startdate=")
            .append(param.getStartDate())
            .append("&enddate=")
            .append(param.getEndDate());
    ResponseEntity<EqptHealthRespJson> responseEntity;
    try {
      responseEntity = restTemplate.getForEntity(httpurl.toString(), EqptHealthRespJson.class);
    } catch (Exception e) {
      throw new BMSException("error", "获取注油量失败,请重试");
    }
    return responseEntity.getBody();
  }

  /** 获取主机列表 */
  @Override
  public EqptHealthRespJson getPlcHosts() {
    logger.debug("远程调用【主机列表】接口开始~~~");
    StringBuffer httpurl = new StringBuffer(loadUrl).append(HOST_LIST);
    ResponseEntity<EqptHealthRespJson> responseEntity;
    try {
      responseEntity = restTemplate.getForEntity(httpurl.toString(), EqptHealthRespJson.class);
    } catch (Exception e) {
      throw new BMSException("error", "获取主机列表失败,请重试");
    }
    return responseEntity.getBody();
  }

  /** 获取设备润滑详情信息（第三方） */
  @Override
  public Object getLubricationInfo(LoadEqptHealthParam param) {
    logger.debug("远程调用【获取设备润滑详情信息】接口开始~~~");

    // 因润滑接口必传时间段，设备润滑详情中内容默认查询最近1个月的内容
    if (param.getStartDate() == null || param.getEndDate() == null) {
      param.setStartDate(LocalDate.now().minusDays(30));
      param.setEndDate(LocalDate.now());
    }

    StringBuilder httpUrl =
        new StringBuilder(loadUrl)
            .append(HISTORY_INFO)
            .append("?plcid=")
            .append(param.getPlcid())
            .append("&startdate=")
            .append(param.getStartDate())
            .append("&enddate=")
            .append(param.getEndDate())
            .append("&deviceid=")
            .append(param.getDeviceid())
            .append("&pointid=")
            .append(param.getPointid())
            .append("&siteid=")
            .append(param.getSiteid());
    ResponseEntity<EqptHealthRespJson> responseEntity;
    try {
      responseEntity = restTemplate.getForEntity(httpUrl.toString(), EqptHealthRespJson.class);
    } catch (Exception e) {
      throw new BMSException("error", "获取设备润滑详情信息失败,请重试");
    }
    if (responseEntity.getBody().getData() == null) {
      return new ArrayList<>();
    } else {
      return responseEntity.getBody().getData();
    }
  }

  @Override
  public Object getLubricationAlarmInfo(LoadEqptHealthParam param) {
    logger.debug("远程调用【报警信息】接口开始~~~");
    StringBuffer httpurl =
        new StringBuffer(loadUrl)
            .append(ALARM_INFO)
            .append("?plcid=")
            .append(param.getPlcid())
            .append("&startdate=")
            .append(param.getStartDate())
            .append("&enddate=")
            .append(param.getEndDate())
            .append("&deviceid=")
            .append(param.getDeviceid());
    ResponseEntity<EqptHealthRespJson> responseEntity;
    try {
      responseEntity = restTemplate.getForEntity(httpurl.toString(), EqptHealthRespJson.class);
    } catch (Exception e) {
      throw new BMSException("error", "获取报警信息失败,请重试");
    }
    EqptHealthRespJson entityBody = responseEntity.getBody();
    if (entityBody.getData() == null) {
      return new ArrayList<>();
    } else {
      return entityBody;
    }
  }

  @Override
  public List<MesThreeDOilPointVO> getAlarmList(LoadEqptHealthParam param) {
    EqptHealthRespJson plcHosts = this.getPlcHosts();
    List<MesThreeDOilPointVO> result = new ArrayList<>();
    if (plcHosts != null && plcHosts.getData() != null) {
      log.error(
          "plcHosts.getData() =>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> {}", plcHosts.getData().toString());
      List<HashMap> list = (List<HashMap>) plcHosts.getData();
      list.forEach(
          e -> {
            MesThreeDOilPointVO item = new MesThreeDOilPointVO();
            item.setId(e.get("id").toString());
            item.setPlcname(e.get("plcname").toString());
            param.setPlcid("");
            EqptHealthRespJson data = this.getAlarmInfo(param);
            if (data != null && data.getData() != null) {
              List<HashMap> dataList = (List<HashMap>) data.getData();
              List<MesThreeDOilAlarmVO> alarmList = new ArrayList<>();
              dataList.forEach(
                  map -> {
                    MesThreeDOilAlarmVO alarmVO = new MesThreeDOilAlarmVO();
                    alarmVO.setGasstate(map.get("gasstate").toString());
                    alarmVO.setSitename(map.get("sitename").toString());
                    alarmVO.setPointname(map.get("pointname").toString());
                    alarmVO.setGasdate(map.get("gasdate").toString());
                    alarmList.add(alarmVO);
                  });
              item.setAlarmList(alarmList);
            }
            result.add(item);
          });
    }
    return result;
  }
}
