package com.yhd.admin.bms.controller.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.MesSwFireWaterConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwFireWaterDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwFireWaterParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwFireWaterVO;
import com.yhd.admin.bms.service.sw.MesSwFireWaterService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 消防水幕检查记录 */
@RestController
@RequestMapping("/safe/firewater")
@Slf4j
public class MesSwFireWaterController
    extends BaseController<MesSwFireWaterConvert, MesSwFireWaterService> {

  public MesSwFireWaterController(MesSwFireWaterConvert convert, MesSwFireWaterService service) {
    super(convert, service);
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwFireWaterVO> pagingQuery(@RequestBody MesSwFireWaterParam queryParam) {
    IPage<MesSwFireWaterDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetail(@RequestBody MesSwFireWaterParam param) {
    return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson addOrModify(@RequestBody MesSwFireWaterParam param) {
    Boolean retVal = service.addOrModify(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson removeBatch(@RequestBody BatchParam param) {
    Boolean retVal = service.removeBatch(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }
}
