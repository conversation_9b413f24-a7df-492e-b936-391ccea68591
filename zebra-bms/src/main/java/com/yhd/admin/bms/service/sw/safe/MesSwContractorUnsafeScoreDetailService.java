package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwContractorUnsafeScoreDetail;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwContractorUnsafeScoreDetailParam;

import java.util.List;

/**
 * 承包商不安全行为积分台账明细-业务层接口
 *
 * <AUTHOR>
 * @date 2023/10/26 15:37
 */
public interface MesSwContractorUnsafeScoreDetailService
    extends IService<MesSwContractorUnsafeScoreDetail> {

  /**
   * 根据条件查询明细列表信息
   *
   * @param param 查询条件
   * @return 承包商不安全行为积分台账明细列表信息
   */
  List<MesSwContractorUnsafeScoreDetail> queryList(MesSwContractorUnsafeScoreDetailParam param);

  /**
   * 根据主表主键id批量删除
   *
   * @param relaIdList 主表主键列表
   */
  void removeBatchByRelaId(List<Long> relaIdList);
}
