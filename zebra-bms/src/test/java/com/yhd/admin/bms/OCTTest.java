package com.yhd.admin.bms;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.tsfile.file.metadata.enums.TSDataType;
import org.junit.Test;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.stream.Collectors;

@Slf4j
public class OCTTest {

    @Test
    public void test1() {
        LocalDateTime startDay = LocalDateTime.now().with(DayOfWeek.MONDAY).with(LocalTime.MIN);
        LocalDateTime endDay = LocalDateTime.now().with(DayOfWeek.SUNDAY).with(LocalTime.MAX);

        System.out.println(startDay);
        System.out.println(endDay);
    }

    @Test
    public void test2() {


        char[] chars = "abc".toCharArray();
        StringBuilder s = new StringBuilder();
        for (char i : chars) {
            s.append(Integer.toBinaryString(i));
        }
        System.out.println(s);
    }

    @Test
    public void testStr() {
        String str = Splitter.fixedLength(20).splitToList("处理措施处理措施处理措施处理措施处理措施处理措施处理措施处理措施处理措施处理措施处理措施").stream().collect(Collectors.joining(" \n "));


        System.out.println(str);
    }

    @Test
    public void testClazz() {

        String str = "1";
        Integer a = 1;
        LocalDateTime dateTime = LocalDateTime.now();

        log.debug("{},{},{}", str.getClass().getName(), a.getClass().getName(), dateTime.getClass().getName());

    }

    @Test
    public void test3(){
        log.info(">>>>>>>>>>>>>>>>>:{}", TSDataType.TEXT.toString());
    }
}
