<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.bms.dao.sw.goods.MesSwGoodsCategoryDao">
    <select id="selectChildren" resultType="com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsCategory"
            parameterType="java.lang.Long">
        WITH recursive r AS (
            SELECT id,
                   parent_id,
                   `name`,
                   `level`,
                   `sort`,
                   `status`,
                   created_by,
                   created_time,
                   updated_by,
                   updated_time
            FROM tb_mes_sw_goods_category
            WHERE parent_id = #{id}
            UNION ALL
            SELECT c.id,
                   c.parent_id,
                   c.`name`,
                   c.`level`,
                   c.`sort`,
                   c.`status`,
                   c.created_by,
                   c.created_time,
                   c.updated_by,
                   c.updated_time
            FROM tb_mes_sw_goods_category c
                     INNER JOIN r ON c.parent_id = r.id
        )
        SELECT r.id,
               r.parent_id,
               r.`name`,
               r.`level`,
               r.`sort`,
               r.`status`,
               r.created_by,
               r.created_time,
               r.updated_by,
               r.updated_time
        FROM r
        ORDER BY r.id,
                 r.`status`;
    </select>
</mapper>
