package com.yhd.admin.bms.controller.sw;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwFactoryConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwFactoryDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwFactoryParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwFactoryVO;
import com.yhd.admin.bms.service.sw.MesSwFactoryService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 工业厂区草坪
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-10-10
 */
@RestController
@RequestMapping("/sw/factory")
public class MesSwFactoryController {


    @Resource
    private MesSwFactoryConvert convert;

    @Resource
    private MesSwFactoryService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwFactoryVO> pagingQuery(@RequestBody MesSwFactoryParam queryParam) {
        IPage<MesSwFactoryDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwFactoryParam param) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    }

    @SysLogs(title = "工业厂区草坪新增", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesSwFactoryParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "工业厂区草坪修改", businessType = BusinessType.UPDATE)
    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MesSwFactoryParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "工业厂区草坪删除", businessType = BusinessType.DELETE)
    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }
}
