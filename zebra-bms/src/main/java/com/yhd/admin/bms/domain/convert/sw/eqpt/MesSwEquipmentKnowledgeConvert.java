package com.yhd.admin.bms.domain.convert.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentKnowledgeDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentKnowledge;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentKnowledgeParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentKnowledgeVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwEquipmentKnowledgeConvert {
  MesSwEquipmentKnowledge toEntity(MesSwEquipmentKnowledgeParam param);

  MesSwEquipmentKnowledgeDTO toDTO(MesSwEquipmentKnowledge entity);

  List<MesSwEquipmentKnowledgeDTO> toDTO(List<MesSwEquipmentKnowledge> entityList);

  MesSwEquipmentKnowledgeVO toVO(MesSwEquipmentKnowledgeDTO dto);

  List<MesSwEquipmentKnowledgeVO> toVO(List<MesSwEquipmentKnowledgeDTO> dtoList);
}
