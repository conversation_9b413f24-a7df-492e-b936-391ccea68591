package com.yhd.admin.bms.domain.convert.sw.produce;

import com.yhd.admin.bms.domain.dto.sw.produce.MesSwProducePlanDTO;
import com.yhd.admin.bms.domain.entity.sw.produce.MesSwProducePlan;
import com.yhd.admin.bms.domain.query.sw.produce.MesSwProducePlanParam;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProducePlanVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesSwProducePlanConvert {
  MesSwProducePlan toEntity(MesSwProducePlanParam param);

  MesSwProducePlanDTO toDTO(MesSwProducePlan entity);

  MesSwProducePlanVO toVO(MesSwProducePlanDTO dto);
}
