package com.yhd.admin.bms.domain.vo.sw.ticket;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @<PERSON>
 * @Date 2024/6/17 8:40
 * @Version 1.0
 */

@EqualsAndHashCode(callSuper = false)
@Data
public class MesSwWorkTicketUseElectricSignUserVO extends BaseVO implements Cloneable, Serializable {
    /**
     * 临时用电申请许可表主键id
     */
    private Long ticketId;
    /**
     * 签字用户类型
     */
    private String userType;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 签字图片url
     */
    private String signUrl;
    /**
     * 审批意见
     */
    private String spOpinion;
    /**
     * 是否完成签字：0false,1true
     */
    private Boolean isSign;
}
