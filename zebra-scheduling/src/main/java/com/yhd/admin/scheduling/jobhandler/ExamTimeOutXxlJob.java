package com.yhd.admin.scheduling.jobhandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 考试超时未交卷处理类
 *
 * <AUTHOR>
 * @date 2024/3/8 10:38
 */
@Component
public class ExamTimeOutXxlJob {
  private static final Logger logger = LoggerFactory.getLogger(ExamTimeOutXxlJob.class);

  // 考试超时强制交卷处理地址
  private final String examUrl = "/api/job/timeOutAutoEndExam";
  @Resource private RestTemplate restTemplate;

  @Value("${bms.url}")
  private String bmsUrl;

  @XxlJob("timeOutAutoEndExam")
  @Transactional(rollbackFor = Exception.class)
  public void timeOutAutoEndExam() {
    String result = restTemplate.getForObject(bmsUrl + examUrl, String.class);

    logger.debug("考试超时强制交卷处理结果：{}", result);
    XxlJobHelper.log(result);
    XxlJobHelper.handleSuccess(result);
  }
}
