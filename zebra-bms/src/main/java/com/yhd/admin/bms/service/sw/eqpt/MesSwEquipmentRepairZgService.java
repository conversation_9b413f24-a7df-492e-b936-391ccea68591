package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairZg;

/**
 * 设备检修工单整改-业务层接口
 *
 * <AUTHOR>
 * @date 2024/8/19 16:34
 */
public interface MesSwEquipmentRepairZgService extends IService<MesSwEquipmentRepairZg> {
  /**
   * 根据检修工单id查询检修整改记录信息
   *
   * @param repairId 检修工单id
   * @return 检修整改记录
   */
  MesSwEquipmentRepairZg getTodoByRepairId(Long repairId);

  /**
   * 根据检修工单id查询处理完成的整改记录信息/最新记录
   *
   * @param repairId 检修工单id
   * @return 处理完成的整改记录
   */
  MesSwEquipmentRepairZg getFinishByRepairId(Long repairId);
}
