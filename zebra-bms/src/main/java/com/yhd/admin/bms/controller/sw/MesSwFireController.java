package com.yhd.admin.bms.controller.sw;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwFireConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwFireDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwFireParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwFireVO;
import com.yhd.admin.bms.service.sw.MesSwFireService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 消防重点部位
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-10-10
 */
@RestController
@RequestMapping("/sw/fire")
public class MesSwFireController {


    @Resource
    private MesSwFireConvert convert;

    @Resource
    private MesSwFireService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwFireVO> pagingQuery(@RequestBody MesSwFireParam queryParam) {
        IPage<MesSwFireDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwFireParam param) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    }

    @SysLogs(title = "消防重点部位新增", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesSwFireParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "消防重点部位修改", businessType = BusinessType.UPDATE)
    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MesSwFireParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "消防重点部位删除", businessType = BusinessType.DELETE)
    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }
}
