package com.yhd.admin.bms.domain.vo.sys;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserAccountVo extends BaseVO implements Serializable {
  /** 账户未过期 */
  private Boolean isAccountNonExpired;
  /** 账户未锁定 */
  private Boolean isAccountNonLocked;
  /** 密码未过期 */
  private Boolean isCredentialsNonExpired;
  /** 是否绑定人员信息 */
  private Boolean isBind;
  /** 角色ID */
  private List<Long> role;

  /** 登录名称 */
  private String username;
  /** 密码 */
  @JsonProperty(access = Access.WRITE_ONLY)
  private String password;

  /** 姓名 */
  private String name;

  /** 人员类别 厂内人员/承包商 */
  private String type;
  /** 所在部门名称 厂内人员——组织编码主键 承包商——承包商编码主键 */
  private Long departmentId;
  /** 所在部门名称 */
  private String department;
  /** 员工编码 */
  private String jobNumber;
  /** 岗位 */
  private String post;

  /** 职务 */
  private String duty;

  /** 联系电话 */
  private String phone;

  /** 邮箱 */
  private String email;
  /** 账户状态 */
  private Boolean isEnable;
  /** 签名 */
  private String signature;
  /** 停送电操作密码 */
  private String tsdOperatePassword;

    /** 承包商信息表 */
    private SysUserAccountContractorVO contractor;


}
