package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 承包商不安全行为积分台账明细
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwContractorUnsafeScoreDetailParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = -2586985189448842795L;

  /** 承包商不安全行为台账主键id */
  private Long contractorUnsafeId;
  /** 年度 */
  private String year;
  /** 月份 */
  private String month;
  /** 本月积分 */
  private Integer currentScore;
  /** 上月剩余积分 */
  private Integer lastScore;
  /** 本月剩余积分 */
  private Integer finalScore;
  /** 月合计积分 */
  private Integer totalScore;
  /** 积分处罚情况 */
  private String punishDesc;
}
