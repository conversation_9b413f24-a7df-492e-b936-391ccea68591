package com.yhd.admin.bms.domain.dto.sw.exam;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import com.yhd.admin.bms.domain.vo.sw.exam.QuestionAnswerOptionVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 用户模拟考试试卷题目答案表
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwMockExamPaperQuestionUserAnswerDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 3770836668225945497L;

  /** 用户模拟考试表主键id */
  private Long userExamId;
  /** 模拟考试试卷题目表主键id */
  private Long paperQuestionId;
  /** 考试试卷标题 */
  private String paperTitle;
  /** 试卷序列号 */
  private Integer serialNum;
  /** 考试试卷分类 */
  private String paperClassify;
  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  private String questionType;
  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  @TableField(exist = false)
  private String questionTypeName;
  /** 试题题目内容 */
  private String questionContent;
  /** 试题难度：1-低，2-中，3-高 */
  private String difficultyType;
  /** 试题难度：1-低，2-中，3-高 */
  @TableField(exist = false)
  private String difficultyTypeName;
  /** 试题标题 */
  private String questionTitle;
  /** 试题题目答案选项 */
  private String questionAnswerOption;
  /** 试题题目答案选项列表 */
  @TableField(exist = false)
  private List<QuestionAnswerOptionVO> answerOptionList;
  /** 试题题目正确答案 */
  private String questionRightAnswer;
  /** 试题题目附件 */
  private String fileUrl;

  @TableField(exist = false)
  private List<String> fileUrlList;
  /** 附件类型 */
  private String fileType;
  /** 出题人账号 */
  private String questionSetter;
  /** 出题人姓名 */
  private String questionSetterName;
  /** 试题题目分数 */
  private Integer questionScore;
  /** 用户答案 */
  private String userAnswer;
  /** 得分 */
  private Integer userScore;
  /** 是否正确：0正确，1错误 */
  private String doRight;
}
