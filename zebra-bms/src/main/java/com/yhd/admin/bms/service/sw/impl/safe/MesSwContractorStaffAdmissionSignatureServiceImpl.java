package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.safe.MesSwContractorStaffAdmissionSignatureDao;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwContractorStaffAdmissionSignature;
import com.yhd.admin.bms.service.sw.safe.MesSwContractorStaffAdmissionSignatureService;
import org.springframework.stereotype.Service;

/**
 * 承包商员工入场管理-签字表
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@Service
public class MesSwContractorStaffAdmissionSignatureServiceImpl
    extends ServiceImpl<
        MesSwContractorStaffAdmissionSignatureDao, MesSwContractorStaffAdmissionSignature>
    implements MesSwContractorStaffAdmissionSignatureService {}
