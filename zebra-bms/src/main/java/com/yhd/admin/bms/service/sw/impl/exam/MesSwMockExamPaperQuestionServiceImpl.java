package com.yhd.admin.bms.service.sw.impl.exam;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.exam.MesSwMockExamPaperQuestionDao;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwMockExamPaperQuestion;
import com.yhd.admin.bms.service.sw.exam.MesSwMockExamPaperQuestionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模拟考试试卷题目表-业务层接口
 *
 * <AUTHOR>
 * @date 2024/11/1 19:30
 */
@Service
public class MesSwMockExamPaperQuestionServiceImpl
    extends ServiceImpl<MesSwMockExamPaperQuestionDao, MesSwMockExamPaperQuestion>
    implements MesSwMockExamPaperQuestionService {
  @Override
  public List<MesSwMockExamPaperQuestion> queryListByUserMockId(Long userMockId) {
    if (userMockId == null) {
      return null;
    }
    LambdaQueryWrapper<MesSwMockExamPaperQuestion> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwMockExamPaperQuestion::getUserMockId, userMockId);

    return baseMapper.selectList(wrapper);
  }
}
