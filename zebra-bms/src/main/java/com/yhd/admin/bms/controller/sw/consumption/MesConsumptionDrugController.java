package com.yhd.admin.bms.controller.sw.consumption;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.consumption.MesConsumptionDrugConvert;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionDrugDTO;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionDrugStatisticsDTO;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionDrugParam;
import com.yhd.admin.bms.domain.vo.sw.consumption.MesConsumptionDrugVO;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionDrugService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 能耗管理-药耗
 *
 * <AUTHOR>
 * @date 2025/07/02 10:22
 */
@RestController
@RequestMapping("/consumption/drug")
@Slf4j
public class MesConsumptionDrugController
    extends BaseController<MesConsumptionDrugConvert, MesConsumptionDrugService> {

  public MesConsumptionDrugController(
      MesConsumptionDrugConvert convert, MesConsumptionDrugService service) {
    super(convert, service);
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesConsumptionDrugVO> pagingQuery(
      @RequestBody MesConsumptionDrugParam queryParam) {
    IPage<MesConsumptionDrugDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MesConsumptionDrugVO> getCurrentDetail(
      @RequestBody MesConsumptionDrugParam param) {
    try {
      return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> addOrModify(@RequestBody MesConsumptionDrugParam param) {
    try {
      return RespJson.buildSuccessResponse(service.addOrModify(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> removeBatch(@RequestBody MesConsumptionDrugParam param) {
    try {
      return RespJson.buildSuccessResponse(service.removeBatch(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 药耗统计
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/queryStatistics",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MesConsumptionDrugStatisticsDTO>> queryStatistics(
      @RequestBody MesConsumptionDrugParam param) {
    try {
      List<MesConsumptionDrugStatisticsDTO> result = service.queryStatistics(param);
      return RespJson.buildSuccessResponse(result);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 药耗统计根据月份查询详情
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/getStatisticsCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MesConsumptionDrugStatisticsDTO>> getStatisticsCurrentDetail(
      @RequestBody MesConsumptionDrugParam param) {
    try {
      List<MesConsumptionDrugStatisticsDTO> result = service.getStatisticsCurrentDetail(param);
      return RespJson.buildSuccessResponse(result);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

}
