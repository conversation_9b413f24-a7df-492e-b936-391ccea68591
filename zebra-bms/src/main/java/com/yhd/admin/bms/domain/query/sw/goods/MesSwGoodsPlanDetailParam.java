package com.yhd.admin.bms.domain.query.sw.goods;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/** 筛板物资计划详情 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwGoodsPlanDetailParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 5935271974454162646L;
  /** 物资计划表主键id */
  private Long planId;
  /** 单位名称 */
  private String orgName;
  /** SPA编码/商城MDM物料编码 */
  private String spa;
  /** 物资表主键id */
  private Long goodsId;
  /** 商品编码 */
  private String goodsCode;
  /** 备件系统描述(对应物资表物资名称) */
  private String goodsDesc;
  /** 物资单位 */
  private String goodsUnit;
  /** 物资数量 */
  private Integer goodsNum;
  /** 物资单价 */
  private Double goodsPrice;
  /** 总价 */
  private Double talalValue;
  /** 到货日期(yyyy-MM-dd) */
  private LocalDate arrivalDate;
  /** 主机信息(主机名称、型号、生产厂家) */
  private String mainEngine;
  /** 用途(在使用物资设备编号+筛上量（正在使用的量）+上月月消耗量) */
  private String purpose;
  /** 更换周期(默认上次此物资提交计划的时间与本次提报日期的时间差) */
  private Integer replaceCycle;
  /** 计划类型(默认国能煤炭专区) */
  private String planType;
  /** 本厂库存 */
  private Long stock;
  /** 物资分类 */
  private String goodsType;
  /** 设备类型 */
  private String eqptType;
  /** 备注 */
  private String remark;
}
