package com.yhd.admin.bms.domain.vo.sw.eqpt;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备巡检表-人员的数据字段
 *
 * <AUTHOR>
 * @date 2023/12/01 18:22
 */
@Data
public class MesSwEquipmentInspectionPersonVO implements Cloneable, Serializable {
  /** 人员账号 */
  private String account;
  /** 人员名称 */
  private String name;
  /** 工种 */
  private String type;
  /** 签字图片地址 */
  private String fileUrl;
  /** 流程实例ID */
  private String processInstanceId;
  /** 任务ID */
  private String taskId;
  /** 工种code */
  private String nodeCode;
}
