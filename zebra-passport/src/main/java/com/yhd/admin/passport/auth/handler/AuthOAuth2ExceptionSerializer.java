package com.yhd.admin.passport.auth.handler;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AuthOAuth2ExceptionSerializer extends StdSerializer<AuthOAuth2Exception> {

  public AuthOAuth2ExceptionSerializer() {
    super(AuthOAuth2Exception.class);
  }

  @Override
  public void serialize(AuthOAuth2Exception value, JsonGenerator gen, SerializerProvider provider)
      throws IOException {
    // TODO 失败异常重写。
    gen.writeStartObject();
    gen.writeStringField("status", "error");
    gen.writeStringField(
        "message",
        value.getLocalizedMessage().equals("账户被锁定，请联系管理员")
            ? "由于多次输入账号密码错误当前账号已停用，请联系管理员"
            : value.getLocalizedMessage());
    gen.writeNumberField(
        "timestamp", LocalDateTime.now().toInstant(ZoneOffset.of("+8")).getEpochSecond());
    if (value.getAdditionalInformation() != null) {
      for (Map.Entry<String, String> entry : value.getAdditionalInformation().entrySet()) {
        String key = entry.getKey();
        String add = entry.getValue();
        gen.writeStringField(key, add);
      }
    }
    gen.writeEndObject();
  }
}
