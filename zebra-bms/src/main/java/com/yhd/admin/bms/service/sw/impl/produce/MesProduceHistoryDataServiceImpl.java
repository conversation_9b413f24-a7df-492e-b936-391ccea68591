package com.yhd.admin.bms.service.sw.impl.produce;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.produce.MesEquipmentWarnDao;
import com.yhd.admin.bms.domain.convert.sw.produce.MesEquipmentWarnConvert;
import com.yhd.admin.bms.domain.dto.sw.produce.MesEquipmentWarnDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesEquipmentParts;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesEquipmentPoint;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesEquipmentPointClassification;
import com.yhd.admin.bms.domain.entity.sw.produce.MesEquipmentWarn;
import com.yhd.admin.bms.domain.enums.produce.KHBeltPointEnum;
import com.yhd.admin.bms.domain.query.sw.HstTimeStatsParam;
import com.yhd.admin.bms.domain.query.sw.produce.MesBeltDataParam;
import com.yhd.admin.bms.domain.query.sw.produce.MesEquipmentWarnParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.dashboard.KHPointDataVO;
import com.yhd.admin.bms.domain.vo.sw.produce.*;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.dashboard.KHPointService;
import com.yhd.admin.bms.service.sw.eqpt.MesEquipmentPartsService;
import com.yhd.admin.bms.service.sw.eqpt.MesEquipmentPointClassificationService;
import com.yhd.admin.bms.service.sw.eqpt.MesEquipmentPointService;
import com.yhd.admin.bms.service.sw.produce.MesProduceHistoryDataService;
import com.yhd.admin.common.utils.StringUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产效率-历史数据库
 *
 * <AUTHOR>
 * @Date 2025/4/22 15:24
 * @Version 1.0
 */
@Service
public class MesProduceHistoryDataServiceImpl extends ServiceImpl<MesEquipmentWarnDao, MesEquipmentWarn>
    implements MesProduceHistoryDataService {

    @Resource
    private KHPointService pointService;

    @Resource
    private MesEquipmentWarnConvert convert;

    @Resource
    private MesEquipmentPointClassificationService classificationService;

    @Resource
    private MesEquipmentPointService equipmentPointService;

    @Resource
    private MesEquipmentPartsService partsService;


    @Override
    public IPage<MesEquipmentWarnDTO> pagingQuery(MesEquipmentWarnParam queryParam) {
        Page<MesEquipmentWarn> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesEquipmentWarn> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //报警时间
        queryChain.between(
            queryParam.getWarnStartTime() != null && queryParam.getWarnEndTime() != null,
            MesEquipmentWarn::getWarnStartTime,
            queryParam.getWarnStartTime(),
            queryParam.getWarnEndTime());
        //报警类型
        queryChain.eq(
            Objects.nonNull(queryParam.getClassificationCode()),
            MesEquipmentWarn::getClassificationCode,
            queryParam.getClassificationCode());
        //设备编码及类别
        queryChain.eq(
            StringUtils.isNotBlank(queryParam.getEqptNoType()),
            MesEquipmentWarn::getEqptNoType,
            queryParam.getEqptNoType());
        queryChain.orderByDesc(MesEquipmentWarn::getCreatedTime);
        IPage<MesEquipmentWarnDTO> warnDTOIPage = queryChain.page(page).convert(this.convert::toDTO);
        warnDTOIPage.getRecords().stream().forEach(warnDTO -> {
            if (Objects.nonNull(warnDTO.getEqptPartId())) {
                MesEquipmentParts parts = partsService.getById(warnDTO.getEqptPartId());
                warnDTO.setEqptPartName(parts.getPartsName());
            }
        });
        return warnDTOIPage;
    }

    @Override
    public Boolean add(MesEquipmentWarnParam param) {
        if (Objects.isNull(param.getClassificationCode()) ||
            Objects.isNull(param.getWarnStartTime()) ||
            Objects.isNull(param.getEqptId()) ||
            Objects.isNull(param.getEqptPartId()) ||
            StringUtils.isBlank(param.getWarnContent())) {
            throw new BMSException("error", "缺少参数");
        }
        //报警类型
        MesEquipmentPointClassification classificationById = classificationService.getById(param.getClassificationCode());
        param.setClassificationName(classificationById.getName());
        //设备部件id
        if (param.getEqptPartId() != null) {
            LambdaQueryWrapper<MesEquipmentParts> partsWrapper = new LambdaQueryWrapper<>();
            partsWrapper.eq(MesEquipmentParts::getId, param.getEqptPartId());
            MesEquipmentParts parts = partsService.getOne(partsWrapper);
            if (Objects.nonNull(parts)) {
                param.setEqptPartName(parts.getPartsName());
                param.setEqptNoType(parts.getEquipmentNoType());
            }
        }
        MesEquipmentWarn entity = convert.toEntity(param);
        return this.save(entity);
    }

    @Override
    public Boolean modify(MesEquipmentWarnParam param) {
        if (Objects.isNull(param.getId()) ||
            Objects.isNull(param.getClassificationCode()) ||
            Objects.isNull(param.getWarnStartTime()) ||
            Objects.isNull(param.getEqptId()) ||
            Objects.isNull(param.getEqptPartId()) ||
            StringUtils.isBlank(param.getWarnContent())) {
            throw new BMSException("error", "缺少参数");
        }
        //报警类型
        MesEquipmentPointClassification classificationById = classificationService.getById(param.getClassificationCode());
        param.setClassificationName(classificationById.getName());
        //设备部件id
        if (param.getEqptPartId() != null) {
            LambdaQueryWrapper<MesEquipmentParts> partsWrapper = new LambdaQueryWrapper<>();
            partsWrapper.eq(MesEquipmentParts::getId, param.getEqptPartId());
            MesEquipmentParts parts = partsService.getOne(partsWrapper);
            if (Objects.nonNull(parts)) {
                param.setEqptPartName(parts.getPartsName());
                param.setEqptNoType(parts.getEquipmentNoType());
            }
        }
        MesEquipmentWarn entity = convert.toEntity(param);
        return this.updateById(entity);
    }

    @Override
    public MesEquipmentWarnDTO getCurrentDetail(MesEquipmentWarnParam param) {
        MesEquipmentWarnDTO dto = convert.toDTO(this.getById(param.getId()));
        //设备部件
        MesEquipmentParts parts = partsService.getById(dto.getEqptPartId());
        if (Objects.nonNull(parts)) {
            dto.setEqptPartName(parts.getPartsName());
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return this.removeByIds(param.getId());
    }

    @Override
    public Boolean saveEqptWarn(MesEquipmentWarnParam param) {
        LambdaQueryWrapper<MesEquipmentPoint> ykWrapper = new LambdaQueryWrapper<>();
        ykWrapper.like(MesEquipmentPoint::getPointClassification, "状态-远控就地%");
        ykWrapper.eq(MesEquipmentPoint::getStatus, true);
        List<MesEquipmentPoint> ykPoints = equipmentPointService.list(ykWrapper);
        if (CollectionUtil.isEmpty(ykPoints)) {
            throw new BMSException("error", "未查询到远控/就地点位数据");
        }
        //获取远控就地的点位地址集合
        List<String> pointList = ykPoints.stream()
            .map(MesEquipmentPoint::getPointAddress)
            .collect(Collectors.toList());
        // 远控/就地点位数据集合
        Map<String, List<KHPointDataVO>> pointValueList = null;
        try {
            HstTimeStatsParam statsParam = new HstTimeStatsParam();
            //设置每次定时器执行的时间区间
            statsParam.setStartTime(LocalDateTime.now().minusHours(2));
            statsParam.setEndTime(LocalDateTime.now());
            statsParam.setPointList(pointList);
            pointValueList = pointService.queryPointListByStepped(statsParam);
        } catch (Exception e) {
            throw new BMSException("error", "远程调用远控/就地点位状态数据接口异常");
        }
        //判断是远控的时间段
        List<MesEquipmentWarnVO> ykList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(pointValueList)) {
            pointValueList.forEach((k, v) -> {
                LambdaQueryWrapper<MesEquipmentPoint> bhWrapper = new LambdaQueryWrapper<>();
                bhWrapper.eq(MesEquipmentPoint::getPointAddress, k);
                bhWrapper.eq(MesEquipmentPoint::getStatus, true);
                MesEquipmentPoint onePoint = equipmentPointService.getOne(bhWrapper);
                if (Objects.isNull(onePoint)) {
                    throw new BMSException("error", "未查询到远控/就地点位");
                }
                MesEquipmentWarnVO yk = new MesEquipmentWarnVO();
                yk.setEqptNoType(onePoint.getEquipmentNoType());
                yk.setEqptPartName(onePoint.getPartsName());
                //时间集合
                List<MesEquipmentWarnTimeVO> times = new ArrayList<>();
                MesEquipmentWarnTimeVO timeVO = new MesEquipmentWarnTimeVO();
                boolean isStartSet = false;
                for (int i = 0; i < v.size(); i++) {
                    KHPointDataVO p = v.get(i);
                    if ((Boolean) p.getValue()) {
                        if (!isStartSet) {
                            timeVO.setStartTime(p.getDateTime());
                            isStartSet = true;
                        }
                    } else {
                        if (isStartSet) {
                            timeVO.setEndTime(v.get(i - 1).getDateTime());
                            times.add(timeVO);
                            timeVO = new MesEquipmentWarnTimeVO();
                            isStartSet = false;
                        }
                    }
                    if (i == v.size() - 1 && isStartSet) {
                        timeVO.setEndTime(p.getDateTime());
                        times.add(timeVO);
                    }
                }
                yk.setTimeList(times);
                ykList.add(yk);
            });
        }
        //报警记录集合
        List<MesEquipmentWarnVO> warnRecords = new ArrayList<>();
        //每个远控/就地点位，远控的所有时间段
        if (!CollectionUtil.isEmpty(ykList)) {
            ykList.forEach(yk -> {
                //保护点位集合
                //条件1：设备编码相同 条件2：设备部件相同 条件3：点位分类的一级目录是保护
                LambdaQueryWrapper<MesEquipmentPoint> bhWrapper = new LambdaQueryWrapper<>();
                bhWrapper.eq(MesEquipmentPoint::getPartsName, yk.getEqptPartName());
                bhWrapper.eq(MesEquipmentPoint::getEquipmentNoType, yk.getEqptNoType());
                bhWrapper.eq(MesEquipmentPoint::getStatus, true);
                bhWrapper.like(MesEquipmentPoint::getPointClassification, "保护%");
                List<MesEquipmentPoint> bhPoints = equipmentPointService.list(bhWrapper);
                if (CollectionUtil.isEmpty(bhPoints)) {
                    throw new BMSException("error", "未查询到保护点位数据");
                }
                List<String> bhPointList = bhPoints.stream()
                    .map(MesEquipmentPoint::getPointAddress)
                    .collect(Collectors.toList());
                List<MesEquipmentWarnTimeVO> timeList = yk.getTimeList();
                if (!CollectionUtil.isEmpty(timeList)) {
                    timeList.forEach(time -> {
                        // 一个远控/就地点位下所有的保护点位数据集合
                        Map<String, List<KHPointDataVO>> bhPointValueList = null;
                        try {
                            HstTimeStatsParam statsParam = new HstTimeStatsParam();
                            statsParam.setStartTime(time.getStartTime());
                            statsParam.setEndTime(time.getEndTime());
                            statsParam.setPointList(bhPointList);
                            bhPointValueList = pointService.queryPointListByStepped(statsParam);
                        } catch (Exception e) {
                            throw new BMSException("error", "远程调用保护点位数据状态接口异常");
                        }
                        if (!CollectionUtil.isEmpty(bhPointValueList)) {
                            //遍历每个保护点位
                            bhPointValueList.forEach((k, v) -> {
                                //通过点位地址去查询部件实体
                                List<MesEquipmentPoint> points = bhPoints.stream()
                                    .filter(p -> p.getPointAddress().equals(k))
                                    .limit(1)
                                    .collect(Collectors.toList());
                                MesEquipmentPoint onePoint = points.get(0);
                                if (Objects.isNull(onePoint)) {
                                    throw new BMSException("error", "未查询到保护点位");
                                }
                                MesEquipmentWarnVO bhVO = new MesEquipmentWarnVO();
                                boolean isStartSet = false;
                                //报警开始时间结束时间
                                for (int i = 0; i < v.size(); i++) {
                                    KHPointDataVO p = v.get(i);
                                    if ((Boolean) p.getValue()) {
                                        if (!isStartSet) {
                                            bhVO.setWarnStartTime(p.getDateTime());
                                            isStartSet = true;
                                        }
                                    } else {
                                        if (isStartSet) {
                                            bhVO.setWarnEndTime(v.get(i - 1).getDateTime());
                                            addWarnRecord(bhVO, onePoint);
                                            warnRecords.add(bhVO);
                                            bhVO = new MesEquipmentWarnVO();
                                            isStartSet = false;
                                        }
                                    }
                                    if (i == v.size() - 1 && isStartSet) {
                                        bhVO.setWarnEndTime(p.getDateTime());
                                        addWarnRecord(bhVO, onePoint);
                                        warnRecords.add(bhVO);
                                    }
                                }
                            });
                        }
                    });
                }
            });
        }
        // 数据生成后，要跟历史数据作比较，
        // 如果相同的点位地址
        // 则需要判断新记录的开始时间是否在原记录的结束时间俩分钟内，
        // 如果是，则编辑老数据，反之则新增
        if (!CollectionUtil.isEmpty(warnRecords)) {
            //获取warnRecords点位地址集合
            List<String> warnAddressList = warnRecords.stream()
                .map(MesEquipmentWarnVO::getWarnAddress)
                .collect(Collectors.toList());
            LambdaQueryWrapper<MesEquipmentWarn> warnWrapper = new LambdaQueryWrapper<>();
            warnWrapper.in(MesEquipmentWarn::getWarnAddress, warnAddressList);
            List<MesEquipmentWarn> warns = this.list(warnWrapper);
            if (!CollectionUtil.isEmpty(warns)) {
                //warnRecords:新数据，warns：老数据
                //说明有关联数据，要判断warns的结束时间跟warnRecords的开始时间是否在2分钟内
                warnRecords.forEach(news -> {
                    //更新老数据，延长时间
                    warns.stream()
                        .filter(olds -> olds.getWarnAddress().equals(news.getWarnAddress()))
                        .filter(olds -> news.getWarnStartTime().minusMinutes(2).isBefore(olds.getWarnEndTime()))
                        .forEach(olds -> {
                            olds.setWarnEndTime(news.getWarnEndTime());
                        });
                });
                //删除对应新数据
                warnRecords.removeIf(news ->
                    warns.stream()
                        .anyMatch(olds -> olds.getWarnAddress().equals(news.getWarnAddress())
                            && news.getWarnStartTime().minusMinutes(2).isBefore(olds.getWarnEndTime())));
                //批量更新旧数据
                this.updateBatchById(warns);
            }
        }
        List<MesEquipmentWarn> warnEntitys = convert.toEntityList(warnRecords);
        return this.saveBatch(warnEntitys);
    }

    @Override
    public List<MesEquipmentWarnTypeVO> getPieChart(MesBeltDataParam param) {
        List<MesEquipmentWarnTypeVO> warnTypeList = new ArrayList<>();
        LambdaQueryWrapper<MesEquipmentWarn> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(
            param.getStartDateTime() != null && param.getEndDateTime() != null,
            MesEquipmentWarn::getWarnStartTime,
            param.getStartDateTime(),
            param.getEndDateTime());
        List<MesEquipmentWarn> warns = this.list(wrapper);
        if (!CollectionUtil.isEmpty(warns)) {
            //按照报警类型分组转成map
            Map<String, List<MesEquipmentWarn>> warnMap =
                warns.stream()
                    .collect(Collectors.groupingBy(MesEquipmentWarn::getClassificationName));
            //按条数排序
            List<List<MesEquipmentWarn>> sortedValues = warnMap.entrySet().stream()
                .sorted(Comparator.comparingInt(e -> e.getValue().size()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
            Collections.reverse(sortedValues);
            //如果大于5，则取前4个,剩下的类型变成其他
            Integer count = 0;
            for (int i = 0; i < sortedValues.size(); i++) {
                MesEquipmentWarnTypeVO warnTypeVO = new MesEquipmentWarnTypeVO();
                if (i < 4) {
                    warnTypeVO.setWarnType(sortedValues.get(i).get(0).getClassificationName());
                    warnTypeVO.setCount(sortedValues.get(i).size());
                    warnTypeList.add(warnTypeVO);
                } else {
                    count += sortedValues.get(i).size();
                }
            }
            if (count > 0) {
                MesEquipmentWarnTypeVO warnTypeVO = new MesEquipmentWarnTypeVO();
                warnTypeVO.setWarnType("其他");
                warnTypeVO.setCount(count);
                warnTypeList.add(warnTypeVO);
            }
        }
        return warnTypeList;
    }

    @Override
    public List<MesBeltDataVO> getBeltValue(MesBeltDataParam param) {
        if (param.getStartDateTime() == null && param.getEndDateTime() == null) {
            LocalDateTime nowTime = LocalDateTime.now();
            //默认查询最近一小时
            param.setStartDateTime(nowTime.minusHours(1));
            param.setEndDateTime(nowTime);
        }
        List<MesBeltDataVO> beltList = new ArrayList<>();
        // 皮带点位key列表
        List<String> keyList = KHBeltPointEnum.getKeyList();
        // 点位数据集合
        Map<String, List<KHPointDataVO>> pointValueList = null;
        try {
            HstTimeStatsParam statsParam = new HstTimeStatsParam();
            statsParam.setStartTime(param.getStartDateTime());
            statsParam.setEndTime(param.getEndDateTime());
            statsParam.setPointList(keyList);
            pointValueList = pointService.queryPointListByStepped(statsParam);
        } catch (Exception e) {
            throw new BMSException("error", "远程调用皮带点位数据接口异常");
        }
        if (!CollectionUtil.isEmpty(pointValueList)) {
            pointValueList.forEach((k, v) -> {
                //点位数据
                MesBeltDataVO beltDataVO = new MesBeltDataVO();
                //最大值、最小值、平均值、瞬时值、基准值数据
                MesBeltDataValueVO beltDataValueVO = new MesBeltDataValueVO();
                // 点位名称
                beltDataVO.setBeltName(getPDValue(k));
                //基准值
                if (KHBeltPointEnum.CON201Amount.getKey().equals(k)) {
                    beltDataValueVO.setBaseValue(BigDecimal.valueOf(1700));
                } else if (KHBeltPointEnum.CON218Amount.getKey().equals(k)) {
                    beltDataValueVO.setBaseValue(BigDecimal.valueOf(1100));
                } else if (KHBeltPointEnum.CON301Amount.getKey().equals(k)) {
                    beltDataValueVO.setBaseValue(BigDecimal.valueOf(700));
                } else if (KHBeltPointEnum.CON302Amount.getKey().equals(k)) {
                    beltDataValueVO.setBaseValue(BigDecimal.valueOf(800));
                }
                if (!CollectionUtil.isEmpty(v)) {
                    // 点位数据集合
                    v.forEach(
                        p -> {
                            if (p.getValue() != null) {
                                //object转成BigDecimal
                                BigDecimal bd = new BigDecimal(p.getValue().toString());
                                p.setValue(bd.setScale(0, RoundingMode.HALF_UP));
                            }
                        });
                    beltDataVO.setPointDataVOList(v);
                    // 转换KHPointDataVOList 为doubles数组
                    double[] tableValue = covertListToDoubleArray(v);
                    // 瞬时值
                    beltDataValueVO.setNowValue(
                        NumberUtil.toBigDecimal(v.get(v.size() - 1).getValue().toString())
                            .setScale(0, RoundingMode.HALF_UP));
                    // 最小值
                    beltDataValueVO.setMinValue(
                        BigDecimal.valueOf(StatUtils.min(tableValue)).setScale(0, RoundingMode.HALF_UP));
                    // 最大值
                    beltDataValueVO.setMaxValue(
                        BigDecimal.valueOf(StatUtils.max(tableValue)).setScale(0, RoundingMode.HALF_UP));
                    // 平均值
                    beltDataValueVO.setAvgValue(
                        BigDecimal.valueOf(StatUtils.mean(tableValue)).setScale(0, RoundingMode.HALF_UP));
                }
                beltDataVO.setBeltDataValueVO(beltDataValueVO);
                beltList.add(beltDataVO);
            });
        }
        //201+218
        MesBeltDataVO belt201And218VO = new MesBeltDataVO();
        belt201And218VO.setBeltName("201+218");
        //获取201和208的pointDataVOList
        Map<String, List<KHPointDataVO>> beltDataMap = beltList.stream()
            .filter(b -> StringUtils.isNotBlank(b.getBeltName()) && b.getPointDataVOList() != null)
            .collect(Collectors.toMap(MesBeltDataVO::getBeltName, MesBeltDataVO::getPointDataVOList));
        List<KHPointDataVO> belt201 = beltDataMap.get("201");
        List<KHPointDataVO> belt218 = beltDataMap.get("218");
        if (!CollectionUtil.isEmpty(belt201) && !CollectionUtil.isEmpty(belt218)) {
            // 201+218数据集合
            List<KHPointDataVO> belt201And218 = new ArrayList<>();
            for (int i = 0; i < belt201.size(); i++) {
                KHPointDataVO belt201Data = belt201.get(i);
                KHPointDataVO belt218Data = belt218.get(i);
                KHPointDataVO belt201And218DataVO = new KHPointDataVO();
                belt201And218DataVO.setPoint(belt201Data.getPoint());
                BigDecimal belt201Value = new BigDecimal(belt201Data.getValue().toString());
                BigDecimal belt218Value = new BigDecimal(belt218Data.getValue().toString());
                belt201And218DataVO.setValue(belt201Value.add(belt218Value)
                    .setScale(0, RoundingMode.HALF_UP));
                LocalDateTime belt201Time = belt201Data.getDateTime();
                belt201And218DataVO.setTime(belt201Time);
                belt201And218.add(belt201And218DataVO);
            }
            belt201And218VO.setPointDataVOList(belt201And218);
            beltList.add(belt201And218VO);
        }
        //301+302
        MesBeltDataVO belt301And302VO = new MesBeltDataVO();
        belt301And302VO.setBeltName("301+302");
        //获取301和302的pointDataVOList
        List<KHPointDataVO> belt301 = beltDataMap.get("301");
        List<KHPointDataVO> belt302 = beltDataMap.get("302");
        if (!CollectionUtil.isEmpty(belt301) && !CollectionUtil.isEmpty(belt302)) {
            // 301+302数据集合
            List<KHPointDataVO> belt301And302 = new ArrayList<>();
            for (int i = 0; i < belt301.size(); i++) {
                KHPointDataVO belt301Data = belt301.get(i);
                KHPointDataVO belt302Data = belt302.get(i);
                KHPointDataVO belt301And302DataVO = new KHPointDataVO();
                belt301And302DataVO.setPoint(belt301Data.getPoint());
                BigDecimal belt301Value = new BigDecimal(belt301Data.getValue().toString());
                BigDecimal belt302Value = new BigDecimal(belt302Data.getValue().toString());
                belt301And302DataVO.setValue(belt301Value.add(belt302Value)
                    .setScale(0, RoundingMode.HALF_UP));
                LocalDateTime belt301Time = belt301Data.getDateTime();
                belt301And302DataVO.setTime(belt301Time);
                belt301And302.add(belt301And302DataVO);
            }
            belt301And302VO.setPointDataVOList(belt301And302);
            beltList.add(belt301And302VO);
        }
        return beltList;
    }

    /**
     * 根据点位地址获取点位名称
     */
    public String getPDValue(String point) {
        if (point != null) {
            if (KHBeltPointEnum.CON201Amount.getKey().equals(point)) {
                return "201";
            } else if (KHBeltPointEnum.CON218Amount.getKey().equals(point)) {
                return "218";
            } else if (KHBeltPointEnum.CON301Amount.getKey().equals(point)) {
                return "301";
            } else if (KHBeltPointEnum.CON302Amount.getKey().equals(point)) {
                return "302";
            } else if (KHBeltPointEnum.CON802Amount.getKey().equals(point)) {
                return "802";
            }
        }
        return null;
    }

    /**
     * 转换KHPointDataVOList 为doubles数组
     */
    private double[] covertListToDoubleArray(List<KHPointDataVO> pointDataVOList) {
        BigDecimal[] array =
            pointDataVOList.stream().map(KHPointDataVO::getValue).toArray(BigDecimal[]::new);
        return Arrays.stream(array).mapToDouble(BigDecimal::doubleValue).toArray();
    }

    private MesEquipmentWarnVO addWarnRecord(MesEquipmentWarnVO bhVO, MesEquipmentPoint onePoint) {
        //点位地址
        bhVO.setWarnAddress(onePoint.getPointAddress());
        //部件id
        bhVO.setEqptPartId(Long.valueOf(onePoint.getPartsId()));
        //部件名称
        bhVO.setEqptPartName(onePoint.getPartsName());
        //设备id
        bhVO.setEqptId(onePoint.getEquipmentDataId());
        //设备编码及类别
        bhVO.setEqptNoType(onePoint.getEquipmentNoType());
        //报警内容
        bhVO.setWarnContent(onePoint.getPointName());
        //点位类型code,eg:1-2-3
        String pointTypeCode = onePoint.getPointClassificationCode();
        String[] codeSplit = pointTypeCode.split("-");
        //获取codeSplit最后一个元素
        String result = codeSplit[codeSplit.length - 1];
        if (StringUtils.isEmpty(result)) {
            throw new BMSException("error", "未查出对应点位类型");
        }
        bhVO.setClassificationCode(Long.valueOf(result));
        MesEquipmentPointClassification classification = classificationService.getById(Long.valueOf(result));
        if (Objects.isNull(classification)) {
            throw new BMSException("error", "未查询到报警分类数据");
        }
        //报警分类名称
        bhVO.setClassificationName(classification.getName());
        return bhVO;
    }
}
