package com.yhd.admin.bms.domain.query.sys;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DicItemParam.java
 * @Description TODO
 * @createTime 2020年06月16日 09:12:00
 */
public class DicItemParam extends Param implements Cloneable, Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 字典表主键
     */
    private Long dicId;
    /**
     * 字典分类
     */
    private String category;
    /**
     * 编码
     */
    private String code;
    /**
     * 编码值
     */
    private String val;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 排序
     */
    private Long orderNum;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = val;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return "DicItemParam{" + "id=" + id + ", dicId=" + dicId + ", category='" + category + '\''
            + ", code='" + code
            + '\'' + ", val='" + val + '\'' + ", status=" + status + ", orderNum=" + orderNum + '}';
    }
}
