package com.yhd.admin.bms.domain.enums.safe;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考试试卷题型
 *
 * <AUTHOR>
 * @date 2024/1/5 9:36
 */
@Getter
public enum ExamPaperQuestionTypeEnum {
  JUDGE_CHOICE("JUDGE_CHOICE", "判断题"),
  SINGLE_CHOICE("SINGLE_CHOICE", "单项选择题"),
  MULTI_CHOICE("MULTI_CHOICE", "多项选择题"),
  ;

  private final String code;
  private final String msg;

  ExamPaperQuestionTypeEnum(String code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  public static List<String> getCodeList() {
    ExamPaperQuestionTypeEnum[] values = ExamPaperQuestionTypeEnum.values();

    return Arrays.stream(values)
        .map(ExamPaperQuestionTypeEnum::getCode)
        .collect(Collectors.toList());
  }

  // 根据状态获取状态枚举
  public static ExamPaperQuestionTypeEnum getStatusEnumByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    ExamPaperQuestionTypeEnum[] values = ExamPaperQuestionTypeEnum.values();
    for (ExamPaperQuestionTypeEnum value : values) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }
}
