package com.yhd.admin.bms.controller.sw.dashboard;

import com.yhd.admin.bms.service.sw.dashboard.MesDosingService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能加药-控制层
 *
 * <AUTHOR>
 * @date 2024/10/26 11:10
 */
@RestController
@RequestMapping("/api/dosing")
public class MesDosingController {
  @Autowired private MesDosingService dosingService;

  @PostMapping(
      value = "/getDosingBoardData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getDosingBoardData() {
    try {
      return RespJson.buildSuccessResponse(dosingService.getDosingBoardData());
    } catch (Exception e) {
      return RespJson.buildFailureResponse("获取智能加药看板点位失败，请检查");
    }
  }
}
