package com.yhd.admin.bms.domain.dto.sw.safe;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 安全培训教师评价表
 *
 * <AUTHOR>
 * @date 2024/3/14 11:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwSafeTrainEvaluateDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 3918297017908432212L;

  /** 培训计划主键id */
  private Long planId;
  /** 学员账号 */
  private String student;
  /** 学员姓名 */
  private String studentName;
  /** 培训内容 */
  private String trainContent;
  /** 培训综合评分 */
  private Double pxzhPf;
  /** 培训教师评价详情 */
  private List<MesSwSafeTrainTeacherEvaluateDetailDTO> teacherEvaluateDetail;
}
