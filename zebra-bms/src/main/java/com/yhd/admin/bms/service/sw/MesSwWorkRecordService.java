package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwWorkRecordDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkRecord;
import com.yhd.admin.bms.domain.query.sw.MesPreClassMeetParam;
import com.yhd.admin.bms.domain.query.sw.MesSwWorkRecordParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

/**
 * 班后会记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-10-10
 */
public interface MesSwWorkRecordService extends IService<MesSwWorkRecord> {

    IPage<MesSwWorkRecordDTO> pagingQuery(MesSwWorkRecordParam queryParam);

    Boolean add(MesSwWorkRecordParam param);

    Boolean modify(MesSwWorkRecordParam param);

    Boolean removeBatch(BatchParam param);

    MesSwWorkRecordDTO getCurrentDetail(MesSwWorkRecordParam param);

    String export(BatchParam param);
}
