package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.safe.MesSwContractorUnsafeScoreDetailDao;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwContractorUnsafeScoreDetail;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwContractorUnsafeScoreDetailParam;
import com.yhd.admin.bms.service.sw.safe.MesSwContractorUnsafeScoreDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 承包商不安全行为积分台账明细-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2023/10/26 15:39
 */
@Service
public class MesSwContractorUnsafeScoreDetailServiceImpl
    extends ServiceImpl<MesSwContractorUnsafeScoreDetailDao, MesSwContractorUnsafeScoreDetail>
    implements MesSwContractorUnsafeScoreDetailService {
  @Override
  public List<MesSwContractorUnsafeScoreDetail> queryList(
      MesSwContractorUnsafeScoreDetailParam param) {
    LambdaQueryWrapper<MesSwContractorUnsafeScoreDetail> wrapper = new LambdaQueryWrapper<>();
    // 主表id
    wrapper.eq(
        Objects.nonNull(param.getContractorUnsafeId()),
        MesSwContractorUnsafeScoreDetail::getContractorUnsafeId,
        param.getContractorUnsafeId());

    return baseMapper.selectList(wrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void removeBatchByRelaId(List<Long> relaIdList) {
    LambdaQueryWrapper<MesSwContractorUnsafeScoreDetail> wrapper = new LambdaQueryWrapper<>();
    wrapper.in(MesSwContractorUnsafeScoreDetail::getContractorUnsafeId, relaIdList);

    baseMapper.delete(wrapper);
  }
}
