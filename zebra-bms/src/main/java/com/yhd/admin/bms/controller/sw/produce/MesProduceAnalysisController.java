package com.yhd.admin.bms.controller.sw.produce;

import com.yhd.admin.bms.domain.query.sw.produce.MesBeltDataParam;
import com.yhd.admin.bms.domain.query.sw.produce.MesProduceAnalysisParam;
import com.yhd.admin.bms.domain.vo.sw.produce.MesBeltDataVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesProduceAnalysisVO;
import com.yhd.admin.bms.service.sw.produce.MesProduceAnalysisService;
import com.yhd.admin.bms.service.sw.produce.MesProduceHistoryDataService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产效率-综合分析
 *
 * <AUTHOR>
 * @Date 2025/4/25 10:17
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/produce/analysis")
public class MesProduceAnalysisController {

    @Resource
    private MesProduceAnalysisService service;

    @PostMapping(
        value = "/getAnalysis",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesProduceAnalysisVO> getAnalysis(@RequestBody MesProduceAnalysisParam param) {
        try {
            return RespJson.buildSuccessResponse(service.getAnalysis(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/withdrawPowerPlant",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> withdrawPowerPlant(@RequestBody MesProduceAnalysisParam param) {
        try {
            return RespJson.buildSuccessResponse(service.withdrawPowerPlant(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }
}
