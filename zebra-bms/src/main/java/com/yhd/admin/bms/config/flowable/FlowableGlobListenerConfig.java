package com.yhd.admin.bms.config.flowable;

import com.yhd.admin.bms.config.flowable.listener.GlobalProcistEndListener;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.delegate.event.FlowableEventDispatcher;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * <AUTHOR> wangsm
 * @title: : FlowableGlobListenerConfig
 * @projectName : flowable
 * @description: 全局监听配置 ContextRefreshedEvent在类被初始化之后触发
 * @date : 2022/04/28
 */
@Configuration
public class FlowableGlobListenerConfig implements ApplicationListener<ContextRefreshedEvent> {
  @Autowired private SpringProcessEngineConfiguration configuration;
  @Autowired private GlobalProcistEndListener globalProcistEndListener;

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    FlowableEventDispatcher dispatcher = configuration.getEventDispatcher();
    // 添加流程实例结束全局监听
    dispatcher.addEventListener(
        globalProcistEndListener, FlowableEngineEventType.PROCESS_COMPLETED);
  }
}
