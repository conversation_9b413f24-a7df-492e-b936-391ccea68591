package com.yhd.admin.bms.domain.query.sw.produce;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 电厂皮带
 *
 * <AUTHOR>
 * @date 2025/1/13 14:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesProduceDcpdParam extends QueryParam {
  private static final long serialVersionUID = 3345471042790315237L;
  /** 开始日期 */
  private LocalDate startDate;
  /** 结束日期 */
  private LocalDate endDate;

  /** 统计日期 */
  private String statsDate;
  /** 电厂皮带901生产产量t */
  private Double quantity;
}
