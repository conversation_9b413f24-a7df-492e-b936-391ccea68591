package com.yhd.admin.bms.service.sys.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sys.ClientDao;
import com.yhd.admin.bms.domain.convert.sys.ClientConvert;
import com.yhd.admin.bms.domain.dto.sys.ClientDTO;
import com.yhd.admin.bms.domain.entity.sys.SysClient;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.ClientQueryParam;
import com.yhd.admin.bms.service.sys.ClientService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:52
 */

@Service
public class ClientServiceImpl extends ServiceImpl<ClientDao, SysClient> implements ClientService {
    private ClientConvert clientConvert;

    public ClientServiceImpl(ClientConvert clientConvert) {
        this.clientConvert = clientConvert;
    }

    /**
     * 新增客户端
     *
     * @param param
     * @return
     */
    @Override
    public Boolean addClient(ClientQueryParam param) {
        return this.save(clientConvert.toEntity(param));
    }

    /**
     * 修改客户端
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyClient(ClientQueryParam param) {
        return this.updateById(clientConvert.toEntity(param));
    }

    /**
     * 分页查询
     *
     * @param queryParam
     * @return
     */
    @Override
    public IPage<ClientDTO> pagingQuery(ClientQueryParam queryParam) {
        IPage<SysClient> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<SysClient> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper
            .eq(StringUtils.isNoneBlank(queryParam.getClientId()), SysClient::getClientId, queryParam.getClientId())
            .eq(StringUtils.isNoneBlank(queryParam.getResourceId()), SysClient::getResourceId,
                queryParam.getResourceId());
        return queryChainWrapper.page(iPage).convert(clientConvert::toDTO);
    }

    /**
     * 根据ID查询详细信息
     *
     * @param queryParam
     * @return
     */
    @Override
    public ClientDTO currentDetail(ClientQueryParam queryParam) {

        return clientConvert.toDTO(this.getById(queryParam.getId()));
    }

    /**
     * 条件查询
     *
     * @param queryParam
     * @return List<ClientDTO>
     */
    @Override
    public List<ClientDTO> queryList(ClientQueryParam queryParam, Boolean check) {
        LambdaQueryChainWrapper<SysClient> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.notIn(check && queryParam.getId() != null, SysClient::getId, queryParam.getId())
            .eq(StringUtils.isNoneBlank(queryParam.getClientId()), SysClient::getClientId, queryParam.getClientId())
            .eq(StringUtils.isNoneBlank(queryParam.getResourceId()), SysClient::getResourceId,
                queryParam.getResourceId());
        return queryChainWrapper.list().stream().map(clientConvert::toDTO).collect(Collectors.toList());
    }

    @Override
    public Boolean removeBatch(BatchParam batchParam) {
        return super.removeByIds(batchParam.getId());
    }

    @Override
    public Boolean checkIfNotExist(ClientQueryParam queryParam) {
        List<ClientDTO> checkVal = queryList(queryParam, Boolean.TRUE);
        return CollectionUtils.isEmpty(checkVal);

    }
}
