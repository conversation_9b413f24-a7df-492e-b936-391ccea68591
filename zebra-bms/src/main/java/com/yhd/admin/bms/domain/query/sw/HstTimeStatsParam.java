package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class HstTimeStatsParam extends QueryParam {

  private static final long serialVersionUID = -4090897548147972326L;
  /** 名称 */
  private String name;

  /** 点位 */
  private String point;

  /** 开始时间 */
  private LocalDateTime startTime;

  /** 结束时间 */
  private LocalDateTime endTime;

  /** 额外的查询条件 */
  private String extWhereSQL;

  private String order;

  private Boolean group;

  /** 分组聚合 */
  private String agg;

  private Integer interval;

  private BigDecimal startValue;

  private BigDecimal endValue;

  // 开始值（数值范围百分比使用）
  private BigDecimal rangeStartValue;
  // 结束值（数值范围百分比使用）
  private BigDecimal rangeEndValue;

  /** 生产数据分析-工艺参数-参数名称 */
  private String  parameterName;

  /** 保留小数位数*/
  private String  decimalScale;

  /** 是否过滤数据 true过滤 false不过滤*/
  private Boolean isFilter;

  /** 查询点位集合 */
  private List<String> pointList;
}
