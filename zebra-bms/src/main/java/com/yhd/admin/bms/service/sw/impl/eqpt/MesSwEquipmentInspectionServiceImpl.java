package com.yhd.admin.bms.service.sw.impl.eqpt;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.constant.FlowConstant;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentInspectionDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentInspectionConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionContentDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionPageDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionPersonDTO;
import com.yhd.admin.bms.domain.dto.sys.DicItemDTO;
import com.yhd.admin.bms.domain.dto.sys.RoleDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentData;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspection;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspectionSignature;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentWorkOrder;
import com.yhd.admin.bms.domain.entity.sys.MesNotice;
import com.yhd.admin.bms.domain.entity.sys.SysParameter;
import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.domain.enums.NotifyEnum;
import com.yhd.admin.bms.domain.enums.flowable.CommentTypeEnum;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionContentParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionParam;
import com.yhd.admin.bms.domain.query.sys.ParameterParam;
import com.yhd.admin.bms.domain.query.sys.UserAccountParam;
import com.yhd.admin.bms.domain.vo.flowable.FlowTaskParam;
import com.yhd.admin.bms.domain.vo.flowable.StartProcessInstanceVo;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionContentVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionPersonVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.flowable.IFlowableProcessInstanceService;
import com.yhd.admin.bms.service.flowable.IFlowableTaskService;
import com.yhd.admin.bms.service.sw.eqpt.*;
import com.yhd.admin.bms.service.sys.*;
import com.yhd.admin.bms.webscoket.service.WebsocketService;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.poi.excel.CellBuilder;
import com.yhd.admin.common.poi.excel.ExcelBuilder;
import com.yhd.admin.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class MesSwEquipmentInspectionServiceImpl
    extends ServiceImpl<MesSwEquipmentInspectionDao, MesSwEquipmentInspection>
    implements MesSwEquipmentInspectionService {

  @Resource private MesSwEquipmentInspectionConvert convert;
  @Resource private DicService dicService;
  @Resource private MesSwEquipmentDataService equipmentService;
  @Resource private IFlowableProcessInstanceService flowableProcessInstanceService;
  @Resource private IFlowableTaskService flowableTaskService;
  @Resource private RoleService roleService;
  @Resource private MesSwEquipmentInspectionSignatureService signatureService;
  @Resource private MesSwEquipmentWorkOrderService workOrderService;
  @Resource private UserAccountService accountService;
  @Resource private WebsocketService websocketService;
  @Resource private MesNoticeService mesNoticeService;
  @Resource private ParameterService parameterService;
  @Resource private MesSwEquipmentInspectionReportService inspectionReportService;

  private static final String defaultBucketName = "swcenter";
  @Resource private StorageServices storageServices;

  @Resource private RedisTemplate redisTemplate;

  @Override
  public IPage<MesSwEquipmentInspectionPageDTO> pagingQuery(MesSwEquipmentInspectionParam param) {
    Page<MesSwEquipmentInspectionPageDTO> page =
        new Page<>(param.getCurrent(), param.getPageSize());

    // 需要我处理的单子
    boolean queryMe = param.getQueryMe() != null && param.getQueryMe();

    Page<MesSwEquipmentInspectionPageDTO> result = new Page<>();
    Set<Object> IDS = new HashSet<>();
    Set<Object> childIDS = new HashSet<>();
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    if (queryMe) {
      // 查询需要我处理的流程id
      List<Task> taskList =
          flowableTaskService.getMyTask(
              account,
              Arrays.asList(
                  FlowConstant.FLOW_EQUIPMENT_INSPECTION_KEY,
                  FlowConstant.FLOW_EQUIPMENT_INSPECTION_LEADER_KEY));
      List<String> processInstanceIdList =
          CollStreamUtil.toList(taskList, Task::getProcessInstanceId);
      if (CollectionUtil.isEmpty(processInstanceIdList)) {
        return result;
      }
      // 根据流程id查询到各个单子id
      LambdaQueryWrapper<MesSwEquipmentInspectionSignature> signWrapper =
          new LambdaQueryWrapper<>();
      signWrapper.select(MesSwEquipmentInspectionSignature::getParentId);
      signWrapper.in(
          MesSwEquipmentInspectionSignature::getProcessInstanceId, processInstanceIdList);
      List<Object> parentIDS = signatureService.listObjs(signWrapper);
      if (CollectionUtil.isEmpty(parentIDS)) {
        return result;
      }
      // 查询巡检的总单id
      LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
      wrapper.select(MesSwEquipmentInspection::getId, MesSwEquipmentInspection::getParentId);
      wrapper.in(MesSwEquipmentInspection::getId, parentIDS);
      List<Map<String, Object>> mapList = this.listMaps(wrapper);
      if (CollectionUtil.isEmpty(mapList)) {
        return result;
      }
      for (Map<String, Object> map : mapList) {
        if (map.get("parent_id") != null) {
          // 巡检子单，则取parentId
          IDS.add(map.get("parent_id"));
          childIDS.add(map.get("id"));
        } else {
          // 巡检总单，则取id
          IDS.add(map.get("id"));
        }
      }
    }
    param.setIDS(IDS);
    result = baseMapper.pagingQuery(page, param);
    if (result == null || CollectionUtil.isEmpty(result.getRecords())) {
      return result;
    }
    List<MesSwEquipmentInspectionPageDTO> list = result.getRecords();
    // 查询子单
    List<MesSwEquipmentInspection> childList = new ArrayList<>();
    if (queryMe && CollectionUtil.isEmpty(childIDS)) {
      // 查询与我相关，并且相关的子单ID为空，则不需要查询子单，避免浪费
    } else {
      List<String> parentIdList =
          CollStreamUtil.toList(list, MesSwEquipmentInspectionPageDTO::getId);
      LambdaQueryWrapper<MesSwEquipmentInspection> childWrapper = new LambdaQueryWrapper<>();
      childWrapper.select(
          MesSwEquipmentInspection::getId,
          MesSwEquipmentInspection::getParentId,
          MesSwEquipmentInspection::getProcessInstanceId,
          MesSwEquipmentInspection::getEquipmentNo,
          MesSwEquipmentInspection::getEquipmentPower,
          MesSwEquipmentInspection::getCreatedDate,
          MesSwEquipmentInspection::getStatusCode,
          MesSwEquipmentInspection::getStatusName);
      childWrapper.in(MesSwEquipmentInspection::getParentId, parentIdList);
      if (CollectionUtil.isNotEmpty(childIDS)) {
        // 查询与我相关，且相关的子单ID不为空，则需要带入查询
        childWrapper.in(MesSwEquipmentInspection::getId, childIDS);
      }
      if (DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_START.equals(param.getStatusCode())) {
        // 查询条件是：巡检中，则子单需要只获取审批中的状态
        childWrapper.eq(
            MesSwEquipmentInspection::getStatusCode,
            DicConstant.DIC_ITEM_CONTRACTOR_ADMISSION_START);
      }
      childWrapper.orderByAsc(
          MesSwEquipmentInspection::getEquipmentNo, MesSwEquipmentInspection::getCreatedTime);
      childList = baseMapper.selectList(childWrapper);
    }
    // 根据总单ID分组
    Map<String, List<MesSwEquipmentInspection>> childMap =
        CollStreamUtil.groupByKey(childList, MesSwEquipmentInspection::getParentId);

    boolean isDelete = isDelete(account);
    for (MesSwEquipmentInspectionPageDTO item : list) {
      // 删除标志，是管理员或者单子的创建人是登录人
      item.setDeleteFlag(isDelete || account.equals(item.getCreatedBy()));
      item.setManagerFlag(isDelete);
      item.setCreatedDate(LocalDateTimeUtil.format(item.getCreatedDateDate(), "yyyy年MM月dd日"));
      if (StringUtils.isNotBlank(item.getProcessInstanceId())) {
        // 获取taskId
        Task task = flowableTaskService.getTask(account, item.getProcessInstanceId());
        if (task != null) item.setTaskId(task.getId());
      }
      List<MesSwEquipmentInspection> sonList = childMap.get(item.getId());
      if (CollectionUtil.isEmpty(sonList)) {
        continue;
      }
      List<MesSwEquipmentInspectionPageDTO> children = new ArrayList<>();
      for (MesSwEquipmentInspection sonItem : sonList) {
        MesSwEquipmentInspectionPageDTO sonDTO = convert.toPageDTO(sonItem);
        String no = sonDTO.getEquipmentNo();
        String power = sonDTO.getEquipmentPower();
        sonDTO.setNoPower(generateEquipmentNoAndPower(no, power));
        sonDTO.setCreatedDate(LocalDateTimeUtil.format(sonItem.getCreatedDate(), "yyyy年MM月dd日"));
        if (StringUtils.isNotBlank(sonItem.getProcessInstanceId())) {
          List<String> strList =
              StrSplitter.split(sonItem.getProcessInstanceId(), ',', 0, true, true);
          for (String processInstanceId : strList) {
            // 获取一次taskId就好
            Task task = flowableTaskService.getTask(account, processInstanceId);
            if (task != null) {
              sonDTO.setTaskId(task.getId());
              break;
            }
          }
        }
        children.add(sonDTO);
      }
      item.setChildren(children);
    }
    return result;
  }

  /**
   * 判断当前登录人是否为管理员
   *
   * @param account
   * @return true-是，false-否
   */
  private boolean isDelete(String account) {
    // 判断是否拥有删除标识
    UserAccountParam accountParam = new UserAccountParam();
    accountParam.setUsername(account);
    List<RoleDTO> roleList = roleService.getRoleByUser(accountParam);
    List<RoleDTO> roleResult =
        roleList.stream()
            .filter(role -> Objects.nonNull(role))
            .filter(role -> DicConstant.ROLE_CODE_ADMIN.equals(role.getRoleCode()))
            .collect(Collectors.toList());
    if (CollectionUtil.isNotEmpty(roleResult)) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 拼接成设备编号+功率
   *
   * @param equipmentNo
   * @param power
   */
  private String generateEquipmentNoAndPower(String equipmentNo, String power) {
    return new StringBuilder("设备")
        .append(equipmentNo)
        .append(" ")
        .append(power)
        .append("KW")
        .toString();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MesSwEquipmentInspectionParam param) {
    MesSwEquipmentInspection entityParent = convert.toEntity(param);
    String entityParentId = IdUtil.randomUUID();
    entityParent.setId(entityParentId);
    if (entityParent.getCreatedDate() == null) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), entityParent.getCreatedDate() + "创建日期为空，请检查！");
    }
    Map<String, List<DicItemDTO>> dicMap =
        redisTemplate.opsForHash().entries(BMSRedisKeyEnum.DIC.getKey());
    // 设备类型
    String equipmentType = entityParent.getEquipmentType();
    String equipmentName = transform(DicConstant.DEVICE_TYPE, equipmentType, dicMap);
    if (StringUtils.isBlank(equipmentName)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), equipmentType + "设备类型查询不到该数据，请检查！");
    }
    entityParent.setEquipmentName(equipmentName);
    param.setEquipmentName(equipmentName);
    // 所属车间
    String workShopCode = entityParent.getWorkShopCode();
    String workShopName = transform(DicConstant.WORK_SHOP, workShopCode, dicMap);
    if (StringUtils.isBlank(workShopName)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), workShopCode + "所属车间查询不到该数据，请检查！");
    }
    entityParent.setWorkShopName(workShopName);
    // 工单类别 例如Ⅰ类和Ⅱ、Ⅲ类
    List<String> classificationCodeList = param.getClassificationCodeList();
    if (CollUtil.isEmpty(classificationCodeList) || classificationCodeList.size() > 1) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), workShopCode + "巡检工单类别不能为空，且只能单选，请检查！");
    }
    // 前端单选，获取位置为1
    String orderCode = classificationCodeList.get(0);
    classificationCodeList = StrSplitter.split(orderCode, ',', 0, true, true);
    StringBuffer classificationName = new StringBuffer();
    if (CollUtil.isNotEmpty(classificationCodeList)) {
      for (String item : classificationCodeList) {
        if (classificationName.length() > 0) {
          classificationName.append("、");
        }
        String name = transform(DicConstant.DEVICE_CLASSIFICATION, item, dicMap);
        // 先去除类这个字，拼接完后再最后加一下，例如Ⅱ、Ⅲ类
        classificationName.append(name.replace("类", ""));
      }
    }
    if (classificationName.length() == 0) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), classificationCodeList + "设备分类找不到，请检查！");
    }
    classificationName.append("类");

    entityParent.setClassificationName(classificationName.toString());
    entityParent.setClassificationCode(orderCode);

    // 巡检工单类别及车间
    String classificationShop =
        StrUtil.format("巡检工单({})--{}", classificationName.toString(), workShopName);
    entityParent.setClassificationShop(classificationShop);
    param.setClassificationShop(classificationShop);

    // 巡检周期，period前端页面隐藏，字段后端自己赋值
    // String period = entityParent.getPeriod();
    String thisInspectionPeriod = entityParent.getThisInspectionPeriod();
    if (StringUtils.isBlank(thisInspectionPeriod)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "本次巡检周期不能为空，请检查！");
    }
    // 根据本次巡检周期，设置巡检周期值
    if (DicConstant.DIC_WEEK_ONE.equals(thisInspectionPeriod)
        || DicConstant.DIC_WEEK_TWO.equals(thisInspectionPeriod)
        || DicConstant.DIC_WEEK_THREE.equals(thisInspectionPeriod)
        || DicConstant.DIC_WEEK_FOUR.equals(thisInspectionPeriod)) {
      if (DicConstant.DIC_WEEK_ONE.equals(thisInspectionPeriod)) {
        entityParent.setThisInspectionStartDate(
            LocalDate.parse(entityParent.getThisInspection() + "-01"));
        entityParent.setThisInspectionEndDate(
            LocalDate.parse(entityParent.getThisInspection() + "-07"));
      } else if (DicConstant.DIC_WEEK_TWO.equals(thisInspectionPeriod)) {
        entityParent.setThisInspectionStartDate(
            LocalDate.parse(entityParent.getThisInspection() + "-08"));
        entityParent.setThisInspectionEndDate(
            LocalDate.parse(entityParent.getThisInspection() + "-14"));
      } else if (DicConstant.DIC_WEEK_THREE.equals(thisInspectionPeriod)) {
        entityParent.setThisInspectionStartDate(
            LocalDate.parse(entityParent.getThisInspection() + "-15"));
        entityParent.setThisInspectionEndDate(
            LocalDate.parse(entityParent.getThisInspection() + "-21"));
      } else if (DicConstant.DIC_WEEK_FOUR.equals(thisInspectionPeriod)) {
        LocalDate thisInspectionStartDate =
            LocalDate.parse(entityParent.getThisInspection() + "-22");
        entityParent.setThisInspectionStartDate(thisInspectionStartDate);
        YearMonth yearMonth = YearMonth.from(thisInspectionStartDate);
        entityParent.setThisInspectionEndDate(yearMonth.atEndOfMonth());
      }
      entityParent.setPeriod(DicConstant.DIC_ITEM_WEEKLY);
      // 按每周巡检
      thisInspectionPeriod =
          dicService.transform(DicConstant.DIC_INSPECTION_PERIOD_WEEK, thisInspectionPeriod);
    } else if (DicConstant.DIC_MONTH_FIVE.equals(thisInspectionPeriod)
        || DicConstant.DIC_MONTH_SIX.equals(thisInspectionPeriod)) {
      if (DicConstant.DIC_MONTH_FIVE.equals(thisInspectionPeriod)) {
        entityParent.setThisInspectionStartDate(
            LocalDate.parse(entityParent.getThisInspection() + "-01"));
        entityParent.setThisInspectionEndDate(
            LocalDate.parse(entityParent.getThisInspection() + "-15"));
      } else if (DicConstant.DIC_MONTH_SIX.equals(thisInspectionPeriod)) {
        LocalDate thisInspectionStartDate =
            LocalDate.parse(entityParent.getThisInspection() + "-16");
        entityParent.setThisInspectionStartDate(thisInspectionStartDate);
        YearMonth yearMonth = YearMonth.from(thisInspectionStartDate);
        entityParent.setThisInspectionEndDate(yearMonth.atEndOfMonth());
      }
      entityParent.setPeriod(DicConstant.DIC_ITEM_HALFMONTH);
      // 按每半月巡检
      thisInspectionPeriod =
          dicService.transform(DicConstant.DIC_INSPECTION_PERIOD_MONTH, thisInspectionPeriod);
    } else {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), thisInspectionPeriod + "本次巡检周期填写不正确，请检查！");
    }
    String period = entityParent.getPeriod();

    // 本次巡检周期
    String thisPeriod =
        StrUtil.format(
            "{}{}",
            LocalDateTimeUtil.format(
                LocalDate.parse(entityParent.getThisInspection() + "-01"), "yyyy年MM月"),
            thisInspectionPeriod);
    entityParent.setThisPeriod(thisPeriod);
    param.setThisPeriod(thisPeriod);

    entityParent.setStatusCode(DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_START);
    entityParent.setStatusName(
        transform(
            DicConstant.DIC_TOTAL_INSPECTION_STATUS,
            DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_START,
            dicMap));

    // 校验单子的重复
    LambdaQueryWrapper<MesSwEquipmentInspection> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(MesSwEquipmentInspection::getEquipmentType, equipmentType);
    queryWrapper.eq(MesSwEquipmentInspection::getClassificationShop, classificationShop);
    queryWrapper.eq(MesSwEquipmentInspection::getThisPeriod, thisPeriod);
    int count = this.count(queryWrapper);
    if (count > 0) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(),
          equipmentName + classificationShop + thisPeriod + "已经提交过，不能重复提交，请检查！");
    }

    String key = equipmentName + classificationShop;
    // 先查询工单流程
    LambdaQueryWrapper<MesSwEquipmentWorkOrder> orderWrapper = new LambdaQueryWrapper<>();
    orderWrapper
        .eq(MesSwEquipmentWorkOrder::getEquipmentType, equipmentType)
        .eq(MesSwEquipmentWorkOrder::getWorkShopCode, workShopCode)
        .eq(MesSwEquipmentWorkOrder::getClassificationCode, orderCode);
    List<MesSwEquipmentWorkOrder> parameterList = workOrderService.list(orderWrapper);
    if (CollUtil.isEmpty(parameterList) || parameterList.size() > 1) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), equipmentName + classificationShop + "查询不到对应的工单流程，请检查！");
    }
    // 获取工单里的巡检内容
    MesSwEquipmentWorkOrder parameter = parameterList.get(0);
    String content = parameter.getOrderContent();
    List<MesSwEquipmentInspectionContentDTO> contentList =
        JSONUtil.toList(content, MesSwEquipmentInspectionContentDTO.class);
    if (CollUtil.isEmpty(contentList)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), key + "查询不到对应的巡检内容，请检查！");
    }
    // 特殊设备的巡检内容变化
    List<MesSwEquipmentInspectionContentDTO> specialContentList = new ArrayList<>();
    // 巡检内容内的周期
    for (MesSwEquipmentInspectionContentDTO contentItem : contentList) {
      // 根据提交的工单，更新内容里的周期
      contentItem.setPeriod(period);
      // 是否为破碎机设备
      if (!"3".equals(equipmentType)) {
        continue;
      }
      MesSwEquipmentInspectionContentDTO item = convert.toContentDTO(contentItem);
      // 破碎机设备 设备为单驱时 B电动机、B减速器、B偶合器自动填入“/”
      if (contentItem.getProject().contains("B电动机")
          || contentItem.getProject().contains("B减速器")
          || contentItem.getProject().contains("B偶合器")) {
        item.setValue("/");
      }
      specialContentList.add(item);
    }
    // 后查询设备数据
    LambdaQueryWrapper<MesSwEquipmentData> wrapper = new LambdaQueryWrapper<>();
    wrapper.select(
        MesSwEquipmentData::getNo,
        MesSwEquipmentData::getName,
        MesSwEquipmentData::getPower,
        MesSwEquipmentData::getTypeCode,
        MesSwEquipmentData::getSingleOrDouble,
        MesSwEquipmentData::getCharterPerson);
    wrapper
        .eq(MesSwEquipmentData::getTypeCode, equipmentType)
        .like(MesSwEquipmentData::getWorkShopCode, workShopCode)
        .in(MesSwEquipmentData::getClassificationCode, classificationCodeList);
    List<MesSwEquipmentData> equipmentList = equipmentService.list(wrapper);
    if (CollUtil.isEmpty(equipmentList)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), key + "，查询不到对应的设备数据，请检查！");
    }
    // 消息集合
    List<MesNotice> noticeList = new ArrayList<>();
    List<MesSwEquipmentInspection> entityList = new ArrayList<>();
    List<MesSwEquipmentInspectionSignature> handlerTotalList = new ArrayList<>();
    MesSwEquipmentInspection entity = null;
    for (MesSwEquipmentData equipItem : equipmentList) {
      entity = convert.toCopyEntity(entityParent);
      String entityId = IdUtil.randomUUID();
      entity.setId(entityId);
      entity.setParentId(entityParentId);
      entity.setEquipmentName(equipItem.getName());
      entity.setEquipmentNo(equipItem.getNo());
      entity.setEquipmentPower(equipItem.getPower());
      entity.setCreatedDate(null);
      if ("3".equals(equipItem.getTypeCode()) && "单驱".equals(equipItem.getSingleOrDouble())) {
        // 破碎机设备 设备为单驱时 B电动机、B减速器、B偶合器自动填入“/”
        entity.setInspectionContent(JSONUtil.toJsonStr(specialContentList));
      } else {
        entity.setInspectionContent(JSONUtil.toJsonStr(contentList));
      }
      // 获取设备包机人
      String charterPerson = equipItem.getCharterPerson();
      entity.setCharterPerson(charterPerson);

      List<MesSwEquipmentInspectionPersonDTO> charterPersonList =
          JSONUtil.toList(charterPerson, MesSwEquipmentInspectionPersonDTO.class);
      // 分组，1、电工 2、钳工 3、运转工
      Map<String, List<MesSwEquipmentInspectionSignature>> processHandler =
          getProcessHandler(charterPersonList, entity);
      StringBuffer processInstanceIdStr = new StringBuffer();
      List<MesSwEquipmentInspectionSignature> handlerList = null;

      // 获取工单里的节点配置 例：1或 1,2
      List<String> node = StrSplitter.split(parameter.getApproveNode(), ',', 0, true, true);
      for (int i = 0; i < node.size(); i++) {
        handlerList = processHandler.get(node.get(i));
        if (CollUtil.isEmpty(handlerList) && handlerList.size() < 2) {
          throw new BMSException(ResultStateEnum.FAIL.getCode(), handlerList + "，查询不到对应的包机人，请检查！");
        }
        if (processInstanceIdStr.length() > 0) {
          processInstanceIdStr.append(",");
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put(
            FlowConstant.FLOW_EQUIPMENT_INSPECTION_NODE_ONE, handlerList.get(0).getAccount());
        variables.put(
            FlowConstant.FLOW_EQUIPMENT_INSPECTION_NODE_TWO, handlerList.get(1).getAccount());
        String processInstanceId =
            startProcess(
                variables,
                FlowConstant.FLOW_EQUIPMENT_INSPECTION_NAME,
                FlowConstant.FLOW_EQUIPMENT_INSPECTION_KEY);
        for (MesSwEquipmentInspectionSignature signItem : handlerList) {
          signItem.setProcessInstanceId(processInstanceId);
          signItem.setParentId(entityId);
          signItem.setId(IdUtil.randomUUID());
        }
        handlerTotalList.addAll(handlerList);
        processInstanceIdStr.append(processInstanceId);
        // 给处理人发通知
        MesNotice notice =
            sendMesNotice(
                handlerList.get(0).getAccount(),
                "/equipment/inspectionSignaDetail",
                generateEquipmentNoAndPower(entity.getEquipmentNo(), entity.getEquipmentPower()),
                processInstanceId);
        noticeList.add(notice);
      }
      entity.setProcessInstanceId(processInstanceIdStr.toString());
      entity.setStatusCode(DicConstant.DIC_ITEM_INSPECTION_PROCESS_START);
      entity.setStatusName(
          transform(
              DicConstant.DIC_INSPECTION_STATUS,
              DicConstant.DIC_ITEM_INSPECTION_PROCESS_START,
              dicMap));
      entityList.add(entity);
    }
    signatureService.saveBatch(handlerTotalList);
    saveBatch(entityList);
    boolean result = save(entityParent);
    for (MesNotice notice : noticeList) {
      websocketService.sendMessage(notice);
    }

    return result;
  }

  /**
   * 根据字典项，取值
   *
   * @param deviceType
   * @param equipmentType
   * @param dicMap
   * @return
   */
  private String transform(
      String deviceType, String equipmentType, Map<String, List<DicItemDTO>> dicMap) {
    List<DicItemDTO> dicItemDTOS = dicMap.get(deviceType);
    if (CollUtil.isEmpty(dicItemDTOS)) {
      return "";
    }
    Optional<String> optional =
        dicItemDTOS.stream()
            .filter(o -> o.getVal().equals(equipmentType))
            .map(DicItemDTO::getCode)
            .findFirst();
    return optional.orElse("");
  }

  /**
   * 将包机人分组展示，1-两个电工，2-两个钳工，3-两个运转工
   *
   * @param charterPersonList
   * @param entity
   * @return
   */
  private Map<String, List<MesSwEquipmentInspectionSignature>> getProcessHandler(
      List<MesSwEquipmentInspectionPersonDTO> charterPersonList, MesSwEquipmentInspection entity) {

    Map<String, List<MesSwEquipmentInspectionSignature>> resultMap = new HashMap<>();
    List<MesSwEquipmentInspectionSignature> resulList1 = new ArrayList<>();
    List<MesSwEquipmentInspectionSignature> resulList2 = new ArrayList<>();
    List<MesSwEquipmentInspectionSignature> resulList3 = new ArrayList<>();
    MesSwEquipmentInspectionSignature signatureItem;
    for (MesSwEquipmentInspectionPersonDTO item : charterPersonList) {
      signatureItem = new MesSwEquipmentInspectionSignature();
      if (StringUtils.isBlank(item.getAccount())) {
        throw new BMSException(
            ResultStateEnum.FAIL.getCode(), entity.getEquipmentNo() + "，查询不到对应的包机人，请检查！");
      }
      signatureItem.setAccount(item.getAccount());
      signatureItem.setName(item.getName());
      signatureItem.setEquipmentNo(entity.getEquipmentNo());
      signatureItem.setNodeName(item.getType());
      signatureItem.setNodeCode(item.getTypeCode());
      // 1、电工
      if (DicConstant.DIC_ITEM_POST_FIVE.equals(item.getTypeCode())
          || (DicConstant.DIC_ITEM_POST_SIX.equals(item.getTypeCode()))) {
        // 获取电工的人员、获取电工主任的人员;
        resulList1.add(signatureItem);
        resultMap.put(DicConstant.PARAMETER_INSPECTION_PROCESS_ONE, resulList1);
      }
      // 2、钳工
      else if (DicConstant.DIC_ITEM_POST_THREE.equals(item.getTypeCode())
          || (DicConstant.DIC_ITEM_POST_FOUR.equals(item.getTypeCode()))) {
        // 获取钳工的人员、获取钳工主任的人员;
        resulList2.add(signatureItem);
        resultMap.put(DicConstant.PARAMETER_INSPECTION_PROCESS_TWO, resulList2);
      }
      // 3、运转工
      else if (DicConstant.DIC_ITEM_POST_ONE.equals(item.getTypeCode())
          || (DicConstant.DIC_ITEM_POST_TWO.equals(item.getTypeCode()))) {
        // 获取运转工的人员、获取运转工主任的人员;
        resulList3.add(signatureItem);
        resultMap.put(DicConstant.PARAMETER_INSPECTION_PROCESS_THREE, resulList3);
      }
    }
    if (resultMap.isEmpty()) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), entity.getEquipmentNo() + "，查询不到对应的包机人，请检查！");
    }
    return resultMap;
  }

  /**
   * 发起流程审批
   *
   * @param variables
   * @param flowName
   * @param flowKey
   * @return
   */
  private String startProcess(Map<String, Object> variables, String flowName, String flowKey) {
    // 发起流程
    String proposer = "_admin";
    // 获取当前用户
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null) {
      proposer = authentication.getName();
    }

    StartProcessInstanceVo startProcessInstanceVo = new StartProcessInstanceVo();
    startProcessInstanceVo.setCreator(proposer);
    startProcessInstanceVo.setCurrentUserCode(proposer);
    startProcessInstanceVo.setFormName(flowName);
    startProcessInstanceVo.setSystemSn("flow");
    startProcessInstanceVo.setProcessDefinitionKey(flowKey);
    startProcessInstanceVo.setBusinessKey(flowKey);

    startProcessInstanceVo.setVariables(variables);
    RespJson<ProcessInstance> returnJson = new RespJson<>();
    try {
      returnJson = flowableProcessInstanceService.startProcessInstanceByKey(startProcessInstanceVo);
      return returnJson.getData().getProcessInstanceId();
      // return IdUtil.randomUUID();
    } catch (Exception e) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), e.getMessage());
    }
  }

  @Override
  public MesSwEquipmentInspectionDTO getCurrentDetail(MesSwEquipmentInspectionParam queryParam) {
    MesSwEquipmentInspection entity = getEntityByParam(queryParam);
    MesSwEquipmentInspectionDTO dto = convert.toDTO(entity);
    dto.setNoPower(
        generateEquipmentNoAndPower(entity.getEquipmentNo(), entity.getEquipmentPower()));
    // 当前登录人
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();

    // 该子单里面所有的查询审批人
    List<MesSwEquipmentInspectionSignature> signList =
        signatureService.queryListByParentId(entity.getId());
    if (CollUtil.isEmpty(signList)) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), entity.getEquipmentNo() + "审批签字表查询不到，请检查！");
    }
    boolean editFlag = false;
    List<MesSwEquipmentInspectionPersonDTO> inspectionList = new ArrayList<>();
    MesSwEquipmentInspectionPersonDTO personDTO = null;
    for (MesSwEquipmentInspectionSignature item : signList) {
      personDTO = new MesSwEquipmentInspectionPersonDTO();
      personDTO.setAccount(item.getAccount());
      personDTO.setName(item.getName());
      String nodeName = item.getNodeName();
      personDTO.setType(nodeName);
      personDTO.setNodeCode(item.getNodeCode());
      personDTO.setFileUrl(item.getFileUrl());
      personDTO.setProcessInstanceId(item.getProcessInstanceId());
      // 获取taskId
      Task task = flowableTaskService.getTask(account, item.getProcessInstanceId());
      if (task != null) {
        String taskName = task.getName();
        if (taskName.contains("主任") && nodeName.contains("主任")) {
          personDTO.setTaskId(task.getId());
        } else if (taskName.contains("工") && nodeName.contains("工") && !nodeName.contains("主任")) {
          personDTO.setTaskId(task.getId());
          editFlag = true;
        }
      }
      inspectionList.add(personDTO);
    }
    dto.setEditFlag(editFlag);
    // 巡检内容
    dto.setInspectionContentList(
        JSONUtil.toList(entity.getInspectionContent(), MesSwEquipmentInspectionContentDTO.class));
    // 包机人
    dto.setCharterPersonList(
        JSONUtil.toList(entity.getCharterPerson(), MesSwEquipmentInspectionPersonDTO.class));
    dto.setInspectionList(inspectionList);
    // 检修提报信息集合
    dto.setInspectionReportList(inspectionReportService.queryListByParentId(entity.getId()));
    return dto;
  }

  private MesSwEquipmentInspection getEntityByParam(MesSwEquipmentInspectionParam queryParam) {
    String id = queryParam.getId();
    String processInstanceId = queryParam.getProcessInstanceId();
    if (StringUtils.isBlank(id) && StringUtils.isBlank(processInstanceId)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    MesSwEquipmentInspection entity = null;
    if (StringUtils.isNotBlank(id)) {
      entity = this.getById(id);
    } else {
      LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
      wrapper.like(MesSwEquipmentInspection::getProcessInstanceId, processInstanceId);
      entity = this.baseMapper.selectOne(wrapper);
    }
    if (entity == null) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), id + "和参数" + processInstanceId + "查不到设备巡检记录，请检查！");
    }
    return entity;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean sign(MesSwEquipmentInspectionParam queryParam) {
    String account = queryParam.getAccount();
    String fileUrl = queryParam.getFileUrl();
    String type = queryParam.getType();
    LocalDate createdDate = queryParam.getCreatedDate();
    String taskId = queryParam.getTaskId();
    String processInstanceId = queryParam.getProcessInstanceId();
    List<MesSwEquipmentInspectionContentParam> inspectionContentList =
        queryParam.getInspectionContentList();
    if (StringUtils.isBlank(account)
        || StringUtils.isBlank(fileUrl)
        || StringUtils.isBlank(type)
        || StringUtils.isBlank(taskId)
        || StringUtils.isBlank(processInstanceId)
        || createdDate == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "必要参数不能为空，请检查！");
    }
    MesSwEquipmentInspection entity = getEntityByParam(queryParam);
    // 是否可修改
    boolean editFlag = false;
    // 获取taskId
    Task task = flowableTaskService.getTask(account, processInstanceId);
    if (task != null) {
      if (task.getName().contains("工")) {
        editFlag = true;
      }
    }
    if (editFlag && createdDate == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "日期不能为空，请检查！");
    }
    if (editFlag) {
      entity.setCreatedDate(createdDate);
      if (CollectionUtil.isEmpty(inspectionContentList)) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), "巡检内容项为空，请检查！");
      }
      Map<String, MesSwEquipmentInspectionContentParam> map =
          CollStreamUtil.toIdentityMap(
              inspectionContentList, MesSwEquipmentInspectionContentParam::getNum);
      List<MesSwEquipmentInspectionContentDTO> contentList =
          JSONUtil.toList(entity.getInspectionContent(), MesSwEquipmentInspectionContentDTO.class);
      contentList.stream()
          .forEach(
              e -> {
                String num = e.getNum();
                MesSwEquipmentInspectionContentParam content = map.get(num);
                if (content == null) {
                  throw new BMSException(ResultStateEnum.FAIL.getCode(), num + "项的巡检内容项找不到，请检查！");
                }
                e.setValue(content.getValue());
              });
      entity.setInspectionContent(JSONUtil.toJsonStr(contentList));
    }
    // 更新签字表----
    List<MesSwEquipmentInspectionSignature> signProcess =
        signatureService.queryListByProcessInstanceId(processInstanceId);
    int success = 0;
    MesNotice notice = null;
    for (int i = 0; i < signProcess.size(); i++) {
      MesSwEquipmentInspectionSignature item = signProcess.get(i);
      if (item.getNodeName().equals(type)) {
        success++;
        item.setFileUrl(fileUrl);
        item.setUpdatedTime(null);
        signatureService.updateById(item);
        int num = i + 1;
        if (num < signProcess.size()) {
          MesSwEquipmentInspectionSignature nextItem = signProcess.get(num);
          notice =
              sendMesNotice(
                  nextItem.getAccount(),
                  "/equipment/inspectionSignaDetail",
                  generateEquipmentNoAndPower(entity.getEquipmentNo(), entity.getEquipmentPower()),
                  processInstanceId);
        }
      }
    }
    if (success == 0) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), queryParam.toString() + "查询不到签字数据，请检查！");
    }
    // 查询还有没有未签字的数据
    LambdaQueryWrapper<MesSwEquipmentInspectionSignature> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentInspectionSignature::getParentId, entity.getId());
    wrapper.isNull(MesSwEquipmentInspectionSignature::getFileUrl);
    int count = signatureService.count(wrapper);
    if (count == 0) {
      // 未签字的数据为0，即设备子单的审批全部走完，更新状态
      String statusName =
          dicService.transform(
              DicConstant.DIC_INSPECTION_STATUS, DicConstant.DIC_ITEM_INSPECTION_PROCESS_FINISH);
      entity.setStatusCode(DicConstant.DIC_ITEM_INSPECTION_PROCESS_FINISH);
      entity.setStatusName(statusName);
    }
    entity.setUpdatedTime(null);
    // 添加检修提报数据
    if (!CollectionUtils.isEmpty(queryParam.getInspectionReportList())) {
      queryParam.getInspectionReportList().forEach(e -> e.setParentId(entity.getId()));
      inspectionReportService.add(queryParam.getInspectionReportList());
    }
    this.baseMapper.updateById(entity);
    // 走完流程
    FlowTaskParam taskParam = new FlowTaskParam();
    taskParam.setTaskId(taskId);
    taskParam.setUserId(account);
    taskParam.setProcessInstanceId(processInstanceId);
    Map<String, Object> variables = new HashMap<>();
    // 同意
    variables.put("reject", true);
    taskParam.setValues(variables);
    flowableTaskService.complete(taskParam);

    // 查询总单下面的设备子单，是否全部审批完成
    LambdaQueryWrapper<MesSwEquipmentInspection> eWrapper = new LambdaQueryWrapper<>();
    eWrapper.eq(MesSwEquipmentInspection::getParentId, entity.getParentId());
    eWrapper.eq(
        MesSwEquipmentInspection::getStatusCode, DicConstant.DIC_ITEM_INSPECTION_PROCESS_START);
    count = this.count(eWrapper);
    if (count == 0) {
      // 审批中的数据为0，则发起承包人审批流程
      saveLeaderProcess(entity.getParentId());
    }
    // 给处理人发通知
    if (notice != null) {
      websocketService.sendMessage(notice);
    }

    return true;
  }

  @Override
  public MesSwEquipmentInspectionVO getCurrentDetailTotal(MesSwEquipmentInspectionParam param) {
    MesSwEquipmentInspection entity = getEntityByParam(param);
    MesSwEquipmentInspectionVO result = convert.toVO(convert.toDTO(entity));
    // 当前登录人
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    // 获取taskId
    if (StringUtils.isNotBlank(entity.getProcessInstanceId())) {
      Task task = flowableTaskService.getTask(account, entity.getProcessInstanceId());
      if (task != null) {
        result.setTaskId(task.getId());
      }
    }
    // 查询子集
    LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
    wrapper.select(
        MesSwEquipmentInspection::getId,
        MesSwEquipmentInspection::getEquipmentNo,
        MesSwEquipmentInspection::getEquipmentPower,
        MesSwEquipmentInspection::getInspectionContent,
        MesSwEquipmentInspection::getCharterPerson);
    wrapper.eq(MesSwEquipmentInspection::getParentId, entity.getId());
    wrapper.orderByAsc(
        MesSwEquipmentInspection::getEquipmentNo, MesSwEquipmentInspection::getCreatedTime);
    List<MesSwEquipmentInspection> sonList = baseMapper.selectList(wrapper);

    /** 各个设备的内容集合 */
    HashMap<String, List<String>> equipmentMap = new HashMap<>();
    /** 各个设备的包机人集合 */
    HashMap<String, List<MesSwEquipmentInspectionPersonVO>> charterPersonMap = new HashMap<>();
    /** 各个设备的审批签字集合 */
    HashMap<String, List<MesSwEquipmentInspectionPersonVO>> inspectionMap = new HashMap<>();
    /** 承包人签名集合 */
    List<MesSwEquipmentInspectionPersonVO> contractorList = new ArrayList<>();

    int size = sonList.size();
    for (int i = 0; i < size; i++) {
      MesSwEquipmentInspection son = sonList.get(i);
      List<MesSwEquipmentInspectionContentVO> inspectionContentList =
          JSONUtil.toList(son.getInspectionContent(), MesSwEquipmentInspectionContentVO.class);
      if (i == 0) {
        // 巡检内容,只取一次
        result.setInspectionContentList(inspectionContentList);
      }
      String no = son.getEquipmentNo();
      String power = son.getEquipmentPower();
      String key = generateEquipmentNoAndPower(no, power);
      // 设备的内容集合
      List<String> valueList =
          CollStreamUtil.toList(inspectionContentList, MesSwEquipmentInspectionContentVO::getValue);
      equipmentMap.put(key, valueList);
      // 设备的包机人集合
      List<MesSwEquipmentInspectionPersonVO> charterPersonList =
          JSONUtil.toList(son.getCharterPerson(), MesSwEquipmentInspectionPersonVO.class);
      charterPersonMap.put(key, charterPersonList);
      // 设备的巡检审批人集合
      List<MesSwEquipmentInspectionPersonVO> inspectionList = new ArrayList<>();
      List<MesSwEquipmentInspectionSignature> signList =
          signatureService.queryListByParentId(son.getId());
      for (MesSwEquipmentInspectionSignature sign : signList) {
        MesSwEquipmentInspectionPersonVO person = convert.toPersonVO(sign);
        person.setType(sign.getNodeName());
        inspectionList.add(person);
      }
      inspectionMap.put(key, inspectionList);
    }
    // 获取承包人签名
    List<MesSwEquipmentInspectionSignature> totalList =
        signatureService.queryListByParentId(entity.getId());
    for (MesSwEquipmentInspectionSignature sign : totalList) {
      MesSwEquipmentInspectionPersonVO person = convert.toPersonVO(sign);
      person.setType(sign.getNodeName());
      contractorList.add(person);
    }

    result.setEquipmentMap(equipmentMap);
    result.setCharterPersonMap(charterPersonMap);
    result.setInspectionMap(inspectionMap);
    result.setContractorList(contractorList);

    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean leaderSign(MesSwEquipmentInspectionParam param) {
    MesSwEquipmentInspection entity = getEntityByParam(param);
    // 获取taskId
    String taskId = param.getTaskId();
    if (StringUtils.isBlank(taskId)
        || StringUtils.isBlank(param.getFileUrl())
        || StringUtils.isBlank(param.getAccount())) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "必要参数不能为空，请检查！");
    }

    String processInstanceId = entity.getProcessInstanceId();
    // 走完流程
    FlowTaskParam taskParam = new FlowTaskParam();
    taskParam.setTaskId(taskId);
    taskParam.setUserId(param.getAccount());
    taskParam.setProcessInstanceId(processInstanceId);
    flowableTaskService.complete(taskParam);

    List<MesSwEquipmentInspectionSignature> signList =
        signatureService.queryListByProcessInstanceId(processInstanceId);
    for (MesSwEquipmentInspectionSignature sign : signList) {
      if (param.getAccount().equals(sign.getAccount())) {
        sign.setFileUrl(param.getFileUrl());
        sign.setUpdatedTime(null);
        signatureService.updateById(sign);
      }
    }

    return false;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void saveLeaderProcess(String id) {
    if (StringUtils.isBlank(id)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "承包人流程必要参数不能为空，请检查！");
    }
    MesSwEquipmentInspection entity = this.getById(id);
    // 查询子集
    LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
    wrapper.select(MesSwEquipmentInspection::getCharterPerson);
    wrapper.eq(MesSwEquipmentInspection::getParentId, entity.getId());
    List<MesSwEquipmentInspection> sonList = baseMapper.selectList(wrapper);
    // 匹配出领导人
    Map<String, String> userMap = new HashMap<>();
    for (MesSwEquipmentInspection item : sonList) {
      List<MesSwEquipmentInspectionPersonDTO> charterPersonList =
          JSONUtil.toList(item.getCharterPerson(), MesSwEquipmentInspectionPersonDTO.class);
      List<MesSwEquipmentInspectionPersonDTO> list =
          charterPersonList.stream()
              .filter(person -> Objects.nonNull(person))
              .filter(person -> DicConstant.DIC_ITEM_POST_SEVEN.equals(person.getTypeCode()))
              .collect(Collectors.toList());
      for (MesSwEquipmentInspectionPersonDTO person : list) {
        userMap.put(person.getAccount(), person.getName());
      }
    }
    if (userMap.isEmpty()) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(),
          entity.getEquipmentName() + entity.getClassificationShop() + "领导人不能为空，请检查！");
    }
    List<String> managerUserList = new ArrayList<>();
    // 组装领导人账号
    userMap.forEach(
        (key, item) -> {
          managerUserList.add(key);
        });
    Map<String, Object> variables = new HashMap<>();
    variables.put(FlowConstant.FLOW_EQUIPMENT_INSPECTION_LEADER_NODE, managerUserList);
    String processInstanceId =
        startProcess(
            variables,
            FlowConstant.FLOW_EQUIPMENT_INSPECTION_LEADER_NAME,
            FlowConstant.FLOW_EQUIPMENT_INSPECTION_LEADER_KEY);
    List<MesSwEquipmentInspectionSignature> resulList = new ArrayList<>();
    userMap.forEach(
        (key, item) -> {
          MesSwEquipmentInspectionSignature signatureItem = new MesSwEquipmentInspectionSignature();
          signatureItem.setId(IdUtil.randomUUID());
          signatureItem.setParentId(entity.getId());
          signatureItem.setAccount(key);
          signatureItem.setName(item);
          signatureItem.setEquipmentNo(entity.getEquipmentNo());
          signatureItem.setProcessInstanceId(processInstanceId);
          resulList.add(signatureItem);
        });
    signatureService.saveBatch(resulList);
    entity.setProcessInstanceId(processInstanceId);
    entity.setStatusCode(DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_FIVE);
    entity.setStatusName(
        dicService.transform(
            DicConstant.DIC_TOTAL_INSPECTION_STATUS,
            DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_FIVE));
    entity.setUpdatedTime(null);
    this.saveOrUpdate(entity);
    // 给处理人发通知
    for (String user : managerUserList) {
      MesNotice notice =
          sendMesNotice(
              user,
              "/equipment/inspectionSigna",
              StrUtil.format(
                  "{}{}{}",
                  entity.getEquipmentName(),
                  entity.getClassificationShop(),
                  entity.getThisPeriod()),
              processInstanceId);
      websocketService.sendMessage(notice);
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean remove(MesSwEquipmentInspectionParam param) {
    String id = param.getId();
    if (StringUtils.isBlank(id)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数不能为空，请检查！");
    }
    MesSwEquipmentInspection entity = this.getById(id);
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), id + "找不到该数据，请检查！");
    }
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    boolean deleteFlag = isDelete(account);
    if (deleteFlag || account.equals(entity.getCreatedBy())) {
      // 查询子单的数据
      LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
      wrapper.select(MesSwEquipmentInspection::getProcessInstanceId);
      wrapper.eq(MesSwEquipmentInspection::getParentId, id);
      List<Object> sonList = this.listObjs(wrapper);
      String processInstanceIdS = CollUtil.join(sonList, ",");
      List<String> processInstanceIdList =
          StrSplitter.split(processInstanceIdS, ',', 0, true, true);

      String processInstanceId = entity.getProcessInstanceId();
      if (StringUtils.isNotBlank(processInstanceId)) {
        processInstanceIdList.add(processInstanceId);
      }
      if (CollectionUtil.isNotEmpty(processInstanceIdList)) {
        // 删除流程
        try {
          flowableProcessInstanceService.deleteProcessInstanceByIdS(processInstanceIdList);
        } catch (Exception e) {
          // 查不到数据导致的异常，不用管
        }
        // 删除消息
        LambdaQueryWrapper<MesNotice> noticeWrapper = new LambdaQueryWrapper<>();
        noticeWrapper.in(MesNotice::getTargetId, processInstanceIdList);
        mesNoticeService.remove(noticeWrapper);
        // 删除各个单子的签名数据
        LambdaQueryWrapper<MesSwEquipmentInspectionSignature> signWrapper =
            new LambdaQueryWrapper<>();
        signWrapper.in(
            MesSwEquipmentInspectionSignature::getProcessInstanceId, processInstanceIdList);
        signatureService.remove(signWrapper);
      }
      // 删除子单的表数据
      LambdaQueryWrapper<MesSwEquipmentInspection> inspectionWrapper = new LambdaQueryWrapper<>();
      inspectionWrapper.eq(MesSwEquipmentInspection::getParentId, id);
      this.remove(inspectionWrapper);
      // 删除总单
      return this.removeById(id);
    } else {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "您不能删除该数据，请检查！");
    }
  }

  @Override
  public String exportExcel(MesSwEquipmentInspectionParam param) {
    String id = param.getId();
    if (StringUtils.isBlank(id)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数不能为空，请检查！");
    }
    MesSwEquipmentInspection entity = this.getById(id);
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), id + "找不到该设备巡检数据，请检查！");
    }
    // 查询子集
    LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
    wrapper.select(
        MesSwEquipmentInspection::getId,
        MesSwEquipmentInspection::getCreatedDate,
        MesSwEquipmentInspection::getEquipmentNo,
        MesSwEquipmentInspection::getEquipmentPower,
        MesSwEquipmentInspection::getInspectionContent,
        MesSwEquipmentInspection::getCharterPerson);
    wrapper.eq(MesSwEquipmentInspection::getParentId, entity.getId());
    wrapper.orderByAsc(
        MesSwEquipmentInspection::getEquipmentNo, MesSwEquipmentInspection::getCreatedTime);
    List<MesSwEquipmentInspection> sonList = baseMapper.selectList(wrapper);

    // 设备的日期集合
    List<LocalDate> createdDateList =
        sonList.stream().map(MesSwEquipmentInspection::getCreatedDate).collect(Collectors.toList());
    // 设备的编号集合
    List<String> noList = CollStreamUtil.toList(sonList, MesSwEquipmentInspection::getEquipmentNo);
    // 设备的功率集合
    List<String> powerList =
        CollStreamUtil.toList(sonList, MesSwEquipmentInspection::getEquipmentPower);

    // 表格内容
    List<MesSwEquipmentInspectionContentVO> inspectionContentList = null;
    // 各个设备的内容数值集合
    HashMap<String, List<String>> equipmentValueMap = new HashMap<>();
    // 各个设备的包机人集合
    HashMap<String, List<MesSwEquipmentInspectionPersonVO>> charterPersonMap = new HashMap<>();
    // 各个设备的审批签字集合
    HashMap<String, List<MesSwEquipmentInspectionSignature>> inspectionMap = new HashMap<>();
    int sizeList = sonList.size();
    for (int i = 0; i < sizeList; i++) {
      MesSwEquipmentInspection son = sonList.get(i);
      String key = son.getEquipmentNo();
      // 巡检内容 设备的内容集合
      inspectionContentList =
          JSONUtil.toList(son.getInspectionContent(), MesSwEquipmentInspectionContentVO.class);
      List<String> valueList =
          CollStreamUtil.toList(inspectionContentList, MesSwEquipmentInspectionContentVO::getValue);
      equipmentValueMap.put(key, valueList);
      // 设备的包机人集合
      charterPersonMap.put(
          key, JSONUtil.toList(son.getCharterPerson(), MesSwEquipmentInspectionPersonVO.class));
      // 设备的巡检审批人集合
      List<MesSwEquipmentInspectionSignature> signList =
          signatureService.queryListByParentId(son.getId());
      inspectionMap.put(key, signList);
    }

    // 获取承包人签名
    List<MesSwEquipmentInspectionSignature> totalList =
        signatureService.queryListByParentId(entity.getId());
    List<String> signList =
        totalList.stream()
            .filter(item -> StringUtils.isNotBlank(item.getFileUrl()))
            .map(MesSwEquipmentInspectionSignature::getName)
            .collect(Collectors.toList());

    String title =
        StrUtil.format(
            "上湾选煤厂{}{}{}",
            entity.getEquipmentName(),
            entity.getClassificationShop(),
            entity.getThisPeriod());
    String fileName = StrUtil.format("{}{}", title, ".xls");
    HSSFWorkbook wb = null;
    ByteArrayInputStream inputStream;
    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      Map<String, String> headers = new HashMap<>();
      headers.put("Content-Type", "application/vnd.ms-excel");
      wb = new HSSFWorkbook();

      HSSFCellStyle titleStyle = ExcelBuilder.createTitleCellStyle(wb);
      HSSFCellStyle contentStyle = ExcelBuilder.createContentCellStyle(wb);
      HSSFSheet wbSheet =
          wb.createSheet(
              StrUtil.format(
                  "{}-{}-{}",
                  entity.getEquipmentName(),
                  entity.getClassificationName(),
                  entity.getWorkShopName()));
      AtomicInteger rowNumSheet = new AtomicInteger();
      // 创建第一行
      HSSFRow row0 = wbSheet.createRow(rowNumSheet.getAndIncrement());
      row0.setHeight((short) 500);
      // 列数=固定内容6列+设备数量
      int six = 6;
      int size = six + sonList.size();
      for (int i = 0; i < size; i++) {
        HSSFCell tempCell = row0.createCell(i);
        tempCell.setCellValue(title);
        tempCell.setCellStyle(titleStyle);
        if (i >= 6) {
          // 开始设备列宽
          wbSheet.setColumnWidth(i, wbSheet.getColumnWidth(i) * 5 / 2);
        }
      }
      // 合并-标题单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
      addMergeCell(wbSheet, row0.getRowNum(), row0.getRowNum(), 0, size - 1);
      // 第二行，列名称
      HSSFRow row1 = wbSheet.createRow(rowNumSheet.getAndIncrement());
      row1.setHeight((short) 500);
      for (int i = 0; i < size; i++) {
        if (i == 0) {
          CellBuilder.build(
              row1, 0, StrUtil.format("巡检周期：{}", entity.getThisPeriod()), contentStyle);
        } else if (i == 4) {
          // 合并-第二行-巡检周期
          addMergeCell(wbSheet, row1.getRowNum(), row1.getRowNum(), 0, 3);
          CellBuilder.build(row1, 4, "日期", contentStyle);
        } else if (i >= six) {
          // 合并-第二行-日期
          if (i == six) {
            addMergeCell(wbSheet, row1.getRowNum(), row1.getRowNum(), 4, 5);
          }
          LocalDate date = createdDateList.get(i - six);
          CellBuilder.build(row1, i, LocalDateTimeUtil.format(date, "yyyy年MM月dd日"), contentStyle);
        } else {
          CellBuilder.build(row1, i, "", contentStyle);
        }
      }
      // 第三行，列名称
      HSSFRow row2 = wbSheet.createRow(rowNumSheet.getAndIncrement());
      row2.setHeight((short) 500);
      List<String> list =
          Stream.of("序号", "项目", "", "巡检内容", "标准", "巡检周期").collect(Collectors.toList());
      for (int i = 0; i < size; i++) {
        if (i >= six) {
          CellBuilder.build(row2, i, "设备编号及对应功率（kW）", contentStyle);
        } else {
          CellBuilder.build(row2, i, list.get(i), contentStyle);
        }
      }
      // 合并-功率文字
      addMergeCell(wbSheet, row2.getRowNum(), row2.getRowNum(), six, size - 1);

      // 第四行，列名称
      HSSFRow row3 = wbSheet.createRow(rowNumSheet.getAndIncrement());
      row3.setHeight((short) 500);
      for (int i = 0; i < size; i++) {
        if (i >= six) {
          CellBuilder.build(row3, i, noList.get(i - six), contentStyle);
        } else {
          CellBuilder.build(row3, i, list.get(i), contentStyle);
        }
      }
      // 第五行，列名称
      HSSFRow row4 = wbSheet.createRow(rowNumSheet.getAndIncrement());
      row4.setHeight((short) 500);
      for (int i = 0; i < size; i++) {
        if (i >= six) {
          CellBuilder.build(row4, i, powerList.get(i - six), contentStyle);
        } else {
          CellBuilder.build(row4, i, list.get(i), contentStyle);
        }
      }
      // 合并-序号
      addMergeCell(wbSheet, row2.getRowNum(), row4.getRowNum(), 0, 0);
      // 合并-项目
      addMergeCell(wbSheet, row2.getRowNum(), row4.getRowNum(), 1, 2);
      // 合并-巡检内容
      addMergeCell(wbSheet, row2.getRowNum(), row4.getRowNum(), 3, 3);
      // 合并-标准
      addMergeCell(wbSheet, row2.getRowNum(), row4.getRowNum(), 4, 4);
      // 合并-巡检周期
      addMergeCell(wbSheet, row2.getRowNum(), row4.getRowNum(), 5, 5);

      // 第六行，表格内容
      if (CollUtil.isEmpty(inspectionContentList)) {
        inspectionContentList = new ArrayList<>();
      }
      // 项目合并
      int projectIndex = 0;
      String projectOld = "";
      // 项目-类型合并
      int typeIndex = 0;
      String typeOld = "";
      // 序号
      int xuhao = 0;
      int contentSize = inspectionContentList.size();
      for (int num = 0; num < contentSize; num++) {
        // 行数
        HSSFRow rowSheet = wbSheet.createRow(rowNumSheet.getAndIncrement());
        MesSwEquipmentInspectionContentVO item = inspectionContentList.get(num);
        for (int i = 0; i < size; i++) {
          // 列数
          if (i >= six) {
            CellBuilder.build(
                rowSheet,
                i,
                StrUtil.format(
                    "{}{}", equipmentValueMap.get(noList.get(i - six)).get(num), item.getUnit()),
                contentStyle);
          } else if (i == 0) {
            CellBuilder.build(rowSheet, 0, item.getNum(), contentStyle);
          } else if (i == 1) {
            String projectName = item.getProject();
            if (StringUtils.isNotBlank(projectOld) && !projectOld.equals(projectName)) {
              // 合并-上一行的数据
              if (StringUtils.isBlank(typeOld)) {
                // 类型为空，则两列需要合并
                addMergeCell(
                    wbSheet, rowSheet.getRowNum() - projectIndex, rowSheet.getRowNum() - 1, 1, 2);
              } else {
                // 类型不为空，则只需要合并一列
                addMergeCell(
                    wbSheet, rowSheet.getRowNum() - projectIndex, rowSheet.getRowNum() - 1, 1, 1);
              }
              projectIndex = 0;
            }
            projectIndex++;
            projectOld = projectName;
            CellBuilder.build(rowSheet, 1, projectName, contentStyle);
          } else if (i == 2) {
            String type = item.getType();
            if (StringUtils.isNotBlank(typeOld) && !typeOld.equals(type)) {
              // 合并-上一行的数据
              addMergeCell(
                  wbSheet, rowSheet.getRowNum() - typeIndex, rowSheet.getRowNum() - 1, 2, 2);
              typeIndex = 0;
            }
            if (StringUtils.isNotBlank(type)) {
              typeIndex++;
            }
            typeOld = type;
            CellBuilder.build(rowSheet, 2, type, contentStyle);
          } else if (i == 3) {
            CellBuilder.build(rowSheet, 3, item.getContent(), contentStyle);
          } else if (i == 4) {
            CellBuilder.build(rowSheet, 4, item.getCriterion(), contentStyle);
          } else if (i == 5) {
            CellBuilder.build(rowSheet, 5, item.getPeriod(), contentStyle);
          }
        }
        if (num == contentSize - 1) {
          // 最后一行
          if (StringUtils.isBlank(typeOld)) {
            addMergeCell(
                wbSheet, rowSheet.getRowNum() - projectIndex + 1, rowSheet.getRowNum(), 1, 2);
          } else {
            addMergeCell(
                wbSheet, rowSheet.getRowNum() - projectIndex + 1, rowSheet.getRowNum(), 1, 1);
          }
          xuhao = Integer.valueOf(item.getNum());
        }
      }
      // 包机人部分
      // 默认取第一个设备的包机人内容，进行遍历
      List<MesSwEquipmentInspectionPersonVO> charterPersonList =
          charterPersonMap.get(noList.get(0));
      int charterSize = charterPersonList.size();
      for (int num = 0; num < charterSize; num++) {
        // 行数
        HSSFRow rowSheet = wbSheet.createRow(rowNumSheet.getAndIncrement());
        MesSwEquipmentInspectionPersonVO item = charterPersonList.get(num);
        for (int i = 0; i < size; i++) {
          if (i == 0) {
            // 0-序号
            CellBuilder.build(rowSheet, 0, ++xuhao, contentStyle);
          } else if (i >= 1 && i <= 3) {
            // 1到3列赋值——>包机人
            CellBuilder.build(rowSheet, i, "包机人", contentStyle);
          } else if (i >= 4 && i <= 5) {
            CellBuilder.build(rowSheet, i, item.getType(), contentStyle);
            // 4到5列合并
            if (i == 5) {
              addMergeCell(wbSheet, rowSheet.getRowNum(), rowSheet.getRowNum(), 4, 5);
            }
          } else {
            CellBuilder.build(
                rowSheet,
                i,
                charterPersonMap.get(noList.get(i - six)).get(num).getName(),
                contentStyle);
          }
        }
        if (num == charterSize - 1) {
          // 最后一行，合并包机人
          addMergeCell(wbSheet, rowSheet.getRowNum() - num, rowSheet.getRowNum(), 1, 3);
        }
      }

      // 备注一行
      HSSFRow remarkRowSheet = wbSheet.createRow(rowNumSheet.getAndIncrement());
      String remark =
          "备注：（1）正常填0；异常填1并将问题填入“点巡检问题闭环管理共享文档”；无此项内容填\"无或/\"；（2）存在双驱（或多个同种部件的）的以点在用的为准，发现异常的在共享文档中描述清楚即可。";
      for (int i = 0; i < size; i++) {
        CellBuilder.build(remarkRowSheet, i, remark, contentStyle);
      }
      // 合并备注一行
      addMergeCell(wbSheet, remarkRowSheet.getRowNum(), remarkRowSheet.getRowNum(), 0, size - 1);
      remarkRowSheet.setHeightInPoints(50);

      // 签字一栏
      // 默认取第一个设备的签字内容，进行遍历
      List<MesSwEquipmentInspectionSignature> inspectionList = inspectionMap.get(noList.get(0));
      int inspectionSize = inspectionList.size();
      String signName = StrUtil.format("{}{}", "厂领导承包责任人：", CollUtil.join(signList, "、"));
      for (int num = 0; num < inspectionSize; num++) {
        // 行数
        HSSFRow rowSheet = wbSheet.createRow(rowNumSheet.getAndIncrement());
        MesSwEquipmentInspectionSignature item = inspectionList.get(num);
        for (int i = 0; i < size; i++) {
          if (i >= 0 && i <= 2) {
            // 0-2列，承包人签字栏
            CellBuilder.build(rowSheet, i, "承包人\n签字栏", contentStyle);
          } else if (i == 3) {
            // 领导签字人
            CellBuilder.build(rowSheet, i, signName, contentStyle);
          } else if (i >= 4 && i <= 5) {
            CellBuilder.build(rowSheet, i, item.getNodeName(), contentStyle);
            if (i == 5) {
              // 合并4-5列
              addMergeCell(wbSheet, rowSheet.getRowNum(), rowSheet.getRowNum(), 4, 5);
            }
          } else {
            MesSwEquipmentInspectionSignature sign =
                inspectionMap.get(noList.get(i - six)).get(num);
            String fileUrl = sign.getFileUrl();
            if (StringUtils.isBlank(fileUrl)) {
              // 未签名
              CellBuilder.build(rowSheet, i, "", contentStyle);
            } else {
              CellBuilder.build(rowSheet, i, sign.getName(), contentStyle);
            }
          }
        }
        if (num == inspectionSize - 1) {
          // 最后一行，合并承包人签字栏
          addMergeCell(wbSheet, rowSheet.getRowNum() - num, rowSheet.getRowNum(), 0, 2);
          // 最后一行，合并厂领导承包责任人
          addMergeCell(wbSheet, rowSheet.getRowNum() - num, rowSheet.getRowNum(), 3, 3);
        }
      }
      wbSheet.autoSizeColumn(0, true);
      wbSheet.autoSizeColumn(3, true);
      wbSheet.autoSizeColumn(4, true);

      wb.write(outputStream);
      inputStream = new ByteArrayInputStream(outputStream.toByteArray());
      storageServices.uploadObject(defaultBucketName, fileName, headers, inputStream);
      // 关闭文件流
      IOUtils.closeQuietly(inputStream);
    } catch (Exception e) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), e.getMessage());
    } finally {
      if (wb != null) {
        IOUtils.closeQuietly(wb);
      }
    }
    return storageServices.getStorageUrl(fileName, defaultBucketName);
  }

  @Override
  public List<String> exportExcelBatch(MesSwEquipmentInspectionParam param) {
    List<String> result = new ArrayList<>();
    List<String> idList = param.getIdList();
    if (CollUtil.isEmpty(idList)) {
      return result;
    }
    MesSwEquipmentInspectionParam equiParam = new MesSwEquipmentInspectionParam();
    for (String id : idList) {
      equiParam.setId(id);
      String url = null;
      try {
        url = exportExcel(equiParam);
      } catch (Exception e) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), e.toString());
      }
      result.add(url);
    }
    return result;
  }

  @Override
  public List<DicItemDTO> querySelectValue(MesSwEquipmentInspectionParam queryParam) {
    String equipmentType = queryParam.getEquipmentType();
    if (StringUtils.isBlank(equipmentType)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "设备类型参数不能为空，请检查！");
    }
    List<DicItemDTO> result = new ArrayList<>();
    DicItemDTO resultItem = null;
    LambdaQueryWrapper<MesSwEquipmentWorkOrder> orderWrapper = new LambdaQueryWrapper<>();
    orderWrapper.eq(MesSwEquipmentWorkOrder::getEquipmentType, equipmentType);
    if (StringUtils.isBlank(queryParam.getWorkShopCode())) {
      // 参数车间为空时，意味着根据设备类型，查询对应的所属车间;
      orderWrapper.select(MesSwEquipmentWorkOrder::getWorkShopCode);
      orderWrapper.groupBy(MesSwEquipmentWorkOrder::getWorkShopCode);
      orderWrapper.orderByAsc(MesSwEquipmentWorkOrder::getWorkShopCode);
      List<MesSwEquipmentWorkOrder> list = workOrderService.list(orderWrapper);
      for (MesSwEquipmentWorkOrder order : list) {
        resultItem = new DicItemDTO();
        resultItem.setVal(order.getWorkShopCode());
        resultItem.setCode(dicService.transform(DicConstant.WORK_SHOP, order.getWorkShopCode()));
        result.add(resultItem);
      }
    } else {
      orderWrapper.select(MesSwEquipmentWorkOrder::getClassificationCode);
      orderWrapper.eq(MesSwEquipmentWorkOrder::getWorkShopCode, queryParam.getWorkShopCode());
      orderWrapper.groupBy(MesSwEquipmentWorkOrder::getClassificationCode);
      orderWrapper.orderByAsc(MesSwEquipmentWorkOrder::getClassificationCode);
      List<MesSwEquipmentWorkOrder> list = workOrderService.list(orderWrapper);
      for (MesSwEquipmentWorkOrder order : list) {
        resultItem = new DicItemDTO();
        resultItem.setVal(order.getClassificationCode());
        // 拼接下拉框
        List<String> classificationCodeList =
            StrSplitter.split(order.getClassificationCode(), ',', 0, true, true);
        StringBuffer classificationName = new StringBuffer();
        if (CollUtil.isNotEmpty(classificationCodeList)) {
          for (String item : classificationCodeList) {
            if (classificationName.length() > 0) {
              classificationName.append("、");
            }
            String name = dicService.transform(DicConstant.DEVICE_CLASSIFICATION, item);
            // 先去除类这个字，拼接完后再最后加一下，例如Ⅱ、Ⅲ类
            classificationName.append(name.replace("类", ""));
          }
        }
        classificationName.append("类");
        resultItem.setCode(classificationName.toString());
        result.add(resultItem);
      }
    }

    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean reject(MesSwEquipmentInspectionParam queryParam) {
    String id = queryParam.getId();
    String processInstanceId = queryParam.getProcessInstanceId();
    if (StringUtils.isBlank(id) && StringUtils.isBlank(processInstanceId)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    String rejectReason = queryParam.getRejectReason();
    if (StringUtils.isBlank(rejectReason)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "驳回理由为空，请检查！");
    }
    MesSwEquipmentInspection entity = null;
    if (id != null) {
      entity = this.getById(id);
    } else {
      LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(MesSwEquipmentInspection::getProcessInstanceId, processInstanceId);
      entity = this.baseMapper.selectOne(wrapper);
    }
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查不到设备巡检数据，请检查！");
    }
    // 当前登录人
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    // 获取taskId
    Task task = flowableTaskService.getTask(account, processInstanceId);
    if (task == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "该巡检任务不存在，请检查！");
    }
    // 走完流程
    FlowTaskParam taskParam = new FlowTaskParam();
    taskParam.setTaskId(task.getId());
    taskParam.setUserId(account);
    taskParam.setProcessInstanceId(processInstanceId);
    taskParam.setComment(rejectReason);
    taskParam.setCommentType(CommentTypeEnum.BH.toString());
    Map<String, Object> variables = new HashMap<>();
    // 驳回
    variables.put("reject", false);
    taskParam.setValues(variables);
    flowableTaskService.complete(taskParam);

    // 获取签字表
    List<MesSwEquipmentInspectionSignature> signProcess =
        signatureService.queryListByProcessInstanceId(processInstanceId);
    if (CollectionUtil.isEmpty(signProcess)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查不到设备巡检签字数据，请检查！");
    }
    for (MesSwEquipmentInspectionSignature sign : signProcess) {
      if (StringUtils.isNotBlank(sign.getFileUrl())) {
        sign.setFileUrl(null);
        sign.setUpdatedTime(null);
        signatureService.updateById(sign);
        MesNotice notice =
            sendMesNoticeReject(
                sign.getAccount(),
                "/equipment/inspectionSignaDetail",
                rejectReason,
                processInstanceId);
        websocketService.sendMessage(notice);
      }
    }
    return true;
  }

  @Override
  public Boolean getAutoCreate() {
    List<SysParameter> list = parameterService.getDetailLikeKey(DicConstant.PARAMETER_AUTO_CREATE);
    if (CollectionUtil.isEmpty(list)) {
      return false;
    }
    SysParameter item = list.get(0);
    if (Boolean.TRUE.toString().equals(item.getParamVal())) {
      return true;
    }
    return false;
  }

  @Override
  public RespJson<Boolean> saveAutoCreate(MesSwEquipmentInspectionParam queryParam) {
    if (queryParam.getAutoCreate() == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    ParameterParam param = new ParameterParam();
    param.setParamKey(DicConstant.PARAMETER_AUTO_CREATE);
    param.setParamVal(queryParam.getAutoCreate().toString());
    List<SysParameter> list = parameterService.getDetailLikeKey(DicConstant.PARAMETER_AUTO_CREATE);
    if (CollectionUtil.isEmpty(list)) {
      parameterService.add(param);
    } else {
      for (SysParameter item : list) {
        item.setParamVal(queryParam.getAutoCreate().toString());
      }
      parameterService.updateBatchById(list);
    }
    RespJson result = RespJson.buildSuccessResponse();
    if (queryParam.getAutoCreate()) {
      // true，则立马创建本周期内的工单
      result.setMessage(saveCreateInspection(new MesSwEquipmentInspectionParam()));
    }
    result.setData(true);
    return result;
  }

  @Override
  public String stopEquipmentInspection() {
    LambdaQueryWrapper<MesSwEquipmentInspection> wrapper = new LambdaQueryWrapper<>();
    // 查询总单
    wrapper.isNull(MesSwEquipmentInspection::getParentId);
    // // 小于等于当天
    wrapper.le(MesSwEquipmentInspection::getThisInspectionEndDate, LocalDate.now().toString());
    // 没补签的总单
    wrapper.eq(MesSwEquipmentInspection::getReplenishSign, "0");
    // 未完成的总单
    wrapper.ne(
        MesSwEquipmentInspection::getStatusCode, DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_END);
    List<MesSwEquipmentInspection> list = this.list(wrapper);
    if (CollectionUtil.isEmpty(list)) {
      return "查不到未完成的巡检总单";
    }
    StringBuffer result = new StringBuffer();
    for (MesSwEquipmentInspection item : list) {
      item.setStatusCode(DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_THREE);
      item.setStatusName(
          dicService.transform(
              DicConstant.DIC_TOTAL_INSPECTION_STATUS,
              DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_THREE));
      item.setUpdatedTime(null);
      if (result.length() > 0) {
        result.append("++++++");
      }
      result
          .append(item.getEquipmentName())
          .append(item.getClassificationShop())
          .append(item.getThisPeriod());
    }
    updateBatchById(list);
    return result.toString();
  }

  @Override
  public Boolean saveReplenishSign(MesSwEquipmentInspectionParam queryParam) {
    String id = queryParam.getId();
    if (StringUtils.isBlank(id)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    MesSwEquipmentInspection entity = this.getById(id);
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查不到设备巡检数据，请检查！");
    }
    // 1代表补签
    entity.setReplenishSign("1");
    entity.setStatusCode(DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_START);
    entity.setStatusName(
        dicService.transform(
            DicConstant.DIC_TOTAL_INSPECTION_STATUS,
            DicConstant.DIC_ITEM_TOTAL_INSPECTION_PROCESS_START));
    entity.setUpdatedTime(null);
    return updateById(entity);
  }

  @Override
  public String saveCreateInspection(MesSwEquipmentInspectionParam param) {
    ParameterParam pparam = new ParameterParam();
    pparam.setParamKey(DicConstant.PARAMETER_AUTO_CREATE);
    List<SysParameter> list = parameterService.getDetailLikeKey(DicConstant.PARAMETER_AUTO_CREATE);
    if (CollectionUtil.isEmpty(list)) {
      return "自动创建巡检工单关闭";
    }
    boolean close = true;
    for (SysParameter item : list) {
      if (Boolean.TRUE.toString().equals(item.getParamVal())) {
        close = Boolean.FALSE;
        break;
      }
    }
    if (close) {
      return "自动创建巡检工单关闭";
    }

    StringBuffer messageSuccess = new StringBuffer("以下的工单创建————》成功");
    LocalDate date = param.getStartDate();
    LocalDate today = LocalDate.now();
    if (date == null) {
      date = LocalDate.now();
    }
    int year = date.getYear();
    Month month = date.getMonth();
    // 先查询工单流程
    LambdaQueryWrapper<MesSwEquipmentWorkOrder> orderWrapper = new LambdaQueryWrapper<>();
    List<MesSwEquipmentWorkOrder> orderList = workOrderService.list(orderWrapper);
    if (CollUtil.isEmpty(orderList)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询不到设备巡检工单，请检查！");
    }

    LocalDate eightDay = LocalDate.of(year, month, 8);
    LocalDate fifteenDay = LocalDate.of(year, month, 15);
    LocalDate twentyTwoDay = LocalDate.of(year, month, 22);
    // 每周的本次巡检周期
    String thisInspectionPeriodWeek = "";
    if (date.isBefore(eightDay)) {
      thisInspectionPeriodWeek = DicConstant.DIC_WEEK_ONE;
    } else if (date.isBefore(fifteenDay)) {
      thisInspectionPeriodWeek = DicConstant.DIC_WEEK_TWO;
    } else if (date.isBefore(twentyTwoDay)) {
      thisInspectionPeriodWeek = DicConstant.DIC_WEEK_THREE;
    } else {
      thisInspectionPeriodWeek = DicConstant.DIC_WEEK_FOUR;
    }

    LocalDate sixteenDay = LocalDate.of(year, month, 16);
    YearMonth yearMonth = YearMonth.from(date);
    // 每半月的本次巡检周期
    String thisInspectionPeriodMonth = "";
    if (date.isBefore(sixteenDay)) {
      thisInspectionPeriodMonth = DicConstant.DIC_MONTH_FIVE;
    } else {
      thisInspectionPeriodMonth = DicConstant.DIC_MONTH_SIX;
    }
    int orderSize = orderList.size();
    MesSwEquipmentInspectionParam inspectionParam = null;
    List<String> classificationCodeList = null;
    for (int i = 0; i < orderSize; i++) {
      MesSwEquipmentWorkOrder item = orderList.get(i);

      inspectionParam = new MesSwEquipmentInspectionParam();
      inspectionParam.setCreatedDate(today);
      inspectionParam.setEquipmentType(item.getEquipmentType());
      inspectionParam.setWorkShopCode(item.getWorkShopCode());
      inspectionParam.setThisInspection(yearMonth.toString());

      classificationCodeList = new ArrayList<>();
      classificationCodeList.add(item.getClassificationCode());
      inspectionParam.setClassificationCodeList(classificationCodeList);

      // 设备分类3类的，需要每月巡检
      if ("3".equals(item.getClassificationCode())) {
        inspectionParam.setThisInspectionPeriod(thisInspectionPeriodMonth);
      } else {
        inspectionParam.setThisInspectionPeriod(thisInspectionPeriodWeek);
      }
      try {
        Boolean result = add(inspectionParam);
        if (result != null && result) {
          getMessage(inspectionParam, messageSuccess);
        }
      } catch (Exception e) {
        log.error(
            "定时创建工单出错:{}——————————————————>{}", JSONUtil.toJsonStr(inspectionParam), e.toString());
      }
    }
    return messageSuccess.toString();
  }

  @Override
  public IPage<MesSwEquipmentInspectionDTO> pagingChildrenQuery(
      MesSwEquipmentInspectionParam queryParam) {
    int pageNum = queryParam.getCurrent() == null ? 1 : queryParam.getCurrent().intValue();
    int pageSize = queryParam.getPageSize() == null ? -1 : queryParam.getPageSize().intValue();
    IPage<MesSwEquipmentInspection> iPage = new Page<>(pageNum, pageSize);
    LambdaQueryChainWrapper<MesSwEquipmentInspection> childWrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    childWrapper.select(
        MesSwEquipmentInspection::getId,
        MesSwEquipmentInspection::getThisPeriod,
        MesSwEquipmentInspection::getThisInspectionStartDate,
        MesSwEquipmentInspection::getCreatedDate);
    childWrapper.eq(MesSwEquipmentInspection::getEquipmentNo, queryParam.getNo());
    childWrapper.orderByDesc(
        MesSwEquipmentInspection::getCreatedDate,
        MesSwEquipmentInspection::getThisInspectionStartDate);
    return childWrapper.page(iPage).convert(convert::toDTO);
  }

  private void getMessage(MesSwEquipmentInspectionParam param, StringBuffer messageSucess) {
    if (messageSucess.length() > 0) {
      messageSucess.append("\n");
    }
    messageSucess
        .append(param.getEquipmentName())
        .append(param.getClassificationShop())
        .append(param.getThisPeriod());
  }

  /**
   * 设置合并单元格
   *
   * @param sheet sheet页对象
   * @param startRowIndex 开始行号
   * @param endRowIndex 结束行号
   * @param startColumnIndex 开始列号
   * @param endColumnIndex 结束列号
   */
  public void addMergeCell(
      HSSFSheet sheet,
      int startRowIndex,
      int endRowIndex,
      int startColumnIndex,
      int endColumnIndex) {
    // 合并单元格区域只有一个单元格时，不合并
    if (endRowIndex == startRowIndex && endColumnIndex == startColumnIndex) {
      return;
    }
    // 添加合并单元格区域
    CellRangeAddress cellRangeAddress =
        new CellRangeAddress(startRowIndex, endRowIndex, startColumnIndex, endColumnIndex);
    sheet.addMergedRegionUnsafe(cellRangeAddress);
  }

  private MesNotice sendMesNotice(
      String user, String jumpRoute, String content, String processInstanceId) {
    MesNotice mesNotice = new MesNotice();
    mesNotice.setType(NotifyEnum.NOTIFICATION.getKey());
    mesNotice.setReadState(false);
    mesNotice.setPushState("1");
    mesNotice.setTitle("设备巡检通知");
    mesNotice.setTargetType(DicConstant.DIC_NOTICE_TYPE_EQUIPMENT_INSPECTION);
    mesNotice.setTargetTypeName(
        dicService.transform(
            DicConstant.DIC_NOTICE_TYPE, DicConstant.DIC_NOTICE_TYPE_EQUIPMENT_INSPECTION));
    mesNotice.setContent(content + " 需要您巡检审批签字！");
    mesNotice.setJumpRoute(jumpRoute);
    mesNotice.setPriority("2");
    mesNotice.setIsDelete(false);
    mesNotice.setTargetId(processInstanceId);
    mesNotice.setJumpParam(processInstanceId);

    mesNotice.setId(null);
    mesNotice.setReceiverAccountName(accountService.getNameByUserName(user));
    mesNotice.setReceiverAccount(user);
    return mesNotice;
  }

  private MesNotice sendMesNoticeReject(
      String user, String jumpRoute, String content, String processInstanceId) {
    MesNotice mesNotice = new MesNotice();
    mesNotice.setType(NotifyEnum.NOTIFICATION.getKey());
    mesNotice.setReadState(false);
    mesNotice.setPushState("1");
    mesNotice.setTitle("设备巡检通知");
    mesNotice.setTargetType(DicConstant.DIC_NOTICE_TYPE_EQUIPMENT_INSPECTION);
    mesNotice.setTargetTypeName(
        dicService.transform(
            DicConstant.DIC_NOTICE_TYPE, DicConstant.DIC_NOTICE_TYPE_EQUIPMENT_INSPECTION));
    mesNotice.setContent(content + " 需重检！");
    mesNotice.setJumpRoute(jumpRoute);
    mesNotice.setPriority("2");
    mesNotice.setIsDelete(false);
    mesNotice.setTargetId(processInstanceId);
    mesNotice.setJumpParam(processInstanceId);

    mesNotice.setId(null);
    mesNotice.setReceiverAccountName(accountService.getNameByUserName(user));
    mesNotice.setReceiverAccount(user);
    return mesNotice;
  }
}
