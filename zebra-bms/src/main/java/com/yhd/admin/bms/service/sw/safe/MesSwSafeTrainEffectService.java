package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafetyTrainingPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainEffectDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainEffect;
import com.yhd.admin.bms.domain.query.sw.MesSwSafetyTrainingPlanParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainEffectParam;

public interface MesSwSafeTrainEffectService extends IService<MesSwSafeTrainEffect> {

  /**
   * 根据条件查询培训效果分页列表(已完成的培训)
   *
   * @param param 请求参数
   * @return 培训效果分页列表
   */
  IPage<MesSwSafetyTrainingPlanDTO> pagingQuery(MesSwSafetyTrainingPlanParam param);

  /**
   * 查询培训效果详情
   *
   * @param param 培训计划主键id
   * @return 培训效果详情
   */
  MesSwSafeTrainEffectDTO getTrainEffectDetail(MesSwSafeTrainEffectParam param);

  /**
   * 编辑培训效果
   *
   * @param param 培训效果表单
   * @return true成功
   */
  Boolean effectEdit(MesSwSafeTrainEffectParam param);
}
