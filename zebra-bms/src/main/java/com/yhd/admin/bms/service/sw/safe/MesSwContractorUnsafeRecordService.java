package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwContractorUnsafeRecordDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwContractorUnsafeRecord;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwContractorUnsafeRecordParam;

import java.util.List;

/**
 * 承包商不安全行为积分台账-业务层接口
 *
 * <AUTHOR>
 * @date 2023/10/26 15:37
 */
public interface MesSwContractorUnsafeRecordService extends IService<MesSwContractorUnsafeRecord> {
  /**
   * 根据条件查询分页列表
   *
   * @param param 查询参数
   * @return 承包商不安全行为积分台账分页列表信息
   */
  IPage<MesSwContractorUnsafeRecordDTO> pagingQuery(MesSwContractorUnsafeRecordParam param);

  /**
   * 根据条件查询列表
   *
   * @param param 查询条件
   * @return 承包商不安全行为积分台账列表信息
   */
  List<MesSwContractorUnsafeRecordDTO> queryList(MesSwContractorUnsafeRecordParam param);

  /**
   * 查询详情信息
   *
   * @param param 查询参数：主键id
   * @return 承包商不安全行为积分台账详情信息
   */
  MesSwContractorUnsafeRecordDTO getCurrentDetail(MesSwContractorUnsafeRecordParam param);

  /**
   * 根据承包商名称和年度查询详情信息
   *
   * @param param 查询参数：主键id
   * @return 承包商不安全行为积分台账详情信息
   */
  MesSwContractorUnsafeRecordDTO getDetailByNameAndYear(MesSwContractorUnsafeRecordParam param);

  /**
   * 新增
   *
   * @param param 承包商不安全行为积分台账表单参数
   * @return true成功，false失败
   */
  Boolean add(MesSwContractorUnsafeRecordParam param);

  /**
   * 编辑
   *
   * @param param 承包商不安全行为积分台账表单参数
   * @return true成功，false失败
   */
  Boolean modify(MesSwContractorUnsafeRecordParam param);

  /**
   * 批量删除
   *
   * @param param 主键id列表
   * @return true成功，false失败
   */
  Boolean removeBatch(MesSwContractorUnsafeRecordParam param);
}
