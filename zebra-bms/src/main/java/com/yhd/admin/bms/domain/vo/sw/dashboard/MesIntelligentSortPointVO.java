package com.yhd.admin.bms.domain.vo.sw.dashboard;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 上湾智能分选点位表实体类
 *
 * <AUTHOR>
 * @date 2025/5/31
 */
@Data
public class MesIntelligentSortPointVO implements Serializable {
  /** 点位地址映射表 */
  public static final String POINT_ADDRESS_CON302_INSTANT_COAL_AMOUNT =
      "XMSW_Lump_CON302_InstantCoalAmount";

  public static final String POINT_ADDRESS_CON302_RUN_STATE = "XMSW_Lump_CON302_RunState";
  public static final String POINT_ADDRESS_HM_VESSEL305_RUN_STATE =
      "XMSW_Lump_HMVessel305_RunState";
  public static final String POINT_ADDRESS_BUCKET310_DENSITY = "XMSW_Lump_Bucket310_Density";
  public static final String POINT_ADDRESS_SCREEN306_RUN_STATE = "XMSW_Lump_Screen306_RunState";
  public static final String POINT_ADDRESS_SCREEN307_RUN_STATE = "XMSW_Lump_Screen307_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP310_LEVEL = "XMSW_Lump_DosingPump310_Level";
  public static final String POINT_ADDRESS_DOSING_PUMP310_RUN_STATE =
      "XMSW_Lump_DosingPump310_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP316_LEVEL = "XMSW_Lump_DosingPump316_Level";
  public static final String POINT_ADDRESS_DOSING_PUMP313_LEVEL = "XMSW_Lump_DosingPump313_Level";
  public static final String POINT_ADDRESS_DOSING_PUMP313_RUN_STATE =
      "XMSW_Lump_DosingPump313_RunState";
  public static final String POINT_ADDRESS_CON701_INSTANT_COAL_AMOUNT =
      "XMSW_Lump_CON701_InstantCoalAmount";
  public static final String POINT_ADDRESS_CON701_RUN_STATE = "XMSW_Lump_CON701_RunState";
  public static final String POINT_ADDRESS_CON701_ASH = "XMSW_Lump_CON701_Ash";
  public static final String POINT_ADDRESS_CON301_INSTANT_COAL_AMOUNT =
      "XMSW_Lump_CON301_InstantCoalAmount";
  public static final String POINT_ADDRESS_CON301_RUN_STATE = "XMSW_Lump_CON301_RunState";
  public static final String POINT_ADDRESS_HM_VESSEL363_RUN_STATE =
      "XMSW_Lump_HMVessel363_RunState";
  public static final String POINT_ADDRESS_BUCKET374_DENSITY = "XMSW_Lump_Bucket374_Density";
  public static final String POINT_ADDRESS_SCREEN366_MOT_RUN_STATE =
      "XMSW_Lump_Screen366_MOT_RunState";
  public static final String POINT_ADDRESS_SCREEN367_MOT_RUN_STATE =
      "XMSW_Lump_Screen367_MOT_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP374_LEVEL = "XMSW_Lump_DosingPump374_Level";
  public static final String POINT_ADDRESS_DOSING_PUMP374_RUN_STATE =
      "XMSW_Lump_DosingPump374_RunState";
  public static final String POINT_ADDRESS_FEED_PUMP383_LEVEL = "XMSW_Lump_FeedPump383_Level";
  public static final String POINT_ADDRESS_DOSING_PUMP376_LEVEL = "XMSW_Lump_DosingPump376_Level";
  public static final String POINT_ADDRESS_DOSING_PUMP376_RUN_STATE =
      "XMSW_Lump_DosingPump376_RunState";
  // XMSW_Lump_Valve310_MValve
  public static final String POINT_ADDRESS_VALVE310_MVALVE = "XMSW_Lump_Valve310_MValve";
  // XMSW_Lump_Bucket310_Magnetic
  public static final String POINT_ADDRESS_BUCKET310_MAGNETIC = "XMSW_Lump_Bucket310_Magnetic";
  // XMSW_Lump_MSeparator314_Magnetic
  public static final String POINT_ADDRESS_MSeparator314_Magnetic =
      "XMSW_Lump_MSeparator314_Magnetic";
  // XMSW_Lump_MSeparator315_Magnetic
  public static final String POINT_ADDRESS_MSeparator315_Magnetic =
      "XMSW_Lump_MSeparator315_Magnetic";
  // XMSW_Lump_MSeparator377_Magnetic
  public static final String POINT_ADDRESS_MSeparator377_Magnetic =
      "XMSW_Lump_MSeparator377_Magnetic";
  // XMSW_Lump_MSeparator378_Magnetic
  public static final String POINT_ADDRESS_MSeparator378_Magnetic =
      "XMSW_Lump_MSeparator378_Magnetic";
  // XMSW_Lump_MSeparator314_RunState
  public static final String POINT_ADDRESS_MSeparator314_RunState =
      "XMSW_Lump_MSeparator314_RunState";
  // XMSW_Lump_MSeparator315_RunState
  public static final String POINT_ADDRESS_MSeparator315_RunState =
      "XMSW_Lump_MSeparator315_RunState";
  // XMSW_Lump_MSeparator377_MOT_RunState
  public static final String POINT_ADDRESS_MSeparator377_MOT_RunState =
      "XMSW_Lump_MSeparator377_MOT_RunState";
  // XMSW_Lump_MSeparator378_MOT_RunState
  public static final String POINT_ADDRESS_MSeparator378_MOT_RunState =
      "XMSW_Lump_MSeparator378_MOT_RunState";
  // XMSW_Lump_DosingPump321_RunState
  public static final String POINT_ADDRESS_DOSING_PUMP321_RUN_STATE =
      "XMSW_Lump_DosingPump321_RunState";
  // XMSW_Lump_DosingPump422_RunState
  public static final String POINT_ADDRESS_DOSING_PUMP422_RUN_STATE =
      "XMSW_Lump_DosingPump422_RunState";
  // XMSW_Lump_MSeparator320_RunState
  public static final String POINT_ADDRESS_MSeparator320_RunState =
      "XMSW_Lump_MSeparator320_RunState";
  // XMSW_Lump_MSeparator397_RunState
  public static final String POINT_ADDRESS_MSeparator397_RunState =
      "XMSW_Lump_MSeparator397_RunState";
  // XMSW_Lump_Valve374_MValve
  public static final String POINT_ADDRESS_VALVE374_MVALVE = "XMSW_Lump_Valve374_MValve";
  // XMSW_Lump_Bucket374_Magnetic
  public static final String POINT_ADDRESS_Bucket374_Magnetic = "XMSW_Lump_Bucket374_Magnetic";

  private static final long serialVersionUID = 1L;

  /**
   * 302胶带机瞬时量 数据类型: 数值 点位地址: XMSW_Lump_CON302_InstantCoalAmount 单位: t/h 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数
   */
  private BigDecimal con302InstantCoalAmount;

  /**
   * 302胶带机运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON302_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con302RunState;

  /**
   * 305浅槽运行状态 数据类型: 状态 点位地址: XMSW_Lump_HMVessel305_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer hmVessel305RunState;

  /** 305密度计 数据类型: 数值 点位地址: XMSW_Lump_Bucket310_Density 单位: Kg/L 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal bucket305Density;

  /**
   * 306振动筛运行状态 数据类型: 状态 点位地址: XMSW_Lump_Screen306_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer screen306RunState;

  /**
   * 307振动筛运行状态 数据类型: 状态 点位地址: XMSW_Lump_Screen307_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer screen307RunState;

  /** 310重介桶密度 数据类型: 数值 点位地址: XMSW_Lump_Bucket310_Density 单位: Kg/L 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal bucket310Density;

  /** 310重介桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump310_Level 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数 */
  private BigDecimal dosingPump310Level;

  /**
   * 310重介泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump310_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump310RunState;

  /** 316煤泥桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump316_Level 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数 */
  private BigDecimal dosingPump316Level;

  /** 313稀介桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump313_Level 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数 */
  private BigDecimal dosingPump313Level;

  /**
   * 313稀介泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump313_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump313RunState;

  /**
   * 701胶带机瞬时量 数据类型: 数值 点位地址: XMSW_Lump_CON701_InstantCoalAmount 单位: t/h 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数
   */
  private BigDecimal con701InstantCoalAmount;

  /**
   * 701运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON701_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con701RunState;

  /** 701灰分 数据类型: 数值 点位地址: XMSW_Lump_CON701_Ash 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位 */
  private BigDecimal con701Ash;

  /**
   * 301胶带机瞬时量 数据类型: 数值 点位地址: XMSW_Lump_CON301_InstantCoalAmount 单位: t/h 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数
   */
  private BigDecimal con301InstantCoalAmount;

  /**
   * 301胶带机运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON301_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con301RunState;

  /**
   * 363浅槽运行状态 数据类型: 状态 点位地址: XMSW_Lump_HMVessel363_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer hmVessel363RunState;

  /** 363密度计 数据类型: 数值 点位地址: XMSW_Lump_Bucket374_Density 单位: g/cm³ 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal bucket363Density;

  /**
   * 366振动筛运行状态 数据类型: 状态 点位地址: XMSW_Lump_Screen366_MOT_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer screen366MotRunState;

  /**
   * 367振动筛运行状态 数据类型: 状态 点位地址: XMSW_Lump_Screen367_MOT_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer screen367MotRunState;

  /** 374重介桶密度 数据类型: 数值 点位地址: XMSW_Lump_Bucket374_Density 单位: g/cm³ 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal bucket374Density;

  /** 374重介桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump374_Level 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数 */
  private BigDecimal dosingPump374Level;

  /**
   * 374重介泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump374_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump374RunState;

  /** 363煤泥桶液位 数据类型: 数值 点位地址: XMSW_Lump_FeedPump383_Level 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数 */
  private BigDecimal feedPump383Level;

  /** 376稀介桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump376_Level 单位: % 更新逻辑: 实时值 取值逻辑: 四舍五入保留1位小数 */
  private BigDecimal dosingPump376Level;

  /**
   * 376稀介泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump376_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump376RunState;

  /** 旧主洗分流箱开度（310合介桶补水阀开度） 数据类型: 数值 点位地址: XMSW_Lump_Valve310_MValve 更新逻辑: 实时值 取值逻辑: 四舍五入保留整数 */
  private BigDecimal valve310MValve;

  /** 旁边加个字段：310分流箱磁性物含量） 数据类型: 数值 点位地址: XMSW_Lump_Bucket310_Magnetic 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal bucket310Magnetic;

  /** 左边:磁选机314磁性物含量： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator314_Magnetic 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal mSeparator314Magnetic;

  /** 左边:磁选机315磁性物含量： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator315_Magnetic 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal mSeparator315Magnetic;

  /** 右边:磁选机377磁性物含量： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator377_Magnetic 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal mSeparator377Magnetic;

  /** 右边:磁选机378磁性物含量： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator378_Magnetic 更新逻辑: 实时值 取值逻辑: 四舍五入保留两位小数 */
  private BigDecimal mSeparator378Magnetic;

  /**
   * 左边:磁选机314运行状态： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator314_RunState 更新逻辑: 实时值 取值逻辑
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator314RunState;

  /**
   * 左边:磁选机315运行状态： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator315_RunState 更新逻辑: 实时值
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator315RunState;

  /**
   * 右边:磁选机377运行状态： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator377_MOT_RunState 更新逻辑: 实时值
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator377RunState;

  /**
   * 右边:磁选机378运行状态： 数据类型: 数值 点位地址: XMSW_Lump_MSeparator378_MOT_RunState 更新逻辑: 实时值
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator378RunState;

  /**
   * 左边：321介质添加泵运行状态： 数据类型: 数值 点位地址: XMSW_Lump_DosingPump321_RunState 更新逻辑: 实时值
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump321RunState;

  /**
   * 右边：422介质添加泵运行状态： 数据类型: 数值 点位地址: XMSW_Lump_DosingPump422_RunState 更新逻辑: 实时值
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump422RunState;

  /**
   * 左边：320加介磁选机运行状态 状态 点位地址: XMSW_Lump_MSeparator320_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator320RunState;

  /**
   * 右边：397加介磁选机运行状态 状态 点位地址: XMSW_Lump_MSeparator397_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator397RunState;

  /** 新主洗分流箱开度：399分流箱分流阀开度： 数据类型: 数值 点位地址: XMSW_Lump_Valve374_MValve 更新逻辑: 实时值 取值逻辑: ，四舍五入保留整数 */
  private BigDecimal mSeparator397MValve;

  /** 旁边加个字段：374分流箱磁性物含量： 数据类型: 数值 点位地址: XMSW_Lump_Bucket374_Magnetic 更新逻辑: 实时值 取值逻辑: ，四舍五入保留两位小数 */
  private BigDecimal bucket374Magnetic;
}
