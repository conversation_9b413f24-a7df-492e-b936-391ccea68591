package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.safe.MesSwWorkTicketElectricalWeldDao;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketElectricalWeldConvert;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmConvert;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketElectricalWeldSignUserConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketElectricalWeldDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketElectricalWeldSignUserDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketElectricalWeld;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketElectricalWeldJobConfirm;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.enums.safe.MesSwWorkTicketElectricalWeldConfirmEnum;
import com.yhd.admin.bms.domain.enums.safe.MesSwWorkTicketElectricalWeldEnum;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketElectWeldStatusEnum;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketElectricalWeldParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketElectricalWeldSignUserParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmService;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketElectricalWeldService;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketElectricalWeldSignUserService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
public class MesSwWorkTicketElectricalWeldServiceImpl
    extends ServiceImpl<MesSwWorkTicketElectricalWeldDao, MesSwWorkTicketElectricalWeld>
    implements MesSwWorkTicketElectricalWeldService {

  private static final String SP_BM = "审批部门/领导";
  private static final String AQ_QZ = "安全技术措施贯彻签字";

  private static final String HQ_JC = "一、焊前及焊接过程检查";
  private static final String HH_JC = "二、焊后检查";

  private static final String HH_JC_XC = "作业结束后，确认施焊地点用水喷洒，施焊人员切断电源和气源，无起火风险或其他异常后，汇报调度室。";
  private static final String HH_JC_ZB = "现场监护1小时，确认无起火危险或其他异常后，向调度室汇报，电气焊作业结束。";

  @Resource private MesSwWorkTicketElectricalWeldConvert workTicketElectricalWeldConvert;

  @Resource
  private MesSwWorkTicketElectricalWeldSignUserService workTicketElectricalWeldSignUserService;

  @Resource
  private MesSwWorkTicketElectricalWeldSignUserConvert workTicketElectricalWeldSignUserConvert;

  @Resource
  private MesSwWorkTicketElectricalWeldJobConfirmService workTicketElectricalWeldJobConfirmService;

  @Resource
  private MesSwWorkTicketElectricalWeldJobConfirmConvert workTicketElectricalWeldJobConfirmConvert;

  private static List<MesSwWorkTicketElectricalWeldJobConfirm> getJobConfirms(Long ticketId) {
    List<MesSwWorkTicketElectricalWeldJobConfirm> jobConfirms = Lists.newLinkedList();
    MesSwWorkTicketElectricalWeldJobConfirm weldJobConfirm;
    for (int i = 0; i < MesSwWorkTicketElectricalWeldConfirmEnum.getCodeList().size(); i++) {
      weldJobConfirm = new MesSwWorkTicketElectricalWeldJobConfirm();
      weldJobConfirm.setTicketId(ticketId);
      weldJobConfirm.setType(HQ_JC);
      if (i > 4) {
        weldJobConfirm.setType(HH_JC);
      }
      weldJobConfirm.setUserType(MesSwWorkTicketElectricalWeldConfirmEnum.getDescList().get(i));
      if (i == 5) {
        weldJobConfirm.setCheckA(HH_JC_XC);
      }
      if (i == 6) {
        weldJobConfirm.setCheckA(HH_JC_ZB);
      }
      jobConfirms.add(weldJobConfirm);
    }
    return jobConfirms;
  }

  private static List<MesSwWorkTicketElectricalWeldSignUserDTO> getSignUserDTOS() {
    List<MesSwWorkTicketElectricalWeldSignUserDTO> signUsers = Lists.newLinkedList();
    MesSwWorkTicketElectricalWeldSignUserDTO signUser;
    // 新建初始化表格数据
    for (int i = 0; i < MesSwWorkTicketElectricalWeldEnum.getCodeList().size(); i++) {
      signUser = new MesSwWorkTicketElectricalWeldSignUserDTO();
      signUser.setType(SP_BM);
      if (i > 5) {
        signUser.setType(AQ_QZ);
      }
      signUser.setUserType(MesSwWorkTicketElectricalWeldEnum.getDescList().get(i));
      signUsers.add(signUser);
    }
    return signUsers;
  }

  @Override
  public IPage<MesSwWorkTicketElectricalWeldDTO> pagingQuery(
      MesSwWorkTicketElectricalWeldParam param) {
    Page<MesSwWorkTicketElectricalWeld> iPage = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MesSwWorkTicketElectricalWeld> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 参数处理
    queryChain
        .eq(
            StringUtils.isNotBlank(param.getStatusName()),
            MesSwWorkTicketElectricalWeld::getStatusName,
            param.getStatusName())
        .eq(
            StringUtils.isNotBlank(param.getStatusCode()),
            MesSwWorkTicketElectricalWeld::getStatusCode,
            param.getStatusCode())
        .eq(
            StringUtils.isNotBlank(param.getWorkContent()),
            MesSwWorkTicketElectricalWeld::getWorkContent,
            param.getWorkContent())
        .eq(
            StringUtils.isNotBlank(param.getWorkLocation()),
            MesSwWorkTicketElectricalWeld::getWorkLocation,
            param.getWorkLocation());
    if (StringUtils.isNotBlank(param.getStartTime())
        && StringUtils.isNotBlank(param.getEndTime())) {
      queryChain.between(
          MesSwWorkTicketElectricalWeld::getSgStartTime, param.getStartTime(), param.getEndTime());
    }
    // 排序
    queryChain.orderByDesc(MesSwWorkTicketElectricalWeld::getCreatedTime);
    IPage<MesSwWorkTicketElectricalWeld> page = queryChain.page(iPage);
    // 当前人员是否可以签字 作废 作业确认签字
    page.getRecords().forEach(this::workTicketDataHandle);
    return page.convert(workTicketElectricalWeldConvert::toDTO);
  }

  /** 当前用户列表按钮权限处理 */
  private void workTicketDataHandle(MesSwWorkTicketElectricalWeld workTicketElectricalWeld) {
    UserDTO userInfo = UserContextHolder.getUserInfo();
    if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
      throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
    }
    // 查询表格数据
    List<MesSwWorkTicketElectricalWeldSignUserDTO> signUserDTOS =
        workTicketElectricalWeldSignUserService.queryListByTicketId(
            workTicketElectricalWeld.getId());
    List<MesSwWorkTicketElectricalWeldJobConfirmDTO> jobConfirmDTOS =
        workTicketElectricalWeldJobConfirmService.queryListByTicketId(
            workTicketElectricalWeld.getId());
    String statusCode = workTicketElectricalWeld.getStatusCode();
    // 工作票状态为审批中和已完成需要处理
    if (statusCode.equals(WorkTicketElectWeldStatusEnum.SPZ.getCode())
        || statusCode.equals(WorkTicketElectWeldStatusEnum.WC_SP.getCode())) {
      // 是否可作废
      if (userInfo.getAccountName().equals(workTicketElectricalWeld.getCreatedBy())) {
        workTicketElectricalWeld.setIsCanZf(Boolean.TRUE);
      }
    }
    // 审批中
    if (statusCode.equals(WorkTicketElectWeldStatusEnum.SPZ.getCode())) {
      if (signUserDTOS.stream()
          .filter(
              dto ->
                  dto.getUserCode() != null && dto.getUserCode().equals(userInfo.getAccountName()))
          .anyMatch(d -> d.getIsSign() == Boolean.FALSE)) {
        workTicketElectricalWeld.setIsCanSp(Boolean.TRUE);
      }
    }
    // 已完成审批 作业中
    if (statusCode.equals(WorkTicketElectWeldStatusEnum.WC_SP.getCode())) {
      if (jobConfirmDTOS.stream()
          .filter(
              dto ->
                  dto.getUserCode() != null && dto.getUserCode().equals(userInfo.getAccountName()))
          .anyMatch(d -> d.getIsSign() == Boolean.FALSE)) {
        workTicketElectricalWeld.setIsCanZySign(Boolean.TRUE);
      }
    }
  }

  @Override
  public MesSwWorkTicketElectricalWeldDTO getCurrentDetail(
      MesSwWorkTicketElectricalWeldParam param) {
    UserDTO userInfo = UserContextHolder.getUserInfo();
    if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
      throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
    }
    MesSwWorkTicketElectricalWeldDTO workTicket = new MesSwWorkTicketElectricalWeldDTO();
    if (param.getId() == null) {
      List<MesSwWorkTicketElectricalWeldSignUserDTO> signUsers = getSignUserDTOS();
      Map<String, List<MesSwWorkTicketElectricalWeldSignUserDTO>> map =
          signUsers.stream()
              .filter(s -> s.getType() != null)
              .collect(Collectors.groupingBy(MesSwWorkTicketElectricalWeldSignUserDTO::getType));
      workTicket.setBranchList(map.get(SP_BM));
      workTicket.setWorkList(map.get(AQ_QZ));
    } else {
      workTicket = workTicketElectricalWeldConvert.toDTO(super.getById(param.getId()));
      // 查询表格数据
      List<MesSwWorkTicketElectricalWeldSignUserDTO> signUserDTOS =
          workTicketElectricalWeldSignUserService.queryListByTicketId(param.getId());
      if (!CollectionUtils.isEmpty(signUserDTOS)) {
        signUserDTOS.forEach(
            e -> {
              if (e.getUserCode().equals(userInfo.getAccountName()) && !e.getIsSign()) {
                e.setIsCurrent(Boolean.TRUE);
              }
            });
        Map<String, List<MesSwWorkTicketElectricalWeldSignUserDTO>> map =
            signUserDTOS.stream()
                .filter(s -> s.getType() != null)
                .collect(Collectors.groupingBy(MesSwWorkTicketElectricalWeldSignUserDTO::getType));
        workTicket.setBranchList(map.get(SP_BM));
        workTicket.setWorkList(map.get(AQ_QZ));
        // 已完成审批 已完成作业
        if (workTicket.getStatusCode().equals(WorkTicketElectWeldStatusEnum.WC_SP.getCode())
            || workTicket.getStatusCode().equals(WorkTicketElectWeldStatusEnum.WC_ZY.getCode())) {
          List<MesSwWorkTicketElectricalWeldJobConfirmDTO> jobConfirmDTOS =
              workTicketElectricalWeldJobConfirmService.queryListByTicketId(param.getId());
          jobConfirmDTOS.forEach(
              e -> {
                if (e.getUserCode().equals(userInfo.getAccountName()) && !e.getIsSign()) {
                  e.setIsCurrent(Boolean.TRUE);
                }
              });
          Map<String, List<MesSwWorkTicketElectricalWeldJobConfirmDTO>> jobMap =
              jobConfirmDTOS.stream()
                  .filter(s -> s.getType() != null)
                  .collect(
                      Collectors.groupingBy(MesSwWorkTicketElectricalWeldJobConfirmDTO::getType));
          workTicket.setJobConfirmBefore(jobMap.get(HQ_JC));
          workTicket.setJobConfirmAfter(jobMap.get(HH_JC));
        }
      }
    }
    return workTicket;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MesSwWorkTicketElectricalWeldParam param) {
    UserDTO userInfo = UserContextHolder.getUserInfo();
    if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
      throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
    }
    check(param);
    param.setStatusCode(WorkTicketStatusEnum.SPZ.getCode());
    param.setStatusName(WorkTicketStatusEnum.SPZ.getDesc());
    MesSwWorkTicketElectricalWeld entity = workTicketElectricalWeldConvert.toEntity(param);
    boolean result = super.saveOrUpdate(entity);
    Long ticketId = entity.getId();
    // 现场作业确认数据初始化
    List<MesSwWorkTicketElectricalWeldJobConfirm> jobConfirms = getJobConfirms(ticketId);

    param
        .getBranchList()
        .forEach(
            t -> {
              t.setTicketId(ticketId);
              if (t.getUserType() != null
                  && t.getUserType().equals(MesSwWorkTicketElectricalWeldEnum.ZBC_LD.getDesc())) {
                jobConfirms.get(2).setUserCode(t.getUserCode());
                jobConfirms.get(2).setUserName(t.getUserName());
              }
            });
    param
        .getWorkList()
        .forEach(
            t -> {
              t.setTicketId(ticketId);
              if (t.getUserType().equals(MesSwWorkTicketElectricalWeldEnum.SH_RY.getDesc())) {
                jobConfirms.get(0).setUserCode(t.getUserCode());
                jobConfirms.get(0).setUserName(t.getUserName());
              } else if (t.getUserType()
                  .equals(MesSwWorkTicketElectricalWeldEnum.AQ_JC.getDesc())) {
                jobConfirms.get(1).setUserCode(t.getUserCode());
                jobConfirms.get(1).setUserName(t.getUserName());
                jobConfirms.get(3).setUserCode(t.getUserCode());
                jobConfirms.get(3).setUserName(t.getUserName());
              } else if (t.getUserType()
                  .equals(MesSwWorkTicketElectricalWeldEnum.HZB_RY.getDesc())) {
                jobConfirms.get(4).setUserCode(t.getUserCode());
                jobConfirms.get(4).setUserName(t.getUserName());
                jobConfirms.get(6).setUserCode(t.getUserCode());
                jobConfirms.get(6).setUserName(t.getUserName());
              } else if (t.getUserType()
                  .equals(MesSwWorkTicketElectricalWeldEnum.XC_FZ.getDesc())) {
                jobConfirms.get(5).setUserCode(t.getUserCode());
                jobConfirms.get(5).setUserName(t.getUserName());
              }
            });
    // 审批部门/领导
    workTicketElectricalWeldSignUserService.saveOrUpdateBatch(
        workTicketElectricalWeldSignUserConvert.toEntity(param.getBranchList()));
    // 安全技术措施贯彻签字
    workTicketElectricalWeldSignUserService.saveOrUpdateBatch(
        workTicketElectricalWeldSignUserConvert.toEntity(param.getWorkList()));
    // 新增现场作业确认
    workTicketElectricalWeldJobConfirmService.saveOrUpdateBatch(jobConfirms);
    return result;
  }

  /** 校验必填 */
  private void check(MesSwWorkTicketElectricalWeldParam param) {
    if (StringUtils.isBlank(param.getWorkContent())
        || StringUtils.isBlank(param.getDate())
        || StringUtils.isBlank(param.getDept())
        || StringUtils.isBlank(param.getWorkLocation())
        || StringUtils.isBlank(param.getSafeCode())
        || StringUtils.isBlank(param.getSafeName())
        || StringUtils.isBlank(param.getXcName())
        || StringUtils.isBlank(param.getXcCode())
        || StringUtils.isBlank(param.getHhzbCode())
        || StringUtils.isBlank(param.getHhzbName())
        || StringUtils.isBlank(param.getPssCode())
        || StringUtils.isBlank(param.getPssName())
        || StringUtils.isBlank(param.getOrganizationCode())
        || StringUtils.isBlank(param.getOrganizationName())
        || StringUtils.isBlank(param.getShCode())
        || StringUtils.isBlank(param.getShName())
        || StringUtils.isBlank(param.getZbCode())
        || StringUtils.isBlank(param.getZbName())
        || StringUtils.isBlank(param.getZzgcCode())
        || StringUtils.isBlank(param.getZzgcName())
        || StringUtils.isBlank(param.getSgEndTime())
        || StringUtils.isBlank(param.getSgEndTime())
        || CollectionUtils.isEmpty(param.getBranchList())
        || CollectionUtils.isEmpty(param.getWorkList())) {
      throw new BMSException(ExceptionEnum.REQUIRED_ERROR);
    }
  }

  @Override
  public Boolean cancel(MesSwWorkTicketElectricalWeldParam param) {
    UserDTO userInfo = UserContextHolder.getUserInfo();
    if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
      throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
    }
    // 查询工作票详情
    MesSwWorkTicketElectricalWeldDTO currentDetail = this.getCurrentDetail(param);
    if (Objects.isNull(currentDetail) || Objects.isNull(currentDetail.getId())) {
      throw new BMSException(ExceptionEnum.DATA_NOT_EXIST);
    }
    // 判断当前是否重复作废
    if (Objects.nonNull(currentDetail.getStatusCode())
        && WorkTicketStatusEnum.ZF.getCode().equals(currentDetail.getStatusCode())) {
      throw new BMSException(ExceptionEnum.CF_ERROR);
    }
    // 判断当前账号是否可以作废
    if (!userInfo.getAccountName().equals(currentDetail.getCreatedBy())) {
      throw new BMSException(ExceptionEnum.WQ_ERROR);
    }
    // 状态变更
    currentDetail.setStatusCode(WorkTicketElectWeldStatusEnum.ZF.getCode());
    currentDetail.setStatusName(WorkTicketElectWeldStatusEnum.ZF.getDesc());
    return super.updateById(workTicketElectricalWeldConvert.dtoToEntity(currentDetail));
  }

  @Override
  public Boolean approve(MesSwWorkTicketElectricalWeldSignUserParam param) {
    if (Objects.isNull(param.getTicketId()) || StringUtils.isBlank(param.getUserType())) {
      throw new BMSException(ExceptionEnum.REQUIRED_ERROR);
    }
    // 查询工作票详情
    MesSwWorkTicketElectricalWeldParam weldParam = new MesSwWorkTicketElectricalWeldParam();
    weldParam.setId(param.getTicketId());
    MesSwWorkTicketElectricalWeldDTO currentDetail = this.getCurrentDetail(weldParam);
    if (Objects.isNull(currentDetail) || Objects.isNull(currentDetail.getId())) {
      throw new BMSException(ExceptionEnum.DATA_NOT_EXIST);
    }
    if (StringUtils.isNotBlank(currentDetail.getStatusCode())
        && currentDetail.getStatusCode().equals(WorkTicketElectWeldStatusEnum.ZF.getCode())) {
      throw new BMSException(ExceptionEnum.CF_ERROR);
    }
    param.setIsSign(Boolean.TRUE);
    workTicketElectricalWeldSignUserService.saveOrUpdate(
        workTicketElectricalWeldSignUserConvert.toEntity(param));
    // 检查是否 已审批完成
    List<MesSwWorkTicketElectricalWeldSignUserDTO> signUserDTOS =
        workTicketElectricalWeldSignUserService.queryListByTicketId(param.getTicketId());
    if (signUserDTOS.stream().noneMatch(d -> d.getIsSign() == Boolean.FALSE)
        && currentDetail.getStatusCode().equals(WorkTicketElectWeldStatusEnum.SPZ.getCode())) {
      currentDetail.setStatusCode(WorkTicketElectWeldStatusEnum.WC_SP.getCode());
      currentDetail.setStatusName(WorkTicketElectWeldStatusEnum.WC_SP.getDesc());
      this.updateById(workTicketElectricalWeldConvert.dtoToEntity(currentDetail));
    }
    return Boolean.TRUE;
  }

  @Override
  public List<MesSwWorkTicketElectricalWeld> getList(MesSwWorkTicketElectricalWeldParam param) {
    LambdaQueryChainWrapper<MesSwWorkTicketElectricalWeld> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    return queryChain.list();
  }

  @Override
  public Boolean approveJob(MesSwWorkTicketElectricalWeldJobConfirmParam param) {
    if (Objects.isNull(param.getTicketId()) || StringUtils.isBlank(param.getUserType())) {
      throw new BMSException(ExceptionEnum.REQUIRED_ERROR);
    }
    // 查询工作票详情
    MesSwWorkTicketElectricalWeldParam weldParam = new MesSwWorkTicketElectricalWeldParam();
    weldParam.setId(param.getTicketId());
    MesSwWorkTicketElectricalWeldDTO currentDetail = this.getCurrentDetail(weldParam);
    if (Objects.isNull(currentDetail) || Objects.isNull(currentDetail.getId())) {
      throw new BMSException(ExceptionEnum.DATA_NOT_EXIST);
    }
    if (StringUtils.isNotBlank(currentDetail.getStatusCode())
        && currentDetail.getStatusCode().equals(WorkTicketElectWeldStatusEnum.ZF.getCode())) {
      throw new BMSException(ExceptionEnum.CF_ERROR);
    }
    param.setIsSign(Boolean.TRUE);
    workTicketElectricalWeldJobConfirmService.saveOrUpdate(
        workTicketElectricalWeldJobConfirmConvert.toEntity(param));
    // 检查是否 已作业完成
    List<MesSwWorkTicketElectricalWeldJobConfirmDTO> jobConfirmDTOS =
        workTicketElectricalWeldJobConfirmService.queryListByTicketId(param.getTicketId());
    if (jobConfirmDTOS.stream().noneMatch(d -> d.getIsSign() == Boolean.FALSE)
        && currentDetail.getStatusCode().equals(WorkTicketElectWeldStatusEnum.WC_SP.getCode())) {
      currentDetail.setStatusCode(WorkTicketElectWeldStatusEnum.WC_ZY.getCode());
      currentDetail.setStatusName(WorkTicketElectWeldStatusEnum.WC_ZY.getDesc());
      this.updateById(workTicketElectricalWeldConvert.dtoToEntity(currentDetail));
    }
    return Boolean.TRUE;
  }
}
