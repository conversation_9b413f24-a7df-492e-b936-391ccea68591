package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesSwHandoverDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwHandover;
import com.yhd.admin.bms.domain.query.sw.MesSwHandoverParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwHandoverVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesSwHandoverConvert {

    MesSwHandoverDTO toDTO(MesSwHandover mesSwHandover);

    MesSwHandoverVO toVO(MesSwHandoverDTO mesSwHandoverDTO);

    MesSwHandover toEntity(MesSwHandoverParam param);

}
