package com.yhd.admin.bms.service.sw.impl.eqpt;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentRepairDao;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentRepairDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepair;
import com.yhd.admin.bms.domain.enums.eqpt.EquipmentRepairStatusEnum;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentRepairRemainVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentRepairUserCountVO;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairCountService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.common.utils.NumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备检修统计-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/10/25 8:55
 */
@Service
public class MesSwEquipmentRepairCountServiceImpl
    extends ServiceImpl<MesSwEquipmentRepairDao, MesSwEquipmentRepair>
    implements MesSwEquipmentRepairCountService {

  private static final Logger logger =
      LoggerFactory.getLogger(MesSwEquipmentRepairCountServiceImpl.class);
  @Autowired private MesSwEquipmentRepairService repairService;

  @Autowired private UserAccountService accountService;

  @Override
  public IPage<MesSwEquipmentRepairDTO> pagingQuery(MesSwEquipmentRepairParam param) {
    logger.debug("请求【根据条件查询统计设备检修任务分页列表】接口开始，参数：{}", param);
    // 默认查询过去7天,包括今天
    if (Objects.isNull(param.getStartTime()) && Objects.isNull(param.getEndTime())) {
      // 开始日期
      param.setStartTime(
          LocalDateTime.parse(
              LocalDate.now().minusDays(6) + " 00:00:00",
              DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
      // 结束日期
      param.setEndTime(LocalDateTime.now());
    }

    return repairService.pagingQuery(param);
  }

  @Override
  public List<MesSwEquipmentRepairDTO> queryList(MesSwEquipmentRepairParam param) {
    // 默认查询过去7天,包括今天
    if (Objects.isNull(param.getStartTime()) && Objects.isNull(param.getEndTime())) {
      // 开始日期
      param.setStartTime(
          LocalDateTime.parse(
              LocalDate.now().minusDays(6) + " 00:00:00",
              DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
      // 结束日期
      param.setEndTime(LocalDateTime.now());
    }

    return repairService.queryList(param);
  }

  @Override
  public List<MesSwEquipmentRepairUserCountVO> tbUserRankCount(MesSwEquipmentRepairParam param) {
    List<MesSwEquipmentRepairUserCountVO> result = Lists.newArrayList();
    // 查询检修任务列表
    List<MesSwEquipmentRepairDTO> repairList = this.queryList(param);
    if (!CollectionUtil.isEmpty(repairList)) {
      Map<String, List<MesSwEquipmentRepairDTO>> dataMap =
          repairList.stream()
              .collect(Collectors.groupingBy(v -> v.getTbUserCode() + "-" + v.getTbUserName()));

      dataMap.forEach(
          (k, v) -> {
            MesSwEquipmentRepairUserCountVO tbUserCountVO = new MesSwEquipmentRepairUserCountVO();
            tbUserCountVO.setUserName(k.split("-")[1]);
            tbUserCountVO.setCount(v.size());
            result.add(tbUserCountVO);
          });

      // 排序
      result.sort((p1, p2) -> p2.getCount() - p1.getCount());
    }

    return result.stream().limit(10).collect(Collectors.toList());
  }

  @Override
  public List<MesSwEquipmentRepairUserCountVO> clUserRankCount(MesSwEquipmentRepairParam param) {
    List<MesSwEquipmentRepairUserCountVO> result = Lists.newArrayList();
    // 查询检修任务列表
    List<MesSwEquipmentRepairDTO> repairList = this.queryList(param);
    if (!CollectionUtil.isEmpty(repairList)) {
      repairList =
          repairList.stream()
              .filter(v -> StringUtils.isNotBlank(v.getZgUserCode()))
              .collect(Collectors.toList());
      Map<String, List<MesSwEquipmentRepairDTO>> dataMap =
          repairList.stream()
              .collect(Collectors.groupingBy(v -> v.getZgUserCode() + "-" + v.getZgUserName()));

      dataMap.forEach(
          (k, v) -> {
            MesSwEquipmentRepairUserCountVO tbUserCountVO = new MesSwEquipmentRepairUserCountVO();
            tbUserCountVO.setUserName(k.split("-")[1]);
            tbUserCountVO.setCount(v.size());
            result.add(tbUserCountVO);
          });

      // 排序
      result.sort((p1, p2) -> p2.getCount() - p1.getCount());
    }

    return result.stream().limit(10).collect(Collectors.toList());
  }

  @Override
  public IPage<MesSwEquipmentRepairRemainVO> remainPagingQuery(MesSwEquipmentRepairParam param) {
       Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
       //当前登录人
      String userName = authentication.getName();
      LambdaQueryWrapper<MesSwEquipmentRepair> wrapper = new LambdaQueryWrapper<>();
    // 工单状态!=已关闭 and 工单状态!=已完成
    wrapper.ne(MesSwEquipmentRepair::getStatusCode, EquipmentRepairStatusEnum.YGB.getCode())
        .ne(MesSwEquipmentRepair::getStatusCode, EquipmentRepairStatusEnum.YWC.getCode());
    if (Objects.nonNull(param.getStartTime()) && Objects.nonNull(param.getEndTime())){
      wrapper.ge(MesSwEquipmentRepair::getTbTime, param.getStartTime())
          .le(MesSwEquipmentRepair::getTbTime, param.getEndTime());
    }
    LocalDateTime nowTime = LocalDateTime.now();
    List<MesSwEquipmentRepair> repairList = repairService.list(wrapper);
    List<MesSwEquipmentRepairRemainVO> dataList = Lists.newArrayList();
    if (!CollectionUtil.isEmpty(repairList)) {
        repairList.forEach(
          v -> {
            MesSwEquipmentRepairRemainVO remainVO = new MesSwEquipmentRepairRemainVO();
            remainVO.setId(v.getId());
            remainVO.setEquipmentNoType(v.getEquipmentNoType());
            remainVO.setExistProblem(v.getExistProblem());
            remainVO.setTbTime(v.getTbTime());
            remainVO.setTbWorkshopCode(v.getTbWorkshopCode());
            remainVO.setTbWorkshopName(v.getTbWorkshopName());
            remainVO.setStatusCode(v.getStatusCode());
            remainVO.setStatusName(v.getStatusName());
            Duration duration = Duration.between(v.getTbTime(), nowTime);
            remainVO.setDuration(NumberUtil.div(duration.toSeconds(), 3600, 1));
            //先判断是哪个车间 （机电车间、生产车间、装车车间）
            String workshopName = v.getZgWorkshopName();
            if (StringUtils.isNotBlank(workshopName)) {
                UserAccountDTO userAccountDTO = accountService.currentDetail(userName);
                String userPost = userAccountDTO.getPost();
                // 判断当前用户是否为对应车间主任
                boolean isWorkshopDirector = false;
                switch (workshopName) {
                    case "机电车间":
                        isWorkshopDirector = "机电车间主任".equals(userPost);
                        break;
                    case "生产车间":
                        isWorkshopDirector = "生产车间主任".equals(userPost);
                        break;
                    case "装车车间":
                        isWorkshopDirector = "装车车间主任".equals(userPost);
                        break;
                }

                if (isWorkshopDirector) {
                    remainVO.setIsCanZjx(Boolean.TRUE);
                }
            }

            dataList.add(remainVO);
          });
    }
    // 排序
    dataList.sort(Comparator.comparing(MesSwEquipmentRepairRemainVO::getDuration).reversed());

    Page<MesSwEquipmentRepairRemainVO> page = new Page<>(param.getCurrent(), param.getPageSize());

    List<MesSwEquipmentRepairRemainVO> result = Lists.newArrayList();
    int pageNum = param.getCurrent() == null ? 1 : param.getCurrent().intValue();
    int pageSize = param.getPageSize() == null ? 10 : param.getPageSize().intValue();
    int index = pageNum > 1 ? (pageNum - 1) * pageSize : 0;
    int total = dataList.size();
    if (!CollectionUtil.isEmpty(dataList)) {
      for (int i = 0; i < param.getPageSize() && index + i < total; i++) {
        MesSwEquipmentRepairRemainVO item = dataList.get(index + i);
        result.add(item);
      }
    }

    page.setRecords(result);
    page.setTotal(dataList.size());

    return page;
  }

  @Override
  public MesSwEquipmentRepairRemainVO remainCount(MesSwEquipmentRepairParam param) {
    MesSwEquipmentRepairRemainVO result = new MesSwEquipmentRepairRemainVO();
    LambdaQueryWrapper<MesSwEquipmentRepair> wrapper = new LambdaQueryWrapper<>();
    // 工单状态!=已关闭 and 工单状态!=已完成
    wrapper.ne(MesSwEquipmentRepair::getStatusCode, EquipmentRepairStatusEnum.YGB.getCode())
        .ne(MesSwEquipmentRepair::getStatusCode, EquipmentRepairStatusEnum.YWC.getCode());
    if (Objects.nonNull(param.getStartTime()) && Objects.nonNull(param.getEndTime())) {
      wrapper.ge(MesSwEquipmentRepair::getTbTime, param.getStartTime())
          .le(MesSwEquipmentRepair::getTbTime, param.getEndTime());
    }
    List<MesSwEquipmentRepair> repairList = repairService.list(wrapper);

    Map<String, Long> statusCountMap = repairList.stream()
        .collect(Collectors.groupingBy(MesSwEquipmentRepair::getStatusCode, Collectors.counting()));
    // CQ_DSH("4", "长期遗留待审核"),
      //    CQ_DJX("5", "长期遗留待检修")
    result.setJxDclCount(statusCountMap.getOrDefault(EquipmentRepairStatusEnum.JX_DCL.getCode(), 0L));
    result.setJxDshCount(statusCountMap.getOrDefault(EquipmentRepairStatusEnum.JX_DSH.getCode(), 0L));
    result.setJxShBhCount(statusCountMap.getOrDefault(EquipmentRepairStatusEnum.JX_SH_BH.getCode(), 0L));
    result.setCqDshCount(statusCountMap.getOrDefault(EquipmentRepairStatusEnum.CQ_DSH.getCode(), 0L));
    result.setCqDjxCount(statusCountMap.getOrDefault(EquipmentRepairStatusEnum.CQ_DJX.getCode(), 0L));
    return result;
    }
}
