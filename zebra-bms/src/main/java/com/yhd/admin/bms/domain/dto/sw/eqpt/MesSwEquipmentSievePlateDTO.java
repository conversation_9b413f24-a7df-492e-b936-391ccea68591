package com.yhd.admin.bms.domain.dto.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwEquipmentSievePlateDTO extends BaseDTO implements Serializable {
    /**
     * 设备资料id
     */
    private Long equipmentDataId;
    /**
     * 筛板名称
     */
    private String name;
    /**
     * 总列号 总数，例如0-9，值为10
     */
    private Integer column;
    /**
     * 总行号 总数，例如0-9，值为10
     */
    private Integer row;
    /**
     * 设备编码
     */
    private String equipmentNo;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 筛板数量
     */
    private BigDecimal plateNum;
    /**
     * 月消耗量
     */
    private BigDecimal monthNum;
    /**
     * 筛机尺寸
     */
    private String plateSize;
    /**
     * 分级粒度
     */
    private String grade;
    /**
     * 材质code
     */
    private Long categoryCode;
    /**
     * 材质name
     */
    private String categoryName;
    /**
     * 孔径
     */
    private String apertureCode;
    /**
     * 孔径name
     */
    private String apertureName;

    private List<MesSwEquipmentSievePlateRecordDTO> recordList;
}
