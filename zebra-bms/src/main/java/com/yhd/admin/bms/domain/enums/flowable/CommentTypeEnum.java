package com.yhd.admin.bms.domain.enums.flowable;

/**
 * 审批意见的类型
 */
public enum CommentTypeEnum {
    SP("审批"),
    BH("驳回"),
    CX("撤销"),
    CH("撤回"),
    ZC("暂存"),
    QS("签收"),
    WP("委派"),
    Z<PERSON>("知会"),
    Z<PERSON>("转阅"),
    <PERSON><PERSON>("已阅"),
    ZB("转办"),
    QJQ("前加签"),
    HJQ("后加签"),
    XTZX("系统执行"),
    TJ("提交"),
    CXTJ("重新提交"),
    SPJS("审批结束"),
    LCZZ("流程终止"),
    SQ("授权"),
    CFTG("重复跳过"),
    XT("协同"),
    PS("评审"),
    FQSP("发起审批"),
    TY("同意");
    private String name; // 名称

    /**
     * 通过type获取Msg
     *
     * @param type
     * @return @Description:
     */
    public static String getEnumMsgByType(String type) {
        for (CommentTypeEnum e : CommentTypeEnum.values()) {
            if (e.toString().equals(type)) {
                return e.name;
            }
        }
        return "";
    }

    private CommentTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
