package com.yhd.admin.bms.domain.enums.safe;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** 电气焊工作票审批用户类型枚举类 */
@Getter
public enum MesSwWorkTicketElectricalWeldEnum {
  SG_FZR("1", "施工单位负责人"),
  CJ_ZR("2", "车间主任"),
  JS_ZG("3", "技术员"),
  AB_ZG("4", "安监员"),
  ZBC_LD("5", "值班厂（站）领导"),
  CZ_LD("6", "厂（站）领导"),
  ZZ_GC("7", "组织贯彻人"),
  AQ_JC("8", "贯彻签字安监员"),
  XC_FZ("9", "现场负责人"),
  PSS_RY("10", "喷洒水人员"),
  SH_RY("11", "施焊人员"),
  HZB_RY("12", "焊后值班人员"),
  ;

  private final String code;
  private final String desc;

  MesSwWorkTicketElectricalWeldEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static List<String> getDescList() {
    MesSwWorkTicketElectricalWeldEnum[] values = MesSwWorkTicketElectricalWeldEnum.values();

    return Arrays.stream(values)
        .map(MesSwWorkTicketElectricalWeldEnum::getDesc)
        .collect(Collectors.toList());
  }

  public static List<String> getCodeList() {
    MesSwWorkTicketElectricalWeldEnum[] values = MesSwWorkTicketElectricalWeldEnum.values();

    return Arrays.stream(values)
        .map(MesSwWorkTicketElectricalWeldEnum::getCode)
        .collect(Collectors.toList());
  }

  public static MesSwWorkTicketElectricalWeldEnum getEnumByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    MesSwWorkTicketElectricalWeldEnum[] values = MesSwWorkTicketElectricalWeldEnum.values();
    for (MesSwWorkTicketElectricalWeldEnum value : values) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public static MesSwWorkTicketElectricalWeldEnum getEnumByName(String name) {
    if (StringUtils.isBlank(name)) {
      return null;
    }
    MesSwWorkTicketElectricalWeldEnum[] values = MesSwWorkTicketElectricalWeldEnum.values();
    for (MesSwWorkTicketElectricalWeldEnum value : values) {
      if (value.getDesc().equals(name)) {
        return value;
      }
    }
    return null;
  }
}
