package com.yhd.admin.bms.controller.sw.dashboard;

import com.yhd.admin.bms.domain.query.sw.produce.MesProduceDcpdParam;
import com.yhd.admin.bms.domain.vo.sys.SysDutyVO;
import com.yhd.admin.bms.service.sw.consumption.MesEcmCountService;
import com.yhd.admin.bms.service.sw.dashboard.CoalBunkerService;
import com.yhd.admin.bms.service.sw.load.LoadDayService;
import com.yhd.admin.bms.service.sw.produce.MesProduceDcpdService;
import com.yhd.admin.bms.service.sw.produce.MesSwPeAnalysisService;
import com.yhd.admin.bms.service.sw.produce.MesSwProducePlanService;
import com.yhd.admin.bms.service.sys.SysDutyService;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.utils.DateUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 首页看板-控制层
 *
 * <AUTHOR>
 * @date 2024/10/2 16:01
 */
@RestController
@RequestMapping("/api/homePage")
public class MesHomePageController {
  @Resource private CoalBunkerService coalBunkerService;
  @Resource private MesSwPeAnalysisService peAnalysisService;
  @Resource private SysDutyService dutyService;
  @Resource private LoadDayService loadDayService;
  @Resource private MesEcmCountService ecmCountService;
  @Resource private MesSwProducePlanService producePlanService;
  @Resource private MesProduceDcpdService dcpdService;

  @PostMapping(
      value = "/getCoalBunkerData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getCoalBunkerData() {
    try {
      return RespJson.buildSuccessResponse(coalBunkerService.getCoalBunkerData());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/getPeAndTotalTimeData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getPeAndTotalTimeData() {
    try {
      return RespJson.buildSuccessResponse(peAnalysisService.getPeAndTotalTimeData());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/getOnDutyData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getOnDutyData() {
    try {
      return RespJson.buildSuccessResponse(dutyService.getSysDutyData());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/updateDutyData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> updateDutyData(@RequestBody SysDutyVO vo) {
    try {
      return RespJson.buildSuccessResponse(dutyService.update(vo));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 日装车完成情况统计
   *
   * @return 统计信息
   */
  @PostMapping(
      value = "/getLoadDayCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getLoadDayCount() {
    try {
      return RespJson.buildSuccessResponse(loadDayService.getLoadDayCount());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 月装车完成情况统计
   *
   * @return 统计信息
   */
  @PostMapping(
      value = "/getLoadMonthCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getLoadMonthCount() {
    try {
      return RespJson.buildSuccessResponse(producePlanService.getLoadFinishMonthCount());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 生产计划产量及完成产量统计
   *
   * @return 统计信息
   */
  @PostMapping(
      value = "/getProducePlanAndFinishCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getProducePlanAndFinishCount() {
    try {
      return RespJson.buildSuccessResponse(producePlanService.getProducePlanAndFinishCount());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 统计当月能源消耗数据
   *
   * @return 当月能源消耗数据
   */
  @PostMapping(
      value = "/getEcmMonthCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getEcmMonthCount() {
    try {
      return RespJson.buildSuccessResponse(ecmCountService.getEcmMonthCount());
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /** 采集电厂皮带数据 */
  @PostMapping(
      value = "/collectDcpdSaveKIO",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> collectDcpdSaveKIO(@RequestBody MesProduceDcpdParam param) {
    try {
      List<String> dateList = DateUtil.dateBetween(param.getStartDate(), param.getEndDate());
      return RespJson.buildSuccessResponse(dcpdService.collectSaveKIO(dateList));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
