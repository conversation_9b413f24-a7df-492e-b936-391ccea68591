package com.yhd.admin.bms.service.sw.produce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.produce.MesSwProducePlanDTO;
import com.yhd.admin.bms.domain.entity.sw.produce.MesSwProducePlan;
import com.yhd.admin.bms.domain.query.sw.produce.MesSwProducePlanParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.load.LoadFinishMonthCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProduceCoalTypeCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProduceFinishDayCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProduceFinishMonthCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProducePlanAndFinishCountVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 生产计划-业务层接口
 *
 * <AUTHOR>
 * @date 2025/1/4 10:25
 */
public interface MesSwProducePlanService extends IService<MesSwProducePlan> {
  /**
   * 根据条件查询分页列表
   *
   * @param param 查询参数
   * @return 生产计划列表
   */
  IPage<MesSwProducePlanDTO> pagingQuery(MesSwProducePlanParam param);

  /**
   * 根据条件查询列表
   *
   * @param param 参数
   * @return 生产计划列表
   */
  List<MesSwProducePlan> queryList(MesSwProducePlanParam param);

  /**
   * 查询生产计划详情
   *
   * @param param 主键id
   * @return 生产计划详情
   */
  MesSwProducePlanDTO getCurrentDetail(MesSwProducePlanParam param);

  /**
   * 新增生产计划
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean add(MesSwProducePlanParam param);
  /**
   * 编辑生产计划
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean modify(MesSwProducePlanParam param);
  /**
   * 删除
   *
   * @param param 表主键id列表
   * @return true成功，false失败
   */
  Boolean removeBatch(BatchParam param);

  /**
   * 生产计划产量及完成产量统计
   *
   * @return 统计数据
   */
  MesSwProducePlanAndFinishCountVO getProducePlanAndFinishCount();

  /**
   * 获取装车完成月份统计
   *
   * @return 装车完成月份统计数据
   */
  LoadFinishMonthCountVO getLoadFinishMonthCount();

  /**
   * 日产量完成情况统计
   *
   * @param date 日期
   * @return 统计数据
   */
  MesSwProduceFinishDayCountVO getProduceFinishDayCount(LocalDate date);

  /**
   * 产量逐日完成情况
   *
   * @param days 天数
   * @return 日统计列表
   */
  List<MesSwProduceFinishDayCountVO> getProduceFinishDayCountList(Integer days);

  /**
   * 月产量完成情况统计
   *
   * @param month 月份
   * @return 统计数据
   */
  MesSwProduceFinishMonthCountVO getProduceFinishMonthCount(String month);

  /**
   * 产量逐月完成情况
   *
   * @param months 月数
   * @return 月统计列表
   */
  List<MesSwProduceFinishMonthCountVO> getProduceFinishMonthCountList(Integer months);

  /**
   * 年产量完成情况统计
   *
   * @return 统计数据
   */
  MesSwProduceFinishMonthCountVO getProduceFinishYearCount();

  /**
   * 统计各煤种产量
   *
   * @return 统计数据
   */
  List<MesSwProduceCoalTypeCountVO> getProduceCoalTypeCount();

  /**
   * 装车外运产量逐月完成情况
   *
   * @param months 月数
   * @return 月统计列表
   */
  List<MesSwProduceFinishMonthCountVO> getLoadFinishMonthCountList(Integer months);
}
