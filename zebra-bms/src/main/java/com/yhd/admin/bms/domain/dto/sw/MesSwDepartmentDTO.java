package com.yhd.admin.bms.domain.dto.sw;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @<PERSON> sa<PERSON>
 * @Date 2024/1/5 22:23
 * @Version 1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwDepartmentDTO extends BaseDTO implements Serializable {
    /**
     * 部门id
     **/
    private String departmentId;
    /**
     * 部门name
     **/
    private String department;
    /**
     * 部门人数
     **/
    private Integer deptNumber;
}
