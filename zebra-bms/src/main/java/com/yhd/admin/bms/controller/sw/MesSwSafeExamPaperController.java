package com.yhd.admin.bms.controller.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeExamPaperConvert;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeExamPaperTopicConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperParam;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperTopicParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwSafeExamPaperVO;
import com.yhd.admin.bms.service.sw.MesSwSafeExamPaperService;
import com.yhd.admin.bms.service.sw.MesSwSafeExamPaperTopicService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @Date 2024/3/3 12:22 @Version 1.0
 *
 * <p>安全考试试卷管理--控制层
 */
@RestController
@RequestMapping("/exam/paper")
@Slf4j
public class MesSwSafeExamPaperController {
  @Resource private MesSwSafeExamPaperConvert convert;
  @Resource private MesSwSafeExamPaperService service;
  @Resource private MesSwSafeExamPaperTopicConvert topicConvert;
  @Resource private MesSwSafeExamPaperTopicService topicService;

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwSafeExamPaperVO> pagingQuery(
      @RequestBody MesSwSafeExamPaperParam queryParam) {
    IPage<MesSwSafeExamPaperDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @PostMapping(
      value = "/getPaperList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getPaperList(@RequestBody MesSwSafeExamPaperParam param) {
    List<MesSwSafeExamPaperDTO> dtoList = service.getPaperList(param);
    return RespJson.buildSuccessResponse(convert.toVOList(dtoList));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetail(@RequestBody MesSwSafeExamPaperParam param) {
    return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @SysLogs(title = "安全考试试卷组卷、编辑", businessType = BusinessType.INSERT)
  public RespJson addOrModify(@RequestBody MesSwSafeExamPaperParam param) {
    Boolean retVal = service.addOrModify(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/ifExist",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson ifExist(@RequestBody MesSwSafeExamPaperParam param) {
    Boolean retVal = service.ifExist(param);
    return RespJson.buildSuccessResponse(retVal);
  }

  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @SysLogs(title = "安全考试试卷删除", businessType = BusinessType.DELETE)
  public RespJson removeBatch(@RequestBody BatchParam batchParam) {
    Boolean retVal = service.removeBatch(batchParam);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/getQuestion",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getQuestion(@RequestBody MesSwSafeExamPaperTopicParam param) {
    return RespJson.buildSuccessResponse(topicConvert.toVO(topicService.getQuestion(param)));
  }

  @PostMapping(
      value = "/getQuestionByAdd",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getQuestionByAdd(@RequestBody MesSwSafeExamPaperTopicParam param) {
    return RespJson.buildSuccessResponse(topicConvert.toVO(topicService.getQuestionByAdd(param)));
  }

  /** 导出试卷pdf */
  @PostMapping(
      value = "/exportPaperPdf",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> exportPaperPdf(@RequestBody MesSwSafeExamPaperParam param) {
    try {
      return RespJson.buildSuccessResponse(service.exportPaperPdf(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
