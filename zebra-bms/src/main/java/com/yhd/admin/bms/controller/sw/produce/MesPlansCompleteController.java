package com.yhd.admin.bms.controller.sw.produce;

import com.yhd.admin.bms.domain.query.sw.HstTimeStatsParam;
import com.yhd.admin.bms.domain.query.sw.produce.MesPlansCompleteParam;
import com.yhd.admin.bms.domain.vo.sw.produce.MesCoalCurveVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesPlansCompleteVO;
import com.yhd.admin.bms.service.sw.produce.MesPlansCompleteService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 各计划完成情况对比
 * <AUTHOR>
 * @Date 2025/3/4 17:40 @Version 1.0
 */
@RestController
@RequestMapping(value = "/plan/complete")
public class MesPlansCompleteController {
    @Resource
    private MesPlansCompleteService completeService;
    @PostMapping(
        value = "/getRawCoalProcessing",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesPlansCompleteVO> getRawCoalProcessing(@RequestBody MesPlansCompleteParam param) {
        try {
            return RespJson.buildSuccessResponse(completeService.getRawCoalProcessing(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getRawCoalProcessingNow",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesPlansCompleteVO> getRawCoalProcessingNow(@RequestBody MesPlansCompleteParam param) {
        try {
            return RespJson.buildSuccessResponse(completeService.getRawCoalProcessingNow(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getCommercialCoal",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesPlansCompleteVO> getCommercialCoal(@RequestBody MesPlansCompleteParam param) {
        try {
            return RespJson.buildSuccessResponse(completeService.getCommercialCoal(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getRawCoalCurve",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesCoalCurveVO> getRawCoalCurve(@RequestBody HstTimeStatsParam param) {
        try {
            return RespJson.buildSuccessResponse(completeService.getRawCoalCurve(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getLumpCoalCurve",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesCoalCurveVO> getLumpCoalCurve(@RequestBody HstTimeStatsParam param) {
        try {
            return RespJson.buildSuccessResponse(completeService.getLumpCoalCurve(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }
}
