package com.yhd.admin.bms.domain.query.sw.exam;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 安全考试计划表查询
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwExamPlanParam extends QueryParam {
    /** 开始日期(yyyy-MM-dd) */
    private LocalDate startDate;
    /** 结束日期(yyyy-MM-dd) */
    private LocalDate endDate;

    /**
     * 考试试卷(组卷)主键id
     */
    private Long examPaperId;

    /**
     * 试卷标题
     */
    private String examTitle;

    /**
     * 试卷分类
     */
    private String examType;

    /**
     * 作者
     */
    private String author;

    /**
     * 计划状态code
     */
    private String planStatus;

    /**
     * 计划状态name
     */
    private String planStatusName;

    /**
     * 相关培训
     */
    private String trainingContent;

    /**
     * 考生来源
     */
    private String departmentId;

    private List<String> departmentIdList;

    private List<String> departmentNameList;

    private String departmentName;
    /**
     * 考试开始日期
     */
    private LocalDate examStartDate;
    /**
     * 考试结束日期
     */
    private LocalDate examEndDate;

    /**
     * 考试时长
     */
    private Long examDuration;

    /**
     * 题目乱序
     */
    private Boolean questionDisorder = false;

    /**
     * 答案乱序
     */
    private Boolean answerDisorder = false;

    /**
     * 抽考标记：0否，1是
     */
    private Boolean extract;

    /**
     * 抽考人数
     */
    private Integer extractUserCount;

    /**
     * 考生
     */
    private String examStudentAccount;
    private String examStudentName;
    /**
     * 考生集合
     */
    private List<String> examStudentAccountList;
    private List<String> examStudentNameList;
    /**
     * 考生人数
     */
    private Integer examNum;
    /**
     * 合格分数
     */
    private Integer passScore;
    /**
     * 需重考人员账号
     */
    private String againAccount;

}
