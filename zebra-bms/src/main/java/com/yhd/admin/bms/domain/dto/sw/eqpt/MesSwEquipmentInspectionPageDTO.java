package com.yhd.admin.bms.domain.dto.sw.eqpt;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 设备巡检表-列表分页的数据字段
 *
 * <AUTHOR>
 * @date 2023/12/01 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwEquipmentInspectionPageDTO implements Cloneable, Serializable {
  private String id;
  /** 设备巡检表主键ID */
  private String parentId;
  /** 设备类型名称 */
  private String equipmentName;
  /** 设备编号 */
  private String equipmentNo;
  /** 设备功率 */
  private String equipmentPower;
  /** 巡检工单类别及车间 */
  private String classificationShop;
  /** 本次巡检周期 */
  private String thisPeriod;
  /** 创建日期String */
  private String createdDate;
  /** 创建日期LocalDate */
  private LocalDate createdDateDate;

  private String createdBy;
  /** 流程状态code */
  private String statusCode;
  /** 流程状态name */
  private String statusName;
  /** 流程实例ID */
  private String processInstanceId;
  /** 设备编号及对应功率 */
  private String noPower;
  /** 任务ID */
  private String taskId;
  /** true-显示列表的删除按钮 false-不显示 */
  private boolean deleteFlag;
  /** true-是管理员 false-不是 */
  private Boolean managerFlag;
  /** 前端需要的字段，默认就是false */
  private boolean show = false;
  /** 一个巡检工单关联多个设备 */
  private List<MesSwEquipmentInspectionPageDTO> children;
}
