package com.yhd.admin.passport.auth.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WebMvcCfg.java
 * @Description TODO 跨域设置
 * @createTime 2020年03月27日 11:14:00
 */
@Configuration
public class CorsCfg {

  @Bean
  @Order(1)
  public FilterRegistrationBean<CorsFilter> corsFilter() {
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    CorsConfiguration config = new CorsConfiguration();
    config.setAllowCredentials(true);
    // 设置你要允许的网站域名，如果全允许则设为 *
    config.addAllowedOrigin("*");
    // 如果要限制 HEADER 或 METHOD 请自行更改
    config.addAllowedHeader("*");
    config.addAllowedMethod("*");
    config.addExposedHeader("JSESSIONID");
    source.registerCorsConfiguration("/**", config);
    // 这个顺序很重要哦，为避免麻烦请设置在最前
    return new FilterRegistrationBean<>(new CorsFilter(source));
  }
}
