package com.yhd.admin.bms.domain.convert.sw.goods;

import java.util.List;

import org.mapstruct.Mapper;

import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsStockDTO;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsStock;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsStockParam;
import com.yhd.admin.bms.domain.vo.sw.goods.MesSwGoodsStockVO;

@Mapper(componentModel = "spring")
public interface MesSwGoodsStockConvert {

    MesSwGoodsStock toEntity(MesSwGoodsStockParam param);

    MesSwGoodsStockDTO toDTO(MesSwGoodsStock entity);

    MesSwGoodsStockVO toVO(MesSwGoodsStockDTO dto);

    List<MesSwGoodsStockDTO> toDTOS(List<MesSwGoodsStock> list);

    List<MesSwGoodsStockVO> toVOS(List<MesSwGoodsStockDTO> list);
}
