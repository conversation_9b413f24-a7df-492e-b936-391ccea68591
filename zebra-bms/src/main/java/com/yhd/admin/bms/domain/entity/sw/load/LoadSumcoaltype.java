package com.yhd.admin.bms.domain.entity.sw.load;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 装车煤种统计
 *
 * <AUTHOR>
 * @date 2025/1/10 9:15
 */
@Data
@TableName("load_sumcoaltype")
@AllArgsConstructor
public class LoadSumcoaltype implements Serializable {
  private static final long serialVersionUID = 1764516432520523823L;
  /** 煤种 */
  private String tllCoaltypedesc;
  /** 数量 */
  private BigDecimal tllTotalnum;
}
