package com.yhd.admin.bms.domain.vo.sw.produce;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.bms.domain.entity.sw.produce.MesProduceDcpd;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 各计划完成情况对比
 * <AUTHOR>
 * @Date 2025/3/4 17:53 @Version 1.0
 */
@Data
public class MesPlansCompleteVO implements Serializable {
    private static final long serialVersionUID = 3309433425570358444L;

    /** 201胶带机日产量 */
    private BigDecimal con201Today;
    /** 201胶带机月产量 */
    private BigDecimal con201Month;
    /** 201胶带机年产量 */
    private BigDecimal con201Year;

    /** 218胶带机日产量 */
    private BigDecimal con218Today;
    /** 218胶带机月产量 */
    private BigDecimal con218Month;
    /** 218胶带机年产量 */
    private BigDecimal con218Year;

    /** 301胶带机日产量 */
    private BigDecimal con301Today;
    /** 301胶带机月产量 */
    private BigDecimal con301Month;
    /** 301胶带机年产量 */
    private BigDecimal con301Year;

    /** 302胶带机日产量 */
    private BigDecimal con302Today;
    /** 302胶带机月产量 */
    private BigDecimal con302Month;
    /** 302胶带机年产量 */
    private BigDecimal con302Year;

    /** 701胶带机日产量 */
    private BigDecimal con701Today;
    /** 701胶带机月产量 */
    private BigDecimal con701Month;
    /** 701胶带机年产量 */
    private BigDecimal con701Year;

    /** 702胶带机日产量 */
    private BigDecimal con702Today;
    /** 702胶带机月产量 */
    private BigDecimal con702Month;
    /** 702胶带机年产量 */
    private BigDecimal con702Year;

    /** 703胶带机日产量 */
    private BigDecimal con703Today;
    /** 703胶带机月产量 */
    private BigDecimal con703Month;
    /** 703胶带机年产量 */
    private BigDecimal con703Year;

    /** 706胶带机日产量 */
    private BigDecimal con706Today;
    /** 706胶带机月产量 */
    private BigDecimal con706Month;
    /** 706胶带机年产量 */
    private BigDecimal con706Year;

    /** 901胶带机日产量 */
    private BigDecimal con901Today;
    /** 901胶带机月产量 */
    private BigDecimal con901Month;
    /** 901胶带机年产量 */
    private BigDecimal con901Year;

    /** 装车当日完成 */
    private BigDecimal weightDay;
    /** 装车当月完成 */
    private BigDecimal weightMonth;
    /** 装车当年完成 */
    private BigDecimal weightYear;
    /** 电厂当日完成 */
    private BigDecimal elecDay;
    /** 电厂当月完成 */
    private BigDecimal elecMonth;
    /** 电厂当年完成 */
    private BigDecimal elecYear;
    /** 当日完成总计 */
    private BigDecimal dayTotal;
    /** 当月完成总计 */
    private BigDecimal monthTotal;
    /** 当年完成总计 */
    private BigDecimal yearTotal;

    /** 装车当日计划 */
    private BigDecimal weightDayPlan;
    /** 装车当月计划 */
    private BigDecimal weightMonthPlan;
    /** 装车当年计划 */
    private BigDecimal weightYearPlan;
    /** 电厂当日计划 */
    private BigDecimal elecDayPlan;
    /** 电厂当月计划 */
    private BigDecimal elecMonthPlan;
    /** 电厂当年计划 */
    private BigDecimal elecYearPlan;
    /** 当日计划总计 */
    private BigDecimal dayPlanTotal;
    /** 当月计划总计 */
    private BigDecimal monthPlanTotal;
    /** 当年计划总计 */
    private BigDecimal yearPlanTotal;

    /** 装车当日超欠 */
    private BigDecimal weightDayOverdue;
    /** 装车当月超欠 */
    private BigDecimal weightMonthOverdue;
    /** 装车当年超欠 */
    private BigDecimal weightYearOverdue;
    /** 电厂当日超欠 */
    private BigDecimal elecDayOverdue;
    /** 电厂当月超欠 */
    private BigDecimal elecMonthOverdue;
    /** 电厂当年超欠 */
    private BigDecimal elecYearOverdue;
    /** 当日超欠总计 */
    private BigDecimal dayOverdueTotal;
    /** 当月超欠总计 */
    private BigDecimal monthOverdueTotal;
    /** 当年超欠总计 */
    private BigDecimal yearOverdueTotal;

    /** 装车月剩余日均 */
    private BigDecimal weightMonthLeave;
    /** 装车年剩余日均 */
    private BigDecimal weightYearLeave;
    /** 电厂月剩余日均 */
    private BigDecimal elecMonthLeave;
    /** 电厂年剩余日均 */
    private BigDecimal elecYearLeave;
    /** 月剩余日均总计 */
    private BigDecimal monthLeaveTotal;
    /** 年剩余日均总计 */
    private BigDecimal yearLeaveTotal;


}
