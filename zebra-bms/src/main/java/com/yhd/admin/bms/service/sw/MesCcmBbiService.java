package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesCcmBbiDTO;
import com.yhd.admin.bms.domain.entity.sw.MesCcmBbi;
import com.yhd.admin.bms.domain.query.sw.MesCcmBbiParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

import java.util.List;

public interface MesCcmBbiService extends IService<MesCcmBbi> {
  /** 分页商品煤管理商户信息 */
  IPage<MesCcmBbiDTO> pagingQuery(MesCcmBbiParam param);

  /** 查询详情 */
  MesCcmBbiDTO getCurrentDetail(MesCcmBbiParam param);

  MesCcmBbiDTO getCurrentDetail(Long id);

  /** 新增 */
  Boolean add(MesCcmBbiParam param);

  /** 修改 */
  Boolean modify(MesCcmBbiParam param);

  /** 删除 */
  Boolean remove(MesCcmBbiParam param);

  /**
   * 批量删除 根据ID主键批量删除。
   *
   * @param batchParam [id]
   * @return 删除条数
   */
  Boolean removeBatch(BatchParam batchParam);

  List<MesCcmBbiDTO> getListCompanyNames();
}
