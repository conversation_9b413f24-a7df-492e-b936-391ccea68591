package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备保护试验点位
 *
 * <AUTHOR>
 * @date 2024/10/26 09:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentProtectPoint extends BaseEntity implements Serializable {
  private static final long serialVersionUID = 6602517290640645238L;

  private Long equipmentId;
  /** 设备编码 */
  private String no;
  /** 设备类型名称 */
  private String typeName;
  /** 保护名称 */
  private String protectName;
  /** 保护点位 */
  private String protectPoint;
}
