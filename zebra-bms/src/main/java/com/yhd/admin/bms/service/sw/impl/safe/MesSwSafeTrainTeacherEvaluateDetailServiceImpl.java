package com.yhd.admin.bms.service.sw.impl.safe;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.safe.MesSwSafeTrainTeacherEvaluateDetailDao;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainTeacherEvaluateDetail;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainTeacherEvaluateDetailParam;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainTeacherEvaluateDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 安全培训教师评价详情-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/3/15 9:15
 */
@Service
public class MesSwSafeTrainTeacherEvaluateDetailServiceImpl
    extends ServiceImpl<MesSwSafeTrainTeacherEvaluateDetailDao, MesSwSafeTrainTeacherEvaluateDetail>
    implements MesSwSafeTrainTeacherEvaluateDetailService {
  @Override
  public List<MesSwSafeTrainTeacherEvaluateDetail> queryList(
      MesSwSafeTrainTeacherEvaluateDetailParam param) {
    LambdaQueryWrapper<MesSwSafeTrainTeacherEvaluateDetail> wrapper = new LambdaQueryWrapper<>();
    // 教师评价表主键id列表
    wrapper.in(
        !CollectionUtil.isEmpty(param.getEvaluateIdList()),
        MesSwSafeTrainTeacherEvaluateDetail::getEvaluateId,
        param.getEvaluateIdList());

    return baseMapper.selectList(wrapper);
  }
}
