package com.yhd.admin.passport.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.passport.domain.dto.RoleDTO;
import com.yhd.admin.passport.domain.entity.SysRole;
import com.yhd.admin.passport.domain.query.UserAccountParam;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleService.java
 * @Description TODO 账户角色业务类
 * @createTime 2020年03月20日 10:46:00
 */
public interface RoleService extends IService<SysRole> {

  /**
   * 根据用户查询对应的用户角色
   *
   * @param param {@link UserAccountParam}
   * @return {@link List<RoleDTO>}
   */
  List<RoleDTO> queryRole(UserAccountParam param);

  /**
   * 根据角色查询授权的菜单权限。
   *
   * @param roleList 角色 ID
   * @return 菜单权限集合
   */
  List<String> getAuthority(List<Long> roleList);
}
