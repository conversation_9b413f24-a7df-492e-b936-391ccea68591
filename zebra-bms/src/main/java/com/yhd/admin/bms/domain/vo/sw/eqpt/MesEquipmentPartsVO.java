package com.yhd.admin.bms.domain.vo.sw.eqpt;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 设备健康中心-设备部件管理
 *
 * <AUTHOR>
 * @date 2025/4/24 10:22
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesEquipmentPartsVO extends BaseVO implements Cloneable, Serializable {
    private static final long serialVersionUID = 1750305153288558417L;

    /** 设备id */
    private Long equipmentDataId;
    /** 设备编码+类别 */
    private String equipmentNoType;
    /** 设备名称 */
    private String equipmentName;
    /** 设备编码 */
    private String equipmentNo;
    /** 设备类型 */
    private String equipmentType;
    /** 设备类型代码 */
    private String equipmentTypeCode;
    /** 设备部件名称 */
    private String partsName;
    /** 规格型号 */
    private String model;
    /** 主要参数 */
    private String mainParameter;
    /** 功率（kw） */
    private String power;
    /** 生产厂家 */
    private String factory;
    /** 生产日期(yyyy-MM-dd) */
    private LocalDate produceDate;
    /** 更换日期 */
    private LocalDate changeDate;
    /** 国产进口 */
    private String domesticImports;
}
