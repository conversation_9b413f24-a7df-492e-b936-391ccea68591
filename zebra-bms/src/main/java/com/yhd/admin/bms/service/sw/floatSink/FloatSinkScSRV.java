package com.yhd.admin.bms.service.sw.floatSink;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.floatSink.MesFloatSinkSc;

import java.util.List;

/**
 * 可选性曲线坐标数据计算表;
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022-7-5
 */
public interface FloatSinkScSRV extends IService<MesFloatSinkSc> {

  /***
   * 根据实验ID删除
   * @param expId
   * @return
   */
  Boolean removeByExpId(String expId);

  List<MesFloatSinkSc> getFloatSinkScSRVListByExpId(String expId);
}
