package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesSwUnsafeLedgerDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwUnsafeLedger;
import com.yhd.admin.bms.domain.query.sw.MesSwUnsafeLedgerParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwUnsafeLedgerVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesSwUnsafeLedgerConvert {

  MesSwUnsafeLedger toEntity(MesSwUnsafeLedgerParam param);

  MesSwUnsafeLedgerDTO toDTO(MesSwUnsafeLedger classes);

  MesSwUnsafeLedgerVO toVO(MesSwUnsafeLedgerDTO classesDTO);
}
