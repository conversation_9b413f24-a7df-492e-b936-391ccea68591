package com.yhd.admin.scada.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/8/12 10:03
 */

@Component
@ConfigurationProperties(value = "modbus")
@Data
public class ModbusCfg {
    private String ip;

    private Integer port;

    private Integer slaveId;

    private Long sleepTime;
}
