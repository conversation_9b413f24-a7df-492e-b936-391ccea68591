package com.yhd.admin.bms.domain.dto.sw.dashboard;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备运行看板
 *
 * <AUTHOR>
 * @date 2025/01/09 10:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesEquipmentRunningDTO extends BaseDTO implements Serializable {
  /** 单位 */
  private String unit;
  /** 系统 */
  private String system;
  /** 开始时间 */
  private LocalDateTime startTime;
  /** 结束时间 */
  private LocalDateTime endTime;
  /** 持续时间 */
  private BigDecimal duration;
  /** 状态 */
  private String status;
  /** 甘特图开始 0.00~24 */
  private BigDecimal start;
  /** 甘特图结束 0.00~24 */
  private BigDecimal end;
}
