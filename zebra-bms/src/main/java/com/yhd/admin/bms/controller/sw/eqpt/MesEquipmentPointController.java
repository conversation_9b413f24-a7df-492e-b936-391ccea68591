package com.yhd.admin.bms.controller.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesEquipmentPointConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesEquipmentPointDTO;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesEquipmentPointParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesEquipmentPointVO;
import com.yhd.admin.bms.service.sw.eqpt.MesEquipmentPointService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 设备健康中心-设备点位管理
 *
 * <AUTHOR>
 * @date 2025/4/23 10:22
 */
@RestController
@RequestMapping(value = "/equipment/points")
public class MesEquipmentPointController {
    @Resource
    private MesEquipmentPointService mesEquipmentPointService;

    @Resource
    private MesEquipmentPointConvert mesEquipmentPointConvert;

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<?> pagingQuery(@RequestBody MesEquipmentPointParam param) {
    IPage<MesEquipmentPointDTO> page = mesEquipmentPointService.pagingQuery(param);
    return new PageRespJson<>(page.convert(mesEquipmentPointConvert::toVO));
  }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesEquipmentPointVO> getCurrentDetail(@RequestBody MesEquipmentPointParam param) {
        return RespJson.buildSuccessResponse(mesEquipmentPointConvert.toVO(mesEquipmentPointService.getCurrentDetail(param)));
    }



  @SysLogs(title = "新增或修改设备点位", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> addOrModify(@RequestBody MesEquipmentPointParam param) {
    Boolean retVal = mesEquipmentPointService.addOrModify(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

    @SysLogs(title = "校验地址是否唯一", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/isOnly",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> isOnly(@RequestBody MesEquipmentPointParam param) {
        Boolean retVal = mesEquipmentPointService.isOnly(param);
        RespJson respJson = new RespJson();
        respJson.setStatus("ok");
        if (retVal) {
            respJson.setMessage("成功");
            respJson.setData(true);
        }else {
            respJson.setMessage(param.getPointAddress() + "设备地址已存在");
            respJson.setData(false);
        }
        return respJson;
    }

  @SysLogs(title = "删除设备点位", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> removeBatch(@RequestBody MesEquipmentPointParam param) {
    Boolean retVal = mesEquipmentPointService.removeBatch(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }
}
