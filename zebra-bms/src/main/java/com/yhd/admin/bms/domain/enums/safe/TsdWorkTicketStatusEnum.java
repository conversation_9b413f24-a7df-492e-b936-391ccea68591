package com.yhd.admin.bms.domain.enums.safe;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** 外施工单位停送电工作票状态枚举类 */
@Getter
public enum TsdWorkTicketStatusEnum {
  //    '工作票状态code：01停电审批中、02停电审批通过、03送电审批中、04送电审批通过、05已作废',
  TD_SPZ("01", "停电审批中"),
  TD_WC("02", "停电审批通过"),
  SD_SPZ("03", "送电审批中"),
  SD_WC("04", "送电审批通过"),
  ZF("05", "已作废"),
  ;

  private final String code;
  private final String desc;

  TsdWorkTicketStatusEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static List<String> getDescList() {
    TsdWorkTicketStatusEnum[] values = TsdWorkTicketStatusEnum.values();

    return Arrays.stream(values).map(TsdWorkTicketStatusEnum::getDesc).collect(Collectors.toList());
  }

  public static List<String> getCodeList() {
    TsdWorkTicketStatusEnum[] values = TsdWorkTicketStatusEnum.values();

    return Arrays.stream(values).map(TsdWorkTicketStatusEnum::getCode).collect(Collectors.toList());
  }

  public static TsdWorkTicketStatusEnum getEnumByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    TsdWorkTicketStatusEnum[] values = TsdWorkTicketStatusEnum.values();
    for (TsdWorkTicketStatusEnum value : values) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public static TsdWorkTicketStatusEnum getEnumByName(String name) {
    if (StringUtils.isBlank(name)) {
      return null;
    }
    TsdWorkTicketStatusEnum[] values = TsdWorkTicketStatusEnum.values();
    for (TsdWorkTicketStatusEnum value : values) {
      if (value.getDesc().equals(name)) {
        return value;
      }
    }
    return null;
  }
}
