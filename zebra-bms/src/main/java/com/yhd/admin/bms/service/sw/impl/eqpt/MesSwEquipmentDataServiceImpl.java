package com.yhd.admin.bms.service.sw.impl.eqpt;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.zxing.WriterException;
import com.yhd.admin.bms.config.zxing.QRCodeGenerator;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentDataDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentDataConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentDataDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentUserDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesEquipmentParts;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentData;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentProtectPoint;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesEquipmentPartsParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentDataParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentProtectPointParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.eqpt.MesEquipmentPartsService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentDataService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentProtectPointService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.StorageServices;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.poi.excel.CellBuilder;
import com.yhd.admin.common.poi.excel.ExcelBuilder;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

/**
 * 设备资料-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2023/10/10 10:38
 */
@Service
@Log4j2
public class MesSwEquipmentDataServiceImpl
    extends ServiceImpl<MesSwEquipmentDataDao, MesSwEquipmentData>
    implements MesSwEquipmentDataService {
  private static final Logger logger = LoggerFactory.getLogger(MesSwEquipmentDataServiceImpl.class);
  private static final String defaultBucketName = "swcenter";

  @Resource private MesSwEquipmentDataConvert equipmentDataConvert;

  @Resource private DicService dicService;

  @Resource private UserAccountService accountService;

  @Resource private MesSwEquipmentProtectPointService pointService;

  @Resource private MesEquipmentPartsService partsService;

  @Resource private StorageServices storageServices;

  @Resource private QRCodeGenerator qRCodeGenerator;

  // 配置HTTP连接池（静态初始化）
  private static final RestTemplate httpRestTemplate = createRestTemplate();

  @Override
  public IPage<MesSwEquipmentDataDTO> pagingQuery(MesSwEquipmentDataParam param) {
    logger.debug("请求【根据条件查询设备资料分页列表】接口开始，参数：{}", param);
    Page<MesSwEquipmentData> page = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MesSwEquipmentData> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 参数处理
    // 设备名称
    queryChain.like(
        StringUtils.isNotBlank(param.getName()), MesSwEquipmentData::getName, param.getName());
    // 设备编码
    queryChain.like(
        StringUtils.isNotBlank(param.getNo()), MesSwEquipmentData::getNo, param.getNo());
    // // 所属系统
    // queryChain.like(
    //     StringUtils.isNotBlank(param.getSysName()),
    //     MesSwEquipmentData::getSysName,
    //     param.getSysName());
    // // 生产厂家
    // queryChain.like(
    //     StringUtils.isNotBlank(param.getFactory()),
    //     MesSwEquipmentData::getFactory,
    //     param.getFactory());
    // // 设备状态
    // queryChain.like(
    //     StringUtils.isNotBlank(param.getStatusName()),
    //     MesSwEquipmentData::getStatusName,
    //     param.getStatusName());
    // 设备类型-字典code
    queryChain.eq(
        StringUtils.isNotBlank(param.getTypeCode()),
        MesSwEquipmentData::getTypeCode,
        param.getTypeCode());
    // // 使用单位
    // queryChain.like(
    //     StringUtils.isNotBlank(param.getOrgName()),
    //     MesSwEquipmentData::getOrgName,
    //     param.getOrgName());

    // 排序
    queryChain.orderByDesc(MesSwEquipmentData::getCreatedTime);

    return queryChain.page(page).convert(equipmentDataConvert::toDTO);
  }

  @Override
  public List<MesSwEquipmentDataDTO> queryList(MesSwEquipmentDataParam param) {
    logger.debug("请求【根据条件查询设备资料列表】接口开始，参数：{}", param);
    LambdaQueryWrapper<MesSwEquipmentData> wrapper = new LambdaQueryWrapper<>();
    // 设备编码类别关键词模糊查询
    if (StringUtils.isNotBlank(param.getNoTypeKey())) {
      wrapper.and(
          w ->
              w.like(MesSwEquipmentData::getNo, param.getNoTypeKey())
                  .or()
                  .like(MesSwEquipmentData::getTypeName, param.getNoTypeKey()));
    }
    wrapper.eq(
        StringUtils.isNotBlank(param.getTypeCode()),
        MesSwEquipmentData::getTypeCode,
        param.getTypeCode());
    wrapper.orderByDesc(MesSwEquipmentData::getCreatedTime);

    return equipmentDataConvert.toDTO(baseMapper.selectList(wrapper));
  }

  @Override
  public MesSwEquipmentDataDTO getCurrentDetail(MesSwEquipmentDataParam param) {
    logger.debug("请求【查询设备资料详情】接口开始，参数：{}", param.getId());
    MesSwEquipmentData equipmentData = this.getById(param.getId());
    logger.debug("设备资料主键id:{}，【查询设备资料详情】，响应数据：{}", param.getId(), equipmentData);
    MesSwEquipmentDataDTO dto = equipmentDataConvert.toDTO(equipmentData);

    dto.setWorkShopCodeList(StrSplitter.split(equipmentData.getWorkShopCode(), ',', 0, true, true));
    dto.setWorkShopNameList(StrSplitter.split(equipmentData.getWorkShopName(), ',', 0, true, true));

    dto.setCharterPersonList(
        JSONUtil.toList(equipmentData.getCharterPerson(), MesSwEquipmentUserDTO.class));

    LambdaQueryWrapper<MesSwEquipmentProtectPoint> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentProtectPoint::getEquipmentId, param.getId());
    List<MesSwEquipmentProtectPoint> pointList = pointService.list(wrapper);
    dto.setPointList(equipmentDataConvert.toPointListDTO(pointList));
    return dto;
  }

  @Override
  public MesSwEquipmentDataDTO getDetailByNo(String no) {
    if (StringUtils.isBlank(no)) {
      logger.error("请求参数不为空");
      return null;
    }
    LambdaQueryWrapper<MesSwEquipmentData> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentData::getNo, no);

    return equipmentDataConvert.toDTO(baseMapper.selectOne(wrapper));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MesSwEquipmentDataParam param) {
    logger.debug("请求【新增设备资料】接口开始，表单参数：{}", param);
    getData(param);
    MesSwEquipmentData entity = equipmentDataConvert.toEntity(param);
    boolean result = this.save(entity);
    List<MesSwEquipmentProtectPointParam> pointList = param.getPointList();
    if (CollectionUtil.isNotEmpty(pointList)) {
      String protectShopCode = param.getProtectShopCode();
      String protectShopName = param.getProtectShopName();
      String powerStatus = param.getPowerStatus();
      if (StringUtils.isBlank(protectShopCode) || StringUtils.isBlank(protectShopName)) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), "有了保护点位，保护试验车间字段需必填，请检查！");
      }
      pointList.stream()
          .forEach(
              e -> {
                e.setEquipmentId(entity.getId());
                e.setNo(entity.getNo());
                e.setTypeName(entity.getTypeName());
                if (StringUtils.isBlank(e.getProtectName())
                    || StringUtils.isBlank(e.getProtectPoint())) {
                  throw new BMSException(ResultStateEnum.FAIL.getCode(), "保护点位的字段为空，请检查！");
                }
              });
    }
    pointService.saveBatch(equipmentDataConvert.toPointListEntity(pointList));
    return result;
  }

  /**
   * 塞值字典项、人员姓名
   *
   * @param param
   */
  private void getData(MesSwEquipmentDataParam param) {
    // 设备类型
    String typeName = dicService.transform(DicConstant.DEVICE_TYPE, param.getTypeCode());
    if (StringUtils.isBlank(typeName)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), param.getTypeCode() + "设备类型找不到，请检查！");
    }
    param.setTypeName(typeName);
    // 设备分类
    String classificationName =
        dicService.transform(DicConstant.DEVICE_CLASSIFICATION, param.getClassificationCode());
    if (StringUtils.isBlank(classificationName)) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(), param.getClassificationCode() + "设备分类找不到，请检查！");
    }
    param.setClassificationName(classificationName);
    // 保护试验车间
    if (StringUtils.isNotBlank(param.getProtectShopCode())) {
      String protectShopName =
          dicService.transform(DicConstant.DIC_PROTECT_SHOP_CODE, param.getProtectShopCode());
      if (StringUtils.isBlank(protectShopName)) {
        throw new BMSException(
            ResultStateEnum.FAIL.getCode(), param.getClassificationCode() + "保护试验车间找不到，请检查！");
      }
      param.setProtectShopName(protectShopName);
    }

    // 所属车间
    List<String> workShopCodeList = param.getWorkShopCodeList();
    StringBuffer workShopName = new StringBuffer();
    if (CollUtil.isNotEmpty(workShopCodeList)) {
      for (String item : workShopCodeList) {
        if (workShopName.length() > 0) {
          workShopName.append(",");
        }
        String workshop = dicService.transform(DicConstant.WORK_SHOP, item);
        if (StringUtils.isBlank(workshop)) {
          throw new BMSException(ResultStateEnum.FAIL.getCode(), item + "所属车间找不到，请检查！");
        }
        workShopName.append(workshop);
      }
    }
    if (workShopName.length() == 0) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), workShopCodeList + "所属车间找不到，请检查！");
    }
    param.setWorkShopName(workShopName.toString());
    // 拼接成逗号串存储
    param.setWorkShopCode(CollUtil.join(workShopCodeList, ","));
    // 包机人，获取姓名
    List<MesSwEquipmentUserDTO> charterPersonList = param.getCharterPersonList();
    if (CollUtil.isEmpty(charterPersonList)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "包机人为空，请检查！");
    }
    for (MesSwEquipmentUserDTO dto : charterPersonList) {
      String name = accountService.getNameByUserName(dto.getAccount());
      if (StringUtils.isBlank(name)) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), dto.getAccount() + "账号人员找不到，请检查！");
      }
      dto.setName(name);
    }
    param.setCharterPerson(JSONUtil.toJsonStr(charterPersonList));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean modify(MesSwEquipmentDataParam param) {
    logger.debug("请求【编辑设备资料】接口开始，表单参数：{}", param);
    getData(param);

    LambdaQueryWrapper<MesSwEquipmentProtectPoint> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentProtectPoint::getEquipmentId, param.getId());
    pointService.remove(wrapper);

    List<MesSwEquipmentProtectPointParam> pointList = param.getPointList();
    if (CollectionUtil.isNotEmpty(pointList)) {
      String protectShopCode = param.getProtectShopCode();
      String protectShopName = param.getProtectShopName();
      String powerStatus = param.getPowerStatus();
      if (StringUtils.isBlank(protectShopCode) || StringUtils.isBlank(protectShopName)) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), "有了保护点位，保护试验车间字段需必填，请检查！");
      }
      pointList.stream()
          .forEach(
              e -> {
                e.setEquipmentId(param.getId());
                e.setNo(param.getNo());
                if (StringUtils.isBlank(e.getProtectName())
                    || StringUtils.isBlank(e.getProtectPoint())) {
                  throw new BMSException(ResultStateEnum.FAIL.getCode(), "保护点位的字段为空，请检查！");
                }
              });
    }
    pointService.saveBatch(equipmentDataConvert.toPointListEntity(pointList));
    return this.updateById(equipmentDataConvert.toEntity(param));
  }

  @Override
  public Boolean removeBatch(MesSwEquipmentDataParam param) {
    logger.debug("请求【删除设备资料】接口开始，表单参数：{}", param.getIds());
    return this.removeByIds(param.getIds());
  }

  @Override
  public List<String> queryNoList(MesSwEquipmentDataParam param) {
    LambdaQueryWrapper<MesSwEquipmentData> wrapper = new LambdaQueryWrapper<>();
    wrapper.orderByDesc(MesSwEquipmentData::getCreatedTime);
    List<MesSwEquipmentData> list = this.list(wrapper);
    List<String> nos = new ArrayList<>();
    if (CollectionUtil.isNotEmpty(list)) {
      nos = list.stream().map(MesSwEquipmentData::getNo).collect(Collectors.toList());
    }
    return nos;
  }

  @Override
  public String export() {
    // 设备
    MesSwEquipmentDataParam param = new MesSwEquipmentDataParam();
    List<MesSwEquipmentDataDTO> datas = this.queryList(param);
    // 部件
    MesEquipmentPartsParam partsParam = new MesEquipmentPartsParam();
    partsParam.setExcludeEquipment(true);
    List<MesEquipmentParts> parts = partsService.queryList(partsParam);
    if (CollectionUtil.isEmpty(datas)) {
      throw new BMSException("error", "无数据可导出");
    }
    String fileName = StrUtil.format("{}{}", "设备台账", ".xls");
    HSSFWorkbook wb = null;
    ByteArrayInputStream inputStream;

    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      Map<String, String> headers = new HashMap<>();
      headers.put("Content-Type", "application/vnd.ms-excel");
      wb = new HSSFWorkbook();
      HSSFSheet wbSheet = wb.createSheet("设备台账");
      AtomicInteger rowNum = new AtomicInteger();
      HSSFCellStyle titleStyle = ExcelBuilder.createGrayHeadCellStyle(wb);
      HSSFCellStyle contentStyle = ExcelBuilder.createContentCellStyle(wb);
      // 第1行，标题设置
      HSSFRow row0 = wbSheet.createRow(rowNum.getAndIncrement());
      row0.setHeight((short) 500); // 设置行高
      HSSFCell row0Cell = row0.createCell(0);
      row0Cell.setCellValue("上湾设备台账");
      row0Cell.setCellStyle(titleStyle);
      // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
      wbSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 18));
      // 第2行
      HSSFRow row1 = wbSheet.createRow(rowNum.getAndIncrement());
      row1.setHeight((short) 500);
      List<String> list =
          Stream.of(
                  "设备类型",
                  "使用单位",
                  "对象代码\n" + "(SAP)",
                  "设备编号",
                  "部件名称",
                  "规格型号",
                  "主要参数",
                  "功率/KW",
                  "序列号",
                  "生产厂家",
                  "生产日期",
                  "使用日期",
                  "设备状态",
                  "所属系统",
                  "地理位置",
                  "国产/进口",
                  "备注",
                  "设备分类",
                  "设备二维码",
                  "设备图片")
              .collect(Collectors.toList());
      for (int i = 0; i < list.size(); i++) {
        CellBuilder.build(row1, i, list.get(i), titleStyle);
      }
      // 设置列宽
      for (int i = 0; i < list.size(); i++) {
        wbSheet.setColumnWidth(i, wbSheet.getColumnWidth(i) * 2);
        // 主要参数列单独加宽
        if (i == 6) {
          wbSheet.setColumnWidth(i, wbSheet.getColumnWidth(i) * 4);
        }
        // 二维码列宽
        if (list.get(i).contains("二维码")) {
          wbSheet.setColumnWidth(i, 20 * 256);
        }
        // 图片列宽
        if (list.get(i).contains("图片")) {
          wbSheet.setColumnWidth(i, 25 * 256);
        }
      }
      if (!CollectionUtil.isEmpty(datas)) {
        HSSFWorkbook finalWb = wb;
        datas.forEach(
            d -> {
              HSSFRow tempRow = wbSheet.createRow(rowNum.getAndIncrement());
              tempRow.setHeight((short) 2000);
              // 设备类型
              CellBuilder.build(tempRow, 0, d.getTypeName(), contentStyle);
              // 使用单位
              CellBuilder.build(tempRow, 1, d.getOrgName(), contentStyle);
              // 对象代码
              CellBuilder.build(tempRow, 2, d.getObjectCode(), contentStyle);
              // 设备编号
              CellBuilder.build(tempRow, 3, d.getNo(), contentStyle);
              // 部件名称
              CellBuilder.build(tempRow, 4, d.getName(), contentStyle);
              // 规格型号
              CellBuilder.build(tempRow, 5, d.getModel(), contentStyle);
              // 主要参数
              CellBuilder.build(tempRow, 6, d.getMainParameter(), contentStyle);
              // 功率
              CellBuilder.build(tempRow, 7, d.getPower(), contentStyle);
              // 序列号
              CellBuilder.build(tempRow, 8, d.getSn(), contentStyle);
              // 生产厂家
              CellBuilder.build(tempRow, 9, d.getFactory(), contentStyle);
              // 生产日期
              CellBuilder.build(
                  tempRow,
                  10,
                  Objects.isNull(d.getProduceDate()) ? "" : d.getProduceDate().toString(),
                  contentStyle);
              // 使用日期
              CellBuilder.build(
                  tempRow,
                  11,
                  Objects.isNull(d.getUseDate()) ? "" : d.getUseDate().toString(),
                  contentStyle);
              // 设备状态
              CellBuilder.build(tempRow, 12, d.getStatusName(), contentStyle);
              // 所属系统
              CellBuilder.build(tempRow, 13, d.getSysName(), contentStyle);
              // 地理位置
              CellBuilder.build(tempRow, 14, d.getLocation(), contentStyle);
              // 国产/进口
              CellBuilder.build(tempRow, 15, d.getDomesticImports(), contentStyle);
              // 备注
              CellBuilder.build(tempRow, 16, d.getRemark(), contentStyle);
              // 设备分类
              CellBuilder.build(tempRow, 17, d.getClassificationName(), contentStyle);
              // 二维码
              try {
                // 获取二维码需要的参数
                JSONObject json = new JSONObject();
                json.put("type", "设备履历扫码");
                json.put("equipmentDataId", d.getId());
                json.put("equipmentName", d.getName());
                json.put("equipmentNoType", d.getNo() + " " + d.getName());
                json.put("no", d.getNo());
                json.put("equipmentType", d.getTypeName());
                // 生成二维码
                byte[] image = qRCodeGenerator.generateQRCodeImage(json.toJSONString(), 130, 130);
                // 创建绘图对象
                Drawing<?> drawing = wbSheet.createDrawingPatriarch();
                // xls文件使用HSSFClientAnchor
                HSSFClientAnchor anchor =
                    new HSSFClientAnchor(
                        0, 0, 0, 0, (short) 18, rowNum.get() - 1, (short) 19, rowNum.get());
                drawing.createPicture(anchor, finalWb.addPicture(image, Workbook.PICTURE_TYPE_PNG));
              } catch (WriterException | IOException e) {
                throw new BMSException(ResultStateEnum.FAIL.getCode(), e.getMessage());
              }
              // 设备对应部件
              List<MesEquipmentParts> correlationParts =
                  parts.stream()
                      .filter(p -> p.getEquipmentDataId().equals(d.getId()))
                      .collect(Collectors.toList());
              if (CollectionUtil.isNotEmpty(correlationParts)) {
                correlationParts.forEach(
                    p -> {
                      HSSFRow tempRow1 = wbSheet.createRow(rowNum.getAndIncrement());
                      tempRow1.setHeight((short) 2000);
                      // 设备类型
                      CellBuilder.build(tempRow1, 0, p.getEquipmentType(), contentStyle);
                      // 使用单位
                      CellBuilder.build(tempRow1, 1, d.getOrgName(), contentStyle);
                      // 对象代码
                      CellBuilder.build(tempRow1, 2, "", contentStyle);
                      // 设备编号
                      CellBuilder.build(tempRow1, 3, p.getEquipmentNo(), contentStyle);
                      // 部件名称
                      CellBuilder.build(tempRow1, 4, p.getPartsName(), contentStyle);
                      // 规格型号
                      CellBuilder.build(tempRow1, 5, p.getModel(), contentStyle);
                      // 主要参数
                      CellBuilder.build(tempRow1, 6, p.getMainParameter(), contentStyle);
                      // 功率
                      CellBuilder.build(tempRow1, 7, p.getPower(), contentStyle);
                      // 序列号
                      CellBuilder.build(tempRow1, 8, "", contentStyle);
                      // 生产厂家
                      CellBuilder.build(tempRow1, 9, p.getFactory(), contentStyle);
                      // 生产日期
                      CellBuilder.build(tempRow1, 10, "", contentStyle);
                      // 使用日期
                      CellBuilder.build(
                          tempRow1,
                          11,
                          Objects.isNull(p.getChangeDate()) ? "" : p.getChangeDate().toString(),
                          contentStyle);
                      // 设备状态
                      CellBuilder.build(tempRow1, 12, d.getStatusName(), contentStyle);
                      // 所属系统
                      CellBuilder.build(tempRow1, 13, d.getSysName(), contentStyle);
                      // 位置
                      CellBuilder.build(tempRow1, 14, d.getLocation(), contentStyle);
                      // 国产/进口
                      CellBuilder.build(tempRow1, 15, p.getDomesticImports(), contentStyle);
                      // 备注
                      CellBuilder.build(tempRow1, 16, "", contentStyle);
                      // 设备分类
                      CellBuilder.build(tempRow1, 17, "", contentStyle);
                    });
              }
            });
      }
      wb.write(outputStream);
      inputStream = new ByteArrayInputStream(outputStream.toByteArray());
      storageServices.uploadObject(defaultBucketName, fileName, headers, inputStream);
      // 关闭文件流
      IOUtils.closeQuietly(inputStream);
    } catch (Exception e) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), e.getMessage());
    } finally {
      if (wb != null) {
        IOUtils.closeQuietly(wb);
      }
    }
    return storageServices.getStorageUrl(fileName, defaultBucketName);
  }

  private static RestTemplate createRestTemplate() {
    PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
    connManager.setMaxTotal(200); // 最大连接数
    connManager.setDefaultMaxPerRoute(50); // 每个路由最大连接数
    connManager.setValidateAfterInactivity(5000); // 5秒空闲后验证连接

    CloseableHttpClient httpClient =
        HttpClientBuilder.create()
            .setConnectionManager(connManager)
            .setConnectionTimeToLive(30, TimeUnit.SECONDS)
            .setDefaultRequestConfig(
                RequestConfig.custom()
                    .setConnectTimeout(5000) // 连接超时5秒
                    .setSocketTimeout(15000) // 读取超时15秒
                    .build())
            .setUserAgent("ExcelImageDownloader/1.0")
            .disableCookieManagement() // 禁用Cookie管理
            .build();

    return new RestTemplate(new HttpComponentsClientHttpRequestFactory(httpClient));
  }

  /** 创建线程池（根据数据量动态调整） */
  private ExecutorService createImageThreadPool(int dataSize) {
    int availableProcessors = Runtime.getRuntime().availableProcessors();
    int corePoolSize = Math.min(4, availableProcessors); // 保守设置核心线程数
    int maxPoolSize = Math.min(8, availableProcessors * 2); // 最大线程数
    int queueSize = Math.min(dataSize * 2, 500); // 队列大小

    return new ThreadPoolExecutor(
        corePoolSize,
        maxPoolSize,
        30,
        TimeUnit.SECONDS,
        new ArrayBlockingQueue<>(queueSize),
        new NamedThreadFactory("img-downloader"),
        new BlockingCallerRunsPolicy() // 自定义阻塞策略
        );
  }

  /** 自定义线程工厂（添加线程命名） */
  private static class NamedThreadFactory implements ThreadFactory {
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final String namePrefix;

    NamedThreadFactory(String namePrefix) {
      this.namePrefix = namePrefix + "-thread-";
    }

    public Thread newThread(Runnable r) {
      return new Thread(r, namePrefix + threadNumber.getAndIncrement());
    }
  }

  /** 自定义阻塞策略（防止任务丢失） */
  private static class BlockingCallerRunsPolicy implements RejectedExecutionHandler {
    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
      if (!executor.isShutdown()) {
        try {
          // 尝试将任务放入队列（最多等待2秒）
          if (!executor.getQueue().offer(r, 2, TimeUnit.SECONDS)) {
            log.warn("任务等待超时，由调用线程执行");
            r.run();
          }
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
          log.warn("任务提交被中断");
        }
      }
    }
  }

    @Override
    public List<String> queryType() {
        List<MesSwEquipmentData> dataList = this.list();
        return dataList.stream()
            .map(dto -> dto.getTypeName().trim())
            .filter(name -> !name.isEmpty()).distinct().collect(Collectors.toList());
    }

}
