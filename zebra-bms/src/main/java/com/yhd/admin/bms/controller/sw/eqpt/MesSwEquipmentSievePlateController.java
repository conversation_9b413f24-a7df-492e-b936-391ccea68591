package com.yhd.admin.bms.controller.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentSievePlateConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.*;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentSievePlateParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentSievePlateRecordParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentSievePlateSumVO;
import com.yhd.admin.bms.domain.vo.sys.MesSwChartVO;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentSievePlateRecordService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentSievePlateService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
@RestController
@RequestMapping(value = "/equipment/sievePlate")
public class MesSwEquipmentSievePlateController
    extends BaseController<MesSwEquipmentSievePlateConvert, MesSwEquipmentSievePlateService> {

    public MesSwEquipmentSievePlateController(
        MesSwEquipmentSievePlateConvert convert, MesSwEquipmentSievePlateService service) {
        super(convert, service);
    }

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private MesSwEquipmentSievePlateRecordService recordService;

    @PostMapping(
        value = "/getPlateList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getPlateList(@RequestBody MesSwEquipmentSievePlateParam param) {
        return RespJson.buildSuccessResponse(service.getPlateList(param));
    }

    /**
     * 筛板管理-列表页筛机统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/getQueryCount",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getQueryCount(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            MesSwEquipmentSievePlateCountDTO dto = service.getQueryCount(param);
            return RespJson.buildSuccessResponse(convert.toVO(dto));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 筛板管理的列表
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwEquipmentSievePlateDTO> pagingQuery(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        IPage<MesSwEquipmentSievePlateDTO> page = service.pagingQuery(param);
        return new PageRespJson<>(page);
    }

    /**
     * 按材质
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/pagingQueryByMaterial",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwEquipmentSievePlateDTO> pagingQueryByMaterial(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        IPage<MesSwEquipmentSievePlateDTO> page = service.pagingQueryByMaterial(param);
        return new PageRespJson<>(page);
    }

    /**
     * 按孔径
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/pagingQueryByAperture",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwEquipmentSievePlateDTO> pagingQueryByAperture(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        IPage<MesSwEquipmentSievePlateDTO> page = service.pagingQueryByAperture(param);
        return new PageRespJson<>(page);
    }

    /**
     * 使用情况-孔径
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/getEqptByAperture",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getEqptByAperture(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            List<MesSwEquipmentSievePlateApertureDTO> dtos = service.getEqptByAperture(param);
            return RespJson.buildSuccessResponse(convert.toApertureListVO(dtos));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 筛面名称的下拉集合
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/getAllPlateList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentSievePlateDTO>> getPlateNameList(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            List<MesSwEquipmentSievePlateDTO> result = service.getAllPlateList(param);
            return RespJson.buildSuccessResponse(result);
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 新增筛面
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/saveAdd",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> saveAdd(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            Boolean retVal = service.saveAdd(param);
            return retVal
                ? RespJson.buildSuccessResponse(Boolean.TRUE)
                : RespJson.buildFailureResponse("");
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 编辑筛面
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/modifyPlate",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modifyPlate(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            Boolean retVal = service.modifyPlate(param);
            return retVal
                ? RespJson.buildSuccessResponse(Boolean.TRUE)
                : RespJson.buildFailureResponse("");
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 删除筛面
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/removePlate",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removePlate(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            Boolean retVal = service.removePlate(param);
            return retVal
                ? RespJson.buildSuccessResponse(Boolean.TRUE)
                : RespJson.buildFailureResponse("");
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            return RespJson.buildSuccessResponse(service.getCurrentDetail(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/saveRecord",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> saveRecord(@RequestBody MesSwEquipmentSievePlateRecordParam param) {
        // 物资goodsId
        String materialCode = param.getMaterialCode();
        if (StringUtils.isBlank(materialCode)) {
            return RespJson.buildFailureResponse("参数为空，请检查！");
        }
        RLock lock = redissonClient.getLock(materialCode);
        try {
            if (lock.tryLock()) {
                Boolean retVal = service.saveRecord(param);
                return retVal
                    ? RespJson.buildSuccessResponse(Boolean.TRUE)
                    : RespJson.buildFailureResponse("");
            } else {
                return RespJson.buildFailureResponse("该物资正在被其他筛板更换，请稍后重新提交下！");
            }
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @PostMapping(
        value = "/saveRecords",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> saveRecords(@RequestBody MesSwEquipmentSievePlateRecordParam param) {
        // 物资goodsId
        String materialCode = param.getMaterialCode();
        if (StringUtils.isBlank(materialCode)) {
            return RespJson.buildFailureResponse("参数为空，请检查！");
        }
        RLock lock = redissonClient.getLock(materialCode);
        try {
            if (lock.tryLock()) {
                Boolean retVal = service.saveRecords(param);
                return retVal
                    ? RespJson.buildSuccessResponse(Boolean.TRUE)
                    : RespJson.buildFailureResponse("");
            } else {
                return RespJson.buildFailureResponse("该物资正在被其他筛板更换，请稍后重新提交下！");
            }
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @PostMapping(
        value = "/getRecordList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getRecordList(@RequestBody MesSwEquipmentSievePlateRecordParam param) {
        try {
            return RespJson.buildSuccessResponse(service.getRecordList(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/exportExcel",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> exportExcel(@RequestBody MesSwEquipmentSievePlateRecordParam param) {
        try {
            return RespJson.buildSuccessResponse(service.exportExcel(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/remove",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> remove(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            Boolean retVal = service.remove(param);
            return retVal
                ? RespJson.buildSuccessResponse(Boolean.TRUE)
                : RespJson.buildFailureResponse("");
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 当前筛板使用情况
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/singlePlate",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentSievePlateSumVO>> singlePlate(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        return RespJson.buildSuccessResponse(service.singlePlate(param));
    }

    /**
     * 全部筛板使用情况
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/allPlate",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentSievePlateSumVO>> allPlate(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        return RespJson.buildSuccessResponse(service.allPlate(param));
    }

    /**
     * 筛板消耗量统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/consumeTotal",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwChartVO>> consumeTotal(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        return RespJson.buildSuccessResponse(service.consumeTotal(param));
    }

    /**
     * 筛板入库量统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/inputTotal",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwChartVO>> inputTotal(@RequestBody MesSwEquipmentSievePlateRecordParam param) {
        return RespJson.buildSuccessResponse(service.inputTotal(param));
    }

    /**
     * 筛板寿命统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/statisticsScrapPlate",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentSievePlateRecordDTO>> statisticsScrapPlate(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        return RespJson.buildSuccessResponse(recordService.statisticsScrapPlate(param));
    }

    /**
     * 筛上量-按材质统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/categoryTotal",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwChartVO>> categoryTotal(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        return RespJson.buildSuccessResponse(service.categoryTotal(param));
    }

    /**
     * 筛上量-按孔径统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/apertureTotal",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwChartVO>> apertureTotal(
        @RequestBody MesSwEquipmentSievePlateParam param) {
        return RespJson.buildSuccessResponse(service.apertureTotal(param));
    }

    /**
     * 导出全部筛板更换记录
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/exportAllExcel",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> exportAllExcel(@RequestBody MesSwEquipmentSievePlateRecordParam param) {
        try {
            return RespJson.buildSuccessResponse(service.exportAllExcel(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 筛板更换记录查询
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/pagingQueryRecord",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwEquipmentSievePlateRecordDTO> pagingQueryRecord(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        IPage<MesSwEquipmentSievePlateRecordDTO> page = recordService.pagingQuery(param);
        return new PageRespJson<>(page);
    }

    /**
     * 筛板更换记录-列表页更换原因统计
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/getQueryRecordCount",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getQueryRecordCount(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        try {
            MesSwEquipmentSievePlateRecordCountDTO dto = recordService.getQueryRecordCount(param);
            return RespJson.buildSuccessResponse(convert.toVO(dto));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/exportPlateChart",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> exportPlateChart(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            return RespJson.buildSuccessResponse(service.exportPlateChart(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/exportEqptByAperture",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> exportEqptByAperture(@RequestBody MesSwEquipmentSievePlateParam param) {
        try {
            return RespJson.buildSuccessResponse(service.exportEqptByAperture(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    /**
     * 获取当前正在使用的材质及尺寸集合
     *
     * @param param
     * @return
     */
    @PostMapping(
        value = "/getListByMaterial",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentSievePlateRecordDTO>> getListByMaterial(
        @RequestBody MesSwEquipmentSievePlateRecordParam param) {
        return RespJson.buildSuccessResponse(recordService.getListByMaterial(param));
    }
}
