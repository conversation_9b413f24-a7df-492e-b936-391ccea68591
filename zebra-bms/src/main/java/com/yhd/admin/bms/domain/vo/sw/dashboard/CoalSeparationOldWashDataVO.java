package com.yhd.admin.bms.domain.vo.sw.dashboard;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 智能分选-旧主洗数据VO
 *
 * <AUTHOR>
 * @date 2024/10/1 10:06
 */
@Data
public class CoalSeparationOldWashDataVO implements Serializable {
  private static final long serialVersionUID = -8299636506844235163L;
  /** 智能模式：原煤310密度 */
  private BigDecimal autoRawCoalMD;
  /** 智能模式：精煤310密度 */
  private BigDecimal autoCleanCoalMD;

  /** 原煤实时入洗量-取302数据 */
  private BigDecimal rawCoalWash302;
  /** 原煤当日入洗量-取302数据 */
  private BigDecimal rawCoalWashToday302;
  /** 精煤实时产量-取701数据 */
  private BigDecimal cleanCoalOutput;
  /** 精煤当日产量-取701数据 */
  private BigDecimal cleanCoalOutputToday;
  /** 精煤产率：701/(301+302) */
  private BigDecimal cleanCoalRate;
  /** 实际矸石产率：802/(301+302) */
  private BigDecimal gangueRate;
  /** 实际矸石产量-取802数据 */
  private BigDecimal gangueOutput;

  /** 321介质添加泵运行状态 */
  private Boolean dosingPump321RunState;
  /** 320介质添加泵运行状态 */
  private Boolean mSeparator320RunState;
  /** 310合介桶液位 */
  private BigDecimal dosingPump310Level;
  /** 310合介桶补水阀开度/345分流箱分流阀开度 */
  private BigDecimal lumpValve310MValve;
  /** 313稀介桶液位 */
  private BigDecimal dosingPump313Level;
  /** 313稀介桶补水阀开度 */
  private BigDecimal valve313MValve;

  /** 310合介泵上密度 */
  private BigDecimal bucket310Density;

  /** 314磁选机(旧主洗) */
  private BigDecimal separator314Magnetic;
  /** 315磁选机(旧主洗) */
  private BigDecimal separator315Magnetic;
  /** 310分流箱磁性物含量计(旧主洗) */
  private BigDecimal bucket310Magnetic;
}
