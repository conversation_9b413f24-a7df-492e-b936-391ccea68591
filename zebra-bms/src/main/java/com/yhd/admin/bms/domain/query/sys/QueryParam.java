package com.yhd.admin.bms.domain.query.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PageParam.java 查询对象
 * @Description 查询父类
 * @createTime 2020年04月08日 08:57:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryParam extends Param {

    private Long current;

    private Long pageSize;

    /**
     * 排序
     */
    private String sorter;

    /**
     * 过滤
     */
    private Filters filters;


}
