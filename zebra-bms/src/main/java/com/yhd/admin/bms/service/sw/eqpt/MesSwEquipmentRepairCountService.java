package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentRepairDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepair;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentRepairRemainVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentRepairUserCountVO;

import java.util.List;

/**
 * 设备检修统计-业务层接口
 *
 * <AUTHOR>
 * @date 2024/8/19 16:36
 */
public interface MesSwEquipmentRepairCountService extends IService<MesSwEquipmentRepair> {

  /**
   * 根据条件查询分页列表
   *
   * @param param 查询参数
   * @return 设备检修统计分页列表信息
   */
  IPage<MesSwEquipmentRepairDTO> pagingQuery(MesSwEquipmentRepairParam param);

  /**
   * 根据条件查询列表
   *
   * @param param 查询条件
   * @return 设备检修列表信息
   */
  List<MesSwEquipmentRepairDTO> queryList(MesSwEquipmentRepairParam param);

  /**
   * 设备检修任务提报排名统计(前10名)
   *
   * @param param 查询条件
   * @return 提报排名统计列表
   */
  List<MesSwEquipmentRepairUserCountVO> tbUserRankCount(MesSwEquipmentRepairParam param);

  /**
   * 设备检修任务处理排名统计(前10名)
   *
   * @param param 查询条件
   * @return 处理排名统计列表
   */
  List<MesSwEquipmentRepairUserCountVO> clUserRankCount(MesSwEquipmentRepairParam param);

  /**
   * 根据条件查询设备检修遗留任务分页列表
   *
   * @param param 查询参数
   * @return 设备检修统计遗留任务分页列表信息
   */
  IPage<MesSwEquipmentRepairRemainVO> remainPagingQuery(MesSwEquipmentRepairParam param);

  /**
   * 根据条件查询设备检修遗留任务统计
   *
   * @param param 查询参数
   * @return 设备检修统计遗留任务统计
   */
  MesSwEquipmentRepairRemainVO remainCount(MesSwEquipmentRepairParam param);
}
