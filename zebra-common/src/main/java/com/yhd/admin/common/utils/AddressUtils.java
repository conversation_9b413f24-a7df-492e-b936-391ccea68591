package com.yhd.admin.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AddressUtils {

  private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);

  // IP地址查询
  public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";

  // 未知地址
  public static final String UNKNOWN = "XX XX";

  public static String getRealAddressByIP(String ip) {
    // 内网不查询
    if (IpUtils.internalIp(ip)) {
      return "内网IP";
    }
    try {
      String rspStr = null;
      if (StringUtils.isEmpty(rspStr)) {
        log.error("获取地理位置,外网不通，自动为空 {}", ip);
        return "内网IP";
      }
      JSONObject obj = JSONUtil.parseObj(rspStr);
      String region = obj.getStr("pro");
      String city = obj.getStr("city");
      return String.format("%s %s", region, city);
    } catch (Exception e) {
      log.error("获取地理位置异常 {}", ip);
    }
    return UNKNOWN;
  }
}
