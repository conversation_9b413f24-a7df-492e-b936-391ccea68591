package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketContractorDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketContractor;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketContractorParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketContractorSignUserParam;

import java.util.List;

/**
 * 承包商施工工作票-业务层接口
 *
 * <AUTHOR>
 * @date 2024/5/28 22:36
 */
public interface MesSwWorkTicketContractorService extends IService<MesSwWorkTicketContractor> {

  /**
   * 根据条件查询分页列表
   *
   * @param param 查询参数
   * @return 承包商施工工作票分页列表信息
   */
  IPage<MesSwWorkTicketContractorDTO> pagingQuery(MesSwWorkTicketContractorParam param);

  /**
   * 根据条件查询列表
   *
   * @param param 查询条件
   * @return 承包商施工工作票列表信息
   */
  List<MesSwWorkTicketContractorDTO> queryList(MesSwWorkTicketContractorParam param);

  /**
   * 查询施工单位列表
   *
   * @return 施工单位列表
   */
  List<String> getSgNameList();

  /**
   * 查询施工地点列表
   *
   * @return 施工地点列表
   */
  List<String> getSgLocationList();

  /**
   * 查询施工项目列表
   *
   * @return 施工项目列表
   */
  List<String> getSgItemList();

  /**
   * 查询详情信息
   *
   * @param param 查询参数：主键id
   * @return 承包商施工工作票详情信息
   */
  MesSwWorkTicketContractorDTO getCurrentDetail(MesSwWorkTicketContractorParam param);

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean add(MesSwWorkTicketContractorParam param);

  /**
   * 作废
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean zf(MesSwWorkTicketContractorParam param);

  /**
   * 审批
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean sp(MesSwWorkTicketContractorSignUserParam param);
}
