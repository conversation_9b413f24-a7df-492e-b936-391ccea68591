package com.yhd.admin.bms.domain.vo.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysNotificationVO.java
 * @Description 系统通知
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysNotificationVO extends BaseVO implements Cloneable, Serializable {

    /**
     * 标题
     */
    private String tittle;
    /**
     * 详细描述
     */
    private String description;
    /**
     * 类型：message、notification
     */
    private String type;
    /**
     * 读取状态
     */
    private Boolean readme;
    /**
     * 接收人账号
     */
    private String receiveBy;
    /**
     * 跳转信息
     */
    private String directInfo;
}
