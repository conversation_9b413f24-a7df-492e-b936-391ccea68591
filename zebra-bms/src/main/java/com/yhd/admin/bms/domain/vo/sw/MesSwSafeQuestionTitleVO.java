package com.yhd.admin.bms.domain.vo.sw;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/7/1 10:56
 * @Version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafeQuestionTitleVO extends BaseVO implements Cloneable, Serializable {
    /**
     *试题分组
     */
    private String title;
    /**
     *试题分类（考试试题/模拟试题）
     */
    private String questionSort;
}
