package com.yhd.admin.bms.domain.convert.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionContentDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionPageDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspection;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspectionSignature;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionPageVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionPersonVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesSwEquipmentInspectionConvert {

  MesSwEquipmentInspection toEntity(MesSwEquipmentInspectionParam param);

  MesSwEquipmentInspection toCopyEntity(MesSwEquipmentInspection classes);

  MesSwEquipmentInspectionDTO toDTO(MesSwEquipmentInspection classes);

  MesSwEquipmentInspectionPageDTO toPageDTO(MesSwEquipmentInspection classes);

  MesSwEquipmentInspectionVO toVO(MesSwEquipmentInspectionDTO classesDTO);

  MesSwEquipmentInspectionPageVO toPageVO(MesSwEquipmentInspectionPageDTO classesDTO);

  MesSwEquipmentInspectionPersonVO toPersonVO(MesSwEquipmentInspectionSignature classesDTO);

  MesSwEquipmentInspectionContentDTO toContentDTO(MesSwEquipmentInspectionContentDTO classesDTO);
}
