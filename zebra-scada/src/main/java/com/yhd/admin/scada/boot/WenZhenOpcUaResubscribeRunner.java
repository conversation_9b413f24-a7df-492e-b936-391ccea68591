package com.yhd.admin.scada.boot;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.api.subscriptions.UaSubscription;
import org.eclipse.milo.opcua.sdk.client.api.subscriptions.UaSubscriptionManager;
import org.eclipse.milo.opcua.stack.core.types.builtin.StatusCode;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;

import javax.annotation.Resource;

/***
 * 重新订阅
 */

@Slf4j
public class WenZhenOpcUaResubscribeRunner implements CommandLineRunner {

  @Qualifier("wenZhenOpcUaClient")
  @Resource
  OpcUaClient opcUaHelper;
  //
  @Resource WenZhenCoalWashOPCUARunner coalWashOPCUARunner;

  /**
   * Callback used to run the bean.
   *
   * @param args incoming main method arguments
   */
  @Override
  public void run(String... args) {
    opcUaHelper
        .getSubscriptionManager()
        .addSubscriptionListener(
            new UaSubscriptionManager.SubscriptionListener() {
              @Override
              public void onSubscriptionTransferFailed(
                  UaSubscription subscription, StatusCode statusCode) {
                if (log.isWarnEnabled()) {
                  log.error(
                      ">>> wenZhenOpcUaClient采集重新订阅：{},{}",
                      subscription.getSubscriptionId(),
                      statusCode.toString());
                }
                try {
                  opcUaHelper.getSubscriptionManager().clearSubscriptions();
                  coalWashOPCUARunner.run();
                } catch (Exception e) {
                  if (log.isErrorEnabled()) {
                    log.error("wenZhenOpcUaClient 重新订阅失败");
                  }
                }
              }
            });
  }
}
