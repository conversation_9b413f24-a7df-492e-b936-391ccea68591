package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.domain.convert.sw.MesCoalBinStatusConvert;
import com.yhd.admin.bms.domain.dto.sw.MesCoalBinStatusDTO;
import com.yhd.admin.bms.domain.entity.sw.MesCoalBinStatus;
import com.yhd.admin.bms.dao.sw.MesCoalBinStatusDao;
import com.yhd.admin.bms.domain.query.sw.MesCoalBinStatusParam;
import com.yhd.admin.bms.service.sw.MesCoalBinStatusService;
import com.yhd.admin.bms.exception.BMSException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 煤仓情状表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14 16:31:40
 */
@Service
public class MesCoalBinStatusServiceImpl extends ServiceImpl<MesCoalBinStatusDao, MesCoalBinStatus> implements MesCoalBinStatusService {
    @Resource
    private MesCoalBinStatusConvert mesCoalBinStatusConvert;

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MesCoalBinStatusDTO> pagingQuery(MesCoalBinStatusParam param) {
        IPage<MesCoalBinStatus> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesCoalBinStatus> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        return queryChain.page(page).convert(mesCoalBinStatusConvert::toDTO);
    }


    @Override
    public List<MesCoalBinStatusDTO> getLatestByNames() {
        List<String> targetNames = Arrays.asList("产品仓1-块煤", "产品仓2-块煤", "产品仓3-混煤", "产品仓4-混煤");

        List<MesCoalBinStatus> allRecords = baseMapper.selectList(
            new LambdaQueryWrapper<MesCoalBinStatus>()
                .in(MesCoalBinStatus::getName, targetNames)
                .orderByDesc(MesCoalBinStatus::getCreatedTime)
        );
        Map<String, MesCoalBinStatus> latestMap = allRecords.stream()
            .collect(Collectors.groupingBy(
                MesCoalBinStatus::getName,
                Collectors.collectingAndThen(
                    Collectors.reducing((a, b) -> a.getCreatedTime().isAfter(b.getCreatedTime()) ? a : b),
                    optional -> optional.orElse(null)
                )
            ));
        return latestMap.values().stream()
            .filter(Objects::nonNull)
            .map(mesCoalBinStatusConvert::toDTO)
            .collect(Collectors.toList());
    }


    /**
     * 新增或修改
     *
     * @param param
     * @return
     */
    @Override
    public Boolean addOrModify(MesCoalBinStatusParam param) {
        MesCoalBinStatus entity = mesCoalBinStatusConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    @Override
    public Boolean addBatch(MesCoalBinStatusParam param) {
        if (CollectionUtils.isEmpty(param.getCoalBinStatusList())){
            throw new BMSException("error","请传入参数");
        } else {
            return super.saveBatch(param.getCoalBinStatusList());
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param
     * @return
     */
    @Override
    public MesCoalBinStatusDTO getCurrentDetails(MesCoalBinStatusParam param) {
        return mesCoalBinStatusConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MesCoalBinStatusParam param) {
        return removeByIds(param.getIds());
    }

}
