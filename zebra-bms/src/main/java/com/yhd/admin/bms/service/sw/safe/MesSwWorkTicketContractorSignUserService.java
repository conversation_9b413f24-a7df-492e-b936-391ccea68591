package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketContractorSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketContractorSignUser;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketContractorSignUserParam;

import java.util.List;

/**
 * 承包商施工工作票签字用户-业务层接口
 *
 * <AUTHOR>
 * @date 2024/5/28 22:36
 */
public interface MesSwWorkTicketContractorSignUserService
    extends IService<MesSwWorkTicketContractorSignUser> {

  /**
   * 根据条件查询列表
   *
   * @param param 参数
   * @return 列表信息
   */
  List<MesSwWorkTicketContractorSignUserDTO> queryList(
      MesSwWorkTicketContractorSignUserParam param);
}
