package com.yhd.admin.bms.controller.sw;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwContractorStartMgtConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwContractorStartMgtDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwContractorStartMgtParam;
import com.yhd.admin.bms.domain.query.sw.MesSwContractorStartMgtTemplateParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwContractorStartMgtVO;
import com.yhd.admin.bms.service.sw.IMesSwContractorStartMgtService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 承包商开工管理审批单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@RestController
@RequestMapping(value = "/contractor/startManagement")
public class MesSwContractorStartMgtController {

    /**
     * service
     */
    @Resource
    private IMesSwContractorStartMgtService service;

    @Resource
    private MesSwContractorStartMgtConvert convert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwContractorStartMgtVO> pagingQuery(@RequestBody MesSwContractorStartMgtParam queryParam) {
        IPage<MesSwContractorStartMgtDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getTemplateDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getTemplateDetail(@RequestBody MesSwContractorStartMgtParam queryParam) {
        service.getTemplateDetail(queryParam);
        return RespJson.buildSuccessResponse(convert.toTemplateListVO(service.getTemplateDetail(queryParam)));
    }

    @PostMapping(
        value = "/templateModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody List<MesSwContractorStartMgtTemplateParam> queryParam) {
        service.templateModify(queryParam);
        return RespJson.buildSuccessResponse();
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesSwContractorStartMgtParam queryParam, OAuth2Authentication authentication) {
//        queryParam.setCreatedBy(authentication.getName());
        service.add(queryParam);
        return RespJson.buildSuccessResponse();
    }

    @PostMapping(
        value = "/saveApproval",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson saveApproval(@RequestBody MesSwContractorStartMgtParam param) {
        try {
            Boolean retVal = service.saveApproval(param);
            return RespJson.buildSuccessResponse(retVal);
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)

    public RespJson getCurrentDetail(@RequestBody MesSwContractorStartMgtParam queryParam) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(queryParam)));
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)

    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        service.removeBatch(batchParam);
        return RespJson.buildSuccessResponse();
    }
}
