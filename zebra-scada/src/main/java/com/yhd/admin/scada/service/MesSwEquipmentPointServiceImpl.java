package com.yhd.admin.scada.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.scada.dao.MesSwEquipmentPointDao;
import com.yhd.admin.scada.domain.Enum.RedisKeyEnum;
import com.yhd.admin.scada.domain.dto.MesSwEquipmentPointDTO;
import com.yhd.admin.scada.domain.entity.MesSwEquipmentPoint;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备资料-温振报警点位管理
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@Service
public class MesSwEquipmentPointServiceImpl
    extends ServiceImpl<MesSwEquipmentPointDao, MesSwEquipmentPoint>
    implements MesSwEquipmentPointService {

  private final RedisTemplate<String, Object> objectRedisTemplate;

  public MesSwEquipmentPointServiceImpl(RedisTemplate<String, Object> objectRedisTemplate) {
    this.objectRedisTemplate = objectRedisTemplate;
  }

  @Override
  public List<String> queryList() {
    LambdaQueryWrapper<MesSwEquipmentPoint> wrapper = new LambdaQueryWrapper<>();
    wrapper
        .isNotNull(MesSwEquipmentPoint::getPointAddress)
        .isNotNull(MesSwEquipmentPoint::getEquipmentDataId);
    List<MesSwEquipmentPoint> pointList = this.list(wrapper);
    Map<String, MesSwEquipmentPointDTO> redisMap = new ConcurrentHashMap<>(pointList.size());
    List<String> pointStrList = new ArrayList<>();
    pointList.forEach(
        o -> {
          MesSwEquipmentPointDTO dto = new MesSwEquipmentPointDTO();
          dto.setId(o.getId());
          dto.setEquipmentDataId(o.getEquipmentDataId());
          dto.setEquipmentNoType(o.getEquipmentNoType());
          dto.setPointName(o.getPointName());
          dto.setPointAddress(o.getPointAddress());
          dto.setWarnDown(o.getWarnDown());
          dto.setWarnUp(o.getWarnUp());
          dto.setRiskDown(o.getRiskDown());
          dto.setRiskUp(o.getRiskUp());
          redisMap.put(String.format(RedisKeyEnum.WEN_ZHEN.getKey(), o.getPointAddress()), dto);

          pointAdd(pointStrList, o.getPointAddress());
          pointAdd(pointStrList, o.getWarnDown());
          pointAdd(pointStrList, o.getWarnUp());
          pointAdd(pointStrList, o.getRiskDown());
          pointAdd(pointStrList, o.getRiskUp());
        });
    Set<String> keys = objectRedisTemplate.keys(String.format(RedisKeyEnum.WEN_ZHEN.getKey(), "*"));
    objectRedisTemplate.delete(keys);

    objectRedisTemplate.opsForValue().multiSet(redisMap);
    return pointStrList;
  }

  private void pointAdd(List<String> pointStrList, String point) {
    if (StringUtils.isNotEmpty(point)) {
      pointStrList.add(point);
    }
  }
}
