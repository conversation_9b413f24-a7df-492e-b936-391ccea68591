package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.safe.MesSwSafeTrainStudentSignDao;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwSafeTrainStudentSignConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainOrgGroupStudentSignDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainStudentSignDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainStudentSign;
import com.yhd.admin.bms.domain.enums.safe.TrainStudentSignStatusEnum;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainStudentSignParam;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainStudentSignService;
import com.yhd.admin.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 安全培训学员签到记录-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/1/4 17:08
 */
@Service
public class MesSwSafeTrainStudentSignServiceImpl
    extends ServiceImpl<MesSwSafeTrainStudentSignDao, MesSwSafeTrainStudentSign>
    implements MesSwSafeTrainStudentSignService {
  private static final Logger logger =
      LoggerFactory.getLogger(MesSwSafeTrainStudentSignServiceImpl.class);
  @Resource private MesSwSafeTrainStudentSignConvert studentSignConvert;

  @Override
  public List<MesSwSafeTrainStudentSignDTO> queryList(MesSwSafeTrainStudentSignParam param) {
    LambdaQueryWrapper<MesSwSafeTrainStudentSign> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(
        Objects.nonNull(param.getCourseId()),
        MesSwSafeTrainStudentSign::getCourseId,
        param.getCourseId());
    // 签到状态
    wrapper.eq(StringUtils.isNotBlank(param.getSignStatus()), MesSwSafeTrainStudentSign::getSignStatus, param.getSignStatus());
    List<MesSwSafeTrainStudentSign> studentSignList = baseMapper.selectList(wrapper);
    if (!CollectionUtils.isEmpty(studentSignList)) {
      // 当前人员签到页面点击权限处理
      studentSignList.forEach(this::signClickAuthHandle);
    }

    return studentSignConvert.toDTO(studentSignList);
  }

  @Override
  public List<MesSwSafeTrainStudentSignDTO> queryListByCourseId(Long courseId) {
    if (null == courseId) {
      return null;
    }
    MesSwSafeTrainStudentSignParam param = new MesSwSafeTrainStudentSignParam();
    param.setCourseId(courseId);

    return this.queryList(param);
  }

  @Override
  public List<MesSwSafeTrainOrgGroupStudentSignDTO> queryOrgGroupListByCourseId(Long courseId) {
    if (Objects.isNull(courseId)) {
      logger.error("请求参数不符合规范");
      return null;
    }
    List<MesSwSafeTrainOrgGroupStudentSignDTO> result = Lists.newArrayList();

    // 根据课程id查询所有学员签到列表
    MesSwSafeTrainStudentSignParam param = new MesSwSafeTrainStudentSignParam();
    param.setCourseId(courseId);
    List<MesSwSafeTrainStudentSignDTO> studentSignList = this.queryList(param);
    if (!CollectionUtils.isEmpty(studentSignList)) {
      // 根据组织进行分组
      Map<String, List<MesSwSafeTrainStudentSignDTO>> orgGroupMap =
          studentSignList.stream()
              .collect(Collectors.groupingBy(c -> c.getDepartmentId() + "-" + c.getDepartment()));

      orgGroupMap.forEach(
          (k, v) -> {
            MesSwSafeTrainOrgGroupStudentSignDTO orgGroupSignDTO =
                new MesSwSafeTrainOrgGroupStudentSignDTO();
            String[] orgGroupStr = k.split("-");
            orgGroupSignDTO.setDepartmentId(Long.parseLong(orgGroupStr[0]));
            orgGroupSignDTO.setDepartment(orgGroupStr[1]);
            orgGroupSignDTO.setStudentSignList(v);
            result.add(orgGroupSignDTO);
          });
    }

    return result;
  }

  @Override
  public MesSwSafeTrainStudentSignDTO getCurrentDetail(MesSwSafeTrainStudentSignParam param) {
    if (Objects.isNull(param.getId())) {
      logger.error("请求参数不为空");
      return null;
    }
    MesSwSafeTrainStudentSign studentSign = this.getById(param.getId());
    // 当前人员签到页面点击权限处理
    if (Objects.nonNull(studentSign)) {
      this.signClickAuthHandle(studentSign);
    }

    return studentSignConvert.toDTO(studentSign);
  }

  @Override
  public MesSwSafeTrainStudentSignDTO getCurrentDetail(Long id) {
    MesSwSafeTrainStudentSignParam param = new MesSwSafeTrainStudentSignParam();
    param.setId(id);
    return this.getCurrentDetail(param);
  }

  @Override
  public MesSwSafeTrainStudentSignDTO getCurrentDetail(Long courseId,String studentCode) {
    LambdaQueryWrapper<MesSwSafeTrainStudentSign> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwSafeTrainStudentSign::getCourseId, courseId);
    wrapper.eq(MesSwSafeTrainStudentSign::getStudentCode, studentCode);
    List<MesSwSafeTrainStudentSign> studentSignList = baseMapper.selectList(wrapper);
    if (studentSignList.isEmpty()){
      return null;
    } else {
      MesSwSafeTrainStudentSign studentSign = studentSignList.get(0);
      // 当前人员签到页面点击权限处理
      if (Objects.nonNull(studentSign)) {
        this.signClickAuthHandle(studentSign);
      }
      return studentSignConvert.toDTO(studentSign);
    }
  }

  @Override
  public Integer getStudentSignCount(Long courseId) {
    MesSwSafeTrainStudentSignParam param = new MesSwSafeTrainStudentSignParam();
    param.setCourseId(courseId);
    param.setSignStatus(TrainStudentSignStatusEnum.SIGNED.getCode());
    List<MesSwSafeTrainStudentSignDTO>  studentSignDTOS= this.queryList(param);
    return studentSignDTOS.size();
  }

    /**
   * 登录用户签到页面点击权限处理
   *
   * @param studentSign 学员签到信息
   */
  private void signClickAuthHandle(MesSwSafeTrainStudentSign studentSign) {
    if (Objects.nonNull(studentSign)) {
      // 状态编码翻译
      studentSign.setSignStatusName(
          Objects.requireNonNull(
                  TrainStudentSignStatusEnum.getStatusEnumByCode(studentSign.getSignStatus()))
              .getMsg());
      // 当前登录人
      String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
      // 判断当前用户能否点击签到按钮 && 课程状态是发起签到状态(前端判断)
      // 1. 学生签到状态是未签到 && 当前用户是签到人员
      if (TrainStudentSignStatusEnum.NOT_SIGN.getCode().equals(studentSign.getSignStatus())
          && studentSign.getStudentCode().equals(account)) {
        studentSign.setStudentSignClick(true);
      }
    }
  }
}
