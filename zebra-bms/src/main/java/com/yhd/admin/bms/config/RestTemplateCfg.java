package com.yhd.admin.bms.config;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Configuration
public class RestTemplateCfg {

  @Bean
  public RestTemplate restTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    OkHttpClient.Builder builder = new OkHttpClient.Builder();
    ConnectionPool okHttpConnectionPool = new ConnectionPool(20, 20, TimeUnit.SECONDS);
    builder.connectionPool(okHttpConnectionPool);
    builder.connectTimeout(10, TimeUnit.MINUTES);
    builder.retryOnConnectionFailure(false);

    restTemplate.setRequestFactory(new OkHttp3ClientHttpRequestFactory(builder.build()));
    //    builder.setConnectTimeout(Duration.ofMinutes(5)).setReadTimeout(Duration.ofMinutes(5));
    return restTemplate;
  }
}
