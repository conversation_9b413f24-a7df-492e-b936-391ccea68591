package com.yhd.admin.bms.service.sw.ticket;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketUseElectricSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketUseElectricSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketUseElectricSignUserParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/17 9:12
 * @Version 1.0
 */
public interface MesSwWorkTicketElectricSignUserService extends IService<MesSwWorkTicketUseElectricSignUser> {
    Boolean approval(MesSwWorkTicketUseElectricSignUserParam param);

    List<MesSwWorkTicketUseElectricSignUserDTO> queryList(MesSwWorkTicketUseElectricSignUserParam signUserParam);
}
