package com.yhd.admin.bms.controller.sw;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeQuestionConvert;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeQuestionTitleConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeQuestionDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeQuestionNumParam;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeQuestionParam;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeQuestionTitleParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwSafeQuestionVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesSwSafeQuestionService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.eums.BusinessType;
import com.yhd.admin.common.poi.excel.DownloadTplBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/4 17:17
 * @Version 1.0
 *
 * 安全考试题库管理--控制层
 */

@RestController
@RequestMapping("/safesubject")
@Slf4j
public class MesSwSafeQuestionController {
    @Resource
    private MesSwSafeQuestionService service;
    @Resource
    private MesSwSafeQuestionConvert convert;
    @Resource
    private MesSwSafeQuestionTitleConvert titleConvert;

//    private static final String SINGLE_CHOICE = "试题导入模板（单选题）.xlsx";
//    private static final String MULTI_CHOICE = "试题导入模板（多选题）.xlsx";
    private static final String JUDGE_CHOICE = "试题导入模板 .xlsx";

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwSafeQuestionVO> pagingQuery(
        @RequestBody MesSwSafeQuestionParam queryParam) {
        IPage<MesSwSafeQuestionDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/relatedTrainingPlanning",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> relatedTrainingPlanning(@RequestBody MesSwSafeQuestionParam param) {
        List<MesSwSafeQuestionDTO> dtoList = service.relatedTrainingPlanning(param);
        return RespJson.buildSuccessResponse(convert.toVOList(dtoList));
    }

    @PostMapping(
        value = "/trainingPlanning",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> trainingPlanning(@RequestBody MesSwSafeQuestionParam param) {
        List<MesSwSafeQuestionDTO> dtoList = service.trainingPlanning(param);
        return RespJson.buildSuccessResponse(convert.toVOList(dtoList));
    }

    @PostMapping(
        value = "/getQuestionTitle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getQuestionTitle(@RequestBody MesSwSafeQuestionParam param) {
        return RespJson.buildSuccessResponse(convert.toVOList(service.getQuestionTitle(param)));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getCurrentDetail(@RequestBody MesSwSafeQuestionParam param) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "安全考试题库新增", businessType = BusinessType.INSERT)
    public RespJson add(@RequestBody MesSwSafeQuestionParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "安全考试题库编辑", businessType = BusinessType.INSERT)
    public RespJson modify(@RequestBody MesSwSafeQuestionParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "安全考试题库删除", businessType = BusinessType.DELETE)
    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        Boolean retVal = service.removeBatch(batchParam);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

//    @SysLogs(title = "导入安全考试题库", businessType = BusinessType.IMPORT)
//    @PostMapping(value = "/import")
//    public RespJson<String> importExcel(@RequestParam("file") MultipartFile file,
//                                        @RequestParam("questionType") String questionType,
//                                        @RequestParam("trainingId") Long trainingId) {
//        if (file.isEmpty()) {
//            return RespJson.buildFailureResponse(ResultStateEnum.FAIL.getCode(), "文件不存在或者为空！");
//        }
//        // 获取文件名称
//        String fileName = file.getOriginalFilename();
//        log.debug("进行导入题库数据EXCEL的操作,文件名称：{}", fileName);
//        return RespJson.buildSuccessResponse(service.importExcel(file,questionType,trainingId));
//    }

    @SysLogs(title = "导入安全考试题库", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/import")
    public RespJson<String> importExcel(@RequestParam("file") MultipartFile file,
                                        @RequestParam("questionSort") String questionSort,
                                        @RequestParam("titleId") Long titleId) {
        if (file.isEmpty()) {
            return RespJson.buildFailureResponse(ResultStateEnum.FAIL.getCode(), "文件不存在或者为空！");
        }
        // 获取文件名称
        String fileName = file.getOriginalFilename();
        log.debug("进行导入题库数据EXCEL的操作,文件名称：{}", fileName);
        return RespJson.buildSuccessResponse(service.importExcel(file,questionSort,titleId));
    }

//    @GetMapping("/single/getTemplate")
//    public void downloadSingleTemplate(HttpServletResponse response) {
//        try {
//            DownloadTplBuilder.downloadExcelTpl(SINGLE_CHOICE, response);
//        } catch (Exception e) {
//            throw new BMSException("error", "模板下载失败");
//        }
//    }
//
//    @GetMapping("/multi/getTemplate")
//    public void downloadMultiTemplate(HttpServletResponse response) {
//        try {
//            DownloadTplBuilder.downloadExcelTpl(MULTI_CHOICE, response);
//        } catch (Exception e) {
//            throw new BMSException("error", "模板下载失败");
//        }
//    }

    @GetMapping("/judge/getTemplate")
    public void downloadJudgeTemplate(HttpServletResponse response) {
        try {
            DownloadTplBuilder.downloadExcelTpl(JUDGE_CHOICE, response);
        } catch (Exception e) {
            throw new BMSException("error", "模板下载失败");
        }
    }

    @PostMapping(
        value = "/pagingQueryByPaper",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwSafeQuestionVO> pagingQueryByPaper(
        @RequestBody MesSwSafeQuestionParam queryParam) {
        IPage<MesSwSafeQuestionDTO> iPage = service.pagingQueryByPaper(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/addTitle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addTitle(@RequestBody MesSwSafeQuestionTitleParam param) {
        Boolean retVal = service.addTitle(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/removeTitle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeTitle(@RequestBody MesSwSafeQuestionTitleParam param) {
        Boolean retVal = service.removeTitle(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/getTitleList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getTitleList(@RequestBody MesSwSafeQuestionTitleParam param) {
        return RespJson.buildSuccessResponse(titleConvert.toVOList(service.getTitleList(param)));
    }

    @PostMapping(
        value = "/getQuestionNum",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getQuestionNum(@RequestBody MesSwSafeQuestionTitleParam param) {
        return RespJson.buildSuccessResponse(service.getQuestionNum(param));
    }

    @PostMapping(
        value = "/autoPaper",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson autoPaper(@RequestBody MesSwSafeQuestionNumParam param) {
        return RespJson.buildSuccessResponse(convert.toVOList(service.autoPaper(param)));
    }
}
