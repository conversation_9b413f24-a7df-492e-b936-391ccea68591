package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesMonitoringPointsDTO;
import com.yhd.admin.bms.domain.entity.sw.MesMonitoringPoints;
import com.yhd.admin.bms.domain.query.sw.MesMonitoringPointsParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

/**
 * <AUTHOR>
 * @Date 2024/6/28 15:29
 * @Version 1.0
 */
public interface MesMonitoringPointsService extends IService<MesMonitoringPoints> {
    IPage<MesMonitoringPointsDTO> pagingQuery(MesMonitoringPointsParam queryParam);

    MesMonitoringPointsDTO getCurrentDetail(MesMonitoringPointsParam param);

    Boolean addOrModify(MesMonitoringPointsParam param);

    Boolean removeBatch(BatchParam param);
}
