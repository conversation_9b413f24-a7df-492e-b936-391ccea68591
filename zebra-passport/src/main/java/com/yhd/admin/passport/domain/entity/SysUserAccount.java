package com.yhd.admin.passport.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysUserAccount extends BaseEntity implements Serializable, Cloneable {

  /** 登录名称 */
  private String username;
  /** 密码 */
  private String password;
  /** 账户未过期 */
  private Boolean isAccountNonExpired;
  /** 账户未锁定 */
  private Boolean isAccountNonLocked;
  /** 密码未过期 */
  private Boolean isCredentialsNonExpired;

  /** 账户状态 */
  private Boolean isEnable;

  /** 员工编码 */
  private String jobNumber;
}
