package com.yhd.admin.bms.domain.query.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleParam.java
 * @Description TODO
 * @createTime 2020年03月30日 14:57:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleParam extends QueryParam {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 角色状态
     */
    private Boolean isEnable;

    /**
     * 权限
     */
    private List<Long> authority;
}
