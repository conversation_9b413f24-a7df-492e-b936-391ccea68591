package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备资料-缺陷推送管理
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentFlaw extends BaseEntity implements Cloneable, Serializable {
  /** 设备资料id */
  private Long equipmentDataId;
  /** 设备编码+类别 */
  private String equipmentNoType;
  /** 缺陷来源 */
  private String originType;
  /** 缺陷来源名称 */
  private String originName;
  /** 缺陷事项 */
  private String flawMatter;
  /** 缺陷推送时间 */
  private LocalDateTime pushTime;
  /** 处理结果 */
  private String handleResult;
  /** 存在问题 */
  private String problem;
  /** 提报车间code */
  private Long tbWorkshopCode;
  /** 提报车间名称 */
  private String tbWorkshopName;
  /** 整改车间code */
  private Long zgWorkshopCode;
  /** 整改车间名称 */
  private String zgWorkshopName;
  /** 处理人 */
  private String handlerAccount;
  /** 处理人名称 */
  private String handlerName;
  /** 处理时间 */
  private LocalDateTime handlerTime;
}
