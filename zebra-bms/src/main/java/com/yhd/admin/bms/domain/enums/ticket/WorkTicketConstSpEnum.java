package com.yhd.admin.bms.domain.enums.ticket;

import com.yhd.admin.bms.domain.enums.safe.ExamStatusEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/5/29 10:02
 * @Version 1.0
 */

@Getter
public enum WorkTicketConstSpEnum {
    CONST_UNIT("CONST_UNIT", "施工单位"),
    WORKSHOP_LEAD("WORKSHOP_LEAD", "车间主任"),
    TECH_LEAD("TECH_LEAD", "技术主管"),
    SAFE_LEAD("SAFE_LEAD", "安管主管"),
    DUTY_LEAD("DUTY_LEAD", "跟班厂（站）领导"),
    FACTORY_LEAD("FACTORY_LEAD", "厂（站）领导");

    private final String code;
    private final String desc;

    WorkTicketConstSpEnum(String code, String description) {
        this.code = code;
        this.desc = description;
    }

    public static List<String> getCodeList() {
        WorkTicketConstSpEnum[] values = WorkTicketConstSpEnum.values();

        return Arrays.stream(values).map(WorkTicketConstSpEnum::getCode).collect(Collectors.toList());
    }

    public static List<String> getValuesList() {
        WorkTicketConstSpEnum[] values = WorkTicketConstSpEnum.values();

        return Arrays.stream(values).map(WorkTicketConstSpEnum::getDesc).collect(Collectors.toList());
    }

    // 根据状态获取状态枚举
    public static ExamStatusEnum getStatusEnumByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        ExamStatusEnum[] values = ExamStatusEnum.values();
        for (ExamStatusEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
