package com.yhd.admin.bms.domain.entity.sw.safe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 承包商不安全行为积分台账
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwContractorUnsafeRecord extends BaseEntity implements Serializable {
  private static final long serialVersionUID = -1429747868223567832L;

  /** 承包商单位名称 */
  private String contractorName;
  /** 承包商单位编码 */
  private String contractorCode;
  /** 年度 */
  private String year;
  /** 责任车间名称 */
  private String dutyWorkshopName;
  /** 责任车间编码 */
  private String dutyWorkshopCode;
  /** 分管领导姓名 */
  private String fgLeaderName;
  /** 分管领导编码 */
  private String fgLeaderCode;
  /** 积分明细列表 */
  @TableField(exist = false)
  private List<MesSwContractorUnsafeScoreDetail> unsafeScoreDetailList;
}
