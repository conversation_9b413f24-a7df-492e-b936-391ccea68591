package com.yhd.admin.bms.service.sw.impl.safe;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwSafeTrainCourseConvert;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwSafeTrainStudentSignConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafetyTrainingPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainCourseDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainSignDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainStudentSignDTO;
import com.yhd.admin.bms.domain.entity.sw.TrainingSchedule;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainCourse;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainStudentSign;
import com.yhd.admin.bms.domain.entity.sys.MesNotice;
import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.domain.enums.NotifyEnum;
import com.yhd.admin.bms.domain.enums.safe.TrainCourseStatusEnum;
import com.yhd.admin.bms.domain.enums.safe.TrainSignFinishEnum;
import com.yhd.admin.bms.domain.enums.safe.TrainStudentSignStatusEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwSafetyTrainingPlanParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainCourseParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainSignParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesSwSafetyTrainingPlanService;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainCourseService;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainSignService;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainStudentSignService;
import com.yhd.admin.bms.webscoket.service.WebsocketService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 安全培训签到-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/1/4 18:16
 */
@Service
public class MesSwSafeTrainSignServiceImpl implements MesSwSafeTrainSignService {
  private static final Logger logger = LoggerFactory.getLogger(MesSwSafeTrainSignServiceImpl.class);

  @Resource private MesSwSafeTrainCourseService trainCourseService;
  @Resource private MesSwSafeTrainCourseConvert trainCourseConvert;
  @Resource private MesSwSafeTrainStudentSignService studentSignService;
  @Resource private MesSwSafeTrainStudentSignConvert studentSignConvert;
  @Resource private MesSwSafetyTrainingPlanService trainingPlanService;
  @Resource private WebsocketService websocketService;
  @Resource private RedisTemplate<String, Object> redisTemplate;

  @Override
  public IPage<MesSwSafeTrainSignDTO> pagingQuery(MesSwSafeTrainSignParam param) {
    logger.debug(
        "请求【根据条件查询培训签到列表】接口开始，分页参数：当前页码={}、每页数量={}。条件参数：日期范围={}、培训内容 ={}",
        param.getCurrent(),
        param.getPageSize(),
        param.getStartDate() + "-" + param.getEndDate(),
        param.getTrainContent());
    // 页面签到列表信息由培训课程表数据按培训日期分组合并而来
    List<MesSwSafeTrainSignDTO> groupList = Lists.newArrayList();
    // 先查询培训课程表数据
    MesSwSafeTrainCourseParam courseParam = new MesSwSafeTrainCourseParam();
    courseParam.setStartDate(param.getStartDate());
    courseParam.setEndDate(param.getEndDate());
    courseParam.setTrainContent(param.getTrainContent());
    if (Objects.nonNull(param.getRelatedToMe()) && param.getRelatedToMe()) {
      courseParam.setRelatedToMe(true);
    }
    List<MesSwSafeTrainCourseDTO> courseList = trainCourseService.queryList(courseParam, false);
    if (!CollectionUtils.isEmpty(courseList)) {
      // 培训课程列表根据计划主键id、培训日期分组
      Map<String, List<MesSwSafeTrainCourseDTO>> trainDateGroupMap =
          courseList.stream()
              .collect(Collectors.groupingBy(v -> v.getPlanId() + "-" + v.getTrainDate()));
      trainDateGroupMap.forEach(
          (k, v) -> {
            MesSwSafeTrainSignDTO trainSign = new MesSwSafeTrainSignDTO();
            MesSwSafeTrainCourseDTO trainCourseDTO = v.get(0);
            trainSign.setTrainDate(trainCourseDTO.getTrainDate());
            trainSign.setPlanId(trainCourseDTO.getPlanId());
            trainSign.setTrainTeacher(trainCourseDTO.getTrainTeacher());
            trainSign.setTrainTeacherName(trainCourseDTO.getTrainTeacherName());
            trainSign.setTrainContent(trainCourseDTO.getTrainContent());
            trainSign.setCourseList(v);

            groupList.add(trainSign);
          });
    }
    // 按培训日期倒叙
    List<MesSwSafeTrainSignDTO> sortResult =
        groupList.stream()
            .sorted(
                Comparator.comparing(
                    MesSwSafeTrainSignDTO::getTrainDate, Comparator.reverseOrder()))
            .collect(Collectors.toList());

    Page<MesSwSafeTrainSignDTO> page = new Page<>(param.getCurrent(), param.getPageSize());

    // 分页处理
    List<MesSwSafeTrainSignDTO> result = Lists.newArrayList();
    int pageNum = param.getCurrent() == null ? 1 : param.getCurrent().intValue();
    int pageSize = param.getPageSize() == null ? 20 : param.getPageSize().intValue();
    int total = sortResult.size();
    int index = pageNum > 1 ? (pageNum - 1) * pageSize : 0;
    for (int i = 0; i < param.getPageSize() && index + i < total; i++) {
      MesSwSafeTrainSignDTO item = sortResult.get(index + i);
      result.add(item);
    }
    page.setRecords(result);
    page.setTotal(sortResult.size());

    return page;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean initTodoSignCourseAndStudent(Long planId) {
    logger.debug("培训计划审批完成,计划ID={}，初始化待签到的课程、学员信息开始......", planId);
    boolean result = false;
    if (Objects.isNull(planId)) {
      logger.error("请求参数不能为空");
      return false;
    }
    // 根据培训计划id查询培训计划详情信息
    MesSwSafetyTrainingPlanParam planParam = new MesSwSafetyTrainingPlanParam();
    planParam.setId(planId);
    MesSwSafetyTrainingPlanDTO trainingPlan = trainingPlanService.getCurrentDetail(planParam);
    logger.debug("培训计划详情信息：{}", trainingPlan);
    if (Objects.nonNull(trainingPlan)) {
      List<MesSwSafeTrainCourse> trainCourseList = Lists.newArrayList();
      List<MesSwSafeTrainStudentSign> studentSignList = Lists.newArrayList();

      // 培训教师账号，多个，计划表jsonArrayStr存储["张三","李四"]
      String teacherAccountJsonStr = trainingPlan.getTrainingTeacherAccount();
      List<String> teacherAccountList = JSON.parseArray(teacherAccountJsonStr, String.class);
      // 培训教师姓名，多个，计划表jsonArrayStr存储["张三","李四"]
      String teacherNameJsonStr = trainingPlan.getTrainingTeacherName();
      List<String> teacherNameList = JSON.parseArray(teacherNameJsonStr, String.class);
      // 获取培训日期，多个，计划表jsonArrayStr存储["2024-01-12","2023-02-22"]
      String trainingSchedule = trainingPlan.getTrainingSchedule();
      logger.debug("培训计划培训日期信息：{}", trainingSchedule);
      // 培训学员部门id
      String departmentIdJsonStr = trainingPlan.getDepartment();
      logger.debug("培训学员来源：{}", departmentIdJsonStr);
      if (StringUtils.isNotBlank(trainingSchedule)) {
        List<TrainingSchedule> trainingSchedules = JSON.parseArray(trainingSchedule, TrainingSchedule.class);
        for (TrainingSchedule schedule : trainingSchedules) {
          // 查询相同计划id和培训日期，课程表是否已存在
          MesSwSafeTrainCourseParam courseParam = new MesSwSafeTrainCourseParam();
          courseParam.setPlanId(planId);
          courseParam.setTrainDate(schedule.getDate());
          List<MesSwSafeTrainCourseDTO> courseList =
              trainCourseService.queryList(courseParam, false);
          if (!CollectionUtils.isEmpty(courseList)) {
            logger.error("培训课程表已存在培训计划id=" + planId + "，培训日期=" + schedule.getDate() + "的培训课程记录");
            return false;
          }
          if (!CollectionUtils.isEmpty(schedule.getTimeList())) {
            for (String courseTime : schedule.getTimeList()) {
              MesSwSafeTrainCourse trainCourse = new MesSwSafeTrainCourse();
              trainCourse.setPlanId(planId); // 计划主键id
              trainCourse.setTrainDate(schedule.getDate()); // 培训日期
              trainCourse.setTrainTeacher(String.join(",", teacherAccountList)); // 培训教师账号
              trainCourse.setTrainTeacherName(String.join(",", teacherNameList)); // 培训教师姓名
              trainCourse.setTrainContent(trainingPlan.getTrainingContent()); // 培训内容
              trainCourse.setCourseTime(courseTime); // 培训时间
              trainCourse.setTrainDepartment(departmentIdJsonStr); // 培训学员部门
              trainCourse.setTrainStudentId(trainingPlan.getStudentId());
              trainCourse.setStudentNumber(trainingPlan.getStudentNumber());
              trainCourseList.add(trainCourse);
            }
          }
        }
      }
      // 获取缓存中，学员列表数据
      HashOperations<String, String, List<UserAccountDTO>> hashOperations =
          redisTemplate.opsForHash();
      List<UserAccountDTO> studentList =
          hashOperations.get(BMSRedisKeyEnum.TRAIN_PLAN_STUDENT.getKey(), planId.toString());
      // 创建签到学员表数据
      if (!CollectionUtils.isEmpty(studentList)) {
        studentList.forEach(
            v -> {
              MesSwSafeTrainStudentSign studentSign = new MesSwSafeTrainStudentSign();
              studentSign.setDepartmentId(v.getDepartmentId()); // 部门ID
              studentSign.setDepartment(v.getDepartment()); // 部门名称
              studentSign.setStudentCode(v.getUsername()); // 学员账号
              studentSign.setStudentName(v.getName()); // 学员姓名
              studentSignList.add(studentSign);
            });
      }

      if (!CollectionUtils.isEmpty(trainCourseList)) {
        // 批量新增培训课程
        trainCourseService.saveBatch(trainCourseList);
        // 批量新增培训学员
        for (MesSwSafeTrainCourse trainCourse : trainCourseList) {
          studentSignList.forEach(v -> v.setCourseId(trainCourse.getId()));
          studentSignService.saveBatch(studentSignList);
        }
        result = true;
        // 删除redis缓存学员列表数据
        redisTemplate.delete(BMSRedisKeyEnum.TRAIN_PLAN_STUDENT.getKey());
        logger.debug("培训计划审批完成,计划ID={}，初始化待签到的课程、学员信息成功！！！", planId);
      }
    }

    return result;
  }

  @Override
  public MesSwSafeTrainSignDTO getCurrentDetail(MesSwSafeTrainSignParam param) {
    if (Objects.isNull(param.getPlanId()) || Objects.isNull(param.getTrainDate())) {
      logger.error("请求参数不符合规范");
      return null;
    }
    MesSwSafeTrainSignDTO trainSignDTO = new MesSwSafeTrainSignDTO();

    MesSwSafeTrainCourseParam courseParam = new MesSwSafeTrainCourseParam();
    courseParam.setPlanId(param.getPlanId());
    courseParam.setTrainDate(param.getTrainDate());
    List<MesSwSafeTrainCourseDTO> courseList = trainCourseService.queryList(courseParam, true);
    if (!CollectionUtils.isEmpty(courseList)) {
      MesSwSafeTrainCourseDTO courseDTO = courseList.get(0);
      trainSignDTO.setPlanId(courseDTO.getPlanId());
      trainSignDTO.setTrainDate(courseDTO.getTrainDate());
      trainSignDTO.setTrainContent(courseDTO.getTrainContent());
      trainSignDTO.setTrainTeacher(courseDTO.getTrainTeacher());
      trainSignDTO.setTrainTeacherName(courseDTO.getTrainTeacherName());
      trainSignDTO.setCourseList(courseList);
    }

    return trainSignDTO;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean teacherSign(MesSwSafeTrainSignParam param) {
    boolean result = false;
    // 参数校验
    if (null == param.getCourseId()
        || StringUtils.isBlank(param.getSignTeacherCode())
        || StringUtils.isBlank(param.getSignTeacherName())
        || StringUtils.isBlank(param.getSignTeacherUrl())) {
      logger.error("请求参数不符合规范！param={}", param);
      throw new BMSException("error", "请求参数不符合规范");
    }
    // 根据课程id查询课程详情
    MesSwSafeTrainCourseDTO trainCourse = trainCourseService.getCurrentDetail(param.getCourseId());
    if (trainCourse != null) {
      // 判断前端提供的教师签到账号是否是该课程的签到老师
      if (!trainCourse.getTrainTeacher().contains(param.getSignTeacherCode())) {
        throw new BMSException("error", "提供的教师签到账号无此课程签到权限");
      }
      if (!trainCourse.isTeacherSignClick()) {
        throw new BMSException("error", "当前用户无此课程签到权限");
      }
      // 进行课程的教师签到信息、课程状态变更
      trainCourse.setTeacherSignTime(LocalDateTime.now()); // 教师签到时间，当前时间
      trainCourse.setSignTeacherCode(param.getSignTeacherCode()); // 签到教师账号
      trainCourse.setSignTeacherName(param.getSignTeacherName()); // 签到教师姓名
      trainCourse.setSignTeacherUrl(param.getSignTeacherUrl()); // 教师签名URL
      trainCourse.setCourseStatus(TrainCourseStatusEnum.SIGNED.getCode()); // 课程状态：课程已签到。
      result = trainCourseService.updateById(trainCourseConvert.toEntity(trainCourse));
    }

    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean studentSign(MesSwSafeTrainSignParam param) {
    boolean result = false;
    // 参数校验
    if (Objects.isNull(param.getCourseId())
        || StringUtils.isBlank(param.getStudentCode())
        || StringUtils.isBlank(param.getStudentName())
        || StringUtils.isBlank(param.getStudentUrl())) {
      logger.error("请求参数不符合规范！param={}", param);
      throw new BMSException("error", "请求参数不符合规范");
    }
    // 查询学员签到详情
    MesSwSafeTrainStudentSignDTO studentSign =
        studentSignService.getCurrentDetail(param.getCourseId(), param.getStudentCode());
    if (studentSign != null) {
      if (!param.getStudentCode().equals(studentSign.getStudentCode())) {
        throw new BMSException("error", "提供的学员账号无此课程签到权限");
      }
      // 查询课程详情信息
      MesSwSafeTrainCourseDTO courseDTO =
          trainCourseService.getCurrentDetail(studentSign.getCourseId());
      // 只有课程状态在发起签到状态，才可以进行学员签到
      if (!TrainCourseStatusEnum.START_SIGN.getCode().equals(courseDTO.getCourseStatus())) {
        throw new BMSException("error", "此课程暂未发起签到");
      }
      if (!studentSign.isStudentSignClick()) {
        throw new BMSException("error", "当前学员无此课程签到权限");
      }
      // 进行学员签到信息、签到状态变更
      studentSign.setSignTime(LocalDateTime.now()); // 学员签到时间
      studentSign.setStudentCode(param.getStudentCode()); // 签到学员账号
      studentSign.setStudentName(param.getStudentName()); // 签到学员姓名
      studentSign.setStudentUrl(param.getStudentUrl()); // 签到学员签名url
      studentSign.setSignStatus(TrainStudentSignStatusEnum.SIGNED.getCode()); // 学员签到状态：已签到

      result = studentSignService.updateById(studentSignConvert.toEntity(studentSign));

      // 课程下所有签到人员都签到，课程学员签到状态已完成
      if (result) {
        List<MesSwSafeTrainStudentSignDTO> signList =
            studentSignService.queryListByCourseId(studentSign.getCourseId());
        // 已完成签到人员
        List<MesSwSafeTrainStudentSignDTO> signFinishList =
            signList.stream()
                .filter(v -> TrainStudentSignStatusEnum.SIGNED.getCode().equals(v.getSignStatus()))
                .collect(Collectors.toList());
        if (signList.size() == signFinishList.size()) {
          // 课程表状态更新
          LambdaUpdateChainWrapper<MesSwSafeTrainCourse> updateCourse =
              new LambdaUpdateChainWrapper<>(trainCourseService.getBaseMapper());
          updateCourse
              .eq(MesSwSafeTrainCourse::getId, studentSign.getCourseId())
              .set(MesSwSafeTrainCourse::getSignFinish, TrainSignFinishEnum.FINISHED.getCode())
              .update();
        }
      }
    }

    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean startEndSign(MesSwSafeTrainSignParam param) {
    boolean result = false;
    if (param.getCourseId() == null) {
      logger.error("请求参数不符合规范！param={}", param);
      throw new BMSException("error", "请求参数不符合规范");
    }
    // 根据课程id查询课程详情
    MesSwSafeTrainCourseDTO trainCourse = trainCourseService.getCurrentDetail(param.getCourseId());
    if (Objects.nonNull(trainCourse)) {
      if (!trainCourse.isStartEndSignClick()) {
        throw new BMSException("error", "当前用户无此课程开启/停止签到权限");
      }

      if (TrainCourseStatusEnum.SIGNED.getCode().equals(trainCourse.getCourseStatus())) {
        // 如果当前课程状态是【课程已签到】状态,为进行发起签到操作
        // 课程状态变更为发起签到。
        trainCourse.setCourseStatus(TrainCourseStatusEnum.START_SIGN.getCode());
        trainCourse.setStartSignTime(LocalDateTime.now()); // 第一次发起签到时间
      } else if (TrainCourseStatusEnum.START_SIGN.getCode().equals(trainCourse.getCourseStatus())) {
        // 如果当前课程状态是【发起签到】状态,为进行停止签到操作
        // 课程状态变更为停止签到。
        trainCourse.setCourseStatus(TrainCourseStatusEnum.END_SIGN.getCode());
        trainCourse.setEndSignTime(LocalDateTime.now()); // 最新一次停止签到时间
      } else if (TrainCourseStatusEnum.END_SIGN.getCode().equals(trainCourse.getCourseStatus())) {
        // 如果当前课程状态是【停止签到】状态,为进行发起签到操作
        // 课程状态变更为发起签到。
        trainCourse.setCourseStatus(TrainCourseStatusEnum.START_SIGN.getCode());
      }
      // 进行开启/停止签到信息、课程状态变更
      result = trainCourseService.updateById(trainCourseConvert.toEntity(trainCourse));
      if (result) { // 发起签到，给学员发送培训签到通知
        try {
          //          this.sendSignNotice(param.getCourseId());
          MesSwSafeTrainCourseDTO course = trainCourseService.getCurrentDetail(param.getCourseId());
          if (Objects.nonNull(course)) {
            if (TrainCourseStatusEnum.START_SIGN.getCode().equals(course.getCourseStatus())) {
              String trainDateStr = course.getTrainDate() + " " + course.getCourseTime();
              String content =
                  NotifyEnum.SAFE_TRAIN_SIGN
                      .getDesc()
                      .replace("date", trainDateStr)
                      .replace("content", course.getTrainContent());
              // 查询该课程签到学员列表
              List<MesSwSafeTrainStudentSignDTO> studentSignList =
                  studentSignService.queryListByCourseId(param.getCourseId());
              if (!CollectionUtils.isEmpty(studentSignList)) {
                for (MesSwSafeTrainStudentSignDTO studentSign : studentSignList) {
                  MesNotice mesNotice = new MesNotice();
                  mesNotice.setReceiverAccount(studentSign.getStudentCode());
                  mesNotice.setReceiverAccountName(studentSign.getStudentName());
                  mesNotice.setType(NotifyEnum.NOTIFICATION.getDesc());
                  mesNotice.setTargetId(String.valueOf(studentSign.getId()));
                  mesNotice.setTitle(NotifyEnum.SAFE_TRAIN_SIGN.getKey());
                  mesNotice.setTargetTypeName("安全培训签到");
                  mesNotice.setContent(content);
                  mesNotice.setJumpRoute("/safety/safetyMonitor/testTraining/safetyCheckInPage");
                  mesNotice.setTargetType("SAFE_SIGN_NOTICE");
                  mesNotice.setJumpParam(
                      course.getPlanId()
                          + "&trainDate="
                          + course.getTrainDate()
                          + "&courseId="
                          + course.getId());
                  mesNotice.setPriority("2");
                  websocketService.sendMessage(mesNotice);
                }
              }
            }
          }
          logger.debug("课程id={}，发起签到，给学员发送培训签到通知成功！！！", param.getCourseId());
        } catch (Exception e) {
          logger.error("发起签到，给学员发送培训签到通知异常！", e);
          throw new BMSException("error", "发起/停止签到失败，请联系管理员");
        }
      }
    }

    return result;
  }

  @Override
  public List<MesSwSafeTrainStudentSignDTO> getTrainStudentList(Long planId) {
    if (planId == null) {
      return null;
    }
    MesSwSafeTrainCourseParam param = new MesSwSafeTrainCourseParam();
    param.setPlanId(planId);
    List<MesSwSafeTrainCourseDTO> courseList = trainCourseService.queryList(param, false);
    if (!CollectionUtils.isEmpty(courseList)) {
      // 查询课程学员信息列表
      return studentSignService.queryListByCourseId(courseList.get(0).getId());
    }

    return null;
  }

  /**
   * 发送安全培训课程签到通知
   *
   * @param courseId 课程id
   */
  private void sendSignNotice(Long courseId) {
    MesSwSafeTrainCourseDTO course = trainCourseService.getCurrentDetail(courseId);
    if (Objects.nonNull(course)) {
      if (TrainCourseStatusEnum.START_SIGN.getCode().equals(course.getCourseStatus())) {
        String trainDateStr = course.getTrainDate() + " " + course.getCourseTime();
        String content =
            NotifyEnum.SAFE_TRAIN_SIGN
                .getDesc()
                .replace("date", trainDateStr)
                .replace("content", course.getTrainContent());
        // 查询该课程签到学员列表
        List<MesSwSafeTrainStudentSignDTO> studentSignList =
            studentSignService.queryListByCourseId(courseId);
        if (!CollectionUtils.isEmpty(studentSignList)) {
          for (MesSwSafeTrainStudentSignDTO studentSign : studentSignList) {
            MesNotice mesNotice = new MesNotice();
            mesNotice.setReceiverAccount(studentSign.getStudentCode());
            mesNotice.setReceiverAccountName(studentSign.getStudentName());
            mesNotice.setType(NotifyEnum.NOTIFICATION.getDesc());
            mesNotice.setTargetId(String.valueOf(studentSign.getId()));
            mesNotice.setTitle(NotifyEnum.SAFE_TRAIN_SIGN.getKey());
            mesNotice.setTargetTypeName("安全培训签到");
            mesNotice.setContent(content);
            mesNotice.setJumpRoute("/safety/safetyMonitor/testTraining/safetyCheckInPage");
            mesNotice.setTargetType("SAFE_SIGN_NOTICE");
            mesNotice.setJumpParam(
                course.getPlanId()
                    + "&trainDate="
                    + course.getTrainDate()
                    + "&courseId="
                    + course.getId());
            mesNotice.setPriority("2");
            websocketService.sendMessage(mesNotice);
          }
        }
      }
    }
  }
}
