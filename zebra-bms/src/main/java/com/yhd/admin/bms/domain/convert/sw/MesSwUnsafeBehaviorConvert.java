package com.yhd.admin.bms.domain.convert.sw;


import com.yhd.admin.bms.domain.dto.sw.MesSwUnsafeBehaviorDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwUnsafeBehavior;
import com.yhd.admin.bms.domain.query.sw.MesSwUnsafeBehaviorParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwUnsafeBehaviorVO;
import org.mapstruct.Mapper;


/**
 * 不安全行为
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-26
 */
@Mapper(componentModel = "spring")
public interface MesSwUnsafeBehaviorConvert {

    MesSwUnsafeBehavior toEntity(MesSwUnsafeBehaviorParam param);

    MesSwUnsafeBehaviorVO toVO(MesSwUnsafeBehaviorDTO dto);

    MesSwUnsafeBehaviorDTO toDTO(MesSwUnsafeBehavior entity);

}
