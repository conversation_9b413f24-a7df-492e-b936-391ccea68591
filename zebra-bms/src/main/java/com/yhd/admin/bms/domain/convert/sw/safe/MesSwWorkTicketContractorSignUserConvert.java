package com.yhd.admin.bms.domain.convert.sw.safe;

import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketContractorSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketContractorSignUser;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketContractorSignUserParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwWorkTicketContractorSignUserVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwWorkTicketContractorSignUserConvert {
  MesSwWorkTicketContractorSignUser toEntity(MesSwWorkTicketContractorSignUserParam param);

  List<MesSwWorkTicketContractorSignUser> toEntity(
      List<MesSwWorkTicketContractorSignUserParam> paramList);

  MesSwWorkTicketContractorSignUser dtoToEntity(MesSwWorkTicketContractorSignUserDTO dto);

  MesSwWorkTicketContractorSignUserDTO toDTO(MesSwWorkTicketContractorSignUser entity);

  List<MesSwWorkTicketContractorSignUserDTO> toDTO(
      List<MesSwWorkTicketContractorSignUser> entityList);

  MesSwWorkTicketContractorSignUserVO toVO(MesSwWorkTicketContractorSignUserDTO dto);
}
