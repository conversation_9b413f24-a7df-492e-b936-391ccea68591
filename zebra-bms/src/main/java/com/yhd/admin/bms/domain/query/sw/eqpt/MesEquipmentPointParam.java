package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 设备健康中心-设备点位管理
 *
 * <AUTHOR>
 * @date 2025/4/23 10:22
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesEquipmentPointParam extends QueryParam implements Cloneable, Serializable {

    /** 主键id列表 */
    private List<Long> ids;
    /** 设备id */
    private Long equipmentDataId;
    /** 设备编码+类别 */
    private String equipmentNoType;
    /** 点位所属部件id */
    private String partsId;
    /** 点位所属部件 */
    private String partsName;
    /** 点位分类 */
    private String pointClassification;
    /** 点位分类代码 */
    private String pointClassificationCode;
    /** 点位名称 */
    private String pointName;
    /** 点位地址，唯一 */
    private String pointAddress;
    /** 点位说明 */
    private String pointExplain;
    /** 点位状态：0禁用，1启用 */
    private Boolean status;

}
