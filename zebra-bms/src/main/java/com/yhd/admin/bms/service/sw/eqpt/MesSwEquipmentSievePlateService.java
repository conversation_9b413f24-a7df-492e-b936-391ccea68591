package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.eqpt.*;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentSievePlate;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentSievePlateParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentSievePlateRecordParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentSievePlateSumVO;
import com.yhd.admin.bms.domain.vo.sys.MesSwChartVO;

import java.util.List;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
public interface MesSwEquipmentSievePlateService extends IService<MesSwEquipmentSievePlate> {

    Boolean saveAdd(MesSwEquipmentSievePlateParam param);

    Boolean modifyPlate(MesSwEquipmentSievePlateParam param);

    MesSwEquipmentSievePlateDTO getCurrentDetail(MesSwEquipmentSievePlateParam param);

    Boolean removePlate(MesSwEquipmentSievePlateParam param);

    List<MesSwEquipmentSievePlateDTO> getPlateList(MesSwEquipmentSievePlateParam param);

    Boolean saveRecord(MesSwEquipmentSievePlateRecordParam param);

    Boolean saveRecords(MesSwEquipmentSievePlateRecordParam param);

    List<MesSwEquipmentSievePlateRecordDTO> getRecordList(MesSwEquipmentSievePlateRecordParam param);

    Boolean remove(MesSwEquipmentSievePlateParam param);

    String exportExcel(MesSwEquipmentSievePlateRecordParam param);

    List<MesSwEquipmentSievePlateSumVO> singlePlate(MesSwEquipmentSievePlateParam param);

    List<MesSwEquipmentSievePlateSumVO> allPlate(MesSwEquipmentSievePlateParam param);

    String exportAllExcel(MesSwEquipmentSievePlateRecordParam param);

    List<MesSwChartVO> consumeTotal(MesSwEquipmentSievePlateRecordParam param);

    List<MesSwChartVO> inputTotal(MesSwEquipmentSievePlateRecordParam param);
    /**
     * 筛板管理-列表页筛机统计
     */
    MesSwEquipmentSievePlateCountDTO getQueryCount(MesSwEquipmentSievePlateParam param);
    /**
     * 按设备
     */
    IPage<MesSwEquipmentSievePlateDTO> pagingQuery(MesSwEquipmentSievePlateParam param);
    /**
     * 按材质
     */
    IPage<MesSwEquipmentSievePlateDTO> pagingQueryByMaterial(MesSwEquipmentSievePlateRecordParam param);
    /**
     * 按孔径
     */
    IPage<MesSwEquipmentSievePlateDTO> pagingQueryByAperture(MesSwEquipmentSievePlateRecordParam param);

    List<MesSwEquipmentSievePlateDTO> getAllPlateList(MesSwEquipmentSievePlateParam param);
    /**
     * 导出筛板布置图
     */
    String exportPlateChart(MesSwEquipmentSievePlateParam param);
    /**
     * 设备使用情况-孔径
     */
    List<MesSwEquipmentSievePlateApertureDTO> getEqptByAperture(MesSwEquipmentSievePlateParam param);
    /**
     * 导出设备使用情况-孔径
     */
    String exportEqptByAperture(MesSwEquipmentSievePlateParam param);
    /**
     * 筛上量-按材质统计
     */
    List<MesSwChartVO> categoryTotal(MesSwEquipmentSievePlateParam param);
    /**
     * 筛上量-按孔径统计
     */
    List<MesSwChartVO> apertureTotal(MesSwEquipmentSievePlateParam param);
}
