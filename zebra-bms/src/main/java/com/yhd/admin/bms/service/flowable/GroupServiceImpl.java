package com.yhd.admin.bms.service.flowable;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.flowable.GroupDao;
import com.yhd.admin.bms.domain.convert.flowable.GroupConvert;
import com.yhd.admin.bms.domain.dto.flowable.GroupDTO;
import com.yhd.admin.bms.domain.entity.flowable.Group;
import com.yhd.admin.bms.domain.entity.flowable.UserGroup;
import com.yhd.admin.bms.domain.entity.sys.SysUserAccount;
import com.yhd.admin.bms.domain.query.flowable.GroupParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/13 19:59
 * @Description:
 * @Version 1.0
 */
@Service
public class GroupServiceImpl extends ServiceImpl<GroupDao, Group> implements GroupService {

    @Resource
    private GroupConvert convert;

    @Resource
    private UserGroupService groupService;

    @Override
    public IPage<GroupDTO> pagingQuery(GroupParam param) {
        Page<Group> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<Group> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(GroupParam queryParam) {
        return this.save(convert.toEntity(queryParam));
    }

    @Override
    public Boolean update(GroupParam queryParam) {
        return this.updateById(convert.toEntity(queryParam));
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        return this.removeByIds(param.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addUserGroupsByGroup(String groupId, List<SysUserAccount> users) {
        if (StringUtils.isNotBlank(groupId)) {
            LambdaQueryWrapper<UserGroup> userGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userGroupLambdaQueryWrapper.eq(UserGroup::getGroup_id_, groupId);
            groupService.remove(userGroupLambdaQueryWrapper);
        }
        List<UserGroup> urs = new ArrayList<>();
        Group group = this.getById(groupId);
        if (!CollectionUtils.isEmpty(users) && StringUtils.isNotBlank(groupId)) {
            users.forEach(user -> {
                UserGroup userGroup = new UserGroup();
                userGroup.setUser_id_(user.getUsername());
                userGroup.setGroup_id_(group.getId_());
                urs.add(userGroup);
            });
        }
        return groupService.saveBatch(urs);
    }
}
