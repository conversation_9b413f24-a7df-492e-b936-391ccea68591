package com.yhd.admin.bms.domain.dto.sw.eqpt;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairHourOrderDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 设备检修工时记录统计表DTO
 *
 * <AUTHOR>
 * @since 2025-07-21 15:24:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentRepairHourOrderStatisticsDTO extends BaseDTO implements Cloneable, Serializable {
    /** 工时记录id */
    private Long orderId;
    /** 日期(yyyy-MM-dd) */
    private LocalDate date;
    /** 检修工时类型-字典 */
    private String typeCode;
    /** 检修工时类型（电工班，钳工班） */
    private String typeName;
    /** 作业人账号 */
    private String workUserCode;
    /** 作业人姓名 */
    private String workUserName;
    /** 工时 */
    private BigDecimal totalWorkHour;
    /** 分数 */
    private Integer score;

    private String post;

    private List<MesSwEquipmentRepairHourOrderDetail> detailList;
}

