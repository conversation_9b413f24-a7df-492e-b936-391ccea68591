package com.yhd.admin.bms.domain.entity.sw.eqpt;

import java.time.LocalDate;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 设备检修维护记录表实体类
 *
 * <AUTHOR>
 * @since 2025-05-30 15:45:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesEquipmentRepairRecord extends BaseEntity implements Cloneable, Serializable {
    /** 设备资料id */
    private Long equipmentDataId;
    /** 设备编码+类别 */
    private String equipmentNoType;
    /** 检修部件 */
    private String equipmentParts;
    /** 检修日期(yyyy-MM-dd) */
    private LocalDate repairDate;
    /** 检修类型：维修、更换 */
    private String repairType;
    /** 检修内容 */
    private String repairContent;
    /** 检修原因 */
    private String repairReason;
    /** 备注 */
    private String remark;
    /** 检修队伍 */
    private String repairTeam;
    /** 检修时长 */
    private String repairTime;
    /** 检修负责人 */
    private String repairLeader;
    /** 检修负责人姓名 */
    private String repairLeaderName;
    /** 现场照片 */
    @TableField(exist = false)
    private List<JSONObject> scenePhoto;
}

