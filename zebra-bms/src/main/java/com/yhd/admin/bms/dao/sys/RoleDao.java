package com.yhd.admin.bms.dao.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.admin.bms.domain.entity.sys.SysRole;
import com.yhd.admin.bms.domain.query.sys.UserAccountParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleDao.java
 * @Description TODO
 * @createTime 2020年03月30日 11:06:00
 */
public interface RoleDao extends BaseMapper<SysRole> {

  /**
   * 根据用户账户查询对应的角色
   *
   * @param param
   * @return
   */
  List<SysRole> selectRoleListByUser(UserAccountParam param);
}
