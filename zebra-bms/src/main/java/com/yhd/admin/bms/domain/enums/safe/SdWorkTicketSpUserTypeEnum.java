package com.yhd.admin.bms.domain.enums.safe;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** 外施工单位送电工作票审批用户类型枚举类 */
@Getter
public enum SdWorkTicketSpUserTypeEnum {
  ZY_FZ("05", "作业负责人"),
  XC_JG("06", "现场监管人"),
  DB_DD("07", "当班调度员"),
  ;

  private final String code;
  private final String desc;

  SdWorkTicketSpUserTypeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static List<String> getDescList() {
    SdWorkTicketSpUserTypeEnum[] values = SdWorkTicketSpUserTypeEnum.values();

    return Arrays.stream(values)
        .map(SdWorkTicketSpUserTypeEnum::getDesc)
        .collect(Collectors.toList());
  }

  public static List<String> getCodeList() {
    SdWorkTicketSpUserTypeEnum[] values = SdWorkTicketSpUserTypeEnum.values();

    return Arrays.stream(values)
        .map(SdWorkTicketSpUserTypeEnum::getCode)
        .collect(Collectors.toList());
  }

  public static SdWorkTicketSpUserTypeEnum getEnumByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    SdWorkTicketSpUserTypeEnum[] values = SdWorkTicketSpUserTypeEnum.values();
    for (SdWorkTicketSpUserTypeEnum value : values) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public static SdWorkTicketSpUserTypeEnum getEnumByName(String name) {
    if (StringUtils.isBlank(name)) {
      return null;
    }
    SdWorkTicketSpUserTypeEnum[] values = SdWorkTicketSpUserTypeEnum.values();
    for (SdWorkTicketSpUserTypeEnum value : values) {
      if (value.getDesc().equals(name)) {
        return value;
      }
    }
    return null;
  }
}
