package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备检修提报复核记录表
 *
 * <AUTHOR>
 * @date 2025/2/10 11:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentRepairTbCheckParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 3623147391919081095L;

  /** 设备检修id */
  private Long repairId;
  /** 提报复核处理人账号，多个逗号分隔 */
  private String clUserCode;
  /** 提报复核处理人姓名，多个逗号分隔 */
  private String clUserName;
  /** 接收时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime receiveTime;
  /** 处理时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime clTime;
  /** 处理描述 */
  private String clDesc;
  /** 处理结果：0未处理，1通过，-1驳回 */
  private String clResult;
  /** 驳回原因 */
  private String bhReason;
}
