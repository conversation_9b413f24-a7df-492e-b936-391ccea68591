package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 设备保护试验台账
 *
 * <AUTHOR>
 * @date 2024/10/26 09:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentProtectLedger extends BaseEntity implements Serializable {
  private static final long serialVersionUID = 6602517290640639734L;

  /** 设备资料id */
  private Long equipmentDataId;
  /** 设备保护点位id */
  private Long equipmentPointId;
  /** 设备编码+类别 */
  private String equipmentNoType;
  /** 保护名称 */
  private String protectName;
  /** 状态 */
  private String statusCode;
  /** 状态名称 */
  private String statusName;
  /** 检查类型 */
  private String checkType;
  /** 1——》通电；2——》未通电 */
  private String powerOnState;
  /** 检查扫描截止时间 */
  private LocalDateTime checkStopTime;
  /** 开始时间 */
  private LocalDate startDate;
  /** 结束时间 */
  private LocalDate endDate;
  /** 领取人 */
  private String receiveAccount;
  /** 领取人名称 */
  private String receiveName;
  /** 完成时间 */
  private LocalDateTime finishTime;
}
