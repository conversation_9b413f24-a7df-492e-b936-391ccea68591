package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesLargePartStatisticDTO;
import com.yhd.admin.bms.domain.dto.sw.MesMaterialManagementDTO;
import com.yhd.admin.bms.domain.entity.sw.MesMaterialManagement;
import com.yhd.admin.bms.domain.query.sw.MesMaterialManagementParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 物资管理表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-11 16:48:28
 */
public interface MesMaterialManagementService extends IService<MesMaterialManagement> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MesMaterialManagementDTO> pagingQuery(MesMaterialManagementParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MesMaterialManagementDTO> queryList(MesMaterialManagementParam param);

    /**
     * 查询部件分类
     *
     * @return 查询结果
     */
    List<String> queryPartCategory();

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MesMaterialManagementParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MesMaterialManagementDTO getCurrentDetails(MesMaterialManagementParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MesMaterialManagementParam param);

    String importExcel(MultipartFile file);

    String export(MesMaterialManagementParam param);

    /**
     * 大部件智能分析分页查询
     *
     * @param param 参数
     * @return 分页查询结果
     */
    IPage<MesLargePartStatisticDTO> getLargePart(MesMaterialManagementParam param);
}
