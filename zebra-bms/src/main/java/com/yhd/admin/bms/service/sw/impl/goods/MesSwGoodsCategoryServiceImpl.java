package com.yhd.admin.bms.service.sw.impl.goods;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.goods.MesSwGoodsCategoryDao;
import com.yhd.admin.bms.domain.convert.sw.goods.MesSwGoodsCategoryConvert;
import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsCategoryDTO;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsCategory;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsStock;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsCategoryParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsCategoryService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsStockService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物资分类-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/25 13:58
 */
@Service
public class MesSwGoodsCategoryServiceImpl
    extends ServiceImpl<MesSwGoodsCategoryDao, MesSwGoodsCategory>
    implements MesSwGoodsCategoryService {

  private final MesSwGoodsCategoryConvert categoryConvert;
  private final MesSwGoodsStockService goodsStockService;

  public MesSwGoodsCategoryServiceImpl(
      MesSwGoodsCategoryConvert categoryConvert, MesSwGoodsStockService goodsStockService) {
    this.categoryConvert = categoryConvert;
    this.goodsStockService = goodsStockService;
  }

  @Override
  public List<MesSwGoodsCategoryDTO> queryList() {
    return selectChildrenById(0L);
  }

  @Override
  public List<MesSwGoodsCategoryDTO> selectChildrenById(Long id) {
    List<MesSwGoodsCategory> allList = baseMapper.selectChildren(id);
    List<MesSwGoodsCategoryDTO> categoryDTOList =
        allList.stream()
            .map(categoryConvert::toDTO)
            .sorted(Comparator.comparing(MesSwGoodsCategoryDTO::getSort))
            .collect(Collectors.toList());

    Map<Long, List<MesSwGoodsCategoryDTO>> allChildren =
        categoryDTOList.stream()
            .sorted(Comparator.comparing(MesSwGoodsCategoryDTO::getSort))
            .collect(Collectors.groupingBy(MesSwGoodsCategoryDTO::getParentId));

    categoryDTOList.forEach(o -> o.setChildren(allChildren.get(o.getId())));

    return categoryDTOList.stream()
        .filter(o -> o.getParentId().equals(id))
        .collect(Collectors.toList());
  }

  @Override
  public MesSwGoodsCategoryDTO getCurrentDetail(MesSwGoodsCategoryParam param) {
    return categoryConvert.toDTO(this.getById(param.getId()));
  }

  @Override
  public Boolean add(MesSwGoodsCategoryParam param) {
    return this.save(categoryConvert.toEntity(param));
  }

  @Override
  public Boolean modify(MesSwGoodsCategoryParam param) {
    MesSwGoodsCategory entity = categoryConvert.toEntity(param);
    // 上级分类不能为自身
    if (entity.getId().equals(entity.getParentId())) {
      throw new BMSException("error", "上级分类不能为自身");
    }

    return this.updateById(entity);
  }

  @Override
  public Boolean modifyStatus(MesSwGoodsCategoryParam param) {
    MesSwGoodsCategory entity = this.getById(param.getId());
    if (param.getStatus().equals(entity.getStatus())) { // 状态未做变更
      return true;
    } else {
      // 停用操作：1. 判断是否有子分类存在启用状态。2. 判断此分类是否被物资引用
      if (entity.getStatus()) {
        List<MesSwGoodsCategory> categoryList =
            this.lambdaQuery()
                .eq(MesSwGoodsCategory::getParentId, param.getId())
                .eq(MesSwGoodsCategory::getStatus, Boolean.TRUE)
                .list();
        if (!CollectionUtils.isEmpty(categoryList)) {
          throw new BMSException("error", "子级分类存在启用状态");
        }
        // 判断此分类是否被物资引用
        List<MesSwGoodsStock> goodsStocks =
            goodsStockService
                .lambdaQuery()
                .eq(MesSwGoodsStock::getCategoryCode, entity.getId())
                .list();
        if (!CollectionUtils.isEmpty(goodsStocks)) {
          throw new BMSException("error", "此分类被物资引用");
        }
      } else { // 启用操作：判断上级分类是否启用
        List<MesSwGoodsCategory> categoryList =
            this.lambdaQuery()
                .eq(MesSwGoodsCategory::getId, entity.getParentId())
                .eq(MesSwGoodsCategory::getStatus, Boolean.FALSE)
                .ne(MesSwGoodsCategory::getParentId, 0L)
                .list();
        if (!CollectionUtils.isEmpty(categoryList)) {
          throw new BMSException("error", "上级分类未启用");
        }
      }

      entity.setStatus(param.getStatus());

      return this.updateById(entity);
    }
  }

  @Override
  public List<Long> getSubCateIdList(Long id) {
    List<Long> subIdList = Lists.newArrayList();
    // 查询子级数据
    List<MesSwGoodsCategoryDTO> allList = this.selectChildrenById(id);

    List<Long> childrenIdList = getChildrenIdList(id, allList);
    subIdList.addAll(childrenIdList);
    // 添加本级
    subIdList.add(id);

    return subIdList;
  }

  /**
   * 获取子级id列表
   *
   * @param id 分类id
   * @param cateList 子级数据列表
   * @return 子级id列表
   */
  private List<Long> getChildrenIdList(Long id, List<MesSwGoodsCategoryDTO> cateList) {
    List<Long> subIdList = Lists.newArrayList();
    for (MesSwGoodsCategoryDTO cate : cateList) {
      if (ObjectUtil.equals(cate.getParentId(), id)) {
        getChildrenIdList(cate.getId(), cateList);

        subIdList.add(cate.getId());
      }
    }

    return subIdList;
  }
}
