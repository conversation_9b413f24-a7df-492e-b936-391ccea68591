package com.yhd.admin.bms.domain.dto.sw;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 交接班
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwHandoverDTO extends BaseDTO implements Serializable, Cloneable {

    /**
     * 交接时间
     */
    private LocalDateTime handoverTime;

    /**
     * 交班人
     */
    private String handover;

    /**
     * 交班人名称
     */
    private String handoverName;

    /**
     * 接班人
     */
    private String successor;

    /**
     * 接班人名称
     */
    private String successorName;

    /**
     * 当班情况
     */
    private String classCondition;
    /**
     * 交接班事宜
     */
    private String handoverCondition;
    /**
     * 设备运转情况
     */
    private String eqptOperation;
    /**
     * 设备卫生
     */
    private String eqptHealth;
    /**
     * 室内工具设施
     */
    private String homeTool;
    /**
     * 室内卫生
     */
    private String homeHealth;
}
