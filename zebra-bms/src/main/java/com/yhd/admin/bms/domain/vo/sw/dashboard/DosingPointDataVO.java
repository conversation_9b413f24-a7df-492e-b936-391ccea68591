package com.yhd.admin.bms.domain.vo.sw.dashboard;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 智能加药看板点位数据
 *
 * <AUTHOR>
 * @date 2024/10/26 10:10
 */
@Data
public class DosingPointDataVO implements Serializable {
  private static final long serialVersionUID = -1603109586134663915L;

  /** 累计原煤入洗量 */
  private BigDecimal rawCoalWashTotal;

  /** 当天阴离子耗药量 */
  private BigDecimal todayAnion;
  /** 累计阴离子耗药量 */
  private BigDecimal totalAnion;
  /** 当天阳离子耗药量 */
  private BigDecimal todayCation;
  /** 累计阳离子耗药量 */
  private BigDecimal totalCation;

  /** 新循环水池-溢流浊度 */
  private BigDecimal new_tank_turbidity;
  /** 旧循环水池-溢流浊度 */
  private BigDecimal old_tank_turbidity;

  /** 1号浓缩池-清水层厚度 */
  private BigDecimal tank_1_layer_thickness;
  /** 1号浓缩池-入料浓度 */
  private BigDecimal tank_1_feed_concentration;
  /** 1号浓缩池-低流浓度 */
  private BigDecimal tank_1_low_flow_concentration;
  /** 2号浓缩池-清水层厚度 */
  private BigDecimal tank_2_layer_thickness;
  /** 2号浓缩池-入料浓度 */
  private BigDecimal tank_2_feed_concentration;
  /** 2号浓缩池-低流浓度 */
  private BigDecimal tank_2_low_flow_concentration;
  /** 3号浓缩池-清水层厚度 */
  private BigDecimal tank_3_layer_thickness;
  /** 3号浓缩池-入料浓度 */
  private BigDecimal tank_3_feed_concentration;
  /** 3号浓缩池-低流浓度 */
  private BigDecimal tank_3_low_flow_concentration;
}
