package com.yhd.admin.bms.domain.convert.sw;


import com.yhd.admin.bms.domain.dto.sw.MesSwWorkRecordAssessmentDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkRecordAssessment;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkRecordAssessmentParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwWorkRecordAssessmentVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 班组人员考核情况
 *
 * <AUTHOR> @qq.com
 * @since 1.0.0 2023-10-10
 */
@Mapper(componentModel = "spring")
public interface MesSwWorkRecordAssessmentConvert {

    MesSwWorkRecordAssessment toEntity(MesSwWorkRecordAssessmentParam param);

    MesSwWorkRecordAssessmentVO toVO(MesSwWorkRecordAssessmentDTO dto);

    MesSwWorkRecordAssessmentDTO toDTO(MesSwWorkRecordAssessment entity);

    List<MesSwWorkRecordAssessment> toEntityList(List<MesSwWorkRecordAssessmentParam> swWorkRecordAssessmentParams);

    List<MesSwWorkRecordAssessmentDTO> toDTOs(List<MesSwWorkRecordAssessment> list);

    List<MesSwWorkRecordAssessmentVO> toVOs(List<MesSwWorkRecordAssessmentDTO> swWorkRecordAssessmentDTOs);
}
