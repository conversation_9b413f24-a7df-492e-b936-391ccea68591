package com.yhd.admin.passport.auth.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * cas集成认证配置项
 *
 * <AUTHOR>
 * @date 2024/4/2 9:56
 */
@Data
@Component
@ConfigurationProperties(prefix = "cas.auth")
public class CasAuthProperties {

    /**
     * cas服务端登录地址
     */
    private String serverLoginUrl;
    /**
     * cas服务端登出地址
     */
    private String serverLogoutUrl;
    /**
     * 应用地址
     */
    private String appUrl;
    /**
     * cas登录跳转路径
     */
    private String appLoginUrl;
    /**
     * 重定向地址
     */
    private String appRedirectUrl;
    /**
     * 角色名称
     */
    private int open;

    public boolean isOpen() {
        return (this.open == 1);
    }
}
