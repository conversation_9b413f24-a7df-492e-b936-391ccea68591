package com.yhd.admin.bms.domain.vo.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleVO.java
 * @Description TODO
 * @createTime 2020年04月28日 15:28:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleVO extends BaseVO {

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 角色状态
     */
    private Boolean isEnable;

    /**
     * 权限
     */
    private List<Long> authority;
}
