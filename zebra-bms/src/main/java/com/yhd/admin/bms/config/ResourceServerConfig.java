package com.yhd.admin.bms.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.lang.NonNull;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.DefaultUserAuthenticationConverter;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ResourceServerConfig.java @Description TODO
 * @createTime 2020年03月23日 21:35:00
 */
@Configuration
@EnableResourceServer
@Order(1)
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @NonNull
    @Autowired
    private ResourceServerProperties resourceServerProperties;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private ResAccessDeniedHandler accessDeniedHandler;

    @Autowired
    private ResExceptionEntryPoint entryPoint;

    @Autowired
    private RemoteTokenServices remoteTokenServices;

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {

        DefaultAccessTokenConverter tokenConverter = new DefaultAccessTokenConverter();
        DefaultUserAuthenticationConverter userAuthenticationConverter =
            new DefaultUserAuthenticationConverter();
        userAuthenticationConverter.setUserDetailsService(userDetailsService);
        tokenConverter.setUserTokenConverter(userAuthenticationConverter);
        remoteTokenServices.setAccessTokenConverter(tokenConverter);

        // 设置资源服务器的 id,从配置文件中读取
        resources
            .resourceId(resourceServerProperties.getResourceId())
            .authenticationEntryPoint(entryPoint)
            .accessDeniedHandler(accessDeniedHandler)
            .tokenServices(remoteTokenServices);
    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        // @formatter:off
    http.authorizeRequests()
        .antMatchers(
            "/**/login",
            "/oauth/*",
            "/time/**",
            "/api/**",
            "/websocket-stomp/**",
            "/flow/image",
            "/rcsude/getTemplate",
            "/**/getTemplate",
            "/pm/fcr/getTemplate"
            )
        .permitAll()
        .anyRequest()
        .authenticated()
        .and()
        .sessionManagement()
        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
        .and()
        .exceptionHandling()
        .accessDeniedHandler(accessDeniedHandler)
        .authenticationEntryPoint(entryPoint)
        .and()
        .userDetailsService(userDetailsService)
        .cors()
        .and()
        .csrf()
        .disable();
    // @formatter:off
  }

  @Bean
  public PasswordEncoder bCryptPasswordEncoder() {
    return new BCryptPasswordEncoder();
  }
}
