package com.yhd.admin.bms.controller.sys;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.bms.domain.convert.sys.OrgConvert;
import com.yhd.admin.bms.domain.dto.sys.OrgDTO;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.OrgParam;
import com.yhd.admin.bms.service.sys.OrgService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/1/9 15:13
 */
@RestController
@RequestMapping(value = "/org")
@Slf4j
public class OrgController {
  private final OrgService orgService;
  private final OrgConvert orgConvert;

  private final ObjectMapper objectMapper;

  public OrgController(OrgService orgService, OrgConvert orgConvert, ObjectMapper objectMapper) {
    this.orgService = orgService;
    this.orgConvert = orgConvert;
    this.objectMapper = objectMapper;
  }

  @PostMapping(
      value = "/queryList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson pagingQuery(
      @RequestBody OrgParam orgParam, OAuth2Authentication auth2Authentication) throws IOException {
    if (log.isDebugEnabled()) {
      log.debug("{}", objectMapper.writeValueAsString(auth2Authentication));
    }
    List<OrgDTO> orgDTOList = orgService.queryList(orgParam.getId());
    return RespJson.buildSuccessResponse(orgConvert.toVO(orgDTOList));
  }

  @SysLogs(title = "增加或者编辑组织结构", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson addOrModify(@RequestBody OrgParam orgParam) {
    Boolean retVal = orgService.addOrModifyOrg(orgParam);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @SysLogs(title = "删除组织结构", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson removeBatch(@RequestBody BatchParam batchParam) {
    Boolean retVal = orgService.removeBatch(batchParam.getId());
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/currentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson currentDetail(@RequestBody OrgParam orgParam) {
    OrgDTO orgDTO = orgService.currentDetail(orgParam.getId());
    return RespJson.buildSuccessResponse(orgConvert.toVO(orgDTO));
  }

  @PostMapping(
      value = "/getWorkshopList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getWorkshopList() {
    List<OrgDTO> workshopList = orgService.getWorkshopList();
    return RespJson.buildSuccessResponse(orgConvert.toVO(workshopList));
  }
}
