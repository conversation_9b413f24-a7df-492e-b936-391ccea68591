package com.yhd.admin.bms.domain.entity.sw;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
public class MesCcmBbi extends BaseEntity implements Serializable {
  /** 公司名 */
  private String companyName;

  /** 公司成立时间 */
  private LocalDate established;

  /** 经营范围 */
  private String businessScope;

  /** 登记机关 */
  private String registrationAuthority;

  /** 注册资本 */
  private String registeredCapital;

  /** 企业类型 */
  private String enterpriseType;

  /** 公司地址 */
  private String address;

  /** 组织机构代码 */
  private String organizationCode;

  /** 统一社会信用代码 */
  private String uscCode;

  /** 纳税人识别号 */
  private String tiNumber;

  /** 进出口企业代码 */
  private String ieeCode;

  /** 经营状态 */
  private String operatingStatus;

  /** 经营期限 */
  private String operatingPeriod;

  /** 人员规模 */
  private String staffSize;

  /** 法定代表人 */
  private String legalRepresentative;

  /** 联系人 */
  private String contactPerson;

  /** 电话 */
  private String phone;

  /** 电子邮箱 */
  private String eMail;

  /** 状态：0禁用，1启用 */
  private Boolean status;
}
