package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainEvaluateDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainEvaluate;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainEvaluateParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwTrainEvaluateCountVO;

import java.util.List;

public interface MesSwSafeTrainEvaluateService extends IService<MesSwSafeTrainEvaluate> {
  /**
   * 根据培训计划id获取培训评价表单数据
   *
   * @param planId 培训计划id
   * @return 培训评价表单
   */
  MesSwSafeTrainEvaluateDTO initEvaluateForm(Long planId);
  /**
   * 学员培训评价
   *
   * @param evaluateParam 评价表单
   * @return true成功，false失败
   */
  Boolean studentEvaluate(MesSwSafeTrainEvaluateParam evaluateParam);

  /**
   * 根据条件查询教师评分列表
   *
   * @param param 查询参数
   * @return 评分列表
   */
  List<MesSwSafeTrainEvaluate> queryList(MesSwSafeTrainEvaluateParam param);

  /**
   * 培训评分统计
   *
   * @param param 培训计划id
   * @return 培训评分统计
   */
  MesSwTrainEvaluateCountVO getTrainEvaluateCount(MesSwSafeTrainEvaluateParam param);

  /**
   * 发送培训评价通知
   *
   * @param param 培训计划id
   * @return true成功，false失败
   */
  Boolean sendEvaluateNotice(MesSwSafeTrainEvaluateParam param);
}
