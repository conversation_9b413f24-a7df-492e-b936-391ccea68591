package com.yhd.admin.bms.domain.entity.sw.safe;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 课件评测表实体类
 *
 * <AUTHOR>
 * @since 2025-07-02 09:17:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSafeTrainEvaluation extends BaseEntity implements Cloneable, Serializable {
      /** 培训计划id */
    private Long planId;
      /** 培训内容 */
    private String trainingContent;
      /** 培训教师 */
    private String trainingTeacherName;
      /** 培训日期 */
    private String trainingDate;
      /** 培训课件名称 */
    private String courseWareName;
      /** 评测类型（课件评测、教师评测） */
    private String evaluationType;
      /** 评测人 */
    private String evaluatorName;
    /** 领导姓名 */
    private String trainingLeaderName;
    /** 审批意见 */
    private String opinion;
      /** 总分 */
    private Integer totalScore;
}

