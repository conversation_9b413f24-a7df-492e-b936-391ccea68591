package com.yhd.admin.scada.boot;

import com.yhd.admin.scada.opc.ua.WenZhenCoalWashSubscript;
import com.yhd.admin.scada.service.MesSwEquipmentPointService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.milo.opcua.stack.core.AttributeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.QualifiedName;
import org.eclipse.milo.opcua.stack.core.types.enumerated.MonitoringMode;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoredItemCreateRequest;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoringParameters;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadValueId;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.Unsigned.uint;

/**
 * <AUTHOR>
 * @version 1.0 @Description
 * @createTime 2021/6/2 11:09
 */
@Component
@Slf4j
public class WenZhenCoalWashOPCUARunner implements CommandLineRunner {

  private final WenZhenCoalWashSubscript coalWashSubscript;
  private final MesSwEquipmentPointService pointService;

  public WenZhenCoalWashOPCUARunner(
      WenZhenCoalWashSubscript coalWashSubscript, MesSwEquipmentPointService pointService) {
    this.coalWashSubscript = coalWashSubscript;
    this.pointService = pointService;
  }

  /**
   * Callback used to run the bean.
   *
   * @param args incoming main method arguments
   * @throws Exception on error
   */
  @Override
  public void run(String... args) {
    try {
      log.error(">>>>>>>>>>>>>>>>>>> 温振数据采集启动");
      // 先在tb_mes_sw_equipment_point表里初始化化KEEPSERVER地址和采集点位
      List<String> pointList = pointService.queryList();

      log.error(">>>>>>>>>>>>>>>>>>> 温振数据采集点位:{}", pointList.size());
      List<MonitoredItemCreateRequest> monitoredItemList = new ArrayList<>(pointList.size());
      for (int i = 0; i < pointList.size(); i++) {
        String item = pointList.get(i);

        ReadValueId readValueId =
            new ReadValueId(
                NodeId.parse(item), AttributeId.Value.uid(), null, QualifiedName.NULL_VALUE);

        MonitoringParameters parameters =
            new MonitoringParameters(
                uint(i),
                1000.0, // sampling interval
                null, // filter, null means use default
                uint(10), // queue size
                true // discard oldest
                );

        MonitoredItemCreateRequest request =
            new MonitoredItemCreateRequest(readValueId, MonitoringMode.Reporting, parameters);
        monitoredItemList.add(request);
      }

      coalWashSubscript.onSubPoint(monitoredItemList);
    } catch (Exception e) {
      log.error("WenZhenCoalWashOPCUARunner 启动失败>>>>>>>>>>>>>>>>>>>,{}", e.toString());
    }
  }
}
