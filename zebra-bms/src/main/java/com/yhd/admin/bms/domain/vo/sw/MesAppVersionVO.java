package com.yhd.admin.bms.domain.vo.sw;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/27 16:57
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesAppVersionVO extends BaseVO implements Serializable, Cloneable {
  /** 版本号 */
  private String newVerCode;
  /** 附件大小 */
  private Integer newApkSize;
  /** 下载地址 */
  private String apkUrl;
  /** 版本名称 */
  private String versionInfo;
  /** 版本描述 */
  private String appDesc;
  /** 是否强制升级;0自升级1强制升级 */
  private String forceUpdate;
  /** 版本类型 */
  private String versionType;

  private String file;

  private Integer firstCode;

  private Integer secondCode;

  private Integer thirdCode;

  /** 设备类型 */
  private String eqptType;
}
