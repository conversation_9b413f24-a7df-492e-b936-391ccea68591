package com.yhd.admin.bms.domain.entity.sw.produce;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电厂皮带901生产产量(定时读取KIO数据)
 *
 * <AUTHOR>
 * @date 2025/1/6 14:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesProduceDcpd extends BaseEntity {
  private static final long serialVersionUID = -3249550426876706512L;
  /** 统计日期 */
  private String statsDate;
  /** 电厂皮带901生产产量t */
  private Double quantity;
}
