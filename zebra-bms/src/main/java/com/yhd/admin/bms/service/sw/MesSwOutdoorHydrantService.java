package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwOutdoorHydrantDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwOutdoorHydrant;
import com.yhd.admin.bms.domain.query.sw.MesSwOutdoorHydrantParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

/**
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface MesSwOutdoorHydrantService extends IService<MesSwOutdoorHydrant> {
    /**
     * 分页查询
     */
    IPage<MesSwOutdoorHydrantDTO> pagingQuery(MesSwOutdoorHydrantParam param);

    /**
     * 查询详情
     */
    MesSwOutdoorHydrantDTO getCurrentDetail(MesSwOutdoorHydrantParam param);

    /**
     * 新增
     */
    Boolean add(MesSwOutdoorHydrantParam param);

    Boolean modify(MesSwOutdoorHydrantParam queryParam);

    Boolean removeBatch(BatchParam batchParam);
}
