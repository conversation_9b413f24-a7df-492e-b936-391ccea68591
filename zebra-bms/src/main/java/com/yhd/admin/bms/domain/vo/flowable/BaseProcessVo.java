package com.yhd.admin.bms.domain.vo.flowable;

import java.util.List;

public class BaseProcessVo {
  /**********************任务相关的参数**********************/
  /** 任务id 必填 */
  private String taskId;
  /**********************审批意见的参数**********************/
  /** 操作人code 必填 */
  private String userCode;
  /** 审批意见 必填 */
  private String message;
  /** 流程实例的id 必填 */
  private String processInstanceId;
  /** 审批类型 必填 */
  private String type;
  /** 集控员* */
  private String centraliController;

  /** 送电集控员* */
  private String sdCentraliController;

  /** 电工 * */
  private List<String> sdElectrician;

  public List<String> getSdElectrician() {
    return sdElectrician;
  }

  public void setSdElectrician(List<String> sdElectrician) {
    this.sdElectrician = sdElectrician;
  }

  public String getSdCentraliController() {
    return sdCentraliController;
  }

  public void setSdCentraliController(String sdCentraliController) {
    this.sdCentraliController = sdCentraliController;
  }

  /** 电工 * */
  private List<String> electrician;

  public String getCentraliController() {
    return centraliController;
  }
  /** 机电经理 */
  private String eleManager;
  /** 电工(电气类) */
  private List<String> dqelectrician;

  /** 申请人 */
  private String applyer;

  /** 车间主任 */
  private String workshopDirector;

  /** 送电机电经理 */
  private String electromechanicaler;

  public String getElectromechanicaler() {
    return electromechanicaler;
  }

  public void setElectromechanicaler(String electromechanicaler) {
    this.electromechanicaler = electromechanicaler;
  }

  public String getApplyer() {
    return applyer;
  }

  public void setApplyer(String applyer) {
    this.applyer = applyer;
  }

  public String getWorkshopDirector() {
    return workshopDirector;
  }

  public void setWorkshopDirector(String workshopDirector) {
    this.workshopDirector = workshopDirector;
  }

  public String getEleManager() {
    return eleManager;
  }

  public void setEleManager(String eleManager) {
    this.eleManager = eleManager;
  }

  public List<String> getDqelectrician() {
    return dqelectrician;
  }

  public void setDqelectrician(List<String> dqelectrician) {
    this.dqelectrician = dqelectrician;
  }

  public void setCentraliController(String centraliController) {
    this.centraliController = centraliController;
  }

  public List<String> getElectrician() {
    return electrician;
  }

  public void setElectrician(List<String> electrician) {
    this.electrician = electrician;
  }

  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public String getUserCode() {
    return userCode;
  }

  public void setUserCode(String userCode) {
    this.userCode = userCode;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getProcessInstanceId() {
    return processInstanceId;
  }

  public void setProcessInstanceId(String processInstanceId) {
    this.processInstanceId = processInstanceId;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }
}
