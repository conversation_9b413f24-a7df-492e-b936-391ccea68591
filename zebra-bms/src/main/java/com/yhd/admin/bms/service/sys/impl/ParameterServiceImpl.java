package com.yhd.admin.bms.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sys.ParameterDao;
import com.yhd.admin.bms.domain.convert.sys.ParameterConvert;
import com.yhd.admin.bms.domain.dto.sys.ParameterDTO;
import com.yhd.admin.bms.domain.entity.sys.SysParameter;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.ParameterParam;
import com.yhd.admin.bms.service.sys.ParameterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ParameterServiceImpl.java @Description TODO
 * @createTime 2020年05月12日 15:02:00
 */
@Service
public class ParameterServiceImpl extends ServiceImpl<ParameterDao, SysParameter>
    implements ParameterService {

  private final ParameterConvert convert;

  public ParameterServiceImpl(ParameterConvert convert) {
    this.convert = convert;
  }

  @Override
  public Boolean add(ParameterParam addParam) {
    return this.save(convert.toEntity(addParam));
  }

  @Override
  public Boolean modify(ParameterParam modifyParam) {
    return this.updateById(convert.toEntity(modifyParam));
  }

  @Override
  public Boolean removeBatch(BatchParam removeParam) {
    return this.removeByIds(removeParam.getId());
  }

  @Override
  public IPage<ParameterDTO> pagingQuery(ParameterParam queryParam) {
    IPage<SysParameter> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<SysParameter> queryChainWrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    queryChainWrapper
        .like(
            StringUtils.isNotBlank(queryParam.getParamKey()),
            SysParameter::getParamKey,
            queryParam.getParamKey())
        .eq(
            StringUtils.isNotBlank(queryParam.getParamVal()),
            SysParameter::getParamVal,
            queryParam.getParamVal())
        .eq(
            StringUtils.isNotBlank(queryParam.getParamType()),
            SysParameter::getParamType,
            queryParam.getParamType())
        .eq(queryParam.getIsEnable() != null, SysParameter::getIsEnable, queryParam.getIsEnable());
    return queryChainWrapper.page(iPage).convert(convert::toDTO);
  }

  @Override
  public ParameterDTO currentDetail(Long id) {
    return convert.toDTO(this.getById(id));
  }

  @Override
  public Boolean validateIfNotExist(ParameterParam param) {
    LambdaQueryChainWrapper<SysParameter> validateChain = new LambdaQueryChainWrapper<>(baseMapper);
    validateChain
        .notIn(param != null, SysParameter::getId, param.getId())
        .eq(SysParameter::getParamKey, param.getParamKey());
    return CollectionUtils.isEmpty(validateChain.list());
  }

  @Override
  public ParameterDTO getDetailByKey(String key) {
    LambdaQueryWrapper<SysParameter> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(StringUtils.isNotBlank(key), SysParameter::getParamKey, key);
    return convert.toDTO(super.getOne(queryWrapper));
  }

  @Override
  public List<ParameterDTO> getDetailByKey(List<String> keys) {
    LambdaQueryChainWrapper<SysParameter> queryChainWrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    queryChainWrapper.in(SysParameter::getParamKey, keys);
    return queryChainWrapper.list().stream().map(convert::toDTO).collect(toList());
  }

  @Override
  public List<ParameterDTO> getParamVal(ParameterParam queryParam) {
    LambdaQueryChainWrapper<SysParameter> queryChainWrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    queryChainWrapper
        .select(SysParameter::getParamVal)
        .eq(
            StringUtils.isNotBlank(queryParam.getParamType()),
            SysParameter::getParamType,
            queryParam.getParamType());
    queryChainWrapper.groupBy(SysParameter::getParamVal);
    return queryChainWrapper.list().stream().map(convert::toDTO).collect(toList());
  }

  @Override
  public ParameterDTO currentDetailByParam(ParameterParam queryParam) {
    if (StringUtils.isNotBlank(queryParam.getParamKey())) {
      return getDetailByKey(queryParam.getParamKey());
    } else {
      return convert.toDTO(this.getById(queryParam.getId()));
    }
  }

    @Override
    public List<SysParameter> getDetailLikeKey(String key) {
        LambdaQueryWrapper<SysParameter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SysParameter::getParamKey, key);
        return baseMapper.selectList(queryWrapper);
    }
}
