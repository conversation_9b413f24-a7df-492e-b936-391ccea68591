package com.yhd.admin.bms.service.sys.impl;

import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.service.sys.CacheCleanUp;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/2 09:55
 */

@Service
public class CacheCleanUpImpl implements CacheCleanUp {
    private final RedisTemplate redisTemplate;

    public CacheCleanUpImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void userMenu() {
        String redisKey = String.format(BMSRedisKeyEnum.USER_MENU.getKey(), "*","*");
        Set<String> keys = redisTemplate.keys(redisKey);

        redisTemplate.delete(keys);
    }
}
