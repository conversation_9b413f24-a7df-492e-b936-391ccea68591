package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesAppVersionDTO;
import com.yhd.admin.bms.domain.entity.sw.MesAppVersion;
import com.yhd.admin.bms.domain.query.sw.MesAppVersionParam;
import com.yhd.admin.bms.domain.vo.sw.MesAppVersionVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2021/12/27 16:58
 * @description
 */
@Mapper(componentModel = "spring")
public interface MesAppVersionConvert {

  MesAppVersionDTO toDTO(MesAppVersion appVersion);

  MesAppVersion toEntity(MesAppVersionParam appVersionParam);

  MesAppVersionVO toVO(MesAppVersionDTO appVersionDTO);
}
