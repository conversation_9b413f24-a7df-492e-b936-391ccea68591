package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 室外消火栓试验维护记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MesSwOutdoorHydrantDetailParam extends QueryParam {

    private static final long serialVersionUID = 1L;
    /**
     * 主表id
     */
    private Long relationId;

    /**
     * 试验维护时间
     */
    private String maintainTime;
    /**
     * 设施编号
     */
    private String equipNo;
    /**
     * 维护人员
     */
    private String maintainer;
    /**
     * 维护人员姓名
     */
    private String maintainerName;
    /**
     * 试验维护存在隐患
     */
    private String troubleDesc;
    /**
     * 隐患消除情况（是否）
     */
    private String eliminationCondition;
    /**
     * 隐患消除时间
     */
    private String eliminationTime;
    /**
     * 复查人
     */
    private String doubleChecker;
    /**
     * 复查人姓名
     */
    private String doubleCheckerName;
    /**
     * 备注
     */
    private String remark;


}
