plugins {
    id 'java'
    id 'war'
}
group = 'com.yhd'
version = '0.0.1-SNAPSHOT'

dependencies {
    implementation project(':zebra-common')
    implementation files('lib/khapi.jar')
    implementation 'org.apache.thrift:libthrift:0.9.1'
    implementation rootProject.ext.dependencies['commons-lang3']
    implementation rootProject.ext.dependencies['commons-pool2']
    implementation rootProject.ext.dependencies['commons-io']
    implementation rootProject.ext.dependencies['mybatis-plus-boot-starter']
    implementation rootProject.ext.dependencies['mapstruct']
    implementation rootProject.ext.dependencies['minio']
    implementation rootProject.ext.dependencies['commons-codec']
    implementation rootProject.ext.dependencies['jpush']
    implementation rootProject.ext.dependencies['poi']
    implementation rootProject.ext.dependencies['poi-ooxml']
    implementation rootProject.ext.dependencies['fastJson']
    implementation rootProject.ext.dependencies['hutool']
    implementation rootProject.ext.dependencies['guava']
    implementation rootProject.ext.dependencies['mssql']
    implementation rootProject.ext.dependencies['easypoi']
    implementation rootProject.ext.dependencies['easypoi-annotation']
    implementation rootProject.ext.dependencies['easypoi-base']
    implementation rootProject.ext.dependencies['redisson']
    implementation rootProject.ext.dependencies['flowable']
    implementation rootProject.ext.dependencies['flowable-engine']
    implementation rootProject.ext.dependencies['commons-math3']
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.security:spring-security-acl'
    implementation 'org.springframework.cloud:spring-cloud-starter-oauth2'
    implementation 'org.springframework.cloud:spring-cloud-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-freemarker'
    implementation 'com.google.zxing:core:3.5.1'
    implementation 'com.google.zxing:javase:3.5.1'

    implementation 'org.apache.iotdb:iotdb-session'
    implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:4.3.1'
    implementation 'org.postgresql:postgresql'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    implementation 'mysql:mysql-connector-java'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor rootProject.ext.dependencies['mapstruct-processor']
    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
    testImplementation rootProject.ext.dependencies['junit-jupiter-api']
    testImplementation rootProject.ext.dependencies['junit-jupiter-params']
    testImplementation rootProject.ext.dependencies['junit-jupiter-engine']
    testAnnotationProcessor rootProject.ext.dependencies['mapstruct-processor']

}
test {
    useJUnitPlatform()
}
