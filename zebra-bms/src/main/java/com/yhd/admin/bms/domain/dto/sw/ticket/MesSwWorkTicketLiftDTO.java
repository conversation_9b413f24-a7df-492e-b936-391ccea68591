package com.yhd.admin.bms.domain.dto.sw.ticket;

import com.yhd.admin.bms.domain.dto.sw.MesSwWorkTicketConstSignUserDTO;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketLiftSignUserVO;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 吊装作业许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-28
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesSwWorkTicketLiftDTO extends BaseDTO implements Cloneable, Serializable {

	/**
	* 许可证编号
	*/
	private String ticketNo;

	/**
	* 申请单位
	*/
	private String applyUnit;

	/**
	* 编制日期
	*/
	private LocalDate orgDate;

	/**
	* 作业地点
	*/
	private String workLocation;

	/**
	* 作业内容
	*/
	private String workContent;

    /**
     * 许可证有效日期-拼接
     */
    private String ticketStartOrEndTime;

	/**
	* 许可证有效日期-开始日期
	*/
	private LocalDateTime ticketStartTime;

	/**
	* 许可证有效日期-结束日期
	*/
	private LocalDateTime ticketEndTime;

	/**
	* 起吊总重量
	*/
	private String ticketWeight;

    /**
     * 吊重作业类型
     */
    private String ticketType;

    /**
     * 吊重作业类型code集合("ticketTypeList":"["aa","bb"]")
     */
    private List<String> ticketTypeList;

    /**
     * 可能产生的危害
     */
    private String ticketHarm;

    /**
     * 可能产生的危害code集合("ticketHarmList":"["aa","bb"]")
     */
    private List<String> ticketHarmList;

    /**
     * 涉及其他特种作业
     */
    private String ticketSpecial;

    /**
     * 涉及其他特种作业code集合("ticketSpecialList":"["aa","bb"]")
     */
    private List<String> ticketSpecialList;

    /**
     * 安全措施落实情况
     */
    private String ticketSafeMeasure;

    /**
     * 安全措施落实情况code集合("ticketSafeMeasureList":"["aa","bb"]")
     */
    private List<String> ticketSafeMeasureList;

	/**
	* 其他安全检查补充事项
	*/
	private String otherSafeCheck;

	/**
	* 操作人员签字
	*/
	private String operateSign;

	/**
	* 监护人员签字
	*/
	private String custodySign;

	/**
	* 指挥人员签字
	*/
	private String commandSign;

	/**
	* 作业负责人签字
	*/
	private String dutySign;

    /**
     * 作业人员确认签字
     */
    private String dutyConfirmSign;

    /**
     * 车间验收人签字
     */
    private String workshopConfirmSign;

    /**
     * 厂部验收人签字
     */
    private String factoryConfirmSign;

    /**
     * 许可证状态code
     */
    private String statusCode;

    /**
     * 许可证状态name
     */
    private String statusName;

    /**
    * 工作票审批用户列表
    */
    private List<MesSwWorkTicketLiftSignUserDTO> userSpTypeList;

    /**
     * 责任车间人员集合
     */
    private List<UserAccountDTO> dutyWorkshopUsers;

    /**
     * 安监员集合
     */
    private List<UserAccountDTO> safeUsers;

    /**
     * 技术员集合
     */
    private List<UserAccountDTO> techUsers;

    /**
     * 值班集合
     */
    private List<UserAccountDTO> leadUsers;

    /**
     * 是否能审批
     */
    private Boolean isCanSp = false;

}
