package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 安全培训评价表
 *
 * <AUTHOR>
 * @date 2024/3/14 11:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwSafeTrainEvaluateParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 3918297017908432212L;

  /** 培训计划主键id */
  private Long planId;
  /** 学员账号 */
  private String student;
  /** 学员姓名 */
  private String studentName;
  /** 培训内容 */
  private String trainContent;
  /** 培训综合评分 */
  private Double pxzhPf;
  /** 培训教师评价详情 */
  private List<MesSwSafeTrainTeacherEvaluateDetailParam> teacherEvaluateDetail;
}
