package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 课件评测表Param
 *
 * <AUTHOR>
 * @since 2025-07-02 09:17:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSafeTrainEvaluationDetailParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** 评测id */
    private Long evaluationId;
    /** 评测内容 */
    private String evaluationStandard;
    /** 评测内容code */
    private String evaluationStandardCode;
    /** 排序 */
    private Long orderNum;
    /** 评测人 */
    private String evaluatorName;
    /** 分数 */
    private Integer score;
    /** 单项总分 */
    private Integer maxScore;
}

