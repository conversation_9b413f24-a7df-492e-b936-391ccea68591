package com.yhd.admin.bms.controller.sw;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesPreClassMeetConvert;
import com.yhd.admin.bms.domain.dto.sw.MesPreClassMeetDTO;
import com.yhd.admin.bms.domain.query.sw.MesPreClassMeetParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesPreClassMeetVO;
import com.yhd.admin.bms.service.sw.MesPreClassMeetService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <p>
 * 班前会记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@RestController
@RequestMapping(value = "/preClassMeet")
public class MesPreClassMeetController {

    /**
     * service
     */
    @Resource
    private MesPreClassMeetService service;

    @Resource
    private MesPreClassMeetConvert convert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesPreClassMeetVO> pagingQuery(@RequestBody MesPreClassMeetParam queryParam) {
        IPage<MesPreClassMeetDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesPreClassMeetParam queryParam, OAuth2Authentication authentication) {
        queryParam.setCreatedBy(authentication.getName());
        service.add(queryParam);
        return RespJson.buildSuccessResponse();
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MesPreClassMeetParam queryParam) {
        service.modify(queryParam);
        return RespJson.buildSuccessResponse();
    }

    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> addOrModify(@RequestBody MesPreClassMeetParam param) {
        Boolean retVal = service.addOrModify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)

    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        service.removeBatch(batchParam);
        return RespJson.buildSuccessResponse();
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getCurrentDetail(@RequestBody MesPreClassMeetParam queryParam) {
        service.getCurrentDetail(queryParam);
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(queryParam)));
    }

    @PostMapping(
        value = "/getTodayConferee",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getTodayConferee(@RequestBody MesPreClassMeetParam param) {
        return RespJson.buildSuccessResponse(service.getTodayConferee(param));
    }

    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> export(@RequestBody BatchParam param) {
        try {
            return RespJson.buildSuccessResponse(service.export(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }
}
