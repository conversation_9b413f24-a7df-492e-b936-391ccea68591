package com.yhd.admin.bms.domain.entity.sw;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 点表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-11-25
 */
@EqualsAndHashCode(callSuper=true)
@Data
public class MesPlcPoint extends BaseEntity implements Cloneable, Serializable {

	/**
	* 设备ID
	*/
	private String deviceNo;

	/**
	* 设备名称
	*/
	private String deviceName;

	/**
	* 点位地址
	*/
	private String pointAddress;

	/**
	* 点位名称
	*/
	private String pointName;

	/**
	* 所属系统
	*/
	private String sys;

	/**
	* 链接方式
	*/
	private String connect;

	/**
	* 类型
	*/
	private String types;

	/**
	* 类型名称
	*/
	private String typesName;

	/**
	* KEEPSERVER地址
	*/
	private String kwpAddress;

	/**
	* 优先级
	*/
	private Integer priority;

	/**
	* 订阅
	*/
	private Boolean onSubscription;

	/**
	* 启用
	*/
	private Boolean enable;

	/**
	* 是否推送kafka
	*/
	private Boolean kafka;

	/**
	* 是否推送websocket
	*/
	private Boolean websocket;

	/**
	* 是否入库
	*/
	private Boolean db;

	/**
	* 是否入msql
	*/
	private Boolean mysql;

	/**
	* 是否过滤
	*/
	private Boolean filter;

	/**
	* 缩放
	*/
	private BigDecimal zoom;

	/**
	* 单位
	*/
	private String unit;

	/**
	* 是否存入消息表
	*/
	private Integer notice;

}
