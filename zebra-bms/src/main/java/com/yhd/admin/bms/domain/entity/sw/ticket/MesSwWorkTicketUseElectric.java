package com.yhd.admin.bms.domain.entity.sw.ticket;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/6/17 8:37
 * @Version 1.0
 */

@EqualsAndHashCode(callSuper = false)
@Data
public class MesSwWorkTicketUseElectric extends BaseEntity implements Cloneable, Serializable {
    /**申请单位*/
    private String applyUnit;
    /**临时用电回路名称code*/
    private String useElectricCode;
    /**临时用电回路名称*/
    private String useElectricName;
    /**计划作业开始时间*/
    private LocalDateTime useStartTime;
    /**计划作业结束时间*/
    private LocalDateTime useEndTime;
    /**作业内容集合*/
    private String taskContent;
    /**危险因素辨识*/
    private String dangerFactor;
    /**工作票状态code*/
    private String statusCode;
    /**工作票状态name*/
    private String statusName;
    /**申请人code*/
    private String applyUserCode;
    /**申请人name*/
    private String applyUserName;

    /** 计划施工时间区间：yyyy-MM-dd hh:mm~yyyy-MM-dd hh:mm */
    @TableField(exist = false)
    private String sgTimeStr;
    /** 是否能审批：true可以，false不可以 */
    @TableField(exist = false)
    private Boolean isCanSp = false;
    /** 是否能作废：true可以，false不可以 */
    @TableField(exist = false)
    private Boolean isCanZf = false;
}
