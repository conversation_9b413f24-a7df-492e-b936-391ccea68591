package com.yhd.admin.bms.domain.convert.sw.safe;

import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketTsdApproveDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketTsdApprove;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketTsdApproveParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwWorkTicketTsdApproveVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwWorkTicketTsdApproveConvert {
  MesSwWorkTicketTsdApprove toEntity(MesSwWorkTicketTsdApproveParam param);

  MesSwWorkTicketTsdApprove dtoToEntity(MesSwWorkTicketTsdApproveDTO dto);

  MesSwWorkTicketTsdApproveDTO toDTO(MesSwWorkTicketTsdApprove entity);

  List<MesSwWorkTicketTsdApproveDTO> toDTO(List<MesSwWorkTicketTsdApprove> entityList);

  MesSwWorkTicketTsdApproveVO toVO(MesSwWorkTicketTsdApproveDTO dto);
}
