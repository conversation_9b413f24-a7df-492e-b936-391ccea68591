package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备巡检工单流程模板-配置表
 *
 * <AUTHOR>
 * @date 2023/12/08 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentWorkOrder extends BaseEntity implements Serializable {
  private static final long serialVersionUID = 6602517290640655678L;

  /** 设备类型 */
  private String equipmentType;
  /** 车间类型 */
  private String workShopCode;
  /** 工单类别类型 */
  private String classificationCode;
  /** 工单名称 */
  private String name;
  /** 内容 */
  private String orderContent;
  /** 审批工种 */
  private String approveNode;
}
