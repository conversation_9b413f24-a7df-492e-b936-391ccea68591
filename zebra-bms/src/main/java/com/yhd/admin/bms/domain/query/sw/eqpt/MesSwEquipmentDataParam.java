package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentUserDTO;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 设备资料表
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentDataParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 6602517290640659483L;

  /** 主键id列表 */
  private List<Long> ids;

  /** 设备编码类别关键词查询 */
  private String noTypeKey;

  /** 设备编码 */
  private String no;
  /** 设备名称 */
  private String name;
  /** 所属系统 */
  private String sysName;
  /** 设备类型名称 */
  private String typeName;
  /** 设备类型编码 */
  private String typeCode;
  /** 使用单位名称 */
  private String orgName;
  /** 使用单位编码 */
  private String orgCode;
  /** 对象代码 */
  private String objectCode;
  /** 规格型号 */
  private String model;
  /** 主要参数 */
  private String mainParameter;
  /** 序列号 */
  private String sn;
  /** 生产厂家 */
  private String factory;
  /** 生产日期(yyyy-MM-dd) */
  private LocalDate produceDate;
  /** 使用日期(yyyy-MM-dd) */
  private LocalDate useDate;
  /** 过煤量(万吨) */
  private Double coalCount;
  /** 原值(元) */
  private Double originalValue;
  /** 设备状态名称 */
  private String statusName;
  /** 设备状态编码 */
  private String statusCode;
  /** 地理位置 */
  private String location;
  /** 功能位置 */
  private String useLocation;
  /** 出厂编号 */
  private String deliveryNo;
  /** 国产进口 */
  private String domesticImports;
  /** 大修情况 */
  private String bigRepair;
  /** 退租时间(yyyy-MM-dd) */
  private LocalDate outDate;
  /** 附件url */
  private String fileUrl;
  /** 资料情况 */
  private String dataDesc;
  /** 备注 */
  private String remark;

  /** 所属车间 */
  private String workShopCode;

  private String workShopName;

  private List<String> workShopCodeList;
  private List<String> workShopNameList;

  /** 功率（kw） */
  private String power;

  /** 设备分类 */
  private String classificationCode;

  private String classificationName;

  /** 保护试验车间 */
  private String protectShopCode;
  /** 保护试验车间 */
  private String protectShopName;
  /** 通电状态点位锁存 */
  private String powerStatus;

  /** 包机人 */
  private String charterPerson;

  /** 单驱/双驱 */
  private String singleOrDouble;

  private List<MesSwEquipmentUserDTO> charterPersonList;

  /** 保护点位 */
  private List<MesSwEquipmentProtectPointParam> pointList;
}
