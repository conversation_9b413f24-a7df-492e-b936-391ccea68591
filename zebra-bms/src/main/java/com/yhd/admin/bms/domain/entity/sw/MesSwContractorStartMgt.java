package com.yhd.admin.bms.domain.entity.sw;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 承包商开工管理审批单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_mes_sw_contractor_start_management")
public class MesSwContractorStartMgt extends BaseEntity implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * 承包商所属组织
     */
    private String contractorOrg;
    /**
     * 承包商所属组织名称
     */
    private String contractorOrgName;
    /**
     * 承包商责任人
     */
    private String contractorLeader;
    /**
     * 承包商责任人名称
     */
    private String contractorLeaderName;
    /**
     * 发起时间
     */
    private String startTime;
    /**
     * 流程状态code
     */
    private String statusCode;
    /**
     * 流程状态name
     */
    private String statusName;
    /**
     * 流程实例ID
     */
    private String processInstanceId;


}
