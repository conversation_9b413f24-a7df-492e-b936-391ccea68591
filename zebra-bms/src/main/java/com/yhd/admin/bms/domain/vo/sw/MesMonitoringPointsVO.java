package com.yhd.admin.bms.domain.vo.sw;

import java.io.Serializable;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> @Date 2024/6/28 15:04 @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesMonitoringPointsVO extends BaseVO implements Serializable, Cloneable {
  /** 监控点名称 */
  private String name;

  /** 所属区域 */
  private String location;

  /** IP地址 */
  private String ip;

  /** 端口号 */
  private String port;

  /** 设备账号 */
  private String account;

  /** 登录密码 */
  private String password;

  // 通道号
  private Long channelNumber;

  // 通道类型
  private String channelType;
  // 监控点类型
  private String monitorPointType;
}
