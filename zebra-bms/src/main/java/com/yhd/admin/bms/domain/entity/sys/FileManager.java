package com.yhd.admin.bms.domain.entity.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class FileManager extends BaseEntity implements Serializable, Cloneable {

  /** 关联Id */
  private Long relationId;
  /** 业务类型 */
  private String businessType;
  /** 附件地址 */
  private String fileUrl;
  /** 附件名称 */
  private String fileName;
  /** 附件类型 */
  private String fileType;
}
