package com.yhd.admin.common.cfg;

import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.lang.Nullable;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/12/1 21:25
 */
public class KeyStringRedisSerializer implements RedisSerializer<String> {

  /*
   * (non-Javadoc)
   * @see org.springframework.data.redis.serializer.RedisSerializer#deserialize(byte[])
   */
  @Override
  public String deserialize(@Nullable byte[] bytes) {
    return (bytes == null
        ? null
        : new String(bytes, StandardCharsets.UTF_8));
  }

  /*
   * (non-Javadoc)
   * @see org.springframework.data.redis.serializer.RedisSerializer#serialize(java.lang.Object)
   */
  @Override
  public byte[] serialize(@Nullable String string) {
    return (string == null ? null : string.getBytes(StandardCharsets.UTF_8));
  }
}
