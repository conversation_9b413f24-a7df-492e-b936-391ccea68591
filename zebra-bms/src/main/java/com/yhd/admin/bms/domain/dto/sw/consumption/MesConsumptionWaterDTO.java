package com.yhd.admin.bms.domain.dto.sw.consumption;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 能耗管理-水耗
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesConsumptionWaterDTO extends BaseDTO implements Serializable {
  /** 日期 */
  private LocalDate date;
  /** 水表1 m³ */
  private BigDecimal water1;
  /** 水表2 m³ */
  private BigDecimal water2;
  /** 水表3 m³ */
  private BigDecimal water3;
  /** 总流量 m³ */
  private BigDecimal totalWater;
  /** 原煤产量 t */
  private BigDecimal rowCoal;
  /** 吨煤水耗 m³/t */
  private BigDecimal waterConsumptionDm;

  private String month;

  private String dateStr;
}
