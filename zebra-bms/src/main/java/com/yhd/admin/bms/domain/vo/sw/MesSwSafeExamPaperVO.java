package com.yhd.admin.bms.domain.vo.sw;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/1 16:44
 * @Version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafeExamPaperVO extends BaseVO implements Cloneable, Serializable {
    /**
     * 作者账号
     */
    private String authorAccount;
    /**
     * 作者名称
     */
    private String authorName;
    /**
     * 试卷标题
     */
    private String paperTitle;
    /**
     * 试卷分类
     */
    private String paperClassify;
    /**
     * 难度组成
     */
    private String paperDiff;
    /**
     * 试卷总分值
     */
    private Integer paperScore;
    /**
     * 试卷状态：0禁用，1启用
     */
    private Boolean status;

    /**
     * 试题集合
     */
    private List<MesSwSafeExamPaperTopicVO> topicList;
}
