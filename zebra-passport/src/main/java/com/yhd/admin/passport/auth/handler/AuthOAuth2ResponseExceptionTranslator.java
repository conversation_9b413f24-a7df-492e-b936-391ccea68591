package com.yhd.admin.passport.auth.handler;

import com.yhd.admin.common.domain.enums.ExceptionCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.DefaultThrowableAnalyzer;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.security.web.util.ThrowableAnalyzer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("authOAuth2ResponseExceptionTranslator")
@Slf4j
public class AuthOAuth2ResponseExceptionTranslator implements
    WebResponseExceptionTranslator<OAuth2Exception> {

  private final ThrowableAnalyzer throwableAnalyzer = new DefaultThrowableAnalyzer();

  @Override
  public ResponseEntity<OAuth2Exception> translate(Exception e) throws Exception {
    // Try to extract a SpringSecurityException from the stacktrace
    Throwable[] causeChain = throwableAnalyzer.determineCauseChain(e);
    // 异常栈获取 OAuth2Exception 异常
    Exception ase = (OAuth2Exception) throwableAnalyzer
        .getFirstThrowableOfType(OAuth2Exception.class, causeChain);
    log.info("{}", ase);
    String errorMsg = "";
    if (ase == null) {
      if (e instanceof UsernameNotFoundException) {
        errorMsg = "用户不存在，请联系管理员";
      } else {
        errorMsg = e.getMessage();
      }
    } else {
      ExceptionCodeEnum exceptionCodeEnum = ExceptionCodeEnum.getEnumByCode(ase.getMessage());
      errorMsg = exceptionCodeEnum == null ? causeChain[0].getLocalizedMessage()
          : exceptionCodeEnum.getDesc();
    }
    return ResponseEntity.status(HttpStatus.OK).body(new AuthOAuth2Exception(errorMsg));
  }
}
