package com.yhd.admin.bms.controller.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketCheckPoreConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketCheckPoreDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketCheckPore;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketCheckPoreParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketCheckPoreSignUserParam;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketCheckPoreService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 检修作业工作票-控制层 */
@RestController
@RequestMapping(value = "/ticket/check")
public class MesSwWorkTicketCheckPoreController {
  @Resource private MesSwWorkTicketCheckPoreConvert workTicketCheckPoreConvert;
  @Resource private MesSwWorkTicketCheckPoreService workTicketCheckPoreService;

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<?> pagingQuery(@RequestBody MesSwWorkTicketCheckPoreParam param) {
    IPage<MesSwWorkTicketCheckPoreDTO> page = workTicketCheckPoreService.pagingQuery(param);
    return new PageRespJson<>(page.convert(workTicketCheckPoreConvert::toVO));
  }

  @PostMapping(
      value = "/getList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getList(@RequestBody MesSwWorkTicketCheckPoreParam param) {
    List<MesSwWorkTicketCheckPore> vos = workTicketCheckPoreService.getList(param);
    return RespJson.buildSuccessResponse(workTicketCheckPoreConvert.ensToVos(vos));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getCurrentDetail(@RequestBody MesSwWorkTicketCheckPoreParam param) {
    return RespJson.buildSuccessResponse(
        workTicketCheckPoreConvert.toVO(workTicketCheckPoreService.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> add(
      @RequestBody MesSwWorkTicketCheckPoreParam param, OAuth2Authentication auth2Authentication) {
    param.setCreatedBy(auth2Authentication.getName());
    Boolean retVal = workTicketCheckPoreService.add(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/cancel",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> cancel(
      @RequestBody MesSwWorkTicketCheckPoreParam param, OAuth2Authentication auth2Authentication) {
    param.setUpdatedBy(auth2Authentication.getName());
    Boolean retVal = workTicketCheckPoreService.cancel(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/approve",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> approve(
      @RequestBody MesSwWorkTicketCheckPoreSignUserParam param,
      OAuth2Authentication auth2Authentication) {
    param.setUpdatedBy(auth2Authentication.getName());
    Boolean retVal = workTicketCheckPoreService.approve(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }
}
