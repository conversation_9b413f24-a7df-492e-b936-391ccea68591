package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 承包商开工管理审批单--流程签字表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MesSwContractorStartMgtSignParam extends QueryParam {

    private static final long serialVersionUID = 1L;

    /**
     * 承包商开工管理审批单表主键ID
     */
    private String parentId;
    /**
     * 审批内容json
     */
    private String approvalContent;
    /**
     * 序号
     */
    private String num;
    /**
     * 入厂工作流程步骤
     */
    private String workflowStep;
    /**
     * 具体工作内容
     */
    private String content;
    /**
     * 分管厂领导
     */
    private String contractorLeader;
    /**
     * 分管厂领领导名称
     */
    private String contractorLeaderName;
    /**
     * 部门主管
     */
    private String deptManager;
    /**
     * 签字人账号
     */
    private String signatureAccount;
    /**
     * 签字人名称
     */
    private String signatureName;
    /**
     * 签字图片地址
     */
    private String signatureUrl;
    /**
     * 签字时间
     */
    private String signatureTime;
    /**
     * 分管领导编码
     */
    private String fgLeader;
    /**
     * 分管领导姓名
     */
    private String fgLeaderName;
    /**
     * 下一步处理部门
     */
    private String disposeDept;
    /**
     * 下一步处理人
     */
    private String dispose;
    /**
     * 下一步处理人名称
     */
    private String disposeName;
    /**
     * 流程实例ID
     */
    private String processInstanceId;


}
