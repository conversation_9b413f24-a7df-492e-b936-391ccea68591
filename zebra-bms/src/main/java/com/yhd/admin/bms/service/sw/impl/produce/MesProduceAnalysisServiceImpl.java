package com.yhd.admin.bms.service.sw.impl.produce;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.bms.domain.entity.sw.produce.MesSwProducePlan;
import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.domain.enums.produce.KHProduceAnalysisEnum;
import com.yhd.admin.bms.domain.query.sw.HstTimeStatsParam;
import com.yhd.admin.bms.domain.query.sw.produce.MesBeltDataParam;
import com.yhd.admin.bms.domain.query.sw.produce.MesProduceAnalysisParam;
import com.yhd.admin.bms.domain.vo.sw.dashboard.KHPointDataVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesProduceAnalysisVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwPeBeltDataVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.dashboard.KHPointService;
import com.yhd.admin.bms.service.sw.load.ZcTrainloadService;
import com.yhd.admin.bms.service.sw.produce.MesProduceAnalysisService;
import com.yhd.admin.bms.service.sw.produce.MesProduceHistoryDataService;
import com.yhd.admin.bms.service.sw.produce.MesSwPeAnalysisService;
import com.yhd.admin.bms.service.sw.produce.MesSwProducePlanService;
import com.yhd.admin.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产效率-综合分析
 *
 * <AUTHOR>
 * @Date 2025/4/24 14:10
 * @Version 1.0
 */
@Service
public class MesProduceAnalysisServiceImpl implements MesProduceAnalysisService {
    private static final Logger logger =
        LoggerFactory.getLogger(MesProduceAnalysisServiceImpl.class);
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private MesSwProducePlanService planService;

    @Resource
    private ZcTrainloadService zcTrainloadService;

    @Resource
    private KHPointService pointService;

    @Resource
    private MesProduceHistoryDataService historyDataService;

    @Resource
    private MesSwPeAnalysisService peAnalysisService;

    @Override
    public MesProduceAnalysisVO getAnalysis(MesProduceAnalysisParam param) {
        //根据昨日18点-今日18点的日期规则获取当日
        LocalDate localDate = setLocalDate();
        //撤出电厂状态key
        String redisKey = String.format(BMSRedisKeyEnum.WITHDRAW_POWER_PLANT.getKey(), localDate);
        //判断redis中是否有这个key
        if (!redisTemplate.hasKey(redisKey)) {
            //没有的话代表今天第一次访问，创建一个晚上六点过期的redis
            setExpirationDate(redisKey,1,localDate);
        }
        MesProduceAnalysisVO analysisVO = new MesProduceAnalysisVO();
        LambdaQueryWrapper<MesSwProducePlan> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(MesSwProducePlan::getPlanTimeStr, localDate.toString());
        planWrapper.eq(MesSwProducePlan::getPlanType, "日计划");
        //当日计划
        MesSwProducePlan producePlan = planService.getOne(planWrapper);
        if (producePlan == null) {
            analysisVO.setStopTime("暂无装车计划");
            analysisVO.setRepairTime("--");
        } else {
            //计划列数
            Long ythwy = producePlan.getYthwy();
            //完成列数
            Integer weightCount = zcTrainloadService.sumWeightCount(String.valueOf(localDate));
            //进行中的装车量
            BigDecimal loadingCount = zcTrainloadService.sumLoadingCount(String.valueOf(localDate)) == null ? BigDecimal.ZERO : zcTrainloadService.sumLoadingCount(String.valueOf(localDate));
            //剩余计划 =（计划列数-完成列数）×4100-进行中的装车量
            BigDecimal surplusPlan = new BigDecimal(ythwy - weightCount).multiply(new BigDecimal(4100)).subtract(loadingCount);
            //当剩余计划＜0时按0计算
            if (surplusPlan.compareTo(BigDecimal.ZERO) < 0) {
                surplusPlan = BigDecimal.ZERO;
            }
            //产品仓量
            BigDecimal productNum = BigDecimal.ZERO;
            //点位key列表
            //产品仓1
            String bupro01AmountKey = KHProduceAnalysisEnum.BUPRO01Amount.getKey();
            //产品仓2
            String bupro02AmountKey = KHProduceAnalysisEnum.BUPRO02Amount.getKey();
            //产品仓3
            String bupro03AmountKey = KHProduceAnalysisEnum.BUPRO03Amount.getKey();
            //产品仓4
            String bupro04AmountKey = KHProduceAnalysisEnum.BUPRO04Amount.getKey();
            List<String> keyList = Arrays.asList(bupro01AmountKey, bupro02AmountKey, bupro03AmountKey, bupro04AmountKey);
            //产品仓点位数据集合
            List<KHPointDataVO> pointValueList = null;
            try {
                pointValueList = pointService.getPointValue(keyList);
            } catch (Exception e) {
                throw new BMSException("error", "调用点位接口异常");
            }
            if (!CollectionUtil.isEmpty(pointValueList)) {
                //将每个value累加
                List<Object> decimals = pointValueList.stream()
                    .filter(Objects::nonNull)
                    .map(KHPointDataVO::getValue)
                    .collect(Collectors.toList());
                for (Object decimal : decimals) {
                    if (decimal != null) {
                        productNum = productNum.add(NumberUtil.toBigDecimal(decimal.toString()));
                    }
                }
                //bigDecimal四舍五入取整
                productNum = productNum.setScale(0, RoundingMode.HALF_UP);
            }
            String con901TodayKey = KHProduceAnalysisEnum.CON901Today.getKey();
            //901胶带机日产量点位数据
            KHPointDataVO pointValue = null;
            try {
                pointValue = pointService.getPointValue(con901TodayKey);
            } catch (Exception e) {
                throw new BMSException("error", "调用点位接口异常");
            }
            //电厂日计划-901胶带机日产量
            BigDecimal dcMinus901Plan = BigDecimal.ZERO;
            //判断电厂是否撤出
            if ("1".equals(getValueByKey(redisKey))) {
                //代表：电厂正常取值
                if (pointValue != null) {
                    //电厂月计划
                    BigDecimal dcPlan = BigDecimal.ZERO;
                    LambdaQueryWrapper<MesSwProducePlan> dcPlanWrapper = new LambdaQueryWrapper<>();
                    dcPlanWrapper.eq(MesSwProducePlan::getPlanTimeStr, localDate.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                    dcPlanWrapper.eq(MesSwProducePlan::getPlanType, "月计划");
                    MesSwProducePlan dcProducePlan = planService.getOne(dcPlanWrapper);
                    if (dcProducePlan != null) {
                        //获取当月天数
                        int dayOfMonth = localDate.lengthOfMonth();
                        dcPlan = NumberUtil.toBigDecimal(dcProducePlan.getZgyh())
                            .divide(new BigDecimal(dayOfMonth), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(10000));
                    }
                    //901日产量
                    BigDecimal dc901Num = NumberUtil.toBigDecimal(pointValue.getValue().toString()).setScale(0, RoundingMode.HALF_UP);
                    //如果小于0，则按0计算
                    dcMinus901Plan = dcPlan.subtract(dc901Num).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : dcPlan.subtract(dc901Num);
                }
            }
            //【剩余计划-产品仓量+（电厂日计划-901）】
            BigDecimal incomplete = surplusPlan.subtract(productNum).add(dcMinus901Plan);
            // 201+218-802
            BigDecimal add218Sub802 = get201Add218Sub802();
            if (add218Sub802.compareTo(BigDecimal.ZERO) == 0 || add218Sub802.compareTo(BigDecimal.ZERO) < 0) {
                analysisVO.setStopTime("--");
                analysisVO.setRepairTime("--");
            } else {
                //生产时长=【剩余计划-产品仓量+（电厂日计划-901）】/（201+218-802）
                BigDecimal productionMinute =
                    incomplete.divide(add218Sub802, 1, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(60));
                //分情况判断
                if (productionMinute.compareTo(BigDecimal.ZERO) == 0 || productionMinute.compareTo(BigDecimal.ZERO) < 0) {
                    //判断707皮带是否停机
                    if (this.if707Down()) {
                        //停机取停机的时间
                        String con707StateKey = KHProduceAnalysisEnum.CON707State.getKey();
                        List<String> con707StateKeyList = Arrays.asList(con707StateKey);
                        //点位数据集合
                        Map<String, List<KHPointDataVO>> con707pointValueList = null;
                        try {
                            HstTimeStatsParam statsParam = new HstTimeStatsParam();
                            statsParam.setStartTime(LocalDateTime.now().minusDays(1));
                            statsParam.setEndTime(LocalDateTime.now());
                            statsParam.setPointList(con707StateKeyList);
                            con707pointValueList = pointService.queryPointListByStepped(statsParam);
                        } catch (Exception e) {
                            throw new BMSException("error", "调用点位接口异常");
                        }
                        if (CollectionUtils.isNotEmpty(con707pointValueList.get(con707StateKey))) {
                            List<KHPointDataVO> kh707VOList = con707pointValueList.get(con707StateKey);
                            //将kh707VOList倒序
                            Collections.reverse(kh707VOList);
                            kh707VOList.forEach(p -> {
                                //运行状态
                                String status = p.getValue().toString();
                                if ("2".equals(status) || "3".equals(status)) {
                                    LocalDateTime afterTime = p.getDateTime();
                                    MesProduceAnalysisVO vo = this.getRepairTime(afterTime, localDate);
                                    analysisVO.setStopTime(vo.getStopTime());
                                    analysisVO.setRepairTime(vo.getRepairTime());
                                }
                                //一旦analysisVO的stopTime有数据，则跳出循环
                                if (StringUtils.isNotBlank(analysisVO.getStopTime()) && StringUtils.isNotBlank(analysisVO.getRepairTime())) {
                                    return;
                                }
                            });
                        }
                    } else {
                        //707未停机生产时长按0计算
                        LocalDateTime afterTime = LocalDateTime.now();
                        MesProduceAnalysisVO vo = this.getRepairTime(afterTime, localDate);
                        analysisVO.setStopTime(vo.getStopTime());
                        analysisVO.setRepairTime(vo.getRepairTime());
                    }
                } else {
                    //获取当前时间
                    LocalDateTime nowTime = LocalDateTime.now();
                    //nowTime加上生产时长
                    LocalDateTime afterTime = nowTime.plusMinutes(productionMinute.longValue());
                    MesProduceAnalysisVO vo = this.getRepairTime(afterTime, localDate);
                    analysisVO.setStopTime(vo.getStopTime());
                    analysisVO.setRepairTime(vo.getRepairTime());
                }
            }
        }
        //生产效率、入洗率
        analysisVO.setProduceEfficiency(getProduceEfficiency());
        analysisVO.setWashEfficiency(getWashEfficiency());
        //饼图
        MesBeltDataParam beltDataParam = new MesBeltDataParam();
        LocalDateTime localDateTime = LocalDateTime.of(localDate.getYear(), localDate.getMonth(), localDate.getDayOfMonth(), 18, 0, 0);
        beltDataParam.setStartDateTime(localDateTime.minusDays(1));
        beltDataParam.setEndDateTime(localDateTime);
        analysisVO.setPieChart(historyDataService.getPieChart(beltDataParam));
        //是否可以撤出电厂
        analysisVO.setIfWithdraw(getValueByKey(redisKey));
        return analysisVO;
    }

    @Override
    public Boolean withdrawPowerPlant(MesProduceAnalysisParam param) {
        String redisKey = String.format(BMSRedisKeyEnum.WITHDRAW_POWER_PLANT.getKey(), this.setLocalDate());
        //先判断这个key是否存在
        if (!redisTemplate.hasKey(redisKey)) {
            throw new BMSException("error", "电厂状态还未更新，请刷新重试");
        }
        if (redisTemplate.opsForValue().get(redisKey).equals(0)) {
            throw new BMSException("error", "电厂已经撤出");
        }
        logger.debug("撤出电厂");
        //修改这个key的值为0
        setExpirationDate(redisKey, 0, setLocalDate());
        return true;
    }

    /**
     * 生产效率
     */
    public String getProduceEfficiency() {
        String key = KHProduceAnalysisEnum.EfficiencyToday.getKey();
        //产品仓点位数据集合
        KHPointDataVO pointValue = null;
        try {
            pointValue = pointService.getPointValue(key);
        } catch (Exception e) {
            throw new BMSException("error", "调用点位接口异常");
        }
        if (Objects.nonNull(pointValue)) {
            if (Objects.nonNull(pointValue.getValue())){
                return NumberUtil.toBigDecimal(pointValue.getValue().toString()).setScale(2, RoundingMode.HALF_UP) + "%";
            }
        }
        return "--";
    }

    /**
     * 707皮带是否停机
     */
    public Boolean if707Down() {
        String key = KHProduceAnalysisEnum.CON707State.getKey();
        //产品仓点位数据集合
        KHPointDataVO pointValue = null;
        try {
            pointValue = pointService.getPointValue(key);
        } catch (Exception e) {
            throw new BMSException("error", "调用点位接口异常");
        }
        return "0".equals(pointValue.getValue()) || "1".equals(pointValue.getPoint());
    }

    /**
     * 检修时长
     *
     * @praam afterTime 完成时间 localDate 日期
     */
    public MesProduceAnalysisVO getRepairTime(LocalDateTime afterTime,LocalDate localDate) {
        MesProduceAnalysisVO analysisVO = new MesProduceAnalysisVO();
        //判断完成时间是否晚于17:00
        if (afterTime.isAfter(LocalDateTime.of(localDate.getYear(), localDate.getMonth(), localDate.getDayOfMonth(), 17, 0, 0))) {
            analysisVO.setStopTime("今日无法停机");
            analysisVO.setRepairTime("0");
        } else {
            String stopTime = afterTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            analysisVO.setStopTime(stopTime);
            //检修时长为17:00减去stopTime
            LocalTime time1 = LocalTime.of(17, 0); // 晚上五点
            LocalTime time2 = LocalTime.of(afterTime.getHour(), afterTime.getMinute()); // 完成时间
            // 计算小时和分钟差异
            int hours = time1.getHour() - time2.getHour();
            int minutes = time1.getMinute() - time2.getMinute();
            if (minutes < 0) { // 处理负分钟数的情况，借位
                hours -= 1;
                minutes += 60; // 加60分钟相当于借位一小时，转换为正数表示
            }
            analysisVO.setRepairTime(String.format("%02d小时%02d分钟", hours, minutes));
        }
        return analysisVO;
    }

    /**
     * 入洗率
     */
    public String getWashEfficiency() {
//        BigDecimal productNum = BigDecimal.ZERO;
//        //点位key列表
//        //201日产量
//        String con201TodayKey = KHProduceAnalysisEnum.CON201Today.getKey();
//        //218日产量
//        String con218TodayKey = KHProduceAnalysisEnum.CON218Today.getKey();
//        //301日产量
//        String con301TodayKey = KHProduceAnalysisEnum.CON301Today.getKey();
//        //302日产量
//        String con302TodayKey = KHProduceAnalysisEnum.CON302Today.getKey();
//        List<String> keyList = Arrays.asList(con201TodayKey, con218TodayKey, con301TodayKey, con302TodayKey);
//        //点位数据集合
//        List<KHPointDataVO> pointValueList = null;
//        try {
//            pointValueList = pointService.getPointValue(keyList);
//        } catch (Exception e) {
//            throw new BMSException("error", "调用点位接口异常");
//        }
//        if (!CollectionUtil.isEmpty(pointValueList)) {
//            Map<String, BigDecimal> pointValueMap = pointValueList.stream().filter(p -> p.getValue() != null)
//                .collect(Collectors.toMap(KHPointDataVO::getPoint, p -> NumberUtil.toBigDecimal(p.getValue().toString())));
//            BigDecimal con201Num = pointValueMap.get(con201TodayKey) == null ? BigDecimal.ZERO : pointValueMap.get(con201TodayKey);
//            BigDecimal con218Num = pointValueMap.get(con218TodayKey) == null ? BigDecimal.ZERO : pointValueMap.get(con218TodayKey);
//            BigDecimal con301Num = pointValueMap.get(con301TodayKey) == null ? BigDecimal.ZERO : pointValueMap.get(con301TodayKey);
//            BigDecimal con302Num = pointValueMap.get(con302TodayKey) == null ? BigDecimal.ZERO : pointValueMap.get(con302TodayKey);
//            if ((con201Num.add(con218Num)).compareTo(BigDecimal.ZERO) == 0) {
//                return "--";
//            }
//            productNum = (con301Num.add(con302Num))
//                .divide(con201Num.add(con218Num), 2, RoundingMode.HALF_UP)
//                .multiply(new BigDecimal(100));
//        }
//        return productNum + "%";
        MesSwPeBeltDataVO beltData = peAnalysisService.getBeltData();
        if (Objects.nonNull(beltData)) {
            if (Objects.nonNull(beltData.getDayRate())) {
                return beltData.getDayRate().setScale(2,RoundingMode.HALF_UP) + "%";
            }
        }
        return "--";
    }


    /**
     * 计算201+218-802
     */
    public BigDecimal get201Add218Sub802() {
        //201
        String con201AmountKey = KHProduceAnalysisEnum.CON201Amount.getKey();
        //218
        String con218AmountKey = KHProduceAnalysisEnum.CON218Amount.getKey();
        //802
        String con802AmountKey = KHProduceAnalysisEnum.CON802Amount.getKey();
        List<String> keyList = Arrays.asList(con201AmountKey, con218AmountKey, con802AmountKey);
        //点位数据集合
        Map<String, List<KHPointDataVO>> pointValueList = null;
        try {
            HstTimeStatsParam statsParam = new HstTimeStatsParam();
            statsParam.setStartTime(LocalDateTime.now().minusDays(1));
            statsParam.setEndTime(LocalDateTime.now());
            statsParam.setPointList(keyList);
            pointValueList = pointService.queryPointListByStepped(statsParam);
        } catch (Exception e) {
            throw new BMSException("error", "调用点位接口异常");
        }
        //201平均值
        BigDecimal avg201 = BigDecimal.ZERO;
        //218平均值
        BigDecimal avg218 = BigDecimal.ZERO;
        //802平均值
        BigDecimal avg802 = BigDecimal.ZERO;
        Map<String, List<KHPointDataVO>> finalPointValueList = new HashMap<>();
        if (!CollectionUtil.isEmpty(pointValueList)) {
            pointValueList.forEach((k, v) -> {
                //阈值，201、218是200
                BigDecimal ifEffective = new BigDecimal("200");
                if (con802AmountKey.equals(k)) {
                    //802是50
                    ifEffective = new BigDecimal("50");
                }
                BigDecimal finalIfEffective = ifEffective;
                List<KHPointDataVO> vos = new ArrayList<>();
                if (!CollectionUtil.isEmpty(v)) {
                    vos = v.stream()
                        .filter(pointDataVO -> NumberUtil.toBigDecimal(pointDataVO.getValue().toString()).compareTo(finalIfEffective) > 0)
                        .sorted(Comparator.comparing(KHPointDataVO::getTime).reversed())
                        .limit(60)
                        .collect(Collectors.toList());
                }
                finalPointValueList.put(k, vos);
            });
            //平均值
            if (!CollectionUtil.isEmpty(finalPointValueList.get(con201AmountKey)) && finalPointValueList.get(con201AmountKey).size() > 0) {
                avg201 = covertListToDoubleArray(finalPointValueList.get(con201AmountKey));
            }
            if (!CollectionUtil.isEmpty(finalPointValueList.get(con218AmountKey)) && finalPointValueList.get(con218AmountKey).size() > 0) {
                avg218 = covertListToDoubleArray(finalPointValueList.get(con218AmountKey));
            }
            if (!CollectionUtil.isEmpty(finalPointValueList.get(con802AmountKey)) && finalPointValueList.get(con802AmountKey).size() > 0) {
                avg802 = covertListToDoubleArray(finalPointValueList.get(con802AmountKey));
            }
        }
        return avg201.add(avg218).subtract(avg802);
    }


    /**
     * 创建晚上18：00过期的redis
     */
    public void setExpirationDate(String key,Integer value,LocalDate date) {
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将LocalDate转换为ZonedDateTime，并设置时间为晚上六点
        // 注意：atTime(int hour, int minute, int second) 方法用于设置时间
        ZonedDateTime zdt = date.atTime(18, 0).atZone(zoneId);
        //默认为1，代表正常取值，未撤出电厂
        opsForValue.set(key, value);
        redisTemplate.expireAt(key, Date.from(zdt.toInstant()));
    }

    /**
     * 昨日18点-今日18点为今天
     */
    public LocalDate setLocalDate() {
        LocalDateTime dateTime = LocalDateTime.now();
        LocalDate date = LocalDate.now();
        //如果dateTime超过六点，则代表明天
        if (dateTime.getHour() >= 18) {
            date = date.plusDays(1);
        }
        return date;
    }

    /**
     * KHPointDataVOList计算平均值
     */
    private BigDecimal covertListToDoubleArray(List<KHPointDataVO> pointDataVOList) {
        //将每个value累加
        BigDecimal productNum = BigDecimal.ZERO;
        List<Object> decimals = pointDataVOList.stream()
            .filter(Objects::nonNull)
            .map(KHPointDataVO::getValue)
            .collect(Collectors.toList());
        for (Object decimal : decimals) {
            if (decimal != null) {
                productNum = productNum.add(NumberUtil.toBigDecimal(decimal.toString()));
            }
        }
        BigDecimal scale = productNum.divide(NumberUtil.toBigDecimal(pointDataVOList.size()), 2, RoundingMode.HALF_UP);
        return scale;
    }

    public String getValueByKey(String key) {
        ValueOperations<String, Object> valueOps = redisTemplate.opsForValue();
        return valueOps.get(key).toString();
    }

}
