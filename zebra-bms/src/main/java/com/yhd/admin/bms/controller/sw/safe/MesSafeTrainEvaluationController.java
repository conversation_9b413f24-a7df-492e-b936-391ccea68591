package com.yhd.admin.bms.controller.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSafeTrainEvaluationConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSafeTrainEvaluationDTO;
import com.yhd.admin.bms.domain.dto.sys.DicItemDTO;
import com.yhd.admin.bms.domain.query.sw.safe.MesSafeTrainEvaluationParam;
import com.yhd.admin.bms.service.sw.safe.MesSafeTrainEvaluationService;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSafeTrainEvaluationVO;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 课件评测表控制层
 *
 * <AUTHOR>
 * @since 2025-07-02 09:17:40
 */
@RestController
@RequestMapping("/train/evaluation")
public class MesSafeTrainEvaluationController {
    @Resource
    private MesSafeTrainEvaluationService mesSafeTrainEvaluationService;
    @Resource
    private MesSafeTrainEvaluationConvert mesSafeTrainEvaluationConvert;


    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MesSafeTrainEvaluationParam param) {
        IPage<MesSafeTrainEvaluationDTO> page = mesSafeTrainEvaluationService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mesSafeTrainEvaluationConvert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesSafeTrainEvaluationVO> getCurrentDetail(@RequestBody MesSafeTrainEvaluationParam param) {
        return RespJson.buildSuccessResponse(mesSafeTrainEvaluationConvert.toVO(mesSafeTrainEvaluationService.getCurrentDetails(param)));
    }

    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MesSafeTrainEvaluationParam param) {
        Boolean retVal = mesSafeTrainEvaluationService.addOrModify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }


    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MesSafeTrainEvaluationParam param) {
        Boolean retVal = mesSafeTrainEvaluationService.removeBatch(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/getCourseWareEvaluationStandards",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCourseWareEvaluationStandards() {
        Map<String, List<String>> result = mesSafeTrainEvaluationService.getCourseWareEvaluationStandards();
        return RespJson.buildSuccessResponse(result);
    }

    @PostMapping(
        value = "/getTeacherEvaluationStandards",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getTeacherEvaluationStandards() {
        List<DicItemDTO> result = mesSafeTrainEvaluationService.getTeacherEvaluationStandards();
        return RespJson.buildSuccessResponse(result);
    }

    @PostMapping(
        value = "/getCourseWareEvaluationOpinion",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCourseWareEvaluationOpinion() {
        List<DicItemDTO> result = mesSafeTrainEvaluationService.getCourseWareEvaluationOpinion();
        return RespJson.buildSuccessResponse(result);
    }

}

