package com.yhd.admin.bms.service.sw.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketAscentSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketAscentSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketAscentSignUserParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

/**
 * 登高作业工作票签字用户信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
public interface MesSwWorkTicketAscentSignUserService extends IService<MesSwWorkTicketAscentSignUser> {

    IPage<MesSwWorkTicketAscentSignUserDTO> pagingQuery(MesSwWorkTicketAscentSignUserParam queryParam);

    Boolean add(MesSwWorkTicketAscentSignUserParam param);

    Boolean modify(MesSwWorkTicketAscentSignUserParam param);

    Boolean removeBatch(BatchParam param);

    MesSwWorkTicketAscentSignUserDTO getCurrentDetail(MesSwWorkTicketAscentSignUserParam param);
}
