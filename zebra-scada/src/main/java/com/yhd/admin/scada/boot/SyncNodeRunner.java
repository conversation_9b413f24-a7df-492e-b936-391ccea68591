package com.yhd.admin.scada.boot;

import com.yhd.admin.scada.domain.entity.MesPlcPoint;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;

import javax.annotation.Resource;

/** 启动时自动获取OPC UA Server 发布节点 */
@Slf4j
public class SyncNodeRunner implements CommandLineRunner {

  @Resource
  @Qualifier("coalWashOpcUaClient")
  private OpcUaClient opcUaHelper;

  //
  // @Resource
  // private PLCPointDao pointDao;
  //
  // @Value("${opc.ua.root}")
  // private String id;

  /**
   * Callback used to run the bean.
   *
   * @param args incoming main method arguments
   * @throws Exception on error
   */
  @Override
  public void run(String... args) throws Exception {

    opcUaHelper.connect().get();

    browseNode(opcUaHelper, NodeId.parse("ns=2;s="));
  }

  private void browseNode(OpcUaClient client, NodeId browseRoot) {
    MesPlcPoint entity;
    // try {
    // List<? extends UaNode> nodes = client.getAddressSpace().browseNodes(browseRoot);
    // for (UaNode uaNode : nodes) {
    // String nodeName = uaNode.getBrowseName().getName();
    // if (nodeName.startsWith("_") | nodeName.startsWith("#") | nodeName.startsWith("<")
    // | nodeName.equals("Server") | nodeName.equals("数据类型示例")) {
    // continue;
    // }
    // log.info("NodeId={}", uaNode.getNodeId().toParseableString());
    // if (uaNode instanceof FolderTypeNode) {
    // browseNode(client, uaNode.getNodeId());
    // }
    //
    // entity = new MesPlcPoint();
    // entity.setPointAddress(uaNode.getBrowseName().getName());
    // entity.setKwpAddress(uaNode.getNodeId().toParseableString());
    // entity.setSys("S0");
    // entity.setDeviceName("D0");
    // entity.setDeviceNo("D0");
    // entity.setConnect("OPCUA");
    // entity.setTypes("T1");
    // entity.setTypesName("数据");
    // entity.setEnable(Boolean.TRUE);
    // entity.setOnSubscription(Boolean.TRUE);
    // entity.setDb(Boolean.TRUE);
    //
    // // pointDao.insert(entity);
    //
    // // recursively browse to children
    // }
    // } catch (UaException e) {
    // throw new RuntimeException(e);
    // } catch (UaException e) {
    // throw new RuntimeException(e);
    // }
  }
}
