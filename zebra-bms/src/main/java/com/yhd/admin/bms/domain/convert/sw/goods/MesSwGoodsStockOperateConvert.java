package com.yhd.admin.bms.domain.convert.sw.goods;

import org.mapstruct.Mapper;

import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsStockOperateDTO;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsStockOperate;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsStockOperateParam;
import com.yhd.admin.bms.domain.vo.sw.goods.MesSwGoodsStockOperateVO;

@Mapper(componentModel = "spring")
public interface MesSwGoodsStockOperateConvert {

    MesSwGoodsStockOperate toEntity(MesSwGoodsStockOperateParam param);

    MesSwGoodsStockOperateDTO toDTO(MesSwGoodsStockOperate entity);

    MesSwGoodsStockOperateVO toVO(MesSwGoodsStockOperateDTO dto);
}
