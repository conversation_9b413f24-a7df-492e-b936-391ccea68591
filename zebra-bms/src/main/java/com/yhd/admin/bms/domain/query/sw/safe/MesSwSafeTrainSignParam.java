package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全培训签到信息：培训课程信息 + 学员签到信息
 *
 * <AUTHOR>
 * @date 2024/1/4 16:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwSafeTrainSignParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = -202601983326886894L;

  /** 开始日期(yyyy-MM-dd) */
  private LocalDate startDate;
  /** 结束日期(yyyy-MM-dd) */
  private LocalDate endDate;
  /** 签到教师账号 */
  private String signTeacherCode;
  /** 签到教师姓名 */
  private String signTeacherName;
  /** 签到教师签名url */
  private String signTeacherUrl;
  /** 教师签到时间 */
  private LocalDateTime teacherSignTime;
  /** 课程表主键id */
  private Long courseId;
  /** 学员签到表主键id */
  private Long studentSignId;

  /** 学员账号 */
  private String studentCode;
  /** 学员姓名 */
  private String studentName;
  /** 学员签名url */
  private String studentUrl;

  /** 培训计划主键id */
  private Long planId;
  /** 培训教师，多个逗号分割 */
  private String trainTeacher;
  /** 培训日期(yyyy-MM-dd) */
  private LocalDate trainDate;
  /** 培训内容 */
  private String trainContent;
  /** 与我相关 */
  private Boolean relatedToMe;
  /** 培训课程列表 */
  private List<MesSwSafeTrainCourseParam> courseList;
}
