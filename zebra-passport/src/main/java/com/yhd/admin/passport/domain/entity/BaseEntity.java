package com.yhd.admin.passport.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaseEntity implements Serializable, Cloneable {

  @TableId(type = IdType.AUTO)
  private Long id;

  /**
   * 创建人
   */
  private String createdBy;
  /**
   * 创建时间
   */
  @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED)
  private LocalDateTime createdTime;
  /**
   * 更新人
   */
  private String updatedBy;
  /**
   * 更新时间
   */
  @TableField(updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED)
  private LocalDateTime updatedTime;
}
