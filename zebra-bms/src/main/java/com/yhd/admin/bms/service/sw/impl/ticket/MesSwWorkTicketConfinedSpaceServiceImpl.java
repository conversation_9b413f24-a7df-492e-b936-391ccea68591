package com.yhd.admin.bms.service.sw.impl.ticket;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.config.RedisIdMaker;
import com.yhd.admin.bms.dao.sw.ticket.MesSwWorkTicketConfinedSpaceDao;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketConfinedSpaceConvert;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwWorkTicketConstSignUserDTO;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketConfinedSpaceDTO;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConst;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConstSignUser;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpace;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpaceSignUser;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketConstGcEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketConstSpEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketLiftSpEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwWorkTicketConstSignUserParam;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketConfinedSpaceParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketConfinedSpaceService;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.CollectionUtils;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 受限空间许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-29
 */
@Service
public class MesSwWorkTicketConfinedSpaceServiceImpl extends ServiceImpl<MesSwWorkTicketConfinedSpaceDao, MesSwWorkTicketConfinedSpace> implements MesSwWorkTicketConfinedSpaceService {

    @Resource
    private MesSwWorkTicketConfinedSpaceConvert convert;

    @Resource
    private MesSwWorkTicketConfinedSpaceSignUserConvert signUserConvert;

    @Resource
    private MesSwWorkTicketConfinedSpaceSignUserService signUserService;

    @Resource
    private UserAccountService userAccountService;

    @Resource
    private DicService dicService;

    @Resource
    private RedisIdMaker redisIdMaker;

    //审批状态
    private static final String DZ_TICKET_STATUS = "DZ_TICKET_STATUS";

    @Override
    public IPage<MesSwWorkTicketConfinedSpaceDTO> pagingQuery(MesSwWorkTicketConfinedSpaceParam queryParam) {
        Page<MesSwWorkTicketConfinedSpace> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwWorkTicketConfinedSpace> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //查询条件
        //许可证编号
        queryChain.eq(StringUtils.isNotBlank(queryParam.getTicketNo()),
            MesSwWorkTicketConfinedSpace::getTicketNo, queryParam.getTicketNo());
        //申请单位
        queryChain.eq(StringUtils.isNotBlank(queryParam.getApplyUnit()),
            MesSwWorkTicketConfinedSpace::getApplyUnit, queryParam.getApplyUnit());
        //受限空间名称
        queryChain.eq(StringUtils.isNotBlank(queryParam.getSpaceName()),
            MesSwWorkTicketConfinedSpace::getSpaceName, queryParam.getSpaceName());
        //作业内容
        queryChain.eq(StringUtils.isNotBlank(queryParam.getWorkContent()),
            MesSwWorkTicketConfinedSpace::getWorkContent, queryParam.getWorkContent());
        //编制日期区间
        if (queryParam.getOrgStartDate() != null && queryParam.getOrgEndDate() != null) {
            queryChain.between(MesSwWorkTicketConfinedSpace::getOrgDate,
                queryParam.getOrgStartDate(), queryParam.getOrgEndDate());
        }
        queryChain.orderByDesc(MesSwWorkTicketConfinedSpace::getCreatedTime);
        IPage<MesSwWorkTicketConfinedSpaceDTO> dtos = queryChain.page(iPage).convert(convert::toDTO);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        for (MesSwWorkTicketConfinedSpaceDTO dto : dtos.getRecords()) {
            // 时间格式化
            String startFormat = dto.getTicketStartTime().format(dateTimeFormatter);
            String endFormat = dto.getTicketEndTime().format(dateTimeFormatter);
            dto.setTicketStartOrEndTime(startFormat + "~" + endFormat);
            this.workTicketDataHandle(dto);
        }
        return dtos;
    }

    @Override
    public MesSwWorkTicketConfinedSpaceDTO initialize(MesSwWorkTicketConfinedSpaceParam param) {
        MesSwWorkTicketConfinedSpaceDTO confinedSpaceDTO = new MesSwWorkTicketConfinedSpaceDTO();
        // 审批用户类型列表
        List<String> spTypeList = WorkTicketLiftSpEnum.getValuesList();
        // 初始化审批
        List<MesSwWorkTicketConfinedSpaceSignUser> spList = new ArrayList<>();
        for (String values : spTypeList) {
            MesSwWorkTicketConfinedSpaceSignUser signUser = new MesSwWorkTicketConfinedSpaceSignUser();
            signUser.setUserType(values);
            spList.add(signUser);
        }
        confinedSpaceDTO.setUserSpTypeList(signUserConvert.toDTOs(spList));
        // 责任车间
        confinedSpaceDTO.setDutyWorkshopUsers(userAccountService.getZRCJList());
        // 安监员
        confinedSpaceDTO.setSafeUsers(userAccountService.getAJYList());
        // 技术员
        confinedSpaceDTO.setTechUsers(userAccountService.getJSYList());
        // 值班领导
        confinedSpaceDTO.setLeadUsers(userAccountService.getZBLDList());
        return confinedSpaceDTO;
    }

    @Override
    public Boolean add(MesSwWorkTicketConfinedSpaceParam param) {
        if (StringUtils.isBlank(param.getApplyUnit()) ||
            StringUtils.isBlank(param.getSpaceName()) ||
            StringUtils.isBlank(param.getWorkContent()) ||
            CollectionUtils.isEmpty(param.getTicketPermitList()) ||
            CollectionUtils.isEmpty(param.getTicketDangerList()) ||
            StringUtils.isBlank(param.getEnterSpaceUser()) ||
            StringUtils.isBlank(param.getSceneSafeUser()) ||
            StringUtils.isBlank(param.getOtherSafeCheck()) ||
            CollectionUtils.isEmpty(param.getMeasureSignList())){
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "请完善必填项");
        }
        // 许可证编号
        param.setTicketNo(redisIdMaker.getTicketNo("SXKJTicketNo",4));
        // 需办理其他许可证
        if (!CollectionUtils.isEmpty(param.getTicketPermitList())){
            param.setTicketPermit(StringUtils.join(param.getTicketPermitList(),"#@@#"));
        }
        // 主要危害因素
        if (!CollectionUtils.isEmpty(param.getTicketDangerList())){
            param.setTicketDanger(StringUtils.join(param.getTicketDangerList(),"#@@#"));
        }
        // 贯彻签字确认
        if (!CollectionUtils.isEmpty(param.getMeasureSignList())){
            param.setMeasureSign(StringUtils.join(param.getMeasureSignList(),"#@@#"));
        }
        // 编制日期
        param.setOrgDate(LocalDate.now());
        // 审批状态
        param.setStatusCode("1");
        param.setStatusName(dicService.transform(DZ_TICKET_STATUS, param.getStatusCode()));
        MesSwWorkTicketConfinedSpace entity = convert.toEntity(param);
        boolean save = this.save(entity);
        if (save) {
            // 审批领导
            List<MesSwWorkTicketConfinedSpaceSignUser> spList = signUserConvert.toEntitys(param.getUserSpTypeList());
            spList.forEach(
                v -> {
                    v.setUserName(userAccountService.getNameByUserName(v.getUserAccount()));
                    v.setTicketId(entity.getId());
                    v.setIsSign(false);
                });
            signUserService.saveBatch(spList);
        }
        return save;
    }

    @Override
    public MesSwWorkTicketConfinedSpaceDTO getCurrentDetail(MesSwWorkTicketConfinedSpaceParam param) {
        MesSwWorkTicketConfinedSpaceDTO confinedSpaceDTO = convert.toDTO(this.getById(param.getId()));
        if (StringUtils.isNotBlank(confinedSpaceDTO.getTicketPermit())){
            confinedSpaceDTO.setTicketPermitList(List.of(confinedSpaceDTO.getTicketPermit().split("#@@#")));
        }
        if (StringUtils.isNotBlank(confinedSpaceDTO.getTicketDanger())){
        confinedSpaceDTO.setTicketDangerList(List.of(confinedSpaceDTO.getTicketDanger().split("#@@#")));
        }
        if (StringUtils.isNotBlank(confinedSpaceDTO.getMeasureSign())) {
      confinedSpaceDTO.setMeasureSignList(List.of(confinedSpaceDTO.getMeasureSign().split("#@@#")));
        }
        // 时间格式化
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String startFormat = confinedSpaceDTO.getTicketStartTime().format(dateTimeFormatter);
        String endFormat = confinedSpaceDTO.getTicketEndTime().format(dateTimeFormatter);
        confinedSpaceDTO.setTicketStartOrEndTime(startFormat + "~" + endFormat);
        // 查询工作票签字用户列表信息
        LambdaQueryWrapper<MesSwWorkTicketConfinedSpaceSignUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(MesSwWorkTicketConfinedSpaceSignUser::getTicketId,param.getId());
        List<MesSwWorkTicketConfinedSpaceSignUserDTO> signUserList = signUserConvert.toDTOs(signUserService.list(userWrapper));
        if (Objects.isNull(signUserList) || signUserList.size() == 0){
            throw new BMSException(ResultStateEnum.FAIL.getCode(),"签字表无数据");
        }
        confinedSpaceDTO.setUserSpTypeList(signUserList);
        return confinedSpaceDTO;
    }

    @Override
    public List<String> getTicketNoList(MesSwWorkTicketConfinedSpaceParam param) {
        LambdaQueryWrapper<MesSwWorkTicketConfinedSpace> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getTicketNo())){
            wrapper.like(MesSwWorkTicketConfinedSpace::getTicketNo,param.getTicketNo());
        }
        List<MesSwWorkTicketConfinedSpace> confinedSpaces = this.list(wrapper);
        List<String> nos = confinedSpaces.stream()
            .map(MesSwWorkTicketConfinedSpace::getTicketNo)
            .distinct()
            .collect(Collectors.toList());
        return nos;
    }

    @Override
    public List<String> getApplyUnitList(MesSwWorkTicketConfinedSpaceParam param) {
        LambdaQueryWrapper<MesSwWorkTicketConfinedSpace> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getApplyUnit())){
            wrapper.like(MesSwWorkTicketConfinedSpace::getApplyUnit,param.getApplyUnit());
        }
        List<MesSwWorkTicketConfinedSpace> confinedSpaces = this.list(wrapper);
        List<String> units = confinedSpaces.stream()
            .map(MesSwWorkTicketConfinedSpace::getApplyUnit)
            .distinct()
            .collect(Collectors.toList());
        return units;
    }


    @Override
    public List<String> getSpaceNameList(MesSwWorkTicketConfinedSpaceParam param) {
        LambdaQueryWrapper<MesSwWorkTicketConfinedSpace> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getSpaceName())){
            wrapper.like(MesSwWorkTicketConfinedSpace::getSpaceName,param.getSpaceName());
        }
        List<MesSwWorkTicketConfinedSpace> confinedSpaces = this.list(wrapper);
        List<String> locations = confinedSpaces.stream()
            .map(MesSwWorkTicketConfinedSpace::getSpaceName)
            .distinct()
            .collect(Collectors.toList());
        return locations;
    }

    @Override
    public List<String> getWorkContentList(MesSwWorkTicketConfinedSpaceParam param) {
        LambdaQueryWrapper<MesSwWorkTicketConfinedSpace> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getWorkContent())){
            wrapper.like(MesSwWorkTicketConfinedSpace::getWorkContent,param.getWorkContent());
        }
        List<MesSwWorkTicketConfinedSpace> confinedSpaces = this.list(wrapper);
        List<String> contents = confinedSpaces.stream()
            .map(MesSwWorkTicketConfinedSpace::getWorkContent)
            .distinct()
            .collect(Collectors.toList());
        return contents;
    }

    /**
     * 当前用户列表按钮权限处理
     *
     * @param confinedSpaceDTO 工作票记录
     */
    private void workTicketDataHandle(MesSwWorkTicketConfinedSpaceDTO confinedSpaceDTO) {
        UserDTO userInfo = UserContextHolder.getUserDetail().getUserInfo();
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
          throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
        }
        // 获取工作票未签字用户列表
        List<MesSwWorkTicketConfinedSpaceSignUser> noSignUsers = signUserService.list(new LambdaQueryWrapper<MesSwWorkTicketConfinedSpaceSignUser>()
          .eq(MesSwWorkTicketConfinedSpaceSignUser::getTicketId, confinedSpaceDTO.getId())
          .eq(MesSwWorkTicketConfinedSpaceSignUser::getIsSign, false));
        // 获取工作票未签字审批用户类型用户列表
        List<String> users = noSignUsers.stream()
            .map(MesSwWorkTicketConfinedSpaceSignUser::getUserAccount)
            .collect(Collectors.toList());
        users.forEach(user -> {
            if (user.contains(userInfo.getAccountName())) {
                confinedSpaceDTO.setIsCanSp(Boolean.TRUE);
            }
        });
    }
}
