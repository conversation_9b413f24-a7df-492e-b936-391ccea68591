package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 物资管理表(MesMaterialManagement)Param
 *
 * <AUTHOR>
 * @since 2025-08-11 16:48:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesMaterialManagementParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** 主键ID */
    private Long id;
    /** 设备类型 */
    private String equipmentType;
    /** 设备型号 */
    private String equipmentModel;
    /** 关联设备号 */
    private String relatedEquipmentCode;
    /** 关联设备数量 */
    private Integer relatedEquipmentCount;
    /** 部件分类 */
    private String partCategory;
    /** 部件名称 */
    private String partName;
    /** 部件号 */
    private String partCode;
    /** 部件数量 */
    private BigDecimal partNumber;
    /** 物料编码 */
    private String materialCode;
    /** 物料名称 */
    private String materialName;
    /** 单位 */
    private String unit;
    /** 安全库存 */
    private BigDecimal safetyStock;
    /** 库存储备数量（新） */
    private BigDecimal newInventoryQuantity;
    /** 库存货架号（新） */
    private String newInventoryShelfNumber;
    /** 库存储备数量（旧） */
    private BigDecimal oldInventoryQuantity;
    /** 库存货架号（旧） */
    private String oldInventoryShelfNumber;
    /** 已报计划 */
    private String reportedPlan;
    /** 备注 */
    private String remarks;
}

