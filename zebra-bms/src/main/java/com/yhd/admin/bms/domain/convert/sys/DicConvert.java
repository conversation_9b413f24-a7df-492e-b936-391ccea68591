package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.DicDTO;
import com.yhd.admin.bms.domain.entity.sys.SysDic;
import com.yhd.admin.bms.domain.query.sys.DicParam;
import com.yhd.admin.bms.domain.vo.sys.DicVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DicConvert.java
 * @Description TODO
 * @createTime 2020年05月20日 16:28:00
 */
@Mapper(componentModel = "spring")
public interface DicConvert {

    DicDTO toDTO(SysDic dic);

    SysDic toEntity(DicParam param);

    DicVO toVO(DicDTO dicDTO);

}
