package com.yhd.admin.bms.domain.query.sw.ticket;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 登高作业工作票签字用户信息查询
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketAscentSignUserParam extends QueryParam {


    /**
     * 承包商施工工作票主键id
     */
    private Long ticketId;

    /**
     * 类型
     */
    private String type;

    /**
     * 签字用户类型
     */
    private String userType;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 签字时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime signTime;

    /**
     * 签字图片url
     */
    private String signUrl;

    /**
     * 审批意见
     */
    private String spOpinion;

    /**
     * 是否完成签字：0false,1true
     */
    private Boolean isSign;


    private LocalDate startDate;

    private LocalDate endDate;
}
