package com.yhd.admin.passport.auth.config;

import com.yhd.admin.passport.auth.config.properties.CasAuthProperties;
import org.jasig.cas.client.authentication.AuthenticationFilter;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
import org.jasig.cas.client.validation.Cas20ProxyReceivingTicketValidationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * cas认证配置类
 *
 * <AUTHOR>
 * @date 2025/8/28 10:11
 */
@Configuration
@Component
public class CasAuthConfig {

    @Autowired
    private CasAuthProperties casAuthProperties;

    // 用户认证
    @Bean
    public FilterRegistrationBean authenticationFilter() {
        FilterRegistrationBean authenticationFilter = new FilterRegistrationBean();
        authenticationFilter.setFilter(new AuthenticationFilter());
        authenticationFilter.addUrlPatterns(casAuthProperties.getAppLoginUrl());
        authenticationFilter.addInitParameter(
            "casServerLoginUrl", casAuthProperties.getServerLoginUrl());
        authenticationFilter.addInitParameter("serverName", casAuthProperties.getAppUrl());
        authenticationFilter.setOrder(1);
        return authenticationFilter;
    }

    // ticket校验
    @Bean
    public FilterRegistrationBean cas20ProxyReceivingTicketValidationFilter() {
        FilterRegistrationBean ticketFilter = new FilterRegistrationBean();
        ticketFilter.setFilter(new Cas20ProxyReceivingTicketValidationFilter());
        ticketFilter.addUrlPatterns(casAuthProperties.getAppLoginUrl());
        //    ticketFilter.addUrlPatterns("/*");
        ticketFilter.addInitParameter("casServerUrlPrefix", casAuthProperties.getServerLoginUrl());
        ticketFilter.addInitParameter("serverName", casAuthProperties.getAppUrl());
        ticketFilter.addInitParameter("encoding", "UTF-8");
        ticketFilter.setOrder(2);
        return ticketFilter;
    }

    // 登录成功后，需要获取登录用户信息
    @Bean
    public FilterRegistrationBean casHttpServletRequestWrapperFilter() {
        FilterRegistrationBean httpServletRequestFilter = new FilterRegistrationBean();
        httpServletRequestFilter.setFilter(new HttpServletRequestWrapperFilter());
        httpServletRequestFilter.addUrlPatterns(casAuthProperties.getAppLoginUrl());
        //    httpServletRequestFilter.addUrlPatterns("/*");
        httpServletRequestFilter.setOrder(3);
        return httpServletRequestFilter;
    }

    // 登录退出
    @Bean
    public FilterRegistrationBean singleSignOutFilter() {
        FilterRegistrationBean bean = new FilterRegistrationBean();
        bean.setName("CAS Single Sign Out Filter");
        bean.setFilter(new SingleSignOutFilter());
        List<String> urlPatterns = new ArrayList<>();
        urlPatterns.add("/*");
        bean.setUrlPatterns(urlPatterns);

        // 排除不需要CAS单点登出处理的URL
        List<String> excludePatterns = new ArrayList<>();
        excludePatterns.add("/**/login");
        excludePatterns.add("/**/logout");
        excludePatterns.add("/oauth/**");
        bean.addInitParameter("ignorePattern", String.join(",", excludePatterns));

        return bean;
    }
}
