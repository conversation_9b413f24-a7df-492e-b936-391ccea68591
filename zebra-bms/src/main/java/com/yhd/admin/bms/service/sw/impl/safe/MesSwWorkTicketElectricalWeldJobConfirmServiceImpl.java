package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmDao;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketElectricalWeldJobConfirm;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketElectricalWeldJobConfirmService;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MesSwWorkTicketElectricalWeldJobConfirmServiceImpl
    extends ServiceImpl<
        MesSwWorkTicketElectricalWeldJobConfirmDao, MesSwWorkTicketElectricalWeldJobConfirm>
    implements MesSwWorkTicketElectricalWeldJobConfirmService {
  @Resource private MesSwWorkTicketElectricalWeldJobConfirmConvert signUserConvert;

  @Override
  public List<MesSwWorkTicketElectricalWeldJobConfirmDTO> queryListByTicketId(Long ticketId) {
    LambdaQueryChainWrapper<MesSwWorkTicketElectricalWeldJobConfirm> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    queryChain
        .eq(
            Objects.nonNull(ticketId),
            MesSwWorkTicketElectricalWeldJobConfirm::getTicketId,
            ticketId)
        .orderByAsc(MesSwWorkTicketElectricalWeldJobConfirm::getId);
    return signUserConvert.toDTO(queryChain.list());
  }
}
