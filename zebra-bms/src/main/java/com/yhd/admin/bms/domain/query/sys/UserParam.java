package com.yhd.admin.bms.domain.query.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserParam.java @Description TODO
 * @createTime 2020年04月22日 14:32:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserParam extends QueryParam {

  private Long id;

  /** 关联账户 */
  private String accountName;

  /** 关联账户 */
  private String oldAccountName;
  /** 姓名 */
  private String name;

  /** 所属部门 */
  private Long orgId;
  /** 头像 */
  private String avatar;
  /** 邮件 */
  private String email;
  /** 签名 */
  private String signature;
  /** 头衔 */
  private String title;
  /** 地址 */
  private String address;
  /** 手机电话 */
  private String phone;
  /** 标签 */
  private String tags;
  /** 状态;0禁用，1启用 */
  private Boolean isEnable;

  /** 创建人 */
  private String createdBy;
  /** 创建时间 */
  private LocalDateTime createdTime;
  /** 更新人 */
  private String updatedBy;
  /** 更新时间 */
  private LocalDateTime updatedTime;

  /** 用户类型 */
  private String type;
  /** 停送电操作密码 */
  private String tsdOperatePassword;

  private String post;
}
