package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 承包商员工入场管理
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketOpenPoreSignParam extends QueryParam
    implements Cloneable, Serializable {
  /** 开工作业主键id */
  private Long ticketId;
  /** 类型 */
  private String type;
  /** 签字用户类型 */
  private String userType;
  /** 用户账号 */
  private String userAccount;
  /** 用户姓名 */
  private String userName;
  /** 签字图片url */
  private String signUrl;
  /** 审批意见 */
  private String spOpinion;
  /** 是否完成签字 */
  private Boolean isSign;
}
