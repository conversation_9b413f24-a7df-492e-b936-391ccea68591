package com.yhd.admin.bms.domain.vo.sys;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysDicItemDTO.java
 * @Description 字典表项
 * @createTime 2020年05月20日 14:15:00
 */
public class DicItemVO extends BaseVO implements Serializable, Cloneable {

    /**
     * 字典表主键
     */
    private Long dicId;
    /**
     * 字典分类
     */
    private String category;
    /**
     * 编码
     */
    private String code;
    /**
     * 编码值
     */
    private String val;
    /**
     * 状态
     */
    private Boolean status;

    /**
     * 排序
     */
    private Long orderNum;

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = val;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return "SysDicItemDTO{" +
            "dicId=" + dicId +
            ", category='" + category + '\'' +
            ", code='" + code + '\'' +
            ", val='" + val + '\'' +
            ", status=" + status +
            '}';
    }
}
