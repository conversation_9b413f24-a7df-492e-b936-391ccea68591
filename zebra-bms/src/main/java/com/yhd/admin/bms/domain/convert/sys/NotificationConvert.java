package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.SysNotificationDTO;
import com.yhd.admin.bms.domain.entity.sys.SysNotification;
import com.yhd.admin.bms.domain.vo.sys.SysNotificationVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NotificationConvert.java
 */
@Mapper(componentModel = "spring")
public interface NotificationConvert {

    SysNotificationDTO toDTO(SysNotification sysNotification);

    SysNotificationVO toVO(SysNotificationDTO NotificationDTO);
}
