package com.yhd.admin.bms.controller.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeHiddenDangerConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeHiddenDangerDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeHiddenDangerParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwSafeHiddenDangerVO;
import com.yhd.admin.bms.service.sw.MesSwSafeHiddenDangerService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 安全隐患检查记录 */
@RestController
@RequestMapping("/safe/hiddendanger")
@Slf4j
public class MesSwSafeHiddenDangerController
    extends BaseController<MesSwSafeHiddenDangerConvert, MesSwSafeHiddenDangerService> {

  public MesSwSafeHiddenDangerController(
      MesSwSafeHiddenDangerConvert convert, MesSwSafeHiddenDangerService service) {
    super(convert, service);
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwSafeHiddenDangerVO> pagingQuery(
      @RequestBody MesSwSafeHiddenDangerParam queryParam) {
    IPage<MesSwSafeHiddenDangerDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetail(@RequestBody MesSwSafeHiddenDangerParam param) {
    try {
      return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson addOrModify(@RequestBody MesSwSafeHiddenDangerParam param) {
    try {
      Boolean retVal = service.add(param);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/complete",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson complete(@RequestBody MesSwSafeHiddenDangerParam param) {
    try {
      Boolean retVal = service.complete(param);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 终止流程
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/revokeProcess",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<String> revokeProcess(@RequestBody MesSwSafeHiddenDangerParam param) {
    try {
      service.revokeProcess(param);
      return RespJson.buildSuccessResponse("该数据作废成功！");
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
