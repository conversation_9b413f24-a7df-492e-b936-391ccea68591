package com.yhd.admin.bms.domain.vo.sw.dashboard;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 上湾智能分选控制逻辑点位表实体类
 *
 * <AUTHOR>
 * @date 2025/5/31
 */
@Data
public class MesIntelligentSortControlVO implements Serializable {
  /** 点位地址映射表 */
  // 旧主洗设备状态类点位
  public static final String POINT_ADDRESS_CON801_RUN_STATE = "XMSW_Lump_CON801_RunState";
  public static final String POINT_ADDRESS_CON701_RUN_STATE = "XMSW_Lump_CON701_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP310_RUN_STATE = "XMSW_Lump_DosingPump310_RunState";
  public static final String POINT_ADDRESS_CON302_RUN_STATE = "XMSW_Lump_CON302_RunState";
  public static final String POINT_ADDRESS_HM_VESSEL305_RUN_STATE = "XMSW_Lump_HMVessel305_RunState";
  public static final String POINT_ADDRESS_MSeparator320_RunState = "XMSW_Lump_MSeparator320_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP321_RUN_STATE = "XMSW_Lump_DosingPump321_RunState";
  
  // 新主洗设备状态类点位
  public static final String POINT_ADDRESS_CON301_RUN_STATE = "XMSW_Lump_CON301_RunState";
  public static final String POINT_ADDRESS_HM_VESSEL363_RUN_STATE = "XMSW_Lump_HMVessel363_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP374_RUN_STATE = "XMSW_Lump_DosingPump374_RunState";
  public static final String POINT_ADDRESS_MSeparator397_RunState = "XMSW_Lump_MSeparator397_RunState";
  public static final String POINT_ADDRESS_DOSING_PUMP422_RUN_STATE = "XMSW_Lump_DosingPump422_RunState";
  
  // 旧主洗数值监测类点位
  public static final String POINT_ADDRESS_BUCKET310_DENSITY = "XMSW_Lump_Bucket310_Density";
  public static final String POINT_ADDRESS_DOSING_PUMP310_LEVEL = "XMSW_Lump_DosingPump310_Level";
  public static final String POINT_ADDRESS_CON801_ASH = "XMSW_Lump_CON801_Ash";
  public static final String POINT_ADDRESS_CON802_INSTANT_COAL_AMOUNT = "XMSW_Lump_CON802_InstantCoalAmount";
  public static final String POINT_ADDRESS_CON302_INSTANT_COAL_AMOUNT = "XMSW_Lump_CON302_InstantCoalAmount";
  
  // 新主洗数值监测类点位
  public static final String POINT_ADDRESS_CON701_ASH = "XMSW_Lump_CON701_Ash";
  public static final String POINT_ADDRESS_CON701_INSTANT_COAL_AMOUNT = "XMSW_Lump_CON701_InstantCoalAmount";
  public static final String POINT_ADDRESS_CON301_INSTANT_COAL_AMOUNT = "XMSW_Lump_CON301_InstantCoalAmount";
  public static final String POINT_ADDRESS_BUCKET374_DENSITY = "XMSW_Lump_Bucket374_Density";
  public static final String POINT_ADDRESS_DOSING_PUMP374_LEVEL = "XMSW_Lump_DosingPump374_Level";
  
  private static final long serialVersionUID = 1L;

  // 设备状态类属性
  /** 介质库泵运行状态 数据类型: 暂未定义 点位地址: 暂未定义 单位: - 更新逻辑: - 取值逻辑: 先定义设备编号 */
  private Integer mediaPumpRunState;

  /** 磁选机运行状态 数据类型: 暂未定义 点位地址: 暂未定义 单位: - 更新逻辑: - 取值逻辑: 先定义设备编号 */
  private Integer magneticSeparatorRunState;

  /** 浅槽运行状态 数据类型: 暂未定义 点位地址: 暂未定义 单位: - 更新逻辑: - 取值逻辑: 先定义设备编号 */
  private Integer shallowSinkRunState;

  /** 入洗皮带运行状态 数据类型: 暂未定义 点位地址: 暂未定义 单位: - 更新逻辑: - 取值逻辑: 先定义设备编号 */
  private Integer inletBeltRunState;

  // 旧主洗设备状态类属性
  /**
   * 801皮带运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON801_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con801RunState;

  /**
   * 701皮带运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON701_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con701RunState;

  /**
   * 310重介泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump310_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump310RunState;
  
  /**
   * 302胶带机运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON302_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con302RunState;
  
  /**
   * 305浅槽运行状态 数据类型: 状态 点位地址: XMSW_Lump_HMVessel305_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer hmVessel305RunState;
  
  /**
   * 320加介磁选机运行状态 数据类型: 状态 点位地址: XMSW_Lump_MSeparator320_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator320RunState;
  
  /**
   * 321介质添加泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump321_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump321RunState;
  
  // 新主洗设备状态类属性
  /**
   * 301胶带机运行状态 数据类型: 状态 点位地址: XMSW_Lump_CON301_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer con301RunState;
  
  /**
   * 363浅槽运行状态 数据类型: 状态 点位地址: XMSW_Lump_HMVessel363_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer hmVessel363RunState;
  
  /**
   * 374重介泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump374_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump374RunState;
  
  /**
   * 397加介磁选机运行状态 数据类型: 状态 点位地址: XMSW_Lump_MSeparator397_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer mSeparator397RunState;
  
  /**
   * 422介质添加泵运行状态 数据类型: 状态 点位地址: XMSW_Lump_DosingPump422_RunState 单位: / 更新逻辑: 实时值 取值逻辑:
   * 0：就地停止；1：远控停止；2：就地运行；3：远控运行；4：故障
   */
  private Integer dosingPump422RunState;

  // 旧主洗数值监测类属性
  /** 
   * 305密度计/310重介桶密度 数据类型: 数值 点位地址: XMSW_Lump_Bucket310_Density 单位: Kg/L 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留两位小数 
   */
  private BigDecimal bucket310Density;

  /** 
   * 310重介桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump310_Level 单位: % 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留1位小数 
   */
  private BigDecimal dosingPump310Level;

  /** 
   * 801矸石灰分 数据类型: 数值 点位地址: XMSW_Lump_CON801_Ash 单位: % 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留1位 
   */
  private BigDecimal con801Ash;

  /** 
   * 801皮带瞬时带煤量 数据类型: 数值 点位地址: XMSW_Lump_CON802_InstantCoalAmount 单位: 吨 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留整数 
   */
  private BigDecimal con802InstantCoalAmount;
  
  /** 
   * 302胶带机瞬时量 数据类型: 数值 点位地址: XMSW_Lump_CON302_InstantCoalAmount 单位: t/h 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留1位小数 
   */
  private BigDecimal con302InstantCoalAmount;

  // 新主洗数值监测类属性
  /** 
   * 701精煤灰分 数据类型: 数值 点位地址: XMSW_Lump_CON701_Ash 单位: % 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留1位 
   */
  private BigDecimal con701Ash;

  /**
   * 701皮带瞬时带煤量 数据类型: 数值 点位地址: XMSW_Lump_CON701_InstantCoalAmount 单位: t/h 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留整数
   */
  private BigDecimal con701InstantCoalAmount;
  
  /**
   * 301胶带机瞬时量 数据类型: 数值 点位地址: XMSW_Lump_CON301_InstantCoalAmount 单位: t/h 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留1位小数
   */
  private BigDecimal con301InstantCoalAmount;
  
  /**
   * 363密度计/374重介桶密度 数据类型: 数值 点位地址: XMSW_Lump_Bucket374_Density 单位: g/cm³ 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留两位小数
   */
  private BigDecimal bucket374Density;
  
  /**
   * 374重介桶液位 数据类型: 数值 点位地址: XMSW_Lump_DosingPump374_Level 单位: % 更新逻辑: 实时值 
   * 取值逻辑: 四舍五入保留1位小数
   */
  private BigDecimal dosingPump374Level;

  /** 入洗皮带瞬时带煤量 数据类型: 暂未定义 点位地址: 暂未定义 单位: - 更新逻辑: - 取值逻辑: 先定义设备编号 */
  private BigDecimal inletBeltInstantCoalAmount;
}
