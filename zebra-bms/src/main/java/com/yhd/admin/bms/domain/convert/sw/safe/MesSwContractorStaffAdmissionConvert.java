package com.yhd.admin.bms.domain.convert.sw.safe;

import com.yhd.admin.bms.domain.dto.sw.safe.MesSwContractorStaffAdmissionDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwContractorStaffAdmissionSignatureDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwContractorStaffAdmissionTemplateDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwContractorStaffAdmission;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwContractorStaffAdmissionSignature;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwContractorStaffAdmissionParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwContractorStaffAdmissionTemplateVO;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwContractorStaffAdmissionVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwContractorStaffAdmissionConvert {

  MesSwContractorStaffAdmission toEntity(MesSwContractorStaffAdmissionParam param);

  MesSwContractorStaffAdmissionDTO toDTO(MesSwContractorStaffAdmission classes);

  MesSwContractorStaffAdmissionVO toVO(MesSwContractorStaffAdmissionDTO classesDTO);

  List<MesSwContractorStaffAdmissionSignatureDTO> toSignatureDTOS(
      List<MesSwContractorStaffAdmissionTemplateDTO> classesDTO);

  List<MesSwContractorStaffAdmissionTemplateVO> toTemplateVOS(
      List<MesSwContractorStaffAdmissionTemplateDTO> classesDTO);

  MesSwContractorStaffAdmissionSignatureDTO toSignatureDTO(
      MesSwContractorStaffAdmissionSignature classes);
}
