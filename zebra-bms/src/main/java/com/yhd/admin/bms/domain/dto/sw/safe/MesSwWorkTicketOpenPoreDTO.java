package com.yhd.admin.bms.domain.dto.sw.safe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 开孔作业许可票表
 *
 * <AUTHOR>
 * @date 2024/05/29 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketOpenPoreDTO extends BaseDTO implements Cloneable, Serializable {
  /** 申请单位 */
  private String applyUnit;
  /** 申请人账号 */
  private String applyAccount;
  /** 申请人名称 */
  private String applyName;
  /** 作业地点 */
  private String workLocation;
  /** 计划施工时间-开始 */
  private LocalDateTime sgStartTime;
  /** 计划施工时间-结束 */
  private LocalDateTime sgEndTime;
  /** 作业内容 */
  private String workContent;
  /** 开孔尺寸 */
  private String openPoreSize;
  /** 至基准面高度 */
  private String planeHeight;
  /** 作业负责人账号 */
  private String chargeAccount;
  /** 作业负责人名称 */
  private String chargeName;
  /** 监护人账号 */
  private String guardianAccount;
  /** 监护人名称 */
  private String guardianName;
  /** 工作票状态code */
  private String statusCode;
  /** 工作票状态name */
  private String statusName;
  /** 工作票提交人code */
  private String tjUserAccount;
  /** 工作票提交人姓名 */
  private String tjUserName;
  /** 工作票提交时间 */
  private LocalDateTime tjTime;
  /** 是否能审批：true可以，false不可以 */
  private Boolean approveFlag = false;
  /** 是否能作废：true可以，false不可以 */
  private Boolean cancelFlag = false;
  /** 计划施工时间区间：yyyy-MM-dd hh:mm~yyyy-MM-dd hh:mm */
  @TableField(exist = false)
  private String sgTimeStr;

  /** 选煤厂分管负责人审批集合 */
  private List<MesSwWorkTicketOpenPoreSignDTO> branchList;
  /** 作业负责人审批 */
  private List<MesSwWorkTicketOpenPoreSignDTO> workList;
}
