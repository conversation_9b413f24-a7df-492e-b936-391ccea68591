package com.yhd.admin.bms.service.sw.impl.safe;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.safe.MesSwSafeTrainEffectDao;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwSafeTrainEffectConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafetyTrainingPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainCourseDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainEffectDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainEffect;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.enums.safe.TrainCourseStatusEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwSafetyTrainingPlanParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainCourseParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainEffectParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesSwSafetyTrainingPlanService;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainCourseService;
import com.yhd.admin.bms.service.sw.safe.MesSwSafeTrainEffectService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 安全培训效果及教师测评报告-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/3/13 16:53
 */
@Service
public class MesSwSafeTrainEffectServiceImpl
    extends ServiceImpl<MesSwSafeTrainEffectDao, MesSwSafeTrainEffect>
    implements MesSwSafeTrainEffectService {
  @Autowired private MesSwSafetyTrainingPlanService planService;
  @Autowired private MesSwSafeTrainCourseService courseService;
  @Autowired private MesSwSafeTrainEffectConvert trainEffectConvert;
  @Autowired private UserAccountService accountService;

  @Override
  public IPage<MesSwSafetyTrainingPlanDTO> pagingQuery(MesSwSafetyTrainingPlanParam param) {

    List<MesSwSafetyTrainingPlanDTO> trainingPlanList = planService.queryList(param);
    List<MesSwSafetyTrainingPlanDTO> finishTrainList = Lists.newArrayList();
    if (!CollectionUtil.isEmpty(trainingPlanList)) {
      // 检索已审批完成培训计划
      List<MesSwSafetyTrainingPlanDTO> spList =
          trainingPlanList.stream()
              .filter(v -> v.getSignOutStatus() == 1)
              .collect(Collectors.toList());
      // 课程已完成
      if (!CollectionUtil.isEmpty(spList)) {
        finishTrainList =
            spList.stream()
                .filter(
                    sp -> {
                      MesSwSafeTrainCourseParam courseParam = new MesSwSafeTrainCourseParam();
                      courseParam.setPlanId(sp.getId());
                      List<MesSwSafeTrainCourseDTO> courseList =
                          courseService.queryList(courseParam, false);
                      List<MesSwSafeTrainCourseDTO> finishCourse =
                          courseList.stream()
                              .filter(
                                  course ->
                                      course
                                          .getCourseStatus()
                                          .equals(TrainCourseStatusEnum.END_SIGN.getCode()))
                              .collect(Collectors.toList());
                      return courseList.size() == finishCourse.size();
                    })
                .collect(Collectors.toList());
      }
    }
    // 编辑/发送通知权限判断：管理员/培训教师
    UserDTO userInfo = UserContextHolder.getUserInfo();
    if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
      throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
    }
    Boolean admin = accountService.isManager(userInfo.getAccountName());
    if (!CollectionUtil.isEmpty(finishTrainList)) {
      finishTrainList.forEach(
          v -> {
            String teacherAccountJsonStr = v.getTrainingTeacherAccount();
            List<String> teacherAccountList = JSONUtil.toList(teacherAccountJsonStr, String.class);
            v.setTrainingTeacherAccountList(teacherAccountList);
            // 培训教师姓名，多个，计划表jsonArrayStr存储["张三","李四"]
            String teacherNameJsonStr = v.getTrainingTeacherName();
            List<String> teacherNameList = JSONUtil.toList(teacherNameJsonStr, String.class);
            v.setTrainingTeacherNameList(teacherNameList);
            if (admin || teacherAccountList.contains(userInfo.getAccountName())) {
              v.setEditFlag(true);
            }
          });
    }
    Page<MesSwSafetyTrainingPlanDTO> page = new Page<>(param.getCurrent(), param.getPageSize());

    List<MesSwSafetyTrainingPlanDTO> result = Lists.newArrayList();
    int pageNum = param.getCurrent() == null ? 1 : param.getCurrent().intValue();
    int pageSize = param.getPageSize() == null ? 20 : param.getPageSize().intValue();
    int index = pageNum > 1 ? (pageNum - 1) * pageSize : 0;
    int total = finishTrainList.size();
    for (int i = 0; i < param.getPageSize() && index + i < total; i++) {
      MesSwSafetyTrainingPlanDTO item = finishTrainList.get(index + i);
      result.add(item);
    }

    page.setRecords(result);
    page.setTotal(finishTrainList.size());

    return page;
  }

  @Override
  public MesSwSafeTrainEffectDTO getTrainEffectDetail(MesSwSafeTrainEffectParam param) {
    if (param.getPlanId() == null) {
      return null;
    }
    MesSwSafeTrainEffect effect = new MesSwSafeTrainEffect();
    LambdaQueryWrapper<MesSwSafeTrainEffect> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwSafeTrainEffect::getPlanId, param.getPlanId());
    List<MesSwSafeTrainEffect> trainEffects = baseMapper.selectList(wrapper);
    if (!CollectionUtil.isEmpty(trainEffects)) {
      effect = trainEffects.get(0);
    }

    return trainEffectConvert.toDTO(effect);
  }

  @Override
  public Boolean effectEdit(MesSwSafeTrainEffectParam param) {
    MesSwSafeTrainEffect trainEffect = trainEffectConvert.toEntity(param);

    return super.saveOrUpdate(trainEffect);
  }
}
