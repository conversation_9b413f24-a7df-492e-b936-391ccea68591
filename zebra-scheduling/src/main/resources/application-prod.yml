spring:
    datasource:
        url: ************************************************************************************************************************
        username: root
        password: Yhd@2024#@
        driver-class-name: com.mysql.cj.jdbc.Driver
        type: com.zaxxer.hikari.HikariDataSource
        hikari:
            minimum-idle: 5
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 600000
            # 连接池最大连接数，默认是10
            maximum-pool-size: 10
            # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
            auto-commit: true
            # 连接池名称
            pool-name: bms-hikari-dev
            # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 600000
            # 数据库连接超时时间,默认30秒，即30000
            connection-timeout: 600000
            connection-test-query: SELECT 1
    redis:
        host: ************
        port: 6379
        timeout: 60s
        password: Yhd@2024
        lettuce:
            pool:
                max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
                max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-idle: 10      # 连接池中的最大空闲连接
                min-idle: 5       # 连接池中的最小空闲连接
        database: 0
server:
    port: 9025

bms:
    url: http://************:9114/bms

xxl:
    job:
        admin:
            addresses: http://************:8061/xxl-job-admin
        accessToken:
        executor:
            appname: xxl-job-executor-xbd
            address:
            ip: ************
            port: 9005
            logpath: /data/applogs/xxl-job/jobhandler
            logretentiondays: 30
