package com.yhd.admin.bms.domain.convert.sw.consumption;

import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionDrugDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionDrug;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionDrugParam;
import com.yhd.admin.bms.domain.vo.sw.consumption.MesConsumptionDrugVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesConsumptionDrugConvert {

  MesConsumptionDrug toEntity(MesConsumptionDrugParam param);

  MesConsumptionDrugDTO toDTO(MesConsumptionDrug entity);

  MesConsumptionDrugVO toVO(MesConsumptionDrugDTO entity);
}
