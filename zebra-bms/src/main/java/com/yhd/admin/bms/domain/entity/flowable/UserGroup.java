package com.yhd.admin.bms.domain.entity.flowable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @<PERSON> wangshengman
 * @Date 2023/3/13 20:26
 * @Description:
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ACT_ID_MEMBERSHIP")
public class UserGroup {
    private String user_id_;

    private String group_id_;
}
