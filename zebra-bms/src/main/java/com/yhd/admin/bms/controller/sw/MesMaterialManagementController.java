package com.yhd.admin.bms.controller.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesMaterialManagementConvert;
import com.yhd.admin.bms.domain.dto.sw.MesLargePartStatisticDTO;
import com.yhd.admin.bms.domain.dto.sw.MesMaterialManagementDTO;
import com.yhd.admin.bms.domain.query.sw.MesMaterialManagementParam;
import com.yhd.admin.bms.domain.vo.sw.MesMaterialManagementVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesMaterialManagementService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.eums.BusinessType;
import com.yhd.admin.common.poi.excel.DownloadTplBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 物资管理表控制层
 *
 * <AUTHOR>
 * @since 2025-08-11 16:48:28
 */
@RestController
@RequestMapping("/material/management")
@Slf4j
public class MesMaterialManagementController {

    private final MesMaterialManagementService mesMaterialManagementService;
    private final MesMaterialManagementConvert mesMaterialManagementConvert;
    private static final String TEMPLATE = "物资管理导入模板.xlsx";

    public MesMaterialManagementController(
        MesMaterialManagementService mesMaterialManagementService,
        MesMaterialManagementConvert mesMaterialManagementConvert
    ) {
        this.mesMaterialManagementService = mesMaterialManagementService;
        this.mesMaterialManagementConvert = mesMaterialManagementConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MesMaterialManagementParam param) {
        IPage<MesMaterialManagementDTO> page = mesMaterialManagementService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mesMaterialManagementConvert::toVO));
    }

    /**
     * 查询部件分类
     *
     * @return 查询结果
     */
    @PostMapping(value = "/queryPartCategory")
    public RespJson<?> queryPartCategory() {
        return RespJson.buildSuccessResponse(mesMaterialManagementService.queryPartCategory());
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MesMaterialManagementVO> getCurrentDetails(@RequestBody MesMaterialManagementParam param) {
        return RespJson.buildSuccessResponse(mesMaterialManagementConvert.toVO(mesMaterialManagementService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> addOrModify(@RequestBody MesMaterialManagementParam param) {
        Boolean retVal = mesMaterialManagementService.addOrModify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MesMaterialManagementParam param) {
        Boolean retVal = mesMaterialManagementService.removeBatch(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }


    @SysLogs(title = "导入物资管理", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/import")
    public RespJson<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RespJson.buildFailureResponse(ResultStateEnum.FAIL.getCode(), "文件不存在或者为空！");
        }
        // 获取文件名称
        String fileName = file.getOriginalFilename();
        log.debug("进行导入物资管理EXCEL的操作,文件名称：{}", fileName);
        return RespJson.buildSuccessResponse(mesMaterialManagementService.importExcel(file));
    }

    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> export(@RequestBody MesMaterialManagementParam param) {
        try {
            return RespJson.buildSuccessResponse(mesMaterialManagementService.export(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @GetMapping("/getTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            DownloadTplBuilder.downloadExcelTpl(TEMPLATE, response);
        } catch (Exception e) {
            throw new BMSException("error", "模板下载失败");
        }
    }

    /**
     * 大部件智能分析分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/getLargePart",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> getLargePart(@RequestBody MesMaterialManagementParam param) {
        IPage<MesLargePartStatisticDTO> page = mesMaterialManagementService.getLargePart(param);
        return new PageRespJson<>(page);
    }
}

