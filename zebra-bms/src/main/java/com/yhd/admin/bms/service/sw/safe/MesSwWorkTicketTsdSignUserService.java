package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketTsdSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketTsdSignUser;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketTsdSignUserParam;

import java.util.List;

public interface MesSwWorkTicketTsdSignUserService extends IService<MesSwWorkTicketTsdSignUser> {

  /**
   * 根据条件查询列表
   *
   * @param param 参数
   * @return 列表信息
   */
  List<MesSwWorkTicketTsdSignUserDTO> queryList(MesSwWorkTicketTsdSignUserParam param);

  /**
   * 查询详情信息
   *
   * @param param 查询参数：主键id
   * @return 用户签字详情
   */
  MesSwWorkTicketTsdSignUserDTO getCurrentDetail(MesSwWorkTicketTsdSignUserParam param);

  /**
   * 审批签字
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean spSign(MesSwWorkTicketTsdSignUserParam param);
}
