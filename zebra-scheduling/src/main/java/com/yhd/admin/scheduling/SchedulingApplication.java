package com.yhd.admin.scheduling;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SchedulingApplication.java @Description TODO
 * @createTime 2020年09月25日 17:15:00
 */
@EnableTransactionManagement
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication
@ServletComponentScan
@MapperScan("com.yhd.admin.scheduling.dao")
public class SchedulingApplication {
  public static void main(String[] args) {
    SpringApplication.run(SchedulingApplication.class, args);
  }
}
