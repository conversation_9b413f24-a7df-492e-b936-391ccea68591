package com.yhd.admin.passport.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.passport.dao.RoleDao;
import com.yhd.admin.passport.domain.convert.RoleConvert;
import com.yhd.admin.passport.domain.dto.RoleDTO;
import com.yhd.admin.passport.domain.entity.SysRole;
import com.yhd.admin.passport.domain.query.UserAccountParam;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleServiceImpl.java
 * @Description TODO
 * @createTime 2020年03月20日 10:48:00
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleDao, SysRole> implements RoleService {

  private final RoleConvert convert;

  public RoleServiceImpl(RoleConvert convert) {
    this.convert = convert;
  }

  @Override
  public List<RoleDTO> queryRole(UserAccountParam param) {
    return convert.toDTO(baseMapper.selectRoleListByUser(param));
  }

  @Override
  public List<String> getAuthority(List<Long> roleList) {
    if (!CollectionUtils.isEmpty(roleList)) {
      List<Long> menuIds = baseMapper.selectMenuByRole(roleList);
      if (!CollectionUtils.isEmpty(menuIds)) {
        return baseMapper.selectAuthorityById(menuIds);
      }
    }

    return Collections.emptyList();
  }
}
