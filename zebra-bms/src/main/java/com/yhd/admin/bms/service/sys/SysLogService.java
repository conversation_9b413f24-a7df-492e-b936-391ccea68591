package com.yhd.admin.bms.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sys.SysLogDTO;
import com.yhd.admin.bms.domain.entity.sys.SysLog;
import com.yhd.admin.bms.domain.query.sys.SysLogParam;

public interface SysLogService extends IService<SysLog> {

  IPage<SysLogDTO> pagingQuery(SysLogParam queryParam);

  Boolean add(SysLogParam SysLogParam);
}
