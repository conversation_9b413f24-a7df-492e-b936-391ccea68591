package com.yhd.admin.bms.domain.query.sw;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/** 安全隐患检查记录 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafeHiddenDangerParam extends QueryParam implements Cloneable, Serializable {
  /** 检查日期 */
  private LocalDate checkDate;
  /** 检查类型 */
  private String checkType;
  /** 检查类型名称 */
  private String checkTypeName;
  /** 责任部门/车间 */
  private String dutyDepType;
  /** 责任部门/车间名称 */
  private String dutyDepName;
  /** 严重程度 */
  private String severityType;
  /** 严重程度名称 */
  private String severityName;
  /** 存在问题 */
  private String existIssue;
  /** 整改责任人 */
  private String rectifyCharge;
  /** 整改责任人名称 */
  private String rectifyChargeName;
  /** 整改期限 */
  private String deadlineType;
  /** 整改期限名称 */
  private String deadlineName;
  /** 截止时间 */
  private LocalDateTime deadlineTime;
  /** 整改意见 */
  private String rectifyOpinion;
  /** 处理人员账号 */
  private String handler;
  /** 处理人员名称 */
  private String handlerName;
  /** 是否整改完成 */
  private String rectifyFinish;
  /** 整改情况说明 */
  private String rectifyExplain;
  /** 当前整改状态 */
  private String statusType;
  /** 当前整改状态名称 */
  private String statusName;
  /** 流程实例ID */
  private String processInstanceId;
  /** 任务ID */
  private String taskId;
  /** 已上传的图片文件 */
  private List<JSONObject> pictureList;
  /** 已上传的视频文件 */
  private List<JSONObject> videoList;
  /** 已上传的图片文件-整改 */
  private List<JSONObject> pictureRectifyList;
  /** 已上传的视频文件-整改 */
  private List<JSONObject> videoRectifyList;

  /** 开始-检查日期 */
  private LocalDate startDate;
  /** 结束-检查日期 */
  private LocalDate endDate;
}
