package com.yhd.admin.bms.domain.dto.sw.eqpt;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwEquipmentInspectionReportDTO extends BaseDTO implements Serializable {
	/**
	* 设备巡检表主键ID
	*/
	private String parentId;

	/**
	* 存在问题
	*/
	private String existQuestion;

    /**
     * 现场照片
     */
    private List<JSONObject> photo;

    /**
     *填报人账号
     */
    private String account;

    /**
     * 整改车间code
     */
    private Long zgWorkshopCode;

    /**
     * 整改车间名称
     */
    private String zgWorkshopName;
}
