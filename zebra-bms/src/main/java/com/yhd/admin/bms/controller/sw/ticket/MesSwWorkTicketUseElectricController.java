package com.yhd.admin.bms.controller.sw.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketElectricConvert;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketUseElectricDTO;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketUseElectricParam;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketUseElectricSignUserParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketUseElectricVO;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketElectricService;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketElectricSignUserService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 临时用电申请许可工作票
 *
 * <AUTHOR>
 * @Date 2024/6/17 9:09
 * @Version 1.0
 */

@RestController
@RequestMapping("/ticket/electric")
public class MesSwWorkTicketUseElectricController {
    @Resource
    private MesSwWorkTicketElectricConvert convert;

    @Resource
    private MesSwWorkTicketElectricService service;

    @Resource
    private MesSwWorkTicketElectricSignUserService signUserService;


    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwWorkTicketUseElectricVO> pagingQuery(@RequestBody MesSwWorkTicketUseElectricParam queryParam) {
        IPage<MesSwWorkTicketUseElectricDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwWorkTicketUseElectricParam param) {
        MesSwWorkTicketUseElectricDTO currentDetail = service.getCurrentDetail(param);
        return RespJson.buildSuccessResponse(currentDetail);
    }

    @SysLogs(title = "临时用电许可工作票初始化数据", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/initialize",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson initialize(@RequestBody MesSwWorkTicketUseElectricParam param) {
        MesSwWorkTicketUseElectricDTO currentDetail = service.initialize(param);
        return RespJson.buildSuccessResponse(convert.toVO(currentDetail));
    }

    @SysLogs(title = "临时用电许可工作票新增", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesSwWorkTicketUseElectricParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "临时用电许可工作票作废", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/cancel",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson cancel(@RequestBody MesSwWorkTicketUseElectricParam param) {
        Boolean retVal = service.cancel(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/getApplyUnitList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getApplyUnitList(@RequestBody MesSwWorkTicketUseElectricParam param) {
        return RespJson.buildSuccessResponse(service.getApplyUnitList(param));
    }

    @PostMapping(
        value = "/getApplyUserList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getApplyUserList(@RequestBody MesSwWorkTicketUseElectricParam param) {
        return RespJson.buildSuccessResponse(service.getApplyUserList(param));
    }

    @PostMapping(
        value = "/getStatusList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getStatusList(@RequestBody MesSwWorkTicketUseElectricParam param) {
        return RespJson.buildSuccessResponse(service.getStatusList(param));
    }

    @SysLogs(title = "临时用电许可工作票签字记录审批", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/approval",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson approval(@RequestBody MesSwWorkTicketUseElectricSignUserParam param) {
        Boolean retVal = signUserService.approval(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }
}
