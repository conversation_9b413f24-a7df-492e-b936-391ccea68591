ext {
    set('springCloudVersion', "Hoxton.SR12")
    mapstructVersion = "1.4.2.Final"
    lombokVersion = "1.18.12"
    jaksonAnnotationsVersion = "2.10.2"
    commonsLang3Version = "3.8.1"
    commonsPool2Version = "2.6.2"
    mybatisPlusVersion = "3.4.1"
    druidVersion = "1.1.21"
    junitVersion = "5.3.2"
    fastdfsVersion = "1.27-SNAPSHOT"
    commonsIOVersion = "2.6"
    servletApiVersion = "4.0.1"
    minioVersion = "7.1.0"
    commonsCodecVerion = '1.13'
    xxlJobVersion = '2.3.1'
    jpush = '3.4.7'
    easyExcel = '2.2.7'
    fastJson = '1.2.75'
    miloVersion = '0.6.8'
    iotdbVersion = '0.13.1'
    poiVersion = "5.0.0"
    flowableVersion = '6.8.0'
    flowableEngineVersion = '6.8.0'
    huToolVersion = "5.8.1"
    mssqlVersion = '7.2.2.jre11'
    modbus4jVersion = '3.0.3'
    easypoiVersion = '4.1.0'
    easypoiAnnotationVersion = '4.1.0'
    easypoiBaseVersion = '4.1.0'
    redissonVersion = '3.35.0'
    math3 = '3.6.1'
    pdfbox = '2.0.24'
    pdfboxTools = '2.0.30'
    casClientVersion = '3.6.4'
    dependencies = [
        "commons-lang3"            : "org.apache.commons:commons-lang3:${commonsLang3Version}",
        "commons-pool2"            : 'org.apache.commons:commons-pool2:2.6.2',
        "mapstruct"                : "org.mapstruct:mapstruct:${mapstructVersion}",
        "mapstruct-processor"      : "org.mapstruct:mapstruct-processor:${mapstructVersion}",
        "lombok"                   : "org.projectlombok:lombok:${lombokVersion}",
        "mybatis-plus-boot-starter": "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}",
        "druid-spring-boot-starter": "com.alibaba:druid-spring-boot-starter:${druidVersion}",
        "junit-jupiter-api"        : "org.junit.jupiter:junit-jupiter-api:${junitVersion}",
        "junit-jupiter-params"     : "org.junit.jupiter:junit-jupiter-params:${junitVersion}",
        "junit-jupiter-engine"     : "org.junit.jupiter:junit-jupiter-engine:${junitVersion}",
        "fastdfs-client-java"      : "org.csource:fastdfs-client-java:${fastdfsVersion}",
        "jackson-annotations"      : "com.fasterxml.jackson.core:jackson-annotations:${jaksonAnnotationsVersion}",
        "commons-io"               : "commons-io:commons-io:${commonsIOVersion}",
        "javax.servlet-api"        : "javax.servlet:javax.servlet-api:${servletApiVersion}",
        "minio"                    : "io.minio:minio:${minioVersion}",
        "commons-codec"            : "commons-codec:commons-codec:${commonsCodecVerion}",
        "xxl-job"                  : "com.xuxueli:xxl-job-core:${xxlJobVersion}",
        "jpush"                    : "cn.jpush.api:jpush-client:${jpush}",
        "easyExcel"                : "com.alibaba:easyexcel:${easyExcel}",
        "fastJson"                 : "com.alibaba:fastjson:${fastJson}",
        "milo-sdk-client"          : "org.eclipse.milo:sdk-client:${miloVersion}",
        "milo-sdk-server"          : "org.eclipse.milo:sdk-server:${miloVersion}",
        "milo-stack-client"        : "org.eclipse.milo:stack-client:${miloVersion}",
        "milo-stack-server'"       : "org.eclipse.milo:stack-server:${miloVersion}",
        "poi"                      : "org.apache.poi:poi:${poiVersion}",
        "poi-ooxml"                : "org.apache.poi:poi-ooxml:${poiVersion}",
        "flowable"                 : "org.flowable:flowable-spring-boot-starter-process:${flowableVersion}",
        "flowable-engine"          : "org.flowable:flowable-engine:${flowableEngineVersion}",
        "guava"                    : 'com.google.guava:guava:31.1-jre',
        "hutool"                   : "cn.hutool:hutool-all:${huToolVersion}",
        "mssql"                    : "com.microsoft.sqlserver:mssql-jdbc:${mssqlVersion}",
        'modbus4j'                 : "com.infiniteautomation:modbus4j:${modbus4jVersion}",
        'easypoi'                  : "cn.afterturn:easypoi:${easypoiVersion}",
        'easypoi-annotation'       : "cn.afterturn:easypoi-annotation:${easypoiAnnotationVersion}",
        'easypoi-base'             : "cn.afterturn:easypoi-base:${easypoiBaseVersion}",
        'redisson'                 : "org.redisson:redisson:${redissonVersion}",
        'commons-math3'            : "org.apache.commons:commons-math3:${math3}",
        'pdfbox'                   : "org.apache.pdfbox:pdfbox:${pdfbox}",
        'pdfboxTools'              : "org.apache.pdfbox:pdfbox-tools:${pdfboxTools}",
        'cas-client'               : "org.jasig.cas.client:cas-client-support-springboot:${casClientVersion}",
    ]
}
ext['log4j2.version'] = '2.17.0'
ext['spring-framework.version'] = '5.2.20.RELEASE'
