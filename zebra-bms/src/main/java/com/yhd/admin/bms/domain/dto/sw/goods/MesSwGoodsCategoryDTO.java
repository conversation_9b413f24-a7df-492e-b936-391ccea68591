package com.yhd.admin.bms.domain.dto.sw.goods;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/** 物资分类-筛板相关物资分类 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwGoodsCategoryDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 4965315569114261484L;

  /** 父类id,一级分类为0 */
  private Long parentId;
  /** 分类名称 */
  private String name;
  /** 层级 */
  private Integer level;
  /** 排序数，越小越靠前 */
  private Integer sort;
  /** 状态：0禁用，1启用 */
  private Boolean status;

  /** 子节点 */
  private List<MesSwGoodsCategoryDTO> children;
}
