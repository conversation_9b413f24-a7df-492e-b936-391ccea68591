package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 安全培训效果表
 *
 * <AUTHOR>
 * @date 2024/3/14 11:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwSafeTrainEffectParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 3918297017908432212L;

  /** 培训计划主键id */
  private Long planId;
  /** 培训内容 */
  private String trainContent;
  /** 培训课程员工的收获或得到启发的情况 */
  private String shqfText;
  /** 员工认为可应用到工作中的要点 */
  private String gzydText;
  /** 员工认为本次培训需改进之处 */
  private String pxgjText;
  /** 对培训课程及教材的评价 */
  private String kcpjText;
  /** 对培训课程及教材的评价-回复 */
  private String kcpjReplyText;
  /** 对讲师的评价 */
  private String jspjText;
  /** 对讲师的评价-回复 */
  private String jspjReplyText;
  /** 培训效果总结 */
  private String xgzjText;
}
