package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeExamPaper;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> @Date 2024/3/3 12:23 @Version 1.0
 */
public interface MesSwSafeExamPaperService extends IService<MesSwSafeExamPaper> {
  IPage<MesSwSafeExamPaperDTO> pagingQuery(MesSwSafeExamPaperParam queryParam);

  List<MesSwSafeExamPaperDTO> getPaperList(MesSwSafeExamPaperParam param);

  MesSwSafeExamPaperDTO getCurrentDetail(MesSwSafeExamPaperParam param);

  Boolean addOrModify(MesSwSafeExamPaperParam param);

  Boolean removeBatch(BatchParam batchParam);
  /** 试卷标题是否重复 */
  Boolean ifExist(MesSwSafeExamPaperParam param);

  /**
   * 导出试卷pdf
   *
   * @param param 参数
   * @return 路径
   */
  String exportPaperPdf(MesSwSafeExamPaperParam param) throws IOException;
}
