package com.yhd.admin.sso.service;

import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.passport.domain.query.UserAccountParam;
import com.yhd.admin.passport.service.UserAccountService;
import com.yhd.admin.sso.PassPortApplicationTests;
import java.util.Optional;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserAccountServiceTest.java
 * @Description TODO
 * @createTime 2020年03月19日 22:06:00
 */

public class UserAccountServiceTest extends PassPortApplicationTests {

  @Autowired
  private UserAccountService userAccountService;

  @Test
  public void testGetUserAccount() {
    UserAccountParam param = new UserAccountParam();
    param.setUserName("jiangzhenghao");
    Optional<UserAccountDTO> userAccount = userAccountService.getUserAccount(param);
    Assert.assertEquals("jiangzhenghao", userAccount.get().getUsername());
    System.out.println(userAccount);
  }
}
