package com.yhd.admin.bms.controller.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsConvert;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsVO;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

/**
 * 设备检修工时记录统计表控制层
 *
 * <AUTHOR>
 * @since 2025-07-21 15:15:28
 */
@RestController
@RequestMapping("/equipment/repairHour/statistics")
public class MesSwEquipmentRepairHourOrderStatisticsController {
    @Resource
    private MesSwEquipmentRepairHourOrderStatisticsService mesSwEquipmentRepairHourOrderStatisticsService;
    @Resource
    private MesSwEquipmentRepairHourOrderStatisticsConvert mesSwEquipmentRepairHourOrderStatisticsConvert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        IPage<YearMonth> page = mesSwEquipmentRepairHourOrderStatisticsService.pagingQuery(param);
        return new PageRespJson<>(page);
    }

    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentRepairHourOrderStatisticsVO>> getCurrentDetails(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        return RespJson.buildSuccessResponse(mesSwEquipmentRepairHourOrderStatisticsConvert.toVOList(mesSwEquipmentRepairHourOrderStatisticsService.getCurrentDetails(param)));
    }

    @PostMapping(
        value = "/pagingQueryOfScore",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQueryOfScore(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        IPage<LocalDate> page = mesSwEquipmentRepairHourOrderStatisticsService.pagingQueryOfScore(param);
        return new PageRespJson<>(page);
    }

    @PostMapping(
        value = "/getCurrentDetailsOfScore",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesSwEquipmentRepairHourOrderStatisticsVO>> getCurrentDetailsOfScore(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        return RespJson.buildSuccessResponse(mesSwEquipmentRepairHourOrderStatisticsConvert.toVOList(mesSwEquipmentRepairHourOrderStatisticsService.getCurrentDetailsOfScore(param)));
    }

    @PostMapping(
        value = "/scoring",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        Boolean retVal = mesSwEquipmentRepairHourOrderStatisticsService.scoring(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/exportOfScore",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> exportOfScore(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        try {
            return RespJson.buildSuccessResponse(mesSwEquipmentRepairHourOrderStatisticsService.exportOfScore(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> export(@RequestBody MesSwEquipmentRepairHourOrderStatisticsParam param) {
        try {
            return RespJson.buildSuccessResponse(mesSwEquipmentRepairHourOrderStatisticsService.export(param));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getModifyFlag",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getModifyFlag() {
        Boolean retVal = mesSwEquipmentRepairHourOrderStatisticsService.getModifyFlag();
        return RespJson.buildSuccessResponse(retVal);
    }

}

