package com.yhd.admin.bms.service.sw.impl.ticket;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.bms.dao.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserDao;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserConvert;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpaceSignUser;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpace;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpaceSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserService;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketConfinedSpaceService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * 受限空间许可证-签字表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-29
 */
@Service
public class MesSwWorkTicketConfinedSpaceSignUserServiceImpl
    extends ServiceImpl<MesSwWorkTicketConfinedSpaceSignUserDao, MesSwWorkTicketConfinedSpaceSignUser>
    implements MesSwWorkTicketConfinedSpaceSignUserService {
    @Resource
    private MesSwWorkTicketConfinedSpaceSignUserConvert signUserConvert;

    @Resource
    private MesSwWorkTicketConfinedSpaceService ConfinedSpaceService;

    @Resource
    private DicService dicService;

    //审批状态
    private static final String DZ_TICKET_STATUS = "DZ_TICKET_STATUS";

    @Override
    public Boolean approval(MesSwWorkTicketConfinedSpaceSignUserParam param) {
        if (Objects.isNull(param.getId())
            || StringUtils.isBlank(param.getSpOpinion())
            || StringUtils.isBlank(param.getSignUrl())) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "请检查参数是否符合规范");
        }
        MesSwWorkTicketConfinedSpaceSignUser entity = signUserConvert.toEntity(param);
        if (StringUtils.isNotBlank(param.getSignUrl())){
            entity.setIsSign(true);
        }
        Boolean result = this.updateById(entity);
        // 查询审批用户签字记录
        MesSwWorkTicketConfinedSpaceSignUser signUser = this.getById(param.getId());
        LambdaQueryWrapper<MesSwWorkTicketConfinedSpaceSignUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwWorkTicketConfinedSpaceSignUser::getTicketId,signUser.getTicketId());
        List<MesSwWorkTicketConfinedSpaceSignUser> signUsers = this.list(wrapper);
        // 审批用户全部签字
        Long spCount = signUsers.stream().filter(v -> v.getIsSign() == false).count();
        if (spCount == 0){
            // 全部签字 修改状态为作业结束签字
            MesSwWorkTicketConfinedSpace ticketConfinedSpace = new MesSwWorkTicketConfinedSpace();
            ticketConfinedSpace.setId(param.getTicketId());
            ticketConfinedSpace.setStatusCode("3");
            ticketConfinedSpace.setStatusName(dicService.transform(DZ_TICKET_STATUS, ticketConfinedSpace.getStatusCode()));
            ConfinedSpaceService.updateById(ticketConfinedSpace);
        }
        return result;
    }

}
