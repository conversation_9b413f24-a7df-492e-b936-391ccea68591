package com.yhd.admin.bms.domain.entity.sw;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 室外消火栓试验维护记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_mes_sw_outdoor_hydrant")
public class MesSwOutdoorHydrant extends BaseEntity implements Serializable, Cloneable  {

private static final long serialVersionUID = 1L;

  /**
   * 单位
   */
private String unit;
  /**
   * 单位名称
   */
private String unitName;
  /**
   * 责任车间
   */
private String workshop;
  /**
   * 责任车间名称
   */
private String workshopName;
  /**
   * 录入时间
   */
private LocalDateTime inputTime;


}
