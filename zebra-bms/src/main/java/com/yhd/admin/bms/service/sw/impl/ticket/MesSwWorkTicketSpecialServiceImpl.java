package com.yhd.admin.bms.service.sw.impl.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.ticket.MesSwWorkTicketSpecialDao;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketSpecialConvert;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketSpecialSignUserConvert;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketSpecialDTO;
import com.yhd.admin.bms.domain.dto.sys.DicItemDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketSpecial;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketSpecialSignUser;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketSpecialParam;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketSpecialSignUserParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketSpecialVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketSpecialService;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketSpecialSignUserService;
import com.yhd.admin.bms.service.sys.DicItemService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 特殊作业工作票
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-29
 */
@Service
public class MesSwWorkTicketSpecialServiceImpl
    extends ServiceImpl<MesSwWorkTicketSpecialDao, MesSwWorkTicketSpecial>
    implements MesSwWorkTicketSpecialService {

  @Resource private MesSwWorkTicketSpecialConvert convert;

  @Resource private DicItemService dicItemService;

  @Resource private MesSwWorkTicketSpecialSignUserConvert signUserConvert;

  @Resource private MesSwWorkTicketSpecialSignUserService signUserService;

  @Resource private UserAccountService accountService;

  @Override
  public IPage<MesSwWorkTicketSpecialDTO> pagingQuery(MesSwWorkTicketSpecialParam queryParam) {
    Page<MesSwWorkTicketSpecial> page =
        new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<MesSwWorkTicketSpecial> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 计划作业时间
    if (!StringUtils.isEmpty(queryParam.getStartTime())
        && !StringUtils.isEmpty(queryParam.getEndTime())) {
      queryChain.between(
          MesSwWorkTicketSpecial::getSgStartTime,
          queryParam.getStartTime(),
          queryParam.getEndTime());
    }
    // 作业内容
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getSgContent()),
        MesSwWorkTicketSpecial::getSgContent,
        queryParam.getSgContent());
    // 作业地点
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getSgLocation()),
        MesSwWorkTicketSpecial::getSgLocation,
        queryParam.getSgLocation());
    // 工作票状态
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getStatusCode()),
        MesSwWorkTicketSpecial::getStatusCode,
        queryParam.getStatusCode());
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getStatusName()),
        MesSwWorkTicketSpecial::getStatusName,
        queryParam.getStatusName());
    queryChain.orderByDesc(MesSwWorkTicketSpecial::getCreatedTime);
    List<MesSwWorkTicketSpecial> records = queryChain.page(page).getRecords();
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    records.forEach(
        e -> {
          if (!Objects.equals(e.getStatusCode(), WorkTicketStatusEnum.ZF.getCode())
              && !Objects.equals(e.getStatusCode(), WorkTicketStatusEnum.WC.getCode())) {
            // 查询所有审批人员
            List<String> stringList =
                signUserService
                    .lambdaQuery()
                    .eq(MesSwWorkTicketSpecialSignUser::getTicketId, e.getId())
                    .list()
                    .stream()
                    .filter(
                        u -> {
                          return Boolean.FALSE.equals(u.getIsSign());
                        })
                    .map(MesSwWorkTicketSpecialSignUser::getUserCode)
                    .collect(Collectors.toList());
            // 当前用户在stringList中,有审批权限
            e.setIsAuthority(stringList.contains(username));
            // 计划施工时间
            String startTime =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(e.getSgStartTime());
            String endTime =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(e.getSgEndTime());
            e.setSgTimes(startTime + "~" + endTime);
          } else {
            e.setIsAuthority(Boolean.FALSE);
          }
        });
    return queryChain.page(page).setRecords(records).convert(convert::toDTO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MesSwWorkTicketSpecialParam param) {
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    MesSwWorkTicketSpecial entity = convert.toEntity(param);
    entity.setTjTime(LocalDateTime.now());
    entity.setStatusCode(WorkTicketStatusEnum.SPZ.getCode());
    entity.setStatusName(WorkTicketStatusEnum.SPZ.getDesc());
    entity.setTjUserCode(username);
    entity.setTjUserName(accountService.getNameByUserName(username));
    boolean saveResult = super.save(entity);
    if (!saveResult) {
      return false;
    }
    List<MesSwWorkTicketSpecialSignUserParam> ascentSignUserParams = new ArrayList<>();

    // 厂站审批
    List<MesSwWorkTicketSpecialSignUserParam> signUserList1 = param.getSignUserList();
    processSignUserList(signUserList1, entity.getId(), "审批部门");
    ascentSignUserParams.addAll(signUserList1);
    signUserService.saveBatch(signUserConvert.toEntityList(ascentSignUserParams));
    return true;
  }

  private void processSignUserList(
      List<MesSwWorkTicketSpecialSignUserParam> signUserList, Long ticketId, String type) {
    if (signUserList != null) {
      signUserList.forEach(
          e -> {
            e.setTicketId(ticketId);
            e.setType(type);
            e.setIsSign(false);
          });
    }
  }

  @Override
  public Boolean modify(MesSwWorkTicketSpecialParam param) {
    MesSwWorkTicketSpecial entity = convert.toEntity(param);
    return super.updateById(entity);
  }

  @Override
  public MesSwWorkTicketSpecialDTO getCurrentDetail(MesSwWorkTicketSpecialParam param) {
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    List<MesSwWorkTicketSpecialSignUser> objects1 = Lists.newArrayList();
    MesSwWorkTicketSpecial byId = super.getById(param.getId());
    List<MesSwWorkTicketSpecialSignUser> list =
        signUserService
            .lambdaQuery()
            .eq(MesSwWorkTicketSpecialSignUser::getTicketId, byId.getId())
            .list();
    // 厂站审批
    List<MesSwWorkTicketSpecialSignUser> collect1 =
        list.stream()
            .filter(
                e -> {
                  return "审批部门".equals(e.getType());
                })
            .collect(Collectors.toList());
    collect1.forEach(
        e -> {
          e.setIsAuthority(username.equals(e.getUserCode()) && Boolean.FALSE.equals(e.getIsSign()));
        });

    objects1.addAll(collect1);
    byId.setSignUserList(objects1);
    return convert.toDTO(byId);
  }

  /**
   * @return @Description:查询字典项
   */
  @Override
  public MesSwWorkTicketSpecialDTO getTypeList() {
    MesSwWorkTicketSpecialDTO mesSwWorkTicketAscentDTO = new MesSwWorkTicketSpecialDTO();
    List<MesSwWorkTicketSpecialSignUser> signUsers = Lists.newArrayList();

    // 登高厂站审批
    List<DicItemDTO> cz = dicItemService.queryDicItemByDicId("special");
    cz.forEach(
        e -> {
          MesSwWorkTicketSpecialSignUser mesSwWorkTicketAscentSignUser =
              new MesSwWorkTicketSpecialSignUser();
          mesSwWorkTicketAscentSignUser.setUserType(e.getVal());
          signUsers.add(mesSwWorkTicketAscentSignUser);
        });

    mesSwWorkTicketAscentDTO.setSignUserList(signUserConvert.toDTOList(signUsers));
    return mesSwWorkTicketAscentDTO;
  }

  @Override
  public List<MesSwWorkTicketSpecialVO> getDataByList() {
    return convert.toVOList(this.list());
  }

  @Override
  public Boolean cancel(MesSwWorkTicketSpecialParam param) {
    // 获取当前用户
    MesSwWorkTicketSpecial mesSwWorkTicketAscent = super.getById(param.getId());
    Objects.requireNonNull(mesSwWorkTicketAscent, "数据异常,请联系管理员");
    if (WorkTicketStatusEnum.ZF.getCode().equals(mesSwWorkTicketAscent.getStatusCode())) {
      throw new BMSException("error", "该单子已经作废");
    }
    MesSwWorkTicketSpecial entity = convert.toEntity(param);
    entity.setStatusCode(WorkTicketStatusEnum.ZF.getCode());
    entity.setStatusName(WorkTicketStatusEnum.ZF.getDesc());
    return this.updateById(entity);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean approve(MesSwWorkTicketSpecialSignUserParam param) {
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    if (null != param.getId()) {
      MesSwWorkTicketSpecialSignUser byId =
          signUserService
              .lambdaQuery()
              .eq(MesSwWorkTicketSpecialSignUser::getId, param.getId())
              .eq(MesSwWorkTicketSpecialSignUser::getUserCode, username)
              .one();
      Objects.requireNonNull(byId, "数据异常,请联系管理员");
      if (Boolean.TRUE.equals(byId.getIsSign())) {
        throw new BMSException("error", "该用户已签字");
      }
      // 作废
      if (WorkTicketStatusEnum.ZF
          .getCode()
          .equals(super.getById(param.getTicketId()).getStatusCode())) {
        throw new BMSException("error", "该单子已经作废");
      }
    }
    List<MesSwWorkTicketSpecialSignUser> swWorkTicketAscentSignUsers =
        signUserService
            .lambdaQuery()
            .eq(MesSwWorkTicketSpecialSignUser::getTicketId, param.getTicketId())
            .list()
            .stream()
            .filter(
                w -> {
                  return null != w.getUserCode() && Boolean.FALSE.equals(w.getIsSign());
                })
            .collect(Collectors.toList());
    if (swWorkTicketAscentSignUsers.size() == 1) {
      MesSwWorkTicketSpecial mesSwWorkTicketAscent = super.getById(param.getTicketId());
      mesSwWorkTicketAscent.setStatusCode(WorkTicketStatusEnum.WC.getCode());
      mesSwWorkTicketAscent.setStatusName(WorkTicketStatusEnum.WC.getDesc());
      super.updateById(mesSwWorkTicketAscent);
    }
    MesSwWorkTicketSpecialSignUser entity = signUserConvert.toEntity(param);
    entity.setIsSign(true);
    entity.setSignTime(LocalDateTime.now());
    // 查询用户签字信息
    return signUserService.saveOrUpdate(entity);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean removeBatch(BatchParam param) {
    return super.removeByIds(param.getId());
  }
}
