package com.yhd.admin.bms.service.sw.impl.eqpt;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentRepairTbCheckDao;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairTbCheck;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairTbCheckService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 备检修提报复核记录表-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/2/10 11:20
 */
@Service
public class MesSwEquipmentRepairTbCheckServiceImpl
    extends ServiceImpl<MesSwEquipmentRepairTbCheckDao, MesSwEquipmentRepairTbCheck>
    implements MesSwEquipmentRepairTbCheckService {
  @Override
  public MesSwEquipmentRepairTbCheck getTodoByRepairId(Long repairId) {
    LambdaQueryWrapper<MesSwEquipmentRepairTbCheck> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentRepairTbCheck::getRepairId, repairId);
    wrapper.eq(MesSwEquipmentRepairTbCheck::getClResult, "0");

    List<MesSwEquipmentRepairTbCheck> tbCheckList = baseMapper.selectList(wrapper);

    return CollectionUtils.isNotEmpty(tbCheckList) ? tbCheckList.get(0) : null;
  }

  @Override
  public MesSwEquipmentRepairTbCheck getFinishByRepairId(Long repairId) {
    LambdaQueryWrapper<MesSwEquipmentRepairTbCheck> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentRepairTbCheck::getRepairId, repairId);
    wrapper.ne(MesSwEquipmentRepairTbCheck::getClResult, "0");

    wrapper.orderByDesc(MesSwEquipmentRepairTbCheck::getReceiveTime);

    List<MesSwEquipmentRepairTbCheck> tbCheckList = baseMapper.selectList(wrapper);

    return CollectionUtils.isNotEmpty(tbCheckList) ? tbCheckList.get(0) : null;
  }
}
