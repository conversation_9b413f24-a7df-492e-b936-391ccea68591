FROM  tomcat:9.0.78-jdk11

ENV  LANG C.UTF-8

ENV spring.profiles.active=prod

ENV JAVA_OPTS="-server -Xms2g -Xmx2g -Dfile.encoding=UTF-8 -Djava.awt.headless=true"

ENV TZ=Asia/Shanghai

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN sed -i 's/0027/0022/g' /usr/local/tomcat/bin/catalina.sh

COPY docker/server.xml /usr/local/tomcat/conf

COPY docker/fonts/SIMSUN.TTC /usr/share/fonts/

WORKDIR /usr/local/tomcat/webapps/

RUN rm -rf *

ADD build/libs/*.war ./scada.war

CMD ["/usr/local/tomcat/bin/catalina.sh", "run"]
