package com.yhd.admin.bms.domain.query.sw.goods;

import java.io.Serializable;
import java.math.BigDecimal;

import com.yhd.admin.bms.domain.query.sys.QueryParam;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物资管理 操作记录
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwGoodsStockOperateParam extends QueryParam implements Serializable {
    /**
     * 物资id
     */
    private Long goodsId;
    /**
     * 操作类别code 1 2
     */
    private String operateCategoryCode;
    /**
     * 操作类别 1入库 2出库
     */
    private String operateCategoryName;
    /**
     * 操作数量
     */
    private BigDecimal operateNum;
    /**
     * 操作后数量
     */
    private BigDecimal afterNum;
    /**
     * 备注
     */
    private String remark;
}
