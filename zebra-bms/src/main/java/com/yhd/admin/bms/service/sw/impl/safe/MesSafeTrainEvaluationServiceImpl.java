package com.yhd.admin.bms.service.sw.impl.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.safe.MesSafeTrainEvaluationDao;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSafeTrainEvaluationConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafetyTrainingPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSafeTrainEvaluationDTO;
import com.yhd.admin.bms.domain.dto.sys.DicItemDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSafeTrainEvaluation;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSafeTrainEvaluationDetail;
import com.yhd.admin.bms.domain.query.sw.MesSwSafetyTrainingPlanParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSafeTrainEvaluationDetailParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSafeTrainEvaluationParam;
import com.yhd.admin.bms.service.sw.MesSwSafetyTrainingPlanService;
import com.yhd.admin.bms.service.sw.safe.MesSafeTrainEvaluationDetailService;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.safe.MesSafeTrainEvaluationService;
import com.yhd.admin.bms.service.sys.DicItemService;
import com.yhd.admin.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 课件评测表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-02 09:17:40
 */
@Service
@Slf4j
public class MesSafeTrainEvaluationServiceImpl extends ServiceImpl<MesSafeTrainEvaluationDao, MesSafeTrainEvaluation> implements MesSafeTrainEvaluationService {
    @Resource
    private MesSafeTrainEvaluationConvert mesSafeTrainEvaluationConvert;
    @Resource
    private MesSafeTrainEvaluationDetailService mesSafeTrainEvaluationDetailService;
    @Resource
    private DicItemService dicItemService;
    @Resource
    private MesSwSafetyTrainingPlanService mesSwSafetyTrainingPlanService;

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MesSafeTrainEvaluationDTO> pagingQuery(MesSafeTrainEvaluationParam param) {
        IPage<MesSafeTrainEvaluation> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSafeTrainEvaluation> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(StringUtils.isNotBlank(param.getTrainingContent()), MesSafeTrainEvaluation::getTrainingContent, param.getTrainingContent());
        wrapper.eq(StringUtils.isNotBlank(param.getCourseWareName()), MesSafeTrainEvaluation::getCourseWareName, param.getCourseWareName());
        wrapper.ge(Objects.nonNull(param.getStartDate()), MesSafeTrainEvaluation::getTrainingDate, param.getStartDate());
        wrapper.le(Objects.nonNull(param.getEndDate()), MesSafeTrainEvaluation::getTrainingDate, param.getEndDate());
        wrapper.eq(Objects.nonNull(param.getTrainingDate()), MesSafeTrainEvaluation::getTrainingDate, param.getTrainingDate());
        wrapper.eq(Objects.nonNull(param.getPlanId()), MesSafeTrainEvaluation::getPlanId, param.getPlanId());
        wrapper.eq(StringUtils.isNotBlank(param.getTrainingTeacherName()), MesSafeTrainEvaluation::getTrainingTeacherName, param.getTrainingTeacherName());
        wrapper.eq(StringUtils.isNotBlank(param.getEvaluationType()), MesSafeTrainEvaluation::getEvaluationType, param.getEvaluationType());
        wrapper.eq(StringUtils.isNotBlank(param.getEvaluatorName()), MesSafeTrainEvaluation::getEvaluatorName, param.getEvaluatorName());
        wrapper.orderByDesc(MesSafeTrainEvaluation::getCreatedTime);
        return wrapper.page(page).convert(mesSafeTrainEvaluationConvert::toDTO);
    }

    /**
     * 新增或修改
     *
     * @param param
     * @return
     */
    @Override
    public Boolean addOrModify(MesSafeTrainEvaluationParam param) {
        if (CollectionUtils.isEmpty(param.getEvaluationDetailList())) {
            throw new BMSException("error", "请传入评测明细数据");
        }
        List<MesSafeTrainEvaluationDetail> evaluationDetailList = param.getEvaluationDetailList();
        if (Objects.isNull(param.getPlanId())){
            MesSwSafetyTrainingPlanParam planParam = new MesSwSafetyTrainingPlanParam();
            planParam.setTrainingContent(param.getTrainingContent());
            List<MesSwSafetyTrainingPlanDTO> planDTOList = mesSwSafetyTrainingPlanService.queryList(planParam);
            param.setPlanId(planDTOList.get(0).getId());
        }

        MesSafeTrainEvaluation entity = mesSafeTrainEvaluationConvert.toEntity(param);
        Integer totalScore = evaluationDetailList.stream().mapToInt(MesSafeTrainEvaluationDetail::getScore).sum();
        entity.setTotalScore(totalScore);

        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            param.setPageSize(20L);
            param.setCurrent(1L);
            if (pagingQuery(param).getTotal() > 0) {
                throw new BMSException("error", "您已测评过");
            }
            if (!save(entity)) {
                return false;
            }
            evaluationDetailList.forEach(detail -> detail.setEvaluationId(entity.getId()));
            return mesSafeTrainEvaluationDetailService.saveBatch(evaluationDetailList);
        } else {
            if (!updateById(entity)) {
                return false;
            }
            evaluationDetailList.forEach(detail -> detail.setEvaluationId(entity.getId()));
            MesSafeTrainEvaluationDetailParam detailParam = new MesSafeTrainEvaluationDetailParam();
            detailParam.setEvaluationId(entity.getId());
            if (!mesSafeTrainEvaluationDetailService.removeBatch(detailParam)) {
                throw new BMSException("error", "删除失败");
            }
            return mesSafeTrainEvaluationDetailService.saveBatch(evaluationDetailList);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param
     * @return
     */
    @Override
    public MesSafeTrainEvaluationDTO getCurrentDetails(MesSafeTrainEvaluationParam param) {
        MesSafeTrainEvaluationDTO dto = mesSafeTrainEvaluationConvert.toDTO(super.getById(param.getId()));
        MesSafeTrainEvaluationDetailParam detailParam = new MesSafeTrainEvaluationDetailParam();
        detailParam.setEvaluationId(param.getId());
        List<MesSafeTrainEvaluationDetail> detailList = mesSafeTrainEvaluationDetailService.queryList(detailParam);
        dto.setEvaluationDetailList(detailList);
        return dto;
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MesSafeTrainEvaluationParam param) {
        if (Objects.nonNull(param.getIds())) {
            for (Long id : param.getIds()) {
                MesSafeTrainEvaluationDetailParam detailParam = new MesSafeTrainEvaluationDetailParam();
                detailParam.setEvaluationId(id);
                boolean result = mesSafeTrainEvaluationDetailService.removeBatch(detailParam);
                if (!result) {
                    throw new BMSException("error", "删除失败");
                }
            }
        }
        return removeByIds(param.getIds());
    }

    @Override
    public Map<String, List<String>> getCourseWareEvaluationStandards() {
        Map<String, List<String>> dicItemMap = new LinkedHashMap<>();
        List<DicItemDTO> dicItemList = dicItemService.queryDicItemByDicId("COURSEWARE_EVALUATION_STANDARDS");
        dicItemList.forEach(dicItem -> {
            List<DicItemDTO> itemDTOList = dicItemService.queryDicItemByDicId(dicItem.getVal());
            if (!itemDTOList.isEmpty()){
                List<String> standards = new ArrayList<>();
                itemDTOList.forEach(item -> {
                    standards.add(item.getCode());
                });
                dicItemMap.put(dicItem.getCode(), standards);
            }
        });
        return dicItemMap;
    }

    @Override
    public List<DicItemDTO> getTeacherEvaluationStandards() {
        List<DicItemDTO> dicItemList = dicItemService.queryDicItemByDicId("TEACHER_EVALUATION_STANDARDS");
        return dicItemList;
    }

    @Override
    public List<DicItemDTO> getCourseWareEvaluationOpinion() {
        List<DicItemDTO> dicItemList = dicItemService.queryDicItemByDicId("getCourseWareEvaluationOpinion");
        return dicItemList;
    }

}
