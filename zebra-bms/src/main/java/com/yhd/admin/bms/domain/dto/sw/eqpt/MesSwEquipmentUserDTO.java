package com.yhd.admin.bms.domain.dto.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备资料表-包机人字段
 *
 * <AUTHOR>
 * @date 2023/11/29 19:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentUserDTO extends BaseDTO {

  /** 人员类型 */
  private String type;
  /** 人员类型 */
  private String typeCode;
  /** 人员名称 */
  private String name;
  /** 人员账号 */
  private String account;
}
