package com.yhd.admin.bms.domain.vo.sw.ticket;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 登高作业工作票
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketAscentVO extends BaseVO implements Cloneable, Serializable {


    /**
     * 申请单位
     */
    private String sgUnit;

    /**
     * 作业地点
     */
    private String sgLocation;

    /**
     * 作业内容
     */
    private String sgContent;

    /**
     * 作业高度
     */
    private String sgHight;

    /**
     * 计划施工开始时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime sgStartTime;

    /**
     * 计划施工结束时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime sgEndTime;

    /**
     * 实际施工开始时间(yyyy-MM-dd hh:mm)
     */
    private String sgTimes;

    /**
     * 申请人code
     */
    private String applyUserCode;

    /**
     * 申请人name
     */
    private String applyUserName;

    /**
     * 现场安全负责人code
     */
    private String safeUserCode;

    /**
     * 现场安全负责人姓名
     */
    private String safeUserName;

    /**
     * 现场技术负责人code
     */
    private String jsUserCode;

    /**
     * 现场技术负责人姓名
     */
    private String jsUserName;

    /**
     * 工作票状态code：01审批中、02审批完成、03作废
     */
    private String statusCode;

    /**
     * 工作票状态名称
     */
    private String statusName;

    /**
     * 工作票提交人code
     */
    private String tjUserCode;

    /**
     * 工作票提交人姓名
     */
    private String tjUserName;

    /**
     * 工作票提交时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime tjTime;

    /**
     * 是否有权限审批
     */
    private Boolean isAuthority;

    //所在厂站审批
    private List<MesSwWorkTicketAscentSignUserVO> signUserList1;
    //作业人员贯彻签字
    private List<MesSwWorkTicketAscentSignUserVO> signUserList2;
}
