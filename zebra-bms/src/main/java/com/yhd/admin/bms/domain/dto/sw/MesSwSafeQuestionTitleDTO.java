package com.yhd.admin.bms.domain.dto.sw;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/7/1 10:57
 * @Version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafeQuestionTitleDTO extends BaseDTO implements Cloneable, Serializable {
    /**
     *试题分组
     */
    private String title;
    /**
     *试题分类（考试试题/模拟试题）
     */
    private String questionSort;
}
