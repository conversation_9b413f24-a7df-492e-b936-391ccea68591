package com.yhd.admin.scada;

import com.serotonin.modbus4j.exception.ErrorResponseException;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/8/11 14:13
 */

@Slf4j
public class TestModBus {

    @Test
    void test() throws ModbusInitException, ModbusTransportException, ErrorResponseException {
        log.debug(">>>>>>>>>>>>. {}", LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

    }


}
