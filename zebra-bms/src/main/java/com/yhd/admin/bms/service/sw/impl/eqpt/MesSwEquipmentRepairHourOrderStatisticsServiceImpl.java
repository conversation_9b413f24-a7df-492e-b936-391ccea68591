package com.yhd.admin.bms.service.sw.impl.eqpt;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsDTO;
import com.yhd.admin.bms.domain.dto.sys.RoleDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairHourOrderDetail;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairHourOrderItem;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairHourOrderStatistics;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsParam;
import com.yhd.admin.bms.domain.query.sys.UserAccountParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairHourOrderItemService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairHourOrderStatisticsService;
import com.yhd.admin.bms.service.sys.RoleService;
import com.yhd.admin.bms.service.sys.StorageServices;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.poi.excel.CellBuilder;
import com.yhd.admin.common.poi.excel.ExcelBuilder;
import com.yhd.admin.common.utils.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 设备检修工时记录统计表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21 15:15:28
 */
@Service
public class MesSwEquipmentRepairHourOrderStatisticsServiceImpl extends ServiceImpl<MesSwEquipmentRepairHourOrderStatisticsDao, MesSwEquipmentRepairHourOrderStatistics> implements MesSwEquipmentRepairHourOrderStatisticsService {
    private static final String defaultBucketName = "swcenter";
    @Resource
    private MesSwEquipmentRepairHourOrderStatisticsConvert statisticsConvert;
    @Resource
    private MesSwEquipmentRepairHourOrderItemService itemService;
    @Resource
    private StorageServices storageServices;
    @Resource
    private RoleService roleService;
    @Resource
    private UserAccountService accountService;

    @Override
    public IPage<YearMonth> pagingQuery(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        IPage<YearMonth> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSwEquipmentRepairHourOrderStatistics> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        if (param.getStartYearMonth() != null) {
            LocalDate startDate = param.getStartYearMonth().atDay(1);
            wrapper.ge(MesSwEquipmentRepairHourOrderStatistics::getDate, startDate);
        }

        if (param.getEndYearMonth() != null) {
            LocalDate endDate = param.getEndYearMonth().atEndOfMonth();
            wrapper.le(MesSwEquipmentRepairHourOrderStatistics::getDate, endDate);
        }

        wrapper.select(MesSwEquipmentRepairHourOrderStatistics::getDate);
        wrapper.groupBy(MesSwEquipmentRepairHourOrderStatistics::getDate);

        List<LocalDate> dateList = wrapper.list().stream()
            .map(MesSwEquipmentRepairHourOrderStatistics::getDate)
            .collect(Collectors.toList());
        List<YearMonth> yearMonthList = dateList.stream()
            .map(YearMonth::from)
            .distinct()
            .sorted(Collections.reverseOrder())
            .collect(Collectors.toList());
        page.setRecords(yearMonthList);
        page.setTotal(yearMonthList.size());
        return page;
    }

    @Override
    public List<MesSwEquipmentRepairHourOrderStatisticsDTO> getCurrentDetails(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        if (Objects.isNull(param.getYearMonth())) {
            throw new BMSException("error","请输入参数");
        }
        YearMonth yearMonth = param.getYearMonth();
        int monthLength = yearMonth.lengthOfMonth();
        LocalDate date = LocalDate.now();
        if (date.getYear() == yearMonth.getYear() && date.getMonth() == yearMonth.getMonth()) {
            monthLength = date.getDayOfMonth();
        }
        LambdaQueryWrapper<MesSwEquipmentRepairHourOrderStatistics> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(MesSwEquipmentRepairHourOrderStatistics::getDate, yearMonth.atDay(1));
        wrapper.le(MesSwEquipmentRepairHourOrderStatistics::getDate, yearMonth.atEndOfMonth());
        List<MesSwEquipmentRepairHourOrderStatistics> data = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        Map<String, List<MesSwEquipmentRepairHourOrderStatistics>> nameMap = data.stream()
            .collect(Collectors.groupingBy(
                MesSwEquipmentRepairHourOrderStatistics::getWorkUserName,
                // 下游收集器：先收集为列表，再按 date 升序排序
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        // 按 date 升序排序
                        list.sort(Comparator.comparing(MesSwEquipmentRepairHourOrderStatistics::getDate));
                        return list;
                    }
                )
            ));

        List<MesSwEquipmentRepairHourOrderStatisticsDTO> result = new ArrayList<>();
        int year = yearMonth.getYear();
        Month month = yearMonth.getMonth();
        for (Map.Entry<String, List<MesSwEquipmentRepairHourOrderStatistics>> entry : nameMap.entrySet()) {
            List<MesSwEquipmentRepairHourOrderStatistics> dailyList = entry.getValue();
            List<MesSwEquipmentRepairHourOrderDetail> detailList = new ArrayList<>();
            MesSwEquipmentRepairHourOrderStatisticsDTO dto = statisticsConvert.toDTO(dailyList.get(0));
            dto.setDate(null);
            dto.setOrderId(null);
            BigDecimal totalWorkHour = BigDecimal.ZERO;
            int totalScore = 0;

            for (int i = 1, j = 0; i <= monthLength; i++) {
                MesSwEquipmentRepairHourOrderDetail detail = new MesSwEquipmentRepairHourOrderDetail();
                if (j < dailyList.size() && dailyList.get(j).getDate().getDayOfMonth() == i) {
                    detail.setDate(dailyList.get(j).getDate());
                    detail.setWorkHour(dailyList.get(j).getTotalWorkHour());
                    detail.setScore(dailyList.get(j).getScore());
                    totalWorkHour = totalWorkHour.add(dailyList.get(j).getTotalWorkHour());
                    if (Objects.nonNull(dailyList.get(j).getScore())){
                        totalScore += dailyList.get(j).getScore();
                    }
                    j++;
                } else {
                    detail.setDate(LocalDate.of(year, month, i));
                }
                detailList.add(detail);
            }
            dto.setDetailList(detailList);
            dto.setTotalWorkHour(totalWorkHour);
            dto.setScore(totalScore);
            // 设置岗位信息
            dto.setPost(getPost(dto.getWorkUserCode()));
            result.add(dto);
        }
        //处理 result数据 当选中电工时，下方详情列表展示岗位为电工的数据；当选择钳工时，下方详情列表展示岗位为钳工加上检修钳工的数据的数据 ，根据字段 post处理，post传电工，就展示电工的数据 ，传钳工，就展示位为钳工加上检修钳工的数据的数据，post不传，就默认展示全部
        if (StringUtils.isNotBlank(param.getPost())) {
            result = result.stream().filter(item -> {
                String itemPost = item.getPost();
                if (StringUtils.isBlank(itemPost)) {
                    return false;
                }
                // 如果传入的是"电工"，只展示岗位为"电工"的数据
                if ("电工".equals(param.getPost())) {
                    return "电工".equals(itemPost);
                }
                // 如果传入的是"钳工"，展示岗位为"钳工"或"检修钳工"的数据
                if ("钳工".equals(param.getPost())) {
                    return "钳工".equals(itemPost) || "检修钳工".equals(itemPost);
                }
                // 其他情况，精确匹配
                return param.getPost().equals(itemPost);
            }).collect(Collectors.toList());
        }
        return result;
    }

    private String getPost(String workUserCode) {
        UserAccountParam param = new UserAccountParam();
        param.setUsername(workUserCode);
        return accountService.currentDetail(param.getUsername()).getPost();
    }

    @Override
    public IPage<LocalDate> pagingQueryOfScore(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        IPage<MesSwEquipmentRepairHourOrderItem> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSwEquipmentRepairHourOrderItem> wrapper = new LambdaQueryChainWrapper<>(itemService.getBaseMapper());
        wrapper.ge(Objects.nonNull(param.getStartDate()), MesSwEquipmentRepairHourOrderItem::getDate, param.getStartDate());
        wrapper.le(Objects.nonNull(param.getEndDate()), MesSwEquipmentRepairHourOrderItem::getDate, param.getEndDate());
//        wrapper.eq(MesSwEquipmentRepairHourOrderItem::getTypeCode, param.getTypeCode());

        wrapper.select(MesSwEquipmentRepairHourOrderItem::getDate);
        wrapper.groupBy(MesSwEquipmentRepairHourOrderItem::getDate);
        wrapper.orderByDesc(MesSwEquipmentRepairHourOrderItem::getDate);

        return wrapper.page(page).convert(MesSwEquipmentRepairHourOrderItem::getDate);
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param
     * @return
     */
    @Override
    public List<MesSwEquipmentRepairHourOrderStatisticsDTO> getCurrentDetailsOfScore(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        if (Objects.isNull(param.getDate())) {
            throw new BMSException("error","请输入参数");
        }
//        if (StringUtils.isBlank(param.getTypeCode())) {
//            throw new BMSException("error","请输入参数");
//        }
        //工时记录统计表
        LambdaQueryWrapper<MesSwEquipmentRepairHourOrderStatistics> wrapper = new LambdaQueryWrapper<>();
        // 查询条件 日期
        wrapper.eq(MesSwEquipmentRepairHourOrderStatistics::getDate, param.getDate());
        // 查询条件 检修工时类型代码
//        wrapper.eq(MesSwEquipmentRepairHourOrderStatistics::getTypeCode, param.getTypeCode());
        List<MesSwEquipmentRepairHourOrderStatistics> statisticsList = baseMapper.selectList(wrapper);
        //处理岗位字段
        statisticsList.forEach(item -> {
            item.setPost(getPost(item.getWorkUserCode()));
        });
        //工时记录子表
        LambdaQueryWrapper<MesSwEquipmentRepairHourOrderItem> itemWrapper = new LambdaQueryWrapper<>();
        // 查询条件 日期
        itemWrapper.eq(MesSwEquipmentRepairHourOrderItem::getDate, param.getDate());
        // 查询条件 检修工时类型代码
//        itemWrapper.eq(MesSwEquipmentRepairHourOrderItem::getTypeCode, param.getTypeCode());
        List<MesSwEquipmentRepairHourOrderItem> itemList = itemService.list(itemWrapper);
        Map<String, List<MesSwEquipmentRepairHourOrderItem>> itemMap = itemList.stream()
            .collect(Collectors.groupingBy(MesSwEquipmentRepairHourOrderItem::getShareUserName));

        List<MesSwEquipmentRepairHourOrderStatisticsDTO> result = new ArrayList<>();
        for (MesSwEquipmentRepairHourOrderStatistics statistics : statisticsList) {
            MesSwEquipmentRepairHourOrderStatisticsDTO statisticsDTO = statisticsConvert.toDTO(statistics);
            List<MesSwEquipmentRepairHourOrderDetail> detailList = new ArrayList<>();
            BigDecimal totalWorkHour = BigDecimal.ZERO;
            // 查询工时记录明细
            List<MesSwEquipmentRepairHourOrderItem> filteredItems = itemMap.getOrDefault(statisticsDTO.getWorkUserName(), Collections.emptyList());

            // 遍历明细 只遍历四次
            for (int i = 1; i <= 4; i++) {
                MesSwEquipmentRepairHourOrderDetail detail = new MesSwEquipmentRepairHourOrderDetail();
                detail.setTitle("工单" + i);
                if (i <= filteredItems.size()) {
                    MesSwEquipmentRepairHourOrderItem item = filteredItems.get(i - 1);
                    detail.setWorkContent(item.getWorkContent());
                    detail.setWorkHour(item.getWorkHour() != null ? item.getWorkHour() : BigDecimal.ZERO);
                    totalWorkHour = totalWorkHour.add(item.getWorkHour());
                } else {
                    detail.setWorkContent("");
                    detail.setWorkHour(BigDecimal.ZERO);
                }
                detailList.add(detail);
            }
            statisticsDTO.setTotalWorkHour(totalWorkHour);
            statisticsDTO.setDetailList(detailList);
            result.add(statisticsDTO);
        }
        //处理 result数据 当选中电工时，下方详情列表展示岗位为电工的数据；当选择钳工时，下方详情列表展示岗位为钳工加上检修钳工的数据的数据 ，根据字段 post处理，post传电工，就展示电工的数据 ，传钳工，就展示位为钳工加上检修钳工的数据的数据，post不传，就默认展示全部
        if (StringUtils.isNotBlank(param.getPost())) {
            result = result.stream().filter(item -> {
                String itemPost = item.getPost();
                if (StringUtils.isBlank(itemPost)) {
                    return false;
                }
                // 如果传入的是"电工"，只展示岗位为"电工"的数据
                if ("电工".equals(param.getPost())) {
                    return "电工".equals(itemPost);
                }
                // 如果传入的是"钳工"，展示岗位为"钳工"或"检修钳工"的数据
                if ("钳工".equals(param.getPost())) {
                    return "钳工".equals(itemPost) || "检修钳工".equals(itemPost);
                }
                // 其他情况，精确匹配
                return param.getPost().equals(itemPost);
            }).collect(Collectors.toList());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean scoring(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        List<MesSwEquipmentRepairHourOrderStatisticsParam> paramList = new ArrayList<>();
        if (param != null) {
            paramList = param.getParamList();
        }
        if (CollectionUtils.isEmpty(paramList)) {
            throw new BMSException("error", "参数不能为空");
        }
        for (MesSwEquipmentRepairHourOrderStatisticsParam item: paramList) {
            MesSwEquipmentRepairHourOrderStatistics entity = statisticsConvert.toEntity(item);
            if (!updateById(entity)) {
                throw new BMSException("error", "修改失败");
            }
        }
        return true;
    }

    @Override
    public Boolean removeBatch(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        if (param == null) {
            throw new BMSException("error", "参数不能为空");
        }
        if (Objects.nonNull(param.getOrderId())) {
            return this.lambdaUpdate()
                .eq(MesSwEquipmentRepairHourOrderStatistics::getOrderId, param.getOrderId())
                .remove();
        }
        return removeByIds(param.getIds());
    }

    @Override
    public Boolean initOrderStatistics(List<MesSwEquipmentRepairHourOrderItem> paramList) {
        Long orderId = paramList.get(0).getParentId();
        LocalDate date = paramList.get(0).getDate();
        // 初始化
        LambdaQueryWrapper<MesSwEquipmentRepairHourOrderStatistics> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwEquipmentRepairHourOrderStatistics::getDate, date);
        List<MesSwEquipmentRepairHourOrderStatistics> existList = this.list(wrapper);
        List<String> workUserCodeList = existList.stream()
            .map(MesSwEquipmentRepairHourOrderStatistics::getWorkUserCode)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        List<MesSwEquipmentRepairHourOrderStatistics> statisticsList = new ArrayList<>();
        for (MesSwEquipmentRepairHourOrderItem item : paramList) {
            BigDecimal totalWorkHour = getTotalWorkHour(item);
            MesSwEquipmentRepairHourOrderStatistics statistics = new MesSwEquipmentRepairHourOrderStatistics();
            if (!workUserCodeList.contains(item.getShareUserCode())) {
                statistics.setOrderId(orderId);
                statistics.setDate(item.getDate());
                statistics.setTypeCode(item.getTypeCode());
                statistics.setTypeName(item.getTypeName());
                statistics.setWorkUserCode(item.getShareUserCode());
                statistics.setWorkUserName(item.getShareUserName());
                statistics.setTotalWorkHour(totalWorkHour);
                statisticsList.add(statistics);
                workUserCodeList.add(item.getShareUserCode());
            } else {
                //只修改总工时
                wrapper.eq(MesSwEquipmentRepairHourOrderStatistics::getWorkUserCode, item.getShareUserCode());
                List<MesSwEquipmentRepairHourOrderStatistics> personList = this.list(wrapper);
                if (CollectionUtils.isNotEmpty(personList)) {
                    statistics = personList.get(0);
                    statistics.setTotalWorkHour(totalWorkHour);
                    updateById(statistics);
                }
            }
        }
        return super.saveBatch(statisticsList);
    }

    private BigDecimal getTotalWorkHour(MesSwEquipmentRepairHourOrderItem itemEntity) {
        BigDecimal totalWorkHour = BigDecimal.ZERO;
        if (itemEntity != null) {
            String workUserCode = itemEntity.getShareUserCode();
            LocalDate date = itemEntity.getDate();
            LambdaQueryWrapper<MesSwEquipmentRepairHourOrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MesSwEquipmentRepairHourOrderItem::getShareUserCode, workUserCode);
            wrapper.eq(MesSwEquipmentRepairHourOrderItem::getDate, date);
            List<MesSwEquipmentRepairHourOrderItem> itemList = itemService.list(wrapper);
            if (!itemList.isEmpty()) {
                totalWorkHour = itemList.stream()
                    .map(MesSwEquipmentRepairHourOrderItem::getWorkHour)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            }
        }
        return totalWorkHour;
    }

    @Override
    public Boolean getModifyFlag() {
        // 当前登录人
        String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
        // 判断是否拥有删除标识
        UserAccountParam accountParam = new UserAccountParam();
        accountParam.setUsername(account);
        List<RoleDTO> roleList = roleService.getRoleByUser(accountParam);
        if (CollectionUtil.isEmpty(roleList)) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "当前登录人角色为空，请检查！");
        }
        List<String> roleCodeList =
            roleList.stream().map(RoleDTO::getRoleCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(roleCodeList)) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "当前登录人角色为空，请检查！");
        }
        // 检修电工、钳工有查看、修改权限
        return roleCodeList.contains(DicConstant.REPAIR_ELECTRICIAN) || roleCodeList.contains(DicConstant.REPAIR_FITTER);
    }

    @Override
    public String exportOfScore(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        List<MesSwEquipmentRepairHourOrderStatisticsDTO> list = getCurrentDetailsOfScore(param);
        if (CollectionUtil.isEmpty(list)) {
            throw new BMSException("error", "无数据可导出");
        }

        String fileName = "设备检修工时评优_" + IdWorker.getTimeId() + ".xls";
        String formattedDate = param.getDate().format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
        HSSFWorkbook wb;
        ByteArrayInputStream inputStream;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/vnd.ms-excel");
            wb = new HSSFWorkbook();
            HSSFSheet wbSheet = wb.createSheet("设备检修工时评优");
            // 格式
            HSSFCellStyle mainTitleStyle = titleStyle(wb);
            HSSFCellStyle titleStyle = titleStyle(wb);
            titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
            HSSFCellStyle contentStyle = ExcelBuilder.createContentCellStyle(wb);
            HSSFCellStyle dateStyle = ExcelBuilder.createContentCellStyle(wb);
            DataFormat format = wb.createDataFormat();
            dateStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));

            AtomicInteger rowNum = new AtomicInteger();
            // 第1行，标题设置
            HSSFRow row0 = wbSheet.createRow(rowNum.getAndIncrement());
            HSSFCell row0Cell = row0.createCell(0);
            row0Cell.setCellValue("检修车间" + formattedDate + "工时评优表");
            row0Cell.setCellStyle(mainTitleStyle);
            //第2行
            HSSFRow row1 = wbSheet.createRow(rowNum.getAndIncrement());
            //第3行
            HSSFRow row2 = wbSheet.createRow(rowNum.getAndIncrement());
            List<String> titleList =
                Stream.of("序号","作业人员","岗位","日期","工单1","工单2","工单3","工单4","总工时","得分")
                    .collect(Collectors.toList());
            CellBuilder.build(row0, 13, "", mainTitleStyle);
            for (int i = 0; i < 3; i++) {
                CellBuilder.build(row1, i, titleList.get(i), titleStyle);
                CellBuilder.build(row2, i, "", titleStyle);
            }
            CellBuilder.build(row1, 3, "日期", titleStyle);
            CellBuilder.build(row2, 3, "", titleStyle);
            for (int i = 4; i < 8; i++) {
                CellBuilder.build(row1, 2*i - 4, "", titleStyle);
                CellBuilder.build(row1, 2*i - 3, titleList.get(i), titleStyle);
            }
            for (int i = 8; i < titleList.size(); i++) {
                CellBuilder.build(row1, i + 4, titleList.get(i), titleStyle);
                CellBuilder.build(row2, i + 4, "", titleStyle);
            }
            for (int i = 4; i < 12; i++) {
                if (i % 2 == 0) {
                    CellBuilder.build(row2, i, "作业内容", titleStyle);
                } else {
                    CellBuilder.build(row2, i, "工时", titleStyle);
                }
            }
            // 设置列宽
            for (int i = 3; i < 12; i++) {
                wbSheet.setColumnWidth(i, wbSheet.getColumnWidth(i) * 2);
            }
            // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
            // 标题行
            wbSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 13));
            // 序号
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
            // 作业人员
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
            // 岗位
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
            // 日期
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3));
            // 工单区域合并
            for (int i = 0; i < 4; i++) {
                wbSheet.addMergedRegion(new CellRangeAddress(1, 1, 4 + i*2, 5 + i*2));
            }
            // 总工时
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 12, 12));
            // 得分
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 13, 13));


            list.forEach(dto -> {
                HSSFRow tempRow = wbSheet.createRow(rowNum.getAndIncrement());
                CellBuilder.build(tempRow, 0, rowNum.get() - 3, contentStyle);
                CellBuilder.build(tempRow, 1, dto.getWorkUserName(), contentStyle);
                CellBuilder.build(tempRow, 2, dto.getPost(), contentStyle); // 添加岗位字段
                CellBuilder.build(tempRow, 3, dto.getDate(), dateStyle);
                CellBuilder.build(tempRow, 12, String.valueOf(dto.getTotalWorkHour()), contentStyle);
                CellBuilder.build(tempRow, 13, dto.getScore(), contentStyle);
                List<MesSwEquipmentRepairHourOrderDetail> detailList = dto.getDetailList();
                for (int i = 0; i < 4; i++) {
                    CellBuilder.build(tempRow, 2 * i + 4, detailList.get(i).getWorkContent(), contentStyle);
                    CellBuilder.build(tempRow, 2 * i + 5, String.valueOf(detailList.get(i).getWorkHour()), contentStyle);
                }
            });
            wb.write(outputStream);

            inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 执行上传
            storageServices.uploadObject(defaultBucketName, fileName, headers, inputStream);
            return storageServices.getStorageUrl(fileName, defaultBucketName);
        } catch (Exception e) {
            log.error("设备检修工时评优导出失败", e);
            throw new BMSException("error", "文件导出失败");
        }
    }

    @Override
    public String export(MesSwEquipmentRepairHourOrderStatisticsParam param) {
        if (Objects.isNull(param.getYearMonth())) {
            throw new BMSException("error", "请输入参数");
        }
        List<MesSwEquipmentRepairHourOrderStatisticsDTO> list = getCurrentDetails(param);
        if (CollectionUtil.isEmpty(list)) {
            throw new BMSException("error", "无数据可导出");
        }
        YearMonth yearMonth = param.getYearMonth();
        int monthLength = yearMonth.lengthOfMonth();
        LocalDate date = LocalDate.now();
        if (date.getYear() == yearMonth.getYear() && date.getMonth() == yearMonth.getMonth()) {
            monthLength = date.getDayOfMonth();
        }

        String fileName = "设备检修工时统计_" + IdWorker.getTimeId() + ".xls";
        String formattedDate = param.getYearMonth().format(DateTimeFormatter.ofPattern("M月"));
        HSSFWorkbook wb ;
        ByteArrayInputStream inputStream;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/vnd.ms-excel");
            wb = new HSSFWorkbook();
            HSSFSheet wbSheet = wb.createSheet("设备检修工时统计");
            // 格式
            HSSFCellStyle mainTitleStyle = titleStyle(wb);
            HSSFCellStyle titleStyle = titleStyle(wb);
            titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
            HSSFCellStyle contentStyle = ExcelBuilder.createContentCellStyle(wb);

            AtomicInteger rowNum = new AtomicInteger();
            // 第1行，标题设置
            HSSFRow row0 = wbSheet.createRow(rowNum.getAndIncrement());
            HSSFCell row0Cell = row0.createCell(0);
            row0Cell.setCellValue("检修车间" + formattedDate + "份工时统计表");
            row0Cell.setCellStyle(mainTitleStyle);
            //第2行
            HSSFRow row1 = wbSheet.createRow(rowNum.getAndIncrement());
            //第3行
            HSSFRow row2 = wbSheet.createRow(rowNum.getAndIncrement());
            List<String> titleList =
                Stream.of("序号","姓名","岗位")
                    .collect(Collectors.toList());
            for (int i = 0; i < titleList.size(); i++) {
                CellBuilder.build(row1, i, titleList.get(i), titleStyle);
                CellBuilder.build(row2, i, "", titleStyle);
            }
            for (int i = 1; i <= monthLength + 1; i++) {
                if (i != monthLength + 1) {
                    CellBuilder.build(row1, 2 * i + 1, i + "日", titleStyle);
                    CellBuilder.build(row1, 2 * i + 2, "", titleStyle);
                } else {
                    CellBuilder.build(row1, 2 * i + 1, "总计", titleStyle);
                    CellBuilder.build(row0, 2 * i + 2, "", mainTitleStyle);
                }
                CellBuilder.build(row2, 2 * i + 1, "工时", titleStyle);
                CellBuilder.build(row2, 2 * i + 2, "加分", titleStyle);
            }
            // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
            // 标题行
            wbSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, monthLength * 2 + 4));
            // 序号
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
            // 姓名
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
            // 岗位
            wbSheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
            // 日期区域合并
            for (int i = 1; i <= monthLength + 1; i++) {
                wbSheet.addMergedRegion(new CellRangeAddress(1, 1, 2 * i + 1, 2 * i + 2));
            }

            int finalMonthLength = monthLength;
            list.forEach(dto -> {
                HSSFRow tempRow = wbSheet.createRow(rowNum.getAndIncrement());
                CellBuilder.build(tempRow, 0, rowNum.get() - 3, contentStyle);
                CellBuilder.build(tempRow, 1, dto.getWorkUserName(), contentStyle);
                CellBuilder.build(tempRow, 2, dto.getPost(), contentStyle); // 添加岗位字段
                List<MesSwEquipmentRepairHourOrderDetail> detailList = dto.getDetailList();
                for (int i = 0; i < finalMonthLength; i++) {
                    if (Objects.nonNull(detailList.get(i).getWorkHour())) {
                        CellBuilder.build(tempRow, 2 * i + 3, String.valueOf(detailList.get(i).getWorkHour()),contentStyle);
                    } else {
                        CellBuilder.build(tempRow, 2 * i + 3, "",contentStyle);
                    }
                    CellBuilder.build(tempRow, 2 * i + 4, detailList.get(i).getScore(), contentStyle);
                }
                CellBuilder.build(tempRow, 2 * finalMonthLength + 3, String.valueOf(dto.getTotalWorkHour()), contentStyle);
                CellBuilder.build(tempRow, 2 * finalMonthLength + 4, dto.getScore(), contentStyle);
            });
            wb.write(outputStream);

            inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            // 执行上传
            storageServices.uploadObject(defaultBucketName, fileName, headers, inputStream);
            return storageServices.getStorageUrl(fileName, defaultBucketName);
        } catch (Exception e) {
            log.error("设备检修工时统计导出失败", e);
            throw new BMSException("error", "文件导出失败");
        }
    }

    private HSSFCellStyle titleStyle(HSSFWorkbook wb) {
        HSSFCellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(IndexedColors.WHITE.index);
        titleStyle.setBottomBorderColor(IndexedColors.BLACK.index);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderTop(BorderStyle.THIN);
        HSSFFont headerFont = wb.createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 12);
        titleStyle.setFont(headerFont);
        return titleStyle;
    }
}
