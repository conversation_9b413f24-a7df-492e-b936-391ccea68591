package com.yhd.admin.bms.controller.api.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesAppVersionConvert;
import com.yhd.admin.bms.domain.dto.sw.MesAppVersionDTO;
import com.yhd.admin.bms.domain.query.sw.MesAppVersionParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesAppVersionVO;
import com.yhd.admin.bms.service.sw.MesAppVersionService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> @Date 2022/6/14 10:53 @Description: @Version 1.0
 */
@RequestMapping("/app/version")
@RestController
public class MesAppVersionController {
  @Resource private MesAppVersionService service;

  @Resource private MesAppVersionConvert convert;

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesAppVersionVO> pagingQuery(@RequestBody MesAppVersionParam queryParam) {
    IPage<MesAppVersionDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @SysLogs(title = "APP版本控制新增或编辑", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson addOrModify(@RequestBody MesAppVersionParam param) {

    Boolean retVal = service.addOrModify(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @SysLogs(title = "APP版本控制删除", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson removeBatch(@RequestBody BatchParam param) {

    Boolean retVal = service.removeBatch(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @GetMapping(value = "/query")
  public RespJson query(
      @RequestParam String versionType, @RequestParam(required = false) String eqptType) {
    return RespJson.buildSuccessResponse(
        convert.toVO(service.getCurrentDetail(versionType, eqptType)));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetail(@RequestBody MesAppVersionParam param) {
    return RespJson.buildSuccessResponse(convert.toVO(service.query(param)));
  }
}
