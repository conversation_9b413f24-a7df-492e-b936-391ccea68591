package com.yhd.admin.bms.domain.vo.flowable;

import java.io.Serializable;
import java.util.Date;

public class FlowNodeVo implements Serializable {
  /** 节点id */
  private String nodeId;
  /** 节点名称 */
  private String nodeName;
  /** 执行人的code */
  private String userCode;
  /** 执行人姓名 */
  private String userName;

  /** 任务节点结束时间 */
  private Date endTime;

  public String getNodeId() {
    return nodeId;
  }

  public void setNodeId(String nodeId) {
    this.nodeId = nodeId;
  }

  public String getNodeName() {
    return nodeName;
  }

  public void setNodeName(String nodeName) {
    this.nodeName = nodeName;
  }

  public String getUserCode() {
    return userCode;
  }

  public void setUserCode(String userCode) {
    this.userCode = userCode;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public Date getEndTime() {
    return endTime;
  }

  public void setEndTime(Date endTime) {
    this.endTime = endTime;
  }

  @Override
  public String toString() {
    return "FlowNodeVo{"
        + "nodeId='"
        + nodeId
        + '\''
        + ", nodeName='"
        + nodeName
        + '\''
        + ", userCode='"
        + userCode
        + '\''
        + ", userName='"
        + userName
        + '\''
        + ", endTime="
        + endTime
        + '}';
  }
}
