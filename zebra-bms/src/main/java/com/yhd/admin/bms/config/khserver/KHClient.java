package com.yhd.admin.bms.config.khserver;

import KHThrift.*;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName KHClient.java
 * @createTime 2024-07-16 19:16:24
 */
@Slf4j
@Service
public class KHClient {

  @Resource private KHServerCfg khServerCfg;

  private KDBApi m_Client = null;

  public KHClient() {}

  public void init() {
    try {
      if (m_Client != null && IsConnected()) {
        return;
      }
      m_Client = new KDBApi();
      ThriftConnectOption ConnOption = new ThriftConnectOption();
      ConnOption.ServerName = khServerCfg.getName();
      ConnOption.ServerPort = khServerCfg.getPort();
      ConnOption.UserName = khServerCfg.getUsername();
      ConnOption.Password = khServerCfg.getPassword();
      ConnOption.NetworkTimeout = khServerCfg.getTimeout();
      ThriftConnectRet Ret = null;
      Ret = m_Client.KDBServerConnect(ConnOption);
      if (Ret.Ret == khbrokerConstants.THRIFT_KERR_OK) {
        log.info("第三方接口重新连接>>>>>>>>>>>>>>>>>KHServer Connect succeed");
      } else {
        log.error(String.format("第三方接口重新连接>>>>>>>>>>>>>>>>>KHServer Connect failed:%d", Ret.Ret));
      }
    } catch (Exception e) {
      log.error("第三方接口重新连接>>>>>>>>>>>>>>>>>KHServer Connect 异常:{}", e.getMessage());
    }
  }

  public boolean IsConnected() {
    boolean KDBServerIsConnected = false;
    try {
      KDBServerIsConnected = m_Client.KDBServerIsConnected();
      if (KDBServerIsConnected) {
        return Boolean.TRUE;
      } else {
        return Boolean.FALSE;
      }
    } catch (Exception e) {
      log.error("第三方接口是否已经连接>>>>>>>>>>>>>>>>>KHServer Connected 异常:{}", e.getMessage());
      return Boolean.FALSE;
    }
  }

  public synchronized List<KDBDataRecordset> OpenRecordset(ThriftDataCriteria Criteria) {
    try {
      if (m_Client == null || !IsConnected()) {
        init();
      }
      ThriftDataOpenRecordsetRet Ret = m_Client.KDBDataOpenRecordset(Criteria);
      if (Ret.Ret == khbrokerConstants.THRIFT_KERR_OK) {
        List<KDBDataRecordset> r = KDBConvert.Thrift2KDB(Ret.DataRecords);
        return r;
      } else {
        return null;
      }

    } catch (Exception e) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(),
          "第三方接口KDBDataOpenRecordset>>>>>>>>>>>>>>>>>查询数据 异常:++++++++++++" + e.getMessage());
    }
  }

  public synchronized List<KDBDataProperties> getCurrentValue(String tagName) {
    try {
      if (m_Client == null || !IsConnected()) {
        init();
      }
      List<String> tagNames = Lists.newArrayList();
      tagNames.add(tagName);
      ThriftDataGetCurrentValueRet ret = m_Client.KDBDataGetCurrentValue(tagNames);
      if (ret.Ret == khbrokerConstants.THRIFT_KERR_OK) {
        return KDBConvert.Convert(ret.DataProperties);
      } else {
        return null;
      }

    } catch (Exception e) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(),
          "第三方接口KDBDataOpenRecordset>>>>>>>>>>>>>>>>>查询数据 异常:++++++++++++" + e.getMessage());
    }
  }

  public synchronized List<KDBDataProperties> getCurrentValue(List<String> tagNames) {
    try {
      if (m_Client == null || !IsConnected()) {
        init();
      }
      ThriftDataGetCurrentValueRet ret = m_Client.KDBDataGetCurrentValue(tagNames);
      if (ret.Ret == khbrokerConstants.THRIFT_KERR_OK) {
        return KDBConvert.Convert(ret.DataProperties);
      } else {
        return null;
      }

    } catch (Exception e) {
      throw new BMSException(
          ResultStateEnum.FAIL.getCode(),
          "第三方接口KDBDataOpenRecordset>>>>>>>>>>>>>>>>>查询数据 异常:++++++++++++" + e.getMessage());
    }
  }

  public void destroy() {
    int Ret = 0;
    if (m_Client == null) {
      return;
    }
    try {
      Ret = m_Client.KDBServerDisconnect();
      if (Ret == khbrokerConstants.THRIFT_KERR_OK) {
        log.info("第三方接口断开>>>>>>>>>>>>>>>>>KHServer DisConnect succeed");
      } else {
        log.info(String.format("第三方接口断开>>>>>>>>>>>>>>>>>KHServer Disconnect failed:%d", Ret));
      }
    } catch (Exception e) {
      log.error("第三方接口断开>>>>>>>>>>>>>>>>>KHServer Disconnect 异常:{}", e.getMessage());
    }
  }
}
