package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.MesSwUnsafeBehaviorDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwUnsafeBehaviorConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwUnsafeBehaviorDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwUnsafeBehavior;
import com.yhd.admin.bms.domain.query.sw.MesSwUnsafeBehaviorParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.service.sw.MesSwUnsafeBehaviorService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.FileService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 不安全行为
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-26
 */
@Service
public class MesSwUnsafeBehaviorServiceImpl extends ServiceImpl<MesSwUnsafeBehaviorDao, MesSwUnsafeBehavior> implements MesSwUnsafeBehaviorService {

    private static final String BUSINESS_NAME = "不安全行为";
    private static final String INSECURE = "不安全行为描述视频";

    private static final String RECORD = "沟通记录视频";

    private static final String REASON = "原因分析视频";

    private static final String CORRECT = "矫正计划视频";

    private static final String EVALATION = "效果评估视频";
    @Resource
    private MesSwUnsafeBehaviorConvert convert;
    @Resource
    private MesSwUnsafeBehaviorService service;
    @Resource
    private FileService fileService;

    @Resource
    private DicService dicService;

    @Resource
    private UserAccountService accountService;

    @Override
    public IPage<MesSwUnsafeBehaviorDTO> pagingQuery(MesSwUnsafeBehaviorParam queryParam) {
        Page<MesSwUnsafeBehavior> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwUnsafeBehavior> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //矫正时间
        if (queryParam.getStartDateTime() != null && queryParam.getEndDateTime() != null) {
            queryChain.between(
                MesSwUnsafeBehavior::getTime,
                queryParam.getStartDateTime(), queryParam.getEndDateTime()
            );
        }        //矫正对象
        queryChain.like(!StringUtils.isEmpty(queryParam.getCorrection()), MesSwUnsafeBehavior::getCorrection, queryParam.getCorrection());
        //责任单位
        queryChain.like(!StringUtils.isEmpty(queryParam.getUnit()), MesSwUnsafeBehavior::getUnit, queryParam.getUnit());
        //矫正风险等级
        queryChain.like(!StringUtils.isEmpty(queryParam.getRiskLevel()), MesSwUnsafeBehavior::getRiskLevel, queryParam.getRiskLevel());
        //负责人
        queryChain.like(!StringUtils.isEmpty(queryParam.getInChargeName()), MesSwUnsafeBehavior::getInChargeName, queryParam.getInChargeName());
        queryChain.orderByDesc(MesSwUnsafeBehavior::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(MesSwUnsafeBehaviorParam param) {
        String riskLevelName = dicService.transform("RISK_LEVEL", param.getRiskLevelCode());
        param.setRiskLevel(riskLevelName);
        this.getUseName(param);
        MesSwUnsafeBehavior entity = convert.toEntity(param);
        boolean b = super.save(entity);
        fileService.insertFile(entity.getId(), BUSINESS_NAME, INSECURE, param.getInsecureVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, RECORD, param.getRecordVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, REASON, param.getReasonVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, CORRECT, param.getCorrectionVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, EVALATION, param.getEvaluationVideoList());
        return b;
    }

    private void getUseName(MesSwUnsafeBehaviorParam param) {
        //负责人
        if (!StringUtils.isEmpty(param.getInCharge())) {
            String nameByUserName = accountService.getNameByUserName(param.getInCharge());
            param.setInChargeName(nameByUserName);
        }
        //沟通记录人
        if (!StringUtils.isEmpty(param.getRecorder())) {
            String re = accountService.getNameByUserName(param.getRecorder());
            param.setRecorderName(re);
        }
        //沟通负责人
        if (!StringUtils.isEmpty(param.getRecordCharge())) {
            String nameByUserName = accountService.getNameByUserName(param.getRecordCharge());
            param.setRecordChargeName(nameByUserName);
        }
        //分析人
        if (!StringUtils.isEmpty(param.getReasoner())) {
            String nameByUserName = accountService.getNameByUserName(param.getReasoner());
            param.setReasonerName(nameByUserName);
        }
        //评估人员
        if (!StringUtils.isEmpty(param.getEvaluationer())) {
            String nameByUserName = accountService.getNameByUserName(param.getEvaluationer());
            param.setEvaluationerName(nameByUserName);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modify(MesSwUnsafeBehaviorParam param) {
        String riskLevelName = dicService.transform("RISK_LEVEL", param.getRiskLevelCode());
        param.setRiskLevel(riskLevelName);
        this.getUseName(param);
        MesSwUnsafeBehavior entity = convert.toEntity(param);
        fileService.insertFile(entity.getId(), BUSINESS_NAME, INSECURE, param.getInsecureVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, RECORD, param.getRecordVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, REASON, param.getReasonVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, CORRECT, param.getCorrectionVideoList());
        fileService.insertFile(entity.getId(), BUSINESS_NAME, EVALATION, param.getEvaluationVideoList());
        return super.updateById(entity);
    }

    @Override
    public MesSwUnsafeBehaviorDTO getCurrentDetail(MesSwUnsafeBehaviorParam param) {
        MesSwUnsafeBehavior behavior = super.getById(param.getId());
        behavior.setInsecureVideoList(fileService.getFileList(behavior.getId(), BUSINESS_NAME, INSECURE));
        behavior.setRecordVideoList(fileService.getFileList(behavior.getId(), BUSINESS_NAME, RECORD));
        behavior.setReasonVideoList(fileService.getFileList(behavior.getId(), BUSINESS_NAME, REASON));
        behavior.setCorrectionVideoList(fileService.getFileList(behavior.getId(), BUSINESS_NAME, CORRECT));
        behavior.setEvaluationVideoList(fileService.getFileList(behavior.getId(), BUSINESS_NAME, EVALATION));
        return convert.toDTO(behavior);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        param.getId().forEach(e -> {
            fileService.removeFile(e, BUSINESS_NAME, INSECURE);
            fileService.removeFile(e, BUSINESS_NAME, RECORD);
            fileService.removeFile(e, BUSINESS_NAME, REASON);
            fileService.removeFile(e, BUSINESS_NAME, CORRECT);
            fileService.removeFile(e, BUSINESS_NAME, EVALATION);
        });
        return super.removeByIds(param.getId());
    }

}
