package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 厂领导抽查复检
 *
 * <AUTHOR>
 * @since 1.0.0 2024-08-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MesSwEquipmentInspectionSpotCheck extends BaseEntity implements Serializable{
    /**
     * 设备巡检主键id
     */
    private String  inspectionId;

	/**
	* 设备编码
	*/
	private String eqptNo;

	/**
	* 设备型号
	*/
	private String eqptName;

	/**
	* 抽检周期-年月
	*/
	private String checkCycle;

    /**
     * 抽检周期-周期code
     */
    private String checkWeekCode;

	/**
	* 抽检状态code
	*/
	private String checkStatusCode;

	/**
	* 抽检状态
	*/
	private String checkStatus;

    /**
     * 抽检时间
     */
    private LocalDateTime checkTime;

    /**
     * 抽检情况code
     */
    private String checkSituationCode;

    /**
     * 抽检情况
     */
    private String checkSituation;

	/**
	* 存在问题
	*/
	private String existQuestion;

    /**
     * 复检人code
     */
    private String checkPerson;

    /** 整改车间code */
    private Long zgWorkshopCode;

    /** 整改车间名称 */
    private String zgWorkshopName;
}
