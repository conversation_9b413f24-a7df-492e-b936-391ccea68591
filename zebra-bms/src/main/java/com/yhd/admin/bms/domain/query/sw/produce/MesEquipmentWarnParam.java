package com.yhd.admin.bms.domain.query.sw.produce;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;


import java.time.LocalDateTime;
import java.util.Date;

/**
 * 生产效率-历史数据库-设备报警记录表查询
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MesEquipmentWarnParam extends QueryParam {
    /**
     * 报警开始时间
     */
    private LocalDateTime warnStartTime;

    /**
     * 报警结束时间
     */
    private LocalDateTime warnEndTime;

    /**
     * 报警类型code
     */
    private Long classificationCode;

    /**
     * 报警类型name
     */
    private String classificationName;

    /**
     * 设备id
     */
    private Long eqptId;

    /**
     * 设备编码及类别
     */
    private String eqptNoType;

    /**
     * 设备部件id
     */
    private Long eqptPartId;

    /**
     * 设备部件名称
     */
    private String eqptPartName;

    /**
     * 报警点位/内容
     */
    private String warnContent;

    /**
     * 点位地址
     */
    private String warnAddress;

    /**
     * 故障原因
     */
    private String warnReason;
}
