package com.yhd.admin.bms.service.sw.dashboard;

import com.yhd.admin.bms.domain.query.sw.HstTimeStatsParam;
import com.yhd.admin.bms.domain.vo.sw.dashboard.KHPointDataVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 亚控点位-业务层接口
 *
 * <AUTHOR>
 * @date 2024/9/28 8:58
 */
public interface KHPointService {

  /**
   * 点位数据-历史查询
   *
   * @param param 参数
   * @return 点位数据列表
   */
  List<KHPointDataVO> queryPointList(HstTimeStatsParam param);

  /**
   * 根据点位名称获取点位值数据信息
   *
   * @param pointName 点位名称
   * @return 点位值数据信息
   */
  KHPointDataVO getPointValue(String pointName);

  List<KHPointDataVO> getPointValue(List<String> pointNames);

  /**
   * 根据点位名称获取点位布尔值数据信息
   *
   * @param pointName 点位名称
   * @return 点位值数据信息
   */
  KHPointDataVO getPointBooleanValue(String pointName);

  /**
   * 根据点位，获取查询日期的消耗量。 例：1月7号是昨日，则计算逻辑是8号的第一个值减去7号的第一个值； 1月7号是当天时间，则计算逻辑是当前时间的最后一值减去当天的第一个值
   *
   * @param date——必须，不为空！
   * @param pointNames——必须，不为空！
   * @return key-点位，value-消耗量。查询不到数据，则key有值、value为null
   */
  Map<String, BigDecimal> getConsumptionByParams(String date, List<String> pointNames);

  /**
   * 根据点位，获取查询日期的煤产量。 例：当日17点59分59秒前的最后一个值为当日产量
   *
   * @param date——必须，不为空！
   * @param pointNames——必须，不为空！
   * @return key-点位，value-消耗量。查询不到数据，则key有值、value为null
   */
  Map<String, BigDecimal> getCoalProductionByParams(String date, List<String> pointNames);

  /**
   * 点位数据-历史查询，默认间隔是1分钟，
   *
   * @param param——startTime或endTime为空，默认当天 、pointList必须不为空
   * @return map key——点位，value——该点位数据集合
   */
  Map<String, List<KHPointDataVO>> queryPointListByStepped(HstTimeStatsParam param);
}
