package com.yhd.admin.bms.domain.dto.sw;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/** 不安全行为台账明细 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwUnsafeLedgerDTO extends BaseDTO implements Cloneable, Serializable {
  /** 责任单位 */
  private String responsibleUnit;
  /** 责任单位名称 */
  private String responsibleUnitName;
  /** 风险等级 */
  private String riskLevel;
  /** 风险等级名称 */
  private String riskLevelName;
  /** 扣分 */
  private Double deductPoints;
  /** 人员不安全行为详细描述 */
  private String description;
  /** 责任人 */
  private String chargePerson;
  /** 责任人名称 */
  private String chargeName;
  /** 检查人 */
  private String examiner;
  /** 检查人名称 */
  private String examinerName;
  /** 检查部门 */
  private String examinerDep;
  /** 检查部门名称 */
  private String examinerDepName;
  /** 车间连带处罚责任人 */
  private String workshopPunisher;
  /** 车间连带处罚责任人名称 */
  private String workshopPunisherName;
  /** 车间连带处罚金额30% */
  private Double workshopFine;
  /** 厂领导连带处罚责任人 */
  private String leaderPunisher;
  /** 厂领导连带处罚责任人名称 */
  private String leaderPunisherName;
  /** 厂领导连带处罚金额10% */
  private Double leaderFine;
  /** 班次 */
  private String classes;
  /** 班次名称 */
  private String classesName;
  /** 检查日期 */
  private LocalDate date;
  /** 检查方式 */
  private String inspection;
  /** 检查方式名称 */
  private String inspectionName;
  /** 是否完成矫正资料 */
  private String finishRectifyMaterial;
  /** 是否完成谈话视频 */
  private String finishVideoTalk;
  /** 视频谈话责任人 */
  private String talkCharge;
  /** 视频谈话责任人名称 */
  private String talkChargeName;
  /** 谈话时长（min） */
  private Double talkDuration;
  /** 罚款金额（元） */
  private Double fine;
  /** 是否缴纳罚款 */
  private String finishFine;
  /** 罚款单号 */
  private String fineOrder;
  /** 备注 */
  private String remark;
  /** 缴费凭证 */
  private List<JSONObject> picturePayList;
  /** 视频监屏照片 */
  private List<JSONObject> pictureMonitorList;
}
