package com.yhd.admin.bms.service.sw.decision;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.decision.MesDecisionContingencyDrillDTO;
import com.yhd.admin.bms.domain.entity.sw.decision.MesDecisionContingencyDrill;
import com.yhd.admin.bms.domain.query.sw.decision.MesDecisionContingencyDrillParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;


/**
 * 智能决策中心-应急演练规划
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-22
 */
public interface MesDecisionContingencyDrillService extends IService<MesDecisionContingencyDrill> {

    IPage<MesDecisionContingencyDrillDTO> pagingQuery(MesDecisionContingencyDrillParam queryParam);

     Boolean add(MesDecisionContingencyDrillParam param);

    Boolean modify(MesDecisionContingencyDrillParam param);

    Boolean removeBatch(BatchParam param);

    MesDecisionContingencyDrillDTO getCurrentDetail(MesDecisionContingencyDrillParam param);

    MesDecisionContingencyDrillDTO queryCount(MesDecisionContingencyDrillParam param);
}
