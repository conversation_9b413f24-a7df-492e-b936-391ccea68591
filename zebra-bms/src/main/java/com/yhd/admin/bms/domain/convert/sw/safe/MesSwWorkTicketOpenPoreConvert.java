package com.yhd.admin.bms.domain.convert.sw.safe;

import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketOpenPoreDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketOpenPoreSignDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketOpenPore;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketOpenPoreSign;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketOpenPoreParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketOpenPoreSignParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwWorkTicketOpenPoreVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwWorkTicketOpenPoreConvert {

  MesSwWorkTicketOpenPore toEntity(MesSwWorkTicketOpenPoreParam param);

  MesSwWorkTicketOpenPoreSign toSignEntity(MesSwWorkTicketOpenPoreSignParam param);

  MesSwWorkTicketOpenPoreDTO toDTO(MesSwWorkTicketOpenPore classes);

  MesSwWorkTicketOpenPoreVO toVO(MesSwWorkTicketOpenPoreDTO classesDTO);

  List<MesSwWorkTicketOpenPoreSignDTO> toSignDTOS(List<MesSwWorkTicketOpenPoreSign> classesDTO);

  // List<MesSwWorkTicketOpenPoreTemplateVO> toTemplateVOS(
  //     List<MesSwWorkTicketOpenPoreTemplateDTO> classesDTO);
  //
  // MesSwWorkTicketOpenPoreSignatureDTO toSignatureDTO(
  //     MesSwWorkTicketOpenPoreSignature classes);
}
