package com.yhd.admin.bms.domain.vo.sw;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 班组人员考核情况
 *
 * <AUTHOR> @qq.com
 * @since 1.0.0 2023-10-10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkRecordAssessmentVO extends BaseVO implements Cloneable, Serializable {


    /**
     * 班组id
     */
    private Long workId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 安全方面
     */
    private String security;

    /**
     * 工作表现
     */
    private String jobPerformance;

    /**
     * 业务技能
     */
    private String businesSkills;

    /**
     * 学习创新
     */
    private String learningInnovation;

    /**
     * 团队意识
     */
    private String teamSpirit;

    /**
     * 文明生产
     */
    private String production;

    /**
     * 合计
     */
    private String sumUp;

    private LocalDateTime time;

    private String nameCode;


}
