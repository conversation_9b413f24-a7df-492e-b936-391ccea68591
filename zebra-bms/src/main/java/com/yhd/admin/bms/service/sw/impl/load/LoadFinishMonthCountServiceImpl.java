package com.yhd.admin.bms.service.sw.impl.load;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.load.LoadFinishMonthCountDao;
import com.yhd.admin.bms.domain.entity.sw.load.LoadFinishMonthCount;
import com.yhd.admin.bms.service.sw.load.LoadFinishMonthCountService;
import org.springframework.stereotype.Service;

/**
 * 月装车完成情况统计-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/1/4 16:06
 */
@Service
public class LoadFinishMonthCountServiceImpl
    extends ServiceImpl<LoadFinishMonthCountDao, LoadFinishMonthCount>
    implements LoadFinishMonthCountService {}
