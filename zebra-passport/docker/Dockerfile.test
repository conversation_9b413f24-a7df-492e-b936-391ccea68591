FROM  tomcat:9.0.78-jdk11

ENV  LANG C.UTF-8

ENV spring.profiles.active=test

ENV JAVA_OPTS="-server -Xms2g -Xmx2g -Dfile.encoding=UTF-8 -Djava.awt.headless=true"

ENV TZ=Asia/Shanghai

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY docker/server.xml /usr/local/tomcat/conf

WORKDIR /usr/local/tomcat/webapps/

RUN rm -rf *

ADD build/libs/*.war ./passport.war

CMD ["/usr/local/tomcat/bin/catalina.sh", "run"]
