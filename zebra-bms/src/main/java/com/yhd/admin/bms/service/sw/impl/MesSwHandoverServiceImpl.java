package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.MesSwHandoverDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwHandoverConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwHandoverDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwHandover;
import com.yhd.admin.bms.domain.query.sw.MesSwHandoverParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.service.sw.MesSwHandoverService;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class MesSwHandoverServiceImpl extends ServiceImpl<MesSwHandoverDao, MesSwHandover>
    implements MesSwHandoverService {

    private final MesSwHandoverConvert convert;

    public MesSwHandoverServiceImpl(MesSwHandoverConvert convert) {
        this.convert = convert;
    }

    @Override
    public Boolean add(MesSwHandoverParam addParam) {
        return this.save(convert.toEntity(addParam));
    }

    @Override
    public Boolean modify(MesSwHandoverParam modifyParam) {
        return this.updateById(convert.toEntity(modifyParam));
    }

    @Override
    public Boolean removeBatch(BatchParam removeParam) {
        return this.removeByIds(removeParam.getId());
    }

    @Override
    public IPage<MesSwHandoverDTO> pagingQuery(MesSwHandoverParam queryParam) {
        IPage<MesSwHandover> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwHandover> queryChainWrapper =
            new LambdaQueryChainWrapper<>(baseMapper);
        if (queryParam.getTime() != null && queryParam.getTime().length >= 2) {
            queryChainWrapper.ge(MesSwHandover::getHandoverTime, queryParam.getTime()[0]);
            queryChainWrapper.le(MesSwHandover::getHandoverTime, queryParam.getTime()[1]);
        }

        queryChainWrapper.like(StringUtils.isNoneBlank(queryParam.getSuccessorName()),
            MesSwHandover::getSuccessorName,
            queryParam.getSuccessorName());
        queryChainWrapper.like(StringUtils.isNoneBlank(queryParam.getHandoverName()),
            MesSwHandover::getHandoverName,
            queryParam.getHandoverName());
        queryChainWrapper.orderByDesc(MesSwHandover::getCreatedTime);

        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public MesSwHandoverDTO currentDetail(MesSwHandoverParam queryParam) {
        return convert.toDTO(this.getById(queryParam.getId()));
    }


}
