package com.yhd.admin.bms.domain.query.sw.decision;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 智能决策中心-应急演练规划
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-22
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesDecisionContingencyDrillParam extends QueryParam implements Cloneable, Serializable {

	/**
	* 日期年月
	*/
	private String drillMonth;

	/**
	* 演练实施单位
	*/
	private String drillUnit;

	/**
	* 演练项目类型
	*/
	private String drillType;

	/**
	* 演练形式code
	*/
	private String drillForm;

	/**
	* 响应级别code
	*/
	private String drillLevel;

	/**
	* 演练负责部门
	*/
	private String drillDept;

	/**
	* 组织部门
	*/
	private String organizeDept;

    /**
     * 附件
     */
    private List<JSONObject> annex;

    /**
     * 开始年月
     */
    private String startMonth;
    /**
     * 日期年月
     */
    private String endMonth;

}
