package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.MesSwWorkTicketConstSignUserDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwWorkTicketConstSignUserConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwWorkTicketConstSignUserDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConst;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConstSignUser;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketConstGcEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketConstSpEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwWorkTicketConstSignUserParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesSwWorkTicketConstService;
import com.yhd.admin.bms.service.sw.MesSwWorkTicketConstSignUserService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/5/29 9:37
 * @Version 1.0
 *
 * 施工工作票签字记录--服务层
 */

@Service
public class MesSwWorkTicketConstSignUserServiceImpl
    extends ServiceImpl<MesSwWorkTicketConstSignUserDao, MesSwWorkTicketConstSignUser>
    implements MesSwWorkTicketConstSignUserService {

    @Resource
    private MesSwWorkTicketConstSignUserConvert signUserConvert;
    @Resource
    private MesSwWorkTicketConstService constService;
    @Override
    public Boolean approval(MesSwWorkTicketConstSignUserParam param) {
        if (Objects.isNull(param.getTicketId())
            || StringUtils.isBlank(param.getUserType())
            || StringUtils.isBlank(param.getSignUrl())) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "请检查参数是否符合规范");
        }
        // 判断当前用户是否可作废
        MesSwWorkTicketConst aConst = constService.getById(param.getTicketId());
        if (Objects.nonNull(aConst.getStatusCode())
            && WorkTicketStatusEnum.ZF.getCode().equals(aConst.getStatusCode())) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "此工作票已作废，不可进行签字");
        }
        MesSwWorkTicketConstSignUser entity = signUserConvert.toEntity(param);
        if (StringUtils.isNotBlank(param.getSignUrl())){
            entity.setIsSign(true);
        }
        Boolean result = false;
        if (param.getId() == null) {
            //没有id 为新增其他人员
            if (!"其他人员".equals(param.getUserType())){
                throw new BMSException(ResultStateEnum.FAIL.getCode(), "用户类型有误，请检查");
            }
            UserDTO userInfo = UserContextHolder.getUserInfo();
            if (!aConst.getTjUserName().equals(userInfo.getName())){
                throw new BMSException(ResultStateEnum.FAIL.getCode(),"该用户无添加其他人员签字权限");
            }
            result = this.save(entity);
        } else {
            // 查询工作票详情
            if (Objects.isNull(this.getById(param.getId()))) {
                throw new BMSException(ResultStateEnum.FAIL.getCode(), "未查询到此工作票信息，请检查");
            }
            result = this.updateById(entity);
        }
        // 审批用户类型列表
        List<String> spTypeList = WorkTicketConstSpEnum.getValuesList();
        // 贯彻签字用户类型列表
        List<String> gcTypeList = WorkTicketConstGcEnum.getValuesList();
        // 查询审批用户以及贯彻签字人员签字记录
        LambdaQueryWrapper<MesSwWorkTicketConstSignUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwWorkTicketConstSignUser::getTicketId,param.getTicketId());
        List<MesSwWorkTicketConstSignUser> signUsers = this.list(wrapper);
        // 审批用户全部签字
        List<MesSwWorkTicketConstSignUser> spList =
        signUsers.stream()
            .filter(v -> spTypeList.contains(v.getUserType()))
            .collect(Collectors.toList());
        Long spCount = spList.stream().filter(v -> v.getIsSign() == false).count();
        // 贯彻人员全部签字
        List<MesSwWorkTicketConstSignUser> gcList =
            signUsers.stream()
                .filter(v -> gcTypeList.contains(v.getUserType()))
                .collect(Collectors.toList());
        Long gcCount = gcList.stream().filter(v -> v.getIsSign() == false).count();
        if (spCount == 0 && gcCount == 0){
            // 全部签字 修改状态为审核完成
            MesSwWorkTicketConst ticketConst = new MesSwWorkTicketConst();
            ticketConst.setId(param.getTicketId());
            ticketConst.setStatusCode(WorkTicketStatusEnum.WC.getCode());
            ticketConst.setStatusName(WorkTicketStatusEnum.WC.getDesc());
            constService.updateById(ticketConst);
        }
        return result;
    }


    @Override
    public List<MesSwWorkTicketConstSignUserDTO> queryList(
        MesSwWorkTicketConstSignUserParam param) {
        LambdaQueryWrapper<MesSwWorkTicketConstSignUser> wrapper = new LambdaQueryWrapper<>();
        // 承包商施工工作票主键id
        wrapper.eq(
            Objects.nonNull(param.getTicketId()),
            MesSwWorkTicketConstSignUser::getTicketId,
            param.getTicketId());
        // 是否完成签字
        wrapper.eq(
            Objects.nonNull(param.getIsSign()),
            MesSwWorkTicketConstSignUser::getIsSign,
            param.getIsSign());

        List<MesSwWorkTicketConstSignUser> list = baseMapper.selectList(wrapper);

        return signUserConvert.toDTOs(list);
    }
}
