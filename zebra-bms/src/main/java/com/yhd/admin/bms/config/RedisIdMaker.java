package com.yhd.admin.bms.config;

import com.yhd.admin.common.utils.DateUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/** 全局唯一编号，根据日期区分 */
@Component
public class RedisIdMaker {

  @Resource private RedisTemplate<String, Object> redisTemplate;
  // 初始值
  private static final Integer ORDER_INIT = 0;
  // 时间格式化
  private static final String DATE_FORMAT = "yyyyMMdd";

  private static final String YEAR_FORMAT = "yyyy";

  /**
   * 根据 业务类型key 最多生成几位 生成唯一编号
   *
   * @param businessKey
   * @param maxLength
   * @return
   */
  public String getBusinessIdNo(String businessKey, Integer maxLength) {
    String id = this.generateId(businessKey, getExpireAtData(), maxLength);
    return format(id);
  }

  /**
   * 根据 业务类型key 最多生成几位 生成唯一编号
   * 获取许可证编号
   *
   * @param businessKey
   * @param maxLength
   * @return
   */
  public String getTicketNo(String businessKey, Integer maxLength) {
      String id = this.generateId(businessKey, getExpireAtYear(), maxLength);
      return formatYear(id);
  }

  /**
   * 通过key获取自增并设定过期时间
   *
   * @param key
   * @param date 过期时间
   * @return
   */
  private String generateId(String key, Date date, Integer maxLength) {
    ValueOperations opsForValue = redisTemplate.opsForValue();
    Object s = opsForValue.get(key);
    if (ObjectUtils.isEmpty(s)) {
      opsForValue.set(key, ORDER_INIT);
      redisTemplate.expireAt(key, date);
    }
    long busId = opsForValue.increment(key);
    int len = String.valueOf(busId).length();
    StringBuffer sb = new StringBuffer();
    if (len < maxLength) {
      for (int i = 0; i < (maxLength - len); i++) {
        sb.append("0");
      }
      sb.append(busId);
    }
    return sb.toString();
  }

  /**
   * 过期时间每天凌晨
   *
   * @return
   */
  private Date getExpireAtData() {
    ZoneId zoneId = ZoneId.systemDefault();
    LocalDate localDate = LocalDate.now().plusDays(1);
    ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
    return Date.from(zdt.toInstant());
  }

  /**
   * 过期时间每年最后一天凌晨
   *
   * @return
   */
  private Date getExpireAtYear() {
      ZoneId zoneId = ZoneId.systemDefault();
      LocalDate localDate = LocalDate.of(LocalDate.now().getYear(), 12, 31);
      ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
      return Date.from(zdt.toInstant());
  }

  /**
   * 格式化时间
   *
   * @return
   */
  private String getDateFormat() {
    DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT);
    LocalDate formatDate = DateUtil.now();
    return formatDate.format(df);
  }

  /**
   * 自定义ID格式
   *
   * @param id
   * @return
   */
  private String format(String id) {
    StringBuffer sb = new StringBuffer();
    sb.append(getDateFormat());
    String strId = sb.toString() + id;
    return strId;
  }

  /**
   * 格式化时间 yy
   *
   * @return
   */
  private String getYearFormat() {
      DateTimeFormatter df = DateTimeFormatter.ofPattern(YEAR_FORMAT);
      LocalDate formatDate = LocalDate.now();
      return formatDate.format(df);
  }

  /**
   * 自定义ID格式
   *
   * @param id
   * @return
   */
  private String formatYear(String id) {
      StringBuffer sb = new StringBuffer();
      sb.append(getYearFormat());
      String strId = sb + id;
      return strId;
  }
}
