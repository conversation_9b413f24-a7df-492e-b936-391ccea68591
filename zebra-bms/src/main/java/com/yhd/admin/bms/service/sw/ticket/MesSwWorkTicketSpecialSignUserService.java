package com.yhd.admin.bms.service.sw.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketSpecialSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketSpecialSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketSpecialSignUserParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;


/**
 * 特殊作业工作票签字用户信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-29
 */
public interface MesSwWorkTicketSpecialSignUserService extends IService<MesSwWorkTicketSpecialSignUser> {

    IPage<MesSwWorkTicketSpecialSignUserDTO> pagingQuery(MesSwWorkTicketSpecialSignUserParam queryParam);

    Boolean add(MesSwWorkTicketSpecialSignUserParam param);

    Boolean modify(MesSwWorkTicketSpecialSignUserParam param);

    Boolean removeBatch(BatchParam param);

    MesSwWorkTicketSpecialSignUserDTO getCurrentDetail(MesSwWorkTicketSpecialSignUserParam param);
}
