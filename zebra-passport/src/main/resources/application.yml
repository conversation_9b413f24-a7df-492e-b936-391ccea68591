server:
    tomcat:
        uri-encoding: UTF-8
    servlet:
        context-path: /passport

spring:
    profiles:
        active: dev
    jackson:
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
        serialization:
            WRITE_DATES_AS_TIMESTAMPS: false
    servlet:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
            enabled: true
    main:
        allow-bean-definition-overriding: false
#mybatis
mybatis-plus:
    configuration:
        map-underscore-to-camel-case: true
        cache-enabled: false
        call-setters-on-nulls: true
        jdbc-type-for-null: 'null'
    global-config:
        banner: false
        db-config:
            table-prefix: 'tb_'
    mapper-locations: classpath*:mapper/**/*Mapper.xml
