package com.yhd.admin.bms.domain.entity.sw.consumption;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 能耗管理-药耗
 *
 * <AUTHOR>
 * @date 2025/07/02 10:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesConsumptionDrug extends BaseEntity implements Serializable {
  /** 日期 */
  private LocalDate date;

  /** 班次 */
  private String classes;

  private String classesName;

  /** 阴离子袋数 */
  private BigDecimal anionNum;

  /** 阴离子每袋重量 */
  private BigDecimal anionWeight;

  /** 阴离子总重量 */
  private BigDecimal anionTotal;

  /** 阳离子袋数 */
  private BigDecimal cationNum;

  /** 阳离子每袋重量 */
  private BigDecimal cationWeight;

  /** 阳离子总重量 */
  private BigDecimal cationTotal;
}
