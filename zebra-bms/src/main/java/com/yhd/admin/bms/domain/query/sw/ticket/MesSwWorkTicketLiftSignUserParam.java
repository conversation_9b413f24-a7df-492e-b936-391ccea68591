package com.yhd.admin.bms.domain.query.sw.ticket;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import java.io.Serializable;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 吊装作业许可证-签字表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-28
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesSwWorkTicketLiftSignUserParam extends QueryParam implements Cloneable, Serializable {

	/**
	* 吊装作业许可证主键id
	*/
	private Long ticketId;

	/**
	* 签字用户类型
	*/
	private String userType;

	/**
	* 用户账号
	*/
	private String userAccount;

	/**
	* 用户姓名
	*/
	private String userName;

	/**
	* 签字图片url
	*/
	private String signUrl;

    /**
     * 审批意见
     */
    private String spOpinion;

	/**
	* 是否完成签字
	*/
	private Boolean isSign;

}
