package com.yhd.admin.bms.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能加药-亚控点位key枚举类
 *
 * <AUTHOR>
 * @date 2024/10/25 9:36
 */
@Getter
public enum KHDosingPointKeyEnum {
  Slurry_Tank601_Turbidity1("XMSW_Slurry_Tank601_Turbidity1", "新主洗循环水池浊度"),
  Slurry_Tank601_Turbidity2("XMSW_Slurry_Tank601_Turbidity2", "旧主洗循环水池浊度"),
  Slurry_Tank601_ConcentrationIn2("XMSW_Slurry_Tank601_ConcentrationIn2", "一号/二号浓缩池入料浓度"),
  Slurry_Tank602_ConcentrationOut("XMSW_Slurry_Tank602_ConcentrationOut", "一号/二号浓缩池底流浓度"),
  Slurry_Tank601_InstrumentNum("XMSW_Slurry_Tank601_InstrumentNum", "一号/二号浓缩池清水层厚度"),
  Slurry_Tank601_ConcentrationIn1("XMSW_Slurry_Tank601_ConcentrationIn1", "三号浓缩池入料浓度"),
  Slurry_Tank412_ConcentrationOut("XMSW_Slurry_Tank412_ConcentrationOut", "三号浓缩池底流浓度"),
  Slurry_Tank412_InstrumentNum("XMSW_Slurry_Tank412_InstrumentNum", "三号浓缩池清水层厚度"),
  ;

  private final String key;
  private final String desc;

  KHDosingPointKeyEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }

  public static List<String> getKeyList() {
    KHDosingPointKeyEnum[] values = KHDosingPointKeyEnum.values();

    return Arrays.stream(values).map(KHDosingPointKeyEnum::getKey).collect(Collectors.toList());
  }

  public static KHDosingPointKeyEnum getEnumByKey(String key) {
    if (StringUtils.isBlank(key)) {
      return null;
    }
    KHDosingPointKeyEnum[] values = KHDosingPointKeyEnum.values();
    for (KHDosingPointKeyEnum value : values) {
      if (value.getKey().equals(key)) {
        return value;
      }
    }
    return null;
  }
}
