package com.yhd.admin.bms.domain.dto.sw.produce;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 生产计划
 *
 * <AUTHOR>
 * @date 2025/1/4 10:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwProducePlanDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = -1429747868223567832L;

  /** 计划类型：月计划、年计划 */
  private String planType;
  /** 计划时间 */
  private String planTimeStr;
  /** 一体化外运 */
  private Long ythwy;
  /** 直供用户 */
  private Long zgyh;
  /** 总计划 */
  private Long total;
}
