package com.yhd.admin.bms.domain.entity.sw;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 班后会记录
 *
 * <AUTHOR> <EMAIL>
 * @since 1.0.0 2023-10-10
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class MesSwWorkRecord extends BaseEntity implements Cloneable, Serializable {

    /**
     * 时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime time;

    /**
     * 车间
     */
    private String workshop;

    /**
     * 班组
     */
    private String teamGroup;

    /**
     * 主持人
     */
    private String emcee;

    /**
     * 主持人账号
     */
    private String emceeeCode;

    /**
     * 参会人员
     */
    private String participants;

    /**
     * 工作完成情况（好的方面）
     */
    private String jobDoneGood;

    /**
     * 工作完成情况（坏的方面）
     */
    private String jobDoneBad;

    /**
     * 违章行为剖析
     */
    private String breaking;

    /**
     * 整改意见及措施
     */
    private String suggestions;

    /**
     * 表扬及批评
     */
    private String praiseCriticism;

    /**
     * 上传附件
     */
    private String fileUrl;

    /**
     * 文件
     */
    private String file;

    /**
     * 参会人员
     */
    @TableField(exist = false)
    private List<String> userLists;


    /**
     * 已上传的文件
     */
    @TableField(exist = false)
    private List<JSONObject> fileList;

}
