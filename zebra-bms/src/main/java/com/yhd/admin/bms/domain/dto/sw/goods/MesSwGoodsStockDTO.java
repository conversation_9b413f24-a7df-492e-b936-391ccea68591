package com.yhd.admin.bms.domain.dto.sw.goods;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物资管理
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwGoodsStockDTO extends BaseDTO implements Serializable {
    /**
     * 物资类别code
     */
    private Long categoryCode;
    /**
     * 物资类别名称
     */
    private String categoryName;
    /**
     * 物资名称
     */
    private String name;
    /**
     * 负责人
     */
    private String chargeName;
    /**
     * 负责人账号
     */
    private String chargeAccount;
    /**
     * 物资数量
     */
    private BigDecimal goodNum;
    /**
     * 物资告警数量
     */
    private BigDecimal warnNum;
    /**
     * 物资单位code
     */
    private String unitCode;
    /**
     * 物资单位名称
     */
    private String unitName;
    /**
     * 颜色
     */
    private String color;
    /**
     * 筛板孔径code
     */
    private String apertureCode;
    /**
     * 筛板孔径name
     */
    private String apertureName;
    /**
     * 筛板尺寸code
     */
    private String sizeCode;
    /**
     * 筛板尺寸name
     */
    private String sizeName;
}
