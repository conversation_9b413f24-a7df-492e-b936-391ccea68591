package com.yhd.admin.bms.domain.vo.sw.produce;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 月产量完成情况统计：单位万吨
 *
 * <AUTHOR>
 * @date 2025/1/4 10:19
 */
@Data
public class MesSwProduceFinishMonthCountVO implements Serializable {
  private static final long serialVersionUID = -1429747868223567832L;

  /** 月份(yyyy-MM) */
  private String month;
  /** 月计划：当月总计划量 */
  private BigDecimal planCount;
  /** 月完成：装车系统+电厂皮带，四舍五入保留两位小数 */
  private BigDecimal finishCount;
  /** 均衡超欠：月完成-月计划 */
  private BigDecimal jhcq;
  /** 月完成占比：完成数/计划数*100，四舍五入保留两位小数 */
  private BigDecimal finishRatio;
  /** 剩余日均：均衡超欠 / 当月剩余天数 四舍五入保留两位小数 */
  private BigDecimal syrj;
}
