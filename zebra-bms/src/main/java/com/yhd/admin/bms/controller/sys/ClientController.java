package com.yhd.admin.bms.controller.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.bms.domain.convert.sys.ClientConvert;
import com.yhd.admin.bms.domain.dto.sys.ClientDTO;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.ClientQueryParam;
import com.yhd.admin.bms.domain.vo.sys.ClientVO;
import com.yhd.admin.bms.service.sys.ClientService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户端/资源模块管理
 *
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/4 09:12
 */
@RequestMapping("/client")
@RestController
public class ClientController {

  private final ClientConvert convert;

  private final ClientService clientService;

  private final ObjectMapper objectMapper;

  public ClientController(
      ClientConvert convert, ClientService clientService, ObjectMapper objectMapper) {
    this.convert = convert;
    this.clientService = clientService;
    this.objectMapper = objectMapper;
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<ClientVO> pagingQuery(@RequestBody ClientQueryParam queryParam) {
    IPage<ClientDTO> iPage = clientService.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @SysLogs(title = "增加客户端模块", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson add(
      @RequestBody ClientQueryParam addParam, OAuth2Authentication auth2Authentication) {
    clientService.addClient(addParam);
    return RespJson.buildSuccessResponse();
  }

  @SysLogs(title = "编辑客户端模块", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/modify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson modify(@RequestBody ClientQueryParam modifyParam) {
    clientService.modifyClient(modifyParam);
    return RespJson.buildSuccessResponse();
  }

  @SysLogs(title = "删除客户端模块", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson removeBatch(@RequestBody BatchParam batchParam) {
    clientService.removeBatch(batchParam);
    return RespJson.buildSuccessResponse();
  }

  @PostMapping(
      value = "/currentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<ClientVO> currentDetail(@RequestBody ClientQueryParam queryParam) {
    return RespJson.buildSuccessResponse(convert.toVO(clientService.currentDetail(queryParam)));
  }

  @PostMapping(
      value = "/queryList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<ClientVO>> queryList(@RequestBody ClientQueryParam queryParam) {
    List<ClientVO> retVal =
        clientService.queryList(queryParam, false).stream()
            .map(convert::toVO)
            .collect(Collectors.toList());
    return RespJson.buildSuccessResponse(retVal);
  }

  @PostMapping(
      value = "/checkIfNotExist",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> checkIfNotExist(@RequestBody ClientQueryParam queryParam) {
    Boolean ifExit = clientService.checkIfNotExist(queryParam);
    return RespJson.buildSuccessResponse(ifExit);
  }
}
