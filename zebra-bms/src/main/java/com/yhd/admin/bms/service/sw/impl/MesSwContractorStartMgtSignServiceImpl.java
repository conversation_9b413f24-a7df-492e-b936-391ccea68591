package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.MesSwContractorStartMgtSignDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwContractorStartMgtSignConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwContractorStartMgtSignDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwContractorStartMgtSign;
import com.yhd.admin.bms.domain.query.sw.MesSwContractorStartMgtSignParam;
import com.yhd.admin.bms.service.sw.IMesSwContractorStartMgtSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
@Slf4j
public class MesSwContractorStartMgtSignServiceImpl extends ServiceImpl<MesSwContractorStartMgtSignDao, MesSwContractorStartMgtSign> implements IMesSwContractorStartMgtSignService {

    @Resource
    private MesSwContractorStartMgtSignConvert convert;

    @Override
    public IPage<MesSwContractorStartMgtSignDTO> pagingQuery(MesSwContractorStartMgtSignParam param) {
        IPage<MesSwContractorStartMgtSign> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSwContractorStartMgtSign> queryChainWrapper =
            new LambdaQueryChainWrapper<>(baseMapper);
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public MesSwContractorStartMgtSignDTO getCurrentDetail(MesSwContractorStartMgtSignParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    public Boolean add(MesSwContractorStartMgtSignParam param) {
        return super.save(convert.toEntity(param));
    }
}
