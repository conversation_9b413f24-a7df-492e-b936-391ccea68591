package com.yhd.admin.bms.config;

import io.minio.MinioClient;
import io.minio.errors.InvalidEndpointException;
import io.minio.errors.InvalidPortException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BeanCfg.java
 * @Description TODO
 * @createTime 2020年04月23日 16:10:00
 */
@Configuration
public class BeanCfg {

    @Bean
    public MinioClient minioClient(OssServerCfg serverCfg)
        throws InvalidPortException, InvalidEndpointException {
        return new MinioClient(serverCfg.getEndpoint(), serverCfg.getPort(), serverCfg.getAccessKey(),
            serverCfg.getSecretKey());
    }
}
