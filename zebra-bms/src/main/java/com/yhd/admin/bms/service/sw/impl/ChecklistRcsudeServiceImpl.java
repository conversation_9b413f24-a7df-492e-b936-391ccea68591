package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.ChecklistRcsudeDao;
import com.yhd.admin.bms.domain.convert.sw.ChecklistRcsudeConvert;
import com.yhd.admin.bms.domain.dto.sw.MesChecklistRcsudeDTO;
import com.yhd.admin.bms.domain.dto.sw.XYDTO;
import com.yhd.admin.bms.domain.entity.sw.floatSink.MesChecklistRcsude;
import com.yhd.admin.bms.domain.entity.sw.floatSink.MesFloatSinkSc;
import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.domain.query.sw.MesChecklistRcsudeParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.RawCoalCurveVO;
import com.yhd.admin.bms.service.sw.ChecklistRcsudeService;
import com.yhd.admin.bms.service.sw.floatSink.FloatSinkScSRV;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ChecklistRcsudeServiceImpl extends ServiceImpl<ChecklistRcsudeDao, MesChecklistRcsude>
    implements ChecklistRcsudeService {

  private final ChecklistRcsudeConvert rcsudeConvert;

  private final FloatSinkScSRV scSRV;

  private final RedisTemplate<String, Object> redisTemplate;

  public ChecklistRcsudeServiceImpl(
      ChecklistRcsudeConvert rcsudeConvert,
      RedisTemplate<String, Object> redisTemplate,
      FloatSinkScSRV scSRV) {
    this.rcsudeConvert = rcsudeConvert;
    this.redisTemplate = redisTemplate;
    this.scSRV = scSRV;
  }

  @Override
  public IPage<MesChecklistRcsudeDTO> pagingQuery(MesChecklistRcsudeParam param) {
    IPage<MesChecklistRcsude> iPage = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MesChecklistRcsude> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    queryChain
        .like(
            StringUtils.isNotBlank(param.getCoalSampleName()),
            MesChecklistRcsude::getCoalSampleName,
            param.getCoalSampleName())
        .like(
            StringUtils.isNotBlank(param.getSamplingLocation()),
            MesChecklistRcsude::getSamplingLocation,
            param.getSamplingLocation())
        .like(
            StringUtils.isNotBlank(param.getCharge()),
            MesChecklistRcsude::getCharge,
            param.getCharge())
        .like(
            StringUtils.isNotBlank(param.getReviewer()),
            MesChecklistRcsude::getReviewer,
            param.getReviewer())
        .like(
            StringUtils.isNotBlank(param.getLister()),
            MesChecklistRcsude::getLister,
            param.getLister())
        .like(
            StringUtils.isNotBlank(param.getExperimenter()),
            MesChecklistRcsude::getExperimenter,
            param.getExperimenter());
    if (param.getSamplingStartDate() != null && param.getSamplingEndDate() != null) {
      queryChain.between(
          MesChecklistRcsude::getSamplingTime,
          param.getSamplingStartDate(),
          param.getSamplingEndDate());
    }
    if (param.getExptStartDate() != null && param.getExptEndDate() != null) {
      queryChain.between(
          MesChecklistRcsude::getExptDate, param.getExptStartDate(), param.getExptEndDate());
    }
    queryChain
        .orderByDesc(MesChecklistRcsude::getSamplingTime)
        .orderByDesc(MesChecklistRcsude::getExptDate);
    return queryChain.page(iPage).convert(rcsudeConvert::toDTO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addRcsude(MesChecklistRcsudeParam param) {

    String fileKey = String.format(BMSRedisKeyEnum.EXP.getKey(), param.getExpId(), "4", "F");
    String url = Objects.requireNonNull(redisTemplate.opsForValue().get(fileKey)).toString();
    param.setExptData("{\"url\":\"" + url + "\"}");
    MesChecklistRcsude checklistRcsude = rcsudeConvert.toEntity(param);
    Boolean success = super.save(checklistRcsude);
    if (StringUtils.isNotBlank(param.getExpId())) {
      this.insertExpData(
          param.getExpId(), checklistRcsude.getId().toString(), checklistRcsude.getExptDate());
    }
    return success;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean modifyRcsude(MesChecklistRcsudeParam param) {
    if (StringUtils.isNotBlank(param.getExpId())) {
      String fileKey = String.format(BMSRedisKeyEnum.EXP.getKey(), param.getExpId(), "4", "F");
      String url = redisTemplate.opsForValue().get(fileKey).toString();
      param.setExptData("{\"url\":\"" + url + "\"}");
      this.deleteExpData(param.getId().toString());
      this.insertExpData(param.getExpId(), param.getId().toString(), param.getExptDate());
    }
    return super.updateById(rcsudeConvert.toEntity(param));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean removeBatch(BatchParam batchParam) {
    if (!CollectionUtils.isEmpty(batchParam.getId())) {
      batchParam.getId().forEach(id -> this.deleteExpData(id.toString()));
    }
    return super.removeByIds(batchParam.getId());
  }

  @Override
  public MesChecklistRcsudeDTO getCurrentDetail(MesChecklistRcsudeParam param) {
    return rcsudeConvert.toDTO(super.getById(param.getId()));
  }

  @Override
  public RawCoalCurveVO getFloatSinkScSRVListByExpId(MesChecklistRcsudeParam param) {
    String expId = param.getExpId();
    RawCoalCurveVO result = new RawCoalCurveVO();
    List<XYDTO> huifen = new ArrayList<>();
    List<XYDTO> fuwu = new ArrayList<>();
    List<XYDTO> chenwu = new ArrayList<>();
    List<XYDTO> midu = new ArrayList<>();
    List<XYDTO> fenxuan = new ArrayList<>();

    XYDTO itemXY = null;
    Double zero = 0d;

    List<MesFloatSinkSc> list = scSRV.getFloatSinkScSRVListByExpId(expId);
    if (!CollectionUtils.isEmpty(list)) {
      for (int i = 0; i < list.size(); i++) {
        MesFloatSinkSc item = list.get(i);
        // fuwu
        itemXY = new XYDTO();
        itemXY.setX(StringUtils.isNotBlank(item.getX1()) ? stringToDouble(item.getX1()) : zero);
        itemXY.setY(StringUtils.isNotBlank(item.getY1()) ? stringToDouble(item.getY1()) : zero);
        fuwu.add(itemXY);

        // chenwu
        itemXY = new XYDTO();
        itemXY.setX(StringUtils.isNotBlank(item.getX2()) ? stringToDouble(item.getX2()) : zero);
        itemXY.setY(StringUtils.isNotBlank(item.getY2()) ? stringToDouble(item.getY2()) : zero);
        chenwu.add(itemXY);

        // huifen
        itemXY = new XYDTO();
        itemXY.setX(StringUtils.isNotBlank(item.getX3()) ? stringToDouble(item.getX3()) : zero);
        itemXY.setY(StringUtils.isNotBlank(item.getY3()) ? stringToDouble(item.getY3()) : zero);
        huifen.add(itemXY);

        // midu
        itemXY = new XYDTO();
        itemXY.setX(StringUtils.isNotBlank(item.getX4()) ? stringToDouble(item.getX4()) : zero);
        itemXY.setY(StringUtils.isNotBlank(item.getY4()) ? stringToDouble(item.getY4()) : zero);
        midu.add(itemXY);

        // fenxuan
        itemXY = new XYDTO();
        itemXY.setX(StringUtils.isNotBlank(item.getX5()) ? stringToDouble(item.getX5()) : zero);
        itemXY.setY(StringUtils.isNotBlank(item.getY5()) ? stringToDouble(item.getY5()) : zero);
        fenxuan.add(itemXY);
      }
    }
    result.setHuifen(huifen);
    result.setFuwu(fuwu);
    result.setChenwu(chenwu);
    result.setMidu(midu);
    result.setFenxuan(fenxuan);
    return result;
  }

  private Double stringToDouble(String val) {
    return new BigDecimal(val).setScale(2, RoundingMode.HALF_UP).doubleValue();
  }

  protected void insertExpData(String redisId, String expId, LocalDate expTime) {
    String scKey = String.format(BMSRedisKeyEnum.EXP.getKey(), redisId, "3", "SC");
    String fileKey = String.format(BMSRedisKeyEnum.EXP.getKey(), redisId, "4", "F");
    // 可选性曲线坐标数据计算表
    List<MesFloatSinkSc> scList = (List<MesFloatSinkSc>) redisTemplate.opsForValue().get(scKey);

    if (!CollectionUtils.isEmpty(scList)) {
      scList.forEach(
          o -> {
            o.setExptId(expId);
            o.setExptDate(expTime);
          });
      scSRV.saveBatch(scList);
    }

    redisTemplate.delete(List.of(scKey, fileKey));
  }

  protected void deleteExpData(String expId) {
    this.scSRV.removeByExpId(expId);
  }
}
