package com.yhd.admin.passport.domain.convert;

import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.passport.domain.entity.SysUserAccount;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserAccountTransform.java
 * @Description UserAccount 转换
 * @createTime 2020年03月19日 21:05:00
 */

@Mapper(componentModel = "spring")
public interface UserAccountConvert {

  /**
   * sysUserAccount 转换成 UserAccountDTO
   *
   * @param userAccount
   * @return
   */
  UserAccountDTO toDTO(SysUserAccount userAccount);
}
