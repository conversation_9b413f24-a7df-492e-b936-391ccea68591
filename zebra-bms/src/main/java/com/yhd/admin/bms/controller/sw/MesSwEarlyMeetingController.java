package com.yhd.admin.bms.controller.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwEarlyMeetingConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwEarlyMeetingDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwEarlyMeetingParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwEarlyMeetingVO;
import com.yhd.admin.bms.service.sw.MesSwEarlyMeetingService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Date 2023/10/10 10:05
 **/


@RestController
@RequestMapping("/earlyMeeting")
@Slf4j
public class MesSwEarlyMeetingController {
    @Autowired
    private MesSwEarlyMeetingService service;
    @Autowired
    private MesSwEarlyMeetingConvert convert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwEarlyMeetingVO> pagingQuery(@RequestBody MesSwEarlyMeetingParam queryParam) {
        IPage<MesSwEarlyMeetingDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwEarlyMeetingParam queryParam) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(queryParam)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "早调会会议记录新增", businessType = BusinessType.INSERT)
    public RespJson add(@RequestBody MesSwEarlyMeetingParam queryParam) {
        boolean retVal = service.add(queryParam);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "早调会会议记录修改", businessType = BusinessType.UPDATE)
    public RespJson modify(@RequestBody MesSwEarlyMeetingParam queryParam) {
        log.debug("{}", queryParam);
        boolean retVal = service.modify(queryParam);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "早调会会议记录删除", businessType = BusinessType.DELETE)
    public RespJson removeBatch(@RequestBody BatchParam queryParam) {
        Boolean retVal = service.removeBatch(queryParam);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }


}
