package com.yhd.admin.passport.auth.provider;

/*
 * Copyright 2004, 2005, 2006 Acegi Technology Pty Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import com.yhd.admin.common.domain.enums.RedisKeyEnum;
import com.yhd.admin.passport.domain.query.UserAccountParam;
import com.yhd.admin.passport.service.UserAccountService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsPasswordService;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.Assert;

import java.util.concurrent.TimeUnit;

/**
 * An {@link AuthenticationProvider} implementation that retrieves user details from a {@link
 * UserDetailsService}.
 *
 * <AUTHOR> Alex
 * <AUTHOR> Winch
 */
public class CustomDaoAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
  // ~ Static fields/initializers
  // =====================================================================================

  /**
   * The plaintext password used to perform PasswordEncoder#matches(CharSequence, String)} on when
   * the user is not found to avoid SEC-2056.
   */
  private static final String USER_NOT_FOUND_PASSWORD = "userNotFoundPassword";

  // ~ Instance fields
  // ================================================================================================

  private PasswordEncoder passwordEncoder;

  /**
   * The password used to perform {@link PasswordEncoder#matches(CharSequence, String)} on when the
   * user is not found to avoid SEC-2056. This is necessary, because some {@link PasswordEncoder}
   * implementations will short circuit if the password is not in a valid format.
   */
  private volatile String userNotFoundEncodedPassword;

  private UserDetailsService userDetailsService;

  private UserDetailsPasswordService userDetailsPasswordService;

  private RedisTemplate redisTemplate;

  private UserAccountService userAccountService;

  public CustomDaoAuthenticationProvider() {
    setPasswordEncoder(PasswordEncoderFactories.createDelegatingPasswordEncoder());
  }

  // ~ Methods
  // ========================================================================================================

  @SuppressWarnings("deprecation")
  protected void additionalAuthenticationChecks(
      UserDetails userDetails, UsernamePasswordAuthenticationToken authentication)
      throws AuthenticationException {
    if (authentication.getCredentials() == null) {
      logger.debug("Authentication failed: no credentials provided");

      throw new BadCredentialsException(
          messages.getMessage(
              "AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
    }

    String presentedPassword = authentication.getCredentials().toString();

    if (!passwordEncoder.matches(presentedPassword, userDetails.getPassword())) {
      logger.debug("Authentication failed: password does not match stored value");
      String passkey =
          String.format(RedisKeyEnum.USER_PASSPWD_ERR_NUM.getKey(), userDetails.getUsername());
      Long num = this.redisTemplate.boundValueOps(passkey).increment(1);
      if (num == 1) {
        this.redisTemplate.boundHashOps(passkey).expire(5, TimeUnit.MINUTES);
      }
      if (num >= 5) {
        // lock User;
        UserAccountParam param = new UserAccountParam();
        param.setUserName(authentication.getName());
        param.setIsAccountNonLocked(Boolean.FALSE);
        userAccountService.updateUserAccount(param);
      }
      throw new BadCredentialsException(
          messages.getMessage(
              "AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
    }
  }

  protected void doAfterPropertiesSet() {
    Assert.notNull(this.userDetailsService, "A UserDetailsService must be set");
  }

  protected final UserDetails retrieveUser(
      String username, UsernamePasswordAuthenticationToken authentication)
      throws AuthenticationException {
    prepareTimingAttackProtection();
    try {
      UserDetails loadedUser = this.getUserDetailsService().loadUserByUsername(username);
      if (loadedUser == null) {
        throw new InternalAuthenticationServiceException(
            "UserDetailsService returned null, which is an interface contract violation");
      }
      return loadedUser;
    } catch (UsernameNotFoundException ex) {
      mitigateAgainstTimingAttack(authentication);
      throw ex;
    } catch (InternalAuthenticationServiceException ex) {
      throw ex;
    } catch (Exception ex) {
      throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
    }
  }

  @Override
  protected Authentication createSuccessAuthentication(
      Object principal, Authentication authentication, UserDetails user) {
    boolean upgradeEncoding =
        this.userDetailsPasswordService != null
            && this.passwordEncoder.upgradeEncoding(user.getPassword());
    if (upgradeEncoding) {
      String presentedPassword = authentication.getCredentials().toString();
      String newPassword = this.passwordEncoder.encode(presentedPassword);
      user = this.userDetailsPasswordService.updatePassword(user, newPassword);
    }
    return super.createSuccessAuthentication(principal, authentication, user);
  }

  private void prepareTimingAttackProtection() {
    if (this.userNotFoundEncodedPassword == null) {
      this.userNotFoundEncodedPassword = this.passwordEncoder.encode(USER_NOT_FOUND_PASSWORD);
    }
  }

  private void mitigateAgainstTimingAttack(UsernamePasswordAuthenticationToken authentication) {
    if (authentication.getCredentials() != null) {
      String presentedPassword = authentication.getCredentials().toString();
      this.passwordEncoder.matches(presentedPassword, this.userNotFoundEncodedPassword);
    }
  }

  /**
   * Sets the PasswordEncoder instance to be used to encode and validate passwords. If not set, the
   * password will be compared using {@link
   * PasswordEncoderFactories#createDelegatingPasswordEncoder()}
   *
   * @param passwordEncoder must be an instance of one of the {@code PasswordEncoder} types.
   */
  public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
    Assert.notNull(passwordEncoder, "passwordEncoder cannot be null");
    this.passwordEncoder = passwordEncoder;
    this.userNotFoundEncodedPassword = null;
  }

  protected PasswordEncoder getPasswordEncoder() {
    return passwordEncoder;
  }

  public void setUserDetailsService(UserDetailsService userDetailsService) {
    this.userDetailsService = userDetailsService;
  }

  protected UserDetailsService getUserDetailsService() {
    return userDetailsService;
  }

  public void setUserDetailsPasswordService(UserDetailsPasswordService userDetailsPasswordService) {
    this.userDetailsPasswordService = userDetailsPasswordService;
  }

  public RedisTemplate getRedisTemplate() {
    return redisTemplate;
  }

  public void setRedisTemplate(RedisTemplate redisTemplate) {
    this.redisTemplate = redisTemplate;
  }

  public UserAccountService getUserAccountService() {
    return userAccountService;
  }

  public void setUserAccountService(UserAccountService userAccountService) {
    this.userAccountService = userAccountService;
  }
}
