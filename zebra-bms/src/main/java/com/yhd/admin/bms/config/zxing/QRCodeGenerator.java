package com.yhd.admin.bms.config.zxing;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/7/9 15:19
 * @Version 1.0
 */
@Slf4j
@Component
public class QRCodeGenerator{
    /**
     * 生成二维码字节数组
     *
     * @param text 内容
     * @param width 宽度
     * @param height 高度
     */
    public byte[] generateQRCodeImage(String text, int width, int height) throws WriterException, IOException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8"); // 设置编码格式为UTF-8
        hints.put(EncodeHintType.MARGIN, 1);
        hints.put(EncodeHintType.QR_COMPACT, true);
        hints.put(EncodeHintType.QR_VERSION, 5);
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hints);
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", baos); // 输出为PNG格式的流
            return baos.toByteArray(); // 返回二维码的字节数组
        }
    }
}
