package com.yhd.admin.bms.controller.sys;

import cn.hutool.json.JSONUtil;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.domain.convert.sys.NotificationConvert;
import com.yhd.admin.bms.domain.entity.sys.SysNotification;
import com.yhd.admin.bms.service.sys.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName NotificationController.java @Description 系统消息 路由
 */
@RestController
@RequestMapping("/notices")
@Slf4j
public class NotificationController {

  @Resource private NotificationService service;

  @Resource private NotificationConvert convert;

  @PostMapping(
      value = "/message",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public String message(OAuth2Authentication authentication) {
    try {
      return JSONUtil.toJsonPrettyStr(service.queryList(authentication.getName()));
    } catch (Exception e) {
      log.error(e.getMessage());
      return "";
    }
  }

  @PostMapping(
      value = {"/read/{id}"},
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public String read(@PathVariable("id") Long id) {
    try {
      SysNotification notify = new SysNotification();
      notify.setId(id);
      notify.setReadme(true);
      service.read(notify);
    } catch (Exception e) {
      log.error(e.getMessage());
    }
    return null;
  }

  @PostMapping(
      value = {"/readall"},
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public String readall(OAuth2Authentication authentication) {
    try {
      log.info(">>>>>{},{}", UserContextHolder.getUserDetail(), UserContextHolder.getUserInfo());
      service.readall(authentication.getName());
    } catch (Exception e) {
      log.error(e.getMessage());
    }
    return null;
  }

  @PostMapping(
      value = {"/readtype/{type}"},
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public String readtype(@PathVariable("type") String type, OAuth2Authentication authentication) {
    try {
      service.readtype(type, authentication.getName());
    } catch (Exception e) {
      log.error(e.getMessage());
    }
    return null;
  }
}
