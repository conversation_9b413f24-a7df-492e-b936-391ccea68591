package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentPointData;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentPointDataParam;

/**
 * 设备资料-温振报警点位数据
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
public interface MesSwEquipmentPointDataService extends IService<MesSwEquipmentPointData> {
  /**
   * 更新接入的数据
   *
   * @param param
   * @return
   */
  Boolean saveData(MesSwEquipmentPointDataParam param);

  /**
   * 初始化报警上限、下限值
   *
   * @param param
   * @return
   */
  Boolean saveUpperAndLower(MesSwEquipmentPointDataParam param);

  /**
   * 定时删除一个月之前的数据
   *
   * @return
   */
  Boolean deleteDataJob();
}
