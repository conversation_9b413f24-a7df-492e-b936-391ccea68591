package com.yhd.admin.bms.controller.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentInspectionSpotCheckConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionSpotCheckDTO;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionSpotCheckParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionSpotCheckVO;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentInspectionSpotCheckService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> @Date 2024/8/19 18:25 @Version 1.0
 */

@RestController
@RequestMapping("/eqpt/spotCheck")
@Slf4j
public class MesSwEquipmentInspectionSpotCheckController {

    @Resource private MesSwEquipmentInspectionSpotCheckService service;

    @Resource private MesSwEquipmentInspectionSpotCheckConvert convert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwEquipmentInspectionSpotCheckVO> pagingQuery(
        @RequestBody MesSwEquipmentInspectionSpotCheckParam queryParam) {
        IPage<MesSwEquipmentInspectionSpotCheckDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getCurrentDetail(@RequestBody MesSwEquipmentInspectionSpotCheckParam param) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/check",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "厂领导复查抽检-领导检查", businessType = BusinessType.UPDATE)
    public RespJson check(@RequestBody MesSwEquipmentInspectionSpotCheckParam param) {
        Boolean retVal = service.check(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/savePush",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson savePush(@RequestBody MesSwEquipmentInspectionSpotCheckParam param) {
        try {
            return RespJson.buildSuccessResponse(service.savePush());
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getCZListByPost",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getCZListByPost(@RequestBody MesSwEquipmentInspectionSpotCheckParam param) {
        return RespJson.buildSuccessResponse(service.getCZListByPost());
    }

}
