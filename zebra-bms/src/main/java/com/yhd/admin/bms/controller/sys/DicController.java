package com.yhd.admin.bms.controller.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sys.DicConvert;
import com.yhd.admin.bms.domain.convert.sys.DicItemConvert;
import com.yhd.admin.bms.domain.dto.sys.DicDTO;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.DicItemParam;
import com.yhd.admin.bms.domain.query.sys.DicParam;
import com.yhd.admin.bms.domain.vo.sys.DicVO;
import com.yhd.admin.bms.service.sys.DicItemService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicContorller.java @Description TODO 字典项管理
 * @createTime 2020年05月22日 09:49:00
 */
@RestController
@RequestMapping("/dic")
public class DicController {

  private final DicConvert convert;

  private final DicService dicService;

  private final DicItemService dicItemService;

  private final DicItemConvert dicItemConvert;

  public DicController(
      DicConvert convert,
      DicService dicService,
      DicItemService dicItemService,
      DicItemConvert dicItemConvert) {
    this.convert = convert;
    this.dicService = dicService;
    this.dicItemConvert = dicItemConvert;
    this.dicItemService = dicItemService;
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<DicVO> pagingQuery(@RequestBody DicParam queryParam) {
    IPage<DicDTO> iPage = dicService.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @SysLogs(title = "增加系统字典", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson add(@RequestBody DicParam addParam, OAuth2Authentication auth2Authentication) {
    addParam.setCreatedBy(auth2Authentication.getName());
    dicService.addDic(addParam);
    return RespJson.buildSuccessResponse();
  }

  @SysLogs(title = "编辑系统字典", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/modify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson modify(
      @RequestBody DicParam modifyParam, OAuth2Authentication auth2Authentication) {
    modifyParam.setUpdatedBy(auth2Authentication.getName());
    dicService.modifyDic(modifyParam);
    return RespJson.buildSuccessResponse();
  }

  @SysLogs(title = "删除系统字典", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson removeBatch(@RequestBody BatchParam batchParam) {
    dicService.removeBatch(batchParam);
    return RespJson.buildSuccessResponse();
  }

  @PostMapping(
      value = "/currentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson currentDetail(@RequestBody DicParam queryParam) {
    return RespJson.buildSuccessResponse(
        convert.toVO(dicService.currentDetail(queryParam.getId())));
  }

  @SysLogs(title = "增加系统字典子项", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/itemSaveOrUpdateBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson itemSaveOrUpdateBatch(@RequestBody BatchParam batchParam) {
    dicItemService.saveOrUpdateBatch(batchParam.getItems());
    return RespJson.buildSuccessResponse();
  }

  @PostMapping(
      value = "/queryDicItemByDicId",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson queryDicItemByDicId(@RequestBody DicItemParam queryParam) {
    return RespJson.buildSuccessResponse(
        dicItemConvert.toVO(dicItemService.queryDicItemByDicId(queryParam)));
  }

  @SysLogs(title = "删除系统字典子项", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/removeItemById",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson removeItemById(@RequestBody DicItemParam removeParam) {
    dicItemService.removeById(removeParam.getId());
    return RespJson.buildSuccessResponse();
  }

  @PostMapping(
      value = "/validateDicIfNotExist",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> validateDicIfNotExist(@RequestBody DicParam validateParam) {
    Boolean ifNotExist = dicService.validateDicIfNotExist(validateParam);
    return RespJson.buildSuccessResponse(ifNotExist);
  }
}
