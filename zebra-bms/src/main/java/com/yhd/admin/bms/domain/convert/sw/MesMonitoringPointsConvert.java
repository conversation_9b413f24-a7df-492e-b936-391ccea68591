package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesMonitoringPointsDTO;
import com.yhd.admin.bms.domain.entity.sw.MesMonitoringPoints;
import com.yhd.admin.bms.domain.query.sw.MesMonitoringPointsParam;
import com.yhd.admin.bms.domain.vo.sw.MesMonitoringPointsVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR> @Date 2024/6/28 15:09 @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface MesMonitoringPointsConvert {

  MesMonitoringPoints toEntity(MesMonitoringPointsParam param);

  MesMonitoringPointsDTO toDTO(MesMonitoringPoints points);

  MesMonitoringPointsVO toVO(MesMonitoringPointsDTO pointsDTO);
}
