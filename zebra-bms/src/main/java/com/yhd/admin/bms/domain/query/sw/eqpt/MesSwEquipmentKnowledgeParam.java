package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 设备知识表
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentKnowledgeParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 6602517290640659483L;
  /** 主键id列表 */
  private List<Long> ids;

  /** 单位名称 */
  private String unitName;
  /** 部门名称 */
  private String deptName;
  /** 是否共享 */
  private String isShare;
  /** 文件名称 */
  private String fileName;
  /** 文件URL */
  private String fileUrl;
  /** 文件信息 */
  private List<JSONObject> fileList;
  /** 备注 */
  private String remark;
}
