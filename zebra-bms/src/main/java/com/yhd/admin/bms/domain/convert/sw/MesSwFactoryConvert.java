package com.yhd.admin.bms.domain.convert.sw;


import com.yhd.admin.bms.domain.dto.sw.MesSwFactoryDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwFactory;
import com.yhd.admin.bms.domain.query.sw.MesSwFactoryParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwFactoryVO;
import org.mapstruct.Mapper;


/**
 * 工业厂区草坪
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-10
 */
@Mapper(componentModel = "spring")
public interface MesSwFactoryConvert {

    MesSwFactory toEntity(MesSwFactoryParam param);

    MesSwFactoryVO toVO(MesSwFactoryDTO dto);

    MesSwFactoryDTO toDTO(MesSwFactory entity);

}
