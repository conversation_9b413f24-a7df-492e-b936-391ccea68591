package com.yhd.admin.bms.domain.vo.sys;

import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 承包商信息表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysUserAccountContractorVO extends BaseVO implements Serializable {
  /** 登录账号 */
  private String username;
  /** 性别 */
  private String gender;

  /** 年龄 */
  private Integer age;

  /** 本工种工龄 */
  private Integer workAge;
  /** 文化程度 */
  private String education;
  /** 出生日期 */
  private String birthday;
  /** 持特种作业证名称 */
  private String certificate;
  /** 初次领证时间 */
  private LocalDate firstCertificate;
  /** 有效期限 */
  private String expiration;
  /** 复审时间 */
  private LocalDate reviewDate;
  /** 入场日期 */
  private LocalDate entryDate;
  /** 离场日期 */
  private LocalDate exitDate;
  /** 紧急联系电话 */
  private String emergencyPhone;
}
