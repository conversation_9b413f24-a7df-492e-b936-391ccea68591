package com.yhd.admin.bms.domain.entity.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysUserAccount extends BaseEntity implements Serializable, Cloneable {
  /** 账户未过期 */
  private Boolean isAccountNonExpired;
  /** 账户未锁定 */
  private Boolean isAccountNonLocked;
  /** 密码未过期 */
  private Boolean isCredentialsNonExpired;
  /** 是否绑定人员信息 */
  private Boolean isBind;

  /** 登录账号 */
  private String username;
  /** 密码 */
  private String password;
  /** 姓名 */
  private String name;
  /** 人员类别 厂内人员/承包商 */
  private String type;
  /** 所在部门名称 厂内人员——组织编码主键 承包商——承包商编码主键 */
  private Long departmentId;
  /** 所在部门名称 */
  private String department;
  /** 员工编码 */
  private String jobNumber;
  /** 岗位 */
  private String post;
  /** 职务 */
  private String duty;
  /** 联系电话 */
  private String phone;
  /** 邮箱 */
  private String email;
  /** 账户状态 */
  private Boolean isEnable;
  /** 签名 */
  private String signature;

  /** 停送电操作密码 */
  private String tsdOperatePassword;
}
