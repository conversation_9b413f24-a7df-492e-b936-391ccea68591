package com.yhd.admin.bms.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sys.OrgDTO;
import com.yhd.admin.bms.domain.entity.sys.SysOrg;
import com.yhd.admin.bms.domain.query.sys.OrgParam;

import java.util.List;

/**
 * <一句话描述>: 组织部门管理 <详细功能>:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/8 16:51
 */
public interface OrgService extends IService<SysOrg> {

  /**
   * <一句话描述> <详细功能>
   *
   * @param *
   * @return java.util.List<com.yhd.admin.bms.domain.dto.OrgDTO>
   * @param: id 主键
   * <AUTHOR>
   * @date 2021/1/8 16:51
   */
  List<OrgDTO> queryList(Long id);

  /**
   * <一句话描述> 新增组织部门管理 <详细功能>
   *
   * @param *
   * @return java.lang.Boolean
   * @param: param
   * @description
   * <AUTHOR>
   * @date 2021/1/8 16:49
   */
  Boolean addOrModifyOrg(OrgParam param);

  /**
   * <一句话描述> <详细功能>
   *
   * @return java.lang.Boolean
   * @param ids 主键数组
   * <AUTHOR>
   * @date 2021/1/8 17:00
   */
  Boolean removeBatch(List<Long> ids);

  /*
   * <一句话简介>: 查询组织部门详情
   * <详细描述>:
   * @param: id
   * @return: com.yhd.admin.bms.domain.dto.OrgDTO
   * @author: jiangZhengHao
   * @date: 2021/1/9
   */
  OrgDTO currentDetail(Long id);

  /**
   * 获取部门的车间列表
   *
   * @return 车间列表
   */
  List<OrgDTO> getWorkshopList();
}
