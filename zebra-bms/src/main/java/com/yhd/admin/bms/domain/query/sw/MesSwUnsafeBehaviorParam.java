package com.yhd.admin.bms.domain.query.sw;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 不安全行为查询
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwUnsafeBehaviorParam extends QueryParam {


    /**
     * 矫正对象
     */
    private String correction;

    /**
     * 矫正对象
     */
    private String correctionCode;

    /**
     * 矫正时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime time;

    /**
     * 责任单位
     */
    private String unit;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 风险等级
     */
    private String riskLevelCode;

    /**
     * 负责人
     */
    private String inCharge;

    /**
     * 负责人
     */
    private String inChargeName;

    /**
     * 不安全行为描述
     */
    private String insecureDesc;

    /**
     * 沟通记录
     */
    private String record;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 记录人
     */
    private String recorderName;

    /**
     * 沟通记录负责人
     */
    private String recordCharge;

    /**
     * 沟通记录负责人
     */
    private String recordChargeName;

    /**
     * 原因分析
     */
    private String reasonDesc;

    /**
     * 分析人
     */
    private String reasoner;

    /**
     * 分析人
     */
    private String reasonerName;

    /**
     * 原因分析日期
     */
    private LocalDate reasonDate;

    /**
     * 矫正计划
     */
    private String correctionPlan;

    /**
     * 措施指定
     */
    private String measures;

    /**
     * 矫正计划日期
     */
    private LocalDate planDate;

    /**
     * 效果评估
     */
    private String evaluation;

    /**
     * 评估人员
     */
    private String evaluationer;

    /**
     * 评估人员
     */
    private String evaluationerName;

    /**
     * 效果评估日期
     */
    private LocalDate evaluationDate;

    /**
     * 矫正对象确认（1-已确认）
     */
    private Integer confirmationer;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private String startDateTime;
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private String endDateTime;

    /**
     * 不安全行为描述视频
     */
    private List<JSONObject> insecureVideoList;

    /**
     * 沟通记录视频
     */
    private List<JSONObject> recordVideoList;

    /**
     * 原因分析视频
     */
    private List<JSONObject> reasonVideoList;

    /**
     * 矫正计划视频
     */
    private List<JSONObject> correctionVideoList;

    /**
     * 效果评估视频
     */
    private List<JSONObject> evaluationVideoList;


    private String signUrl;
}
