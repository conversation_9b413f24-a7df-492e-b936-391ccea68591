package com.yhd.admin.bms.domain.entity.sw.safe;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 承包商施工工作票签字用户信息
 *
 * <AUTHOR>
 * @date 2024/5/28 22:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwWorkTicketContractorSignUser extends BaseEntity implements Serializable {
  private static final long serialVersionUID = -2345729134645184940L;

  /** 承包商施工工作票主键id */
  private Long ticketId;
  /** 签字用户类型 */
  private String userType;
  /** 用户账号 */
  private String userCode;
  /** 用户姓名 */
  private String userName;
  /** 签字图片url */
  private String signUrl;
  /** 审批意见 */
  private String spOpinion;
  /** 是否完成签字：0false,1true */
  private Boolean isSign;
}
