package com.yhd.admin.bms.controller.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwWorkTicketConstConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwWorkTicketConstDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwWorkTicketConstParam;
import com.yhd.admin.bms.domain.query.sw.MesSwWorkTicketConstSignUserParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwWorkTicketConstVO;
import com.yhd.admin.bms.service.sw.MesSwWorkTicketConstService;
import com.yhd.admin.bms.service.sw.MesSwWorkTicketConstSignUserService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/5/29 9:02
 * @Version 1.0
 *
 * 施工工作票--控制层
 */

@RestController
@RequestMapping("/work/ticket/const")
public class MesSwWorkTicketConstController{

    @Resource
    private MesSwWorkTicketConstConvert convert;

    @Resource
    private MesSwWorkTicketConstService service;

    @Resource
    private MesSwWorkTicketConstSignUserService signUserService;


    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwWorkTicketConstVO> pagingQuery(@RequestBody MesSwWorkTicketConstParam queryParam) {
        IPage<MesSwWorkTicketConstDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwWorkTicketConstParam param) {
        MesSwWorkTicketConstDTO currentDetail = service.getCurrentDetail(param);
        return RespJson.buildSuccessResponse(currentDetail);
    }

    @SysLogs(title = "施工工作票初始化数据", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/initialize",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson initialize(@RequestBody MesSwWorkTicketConstParam param) {
        MesSwWorkTicketConstDTO currentDetail = service.initialize(param);
        return RespJson.buildSuccessResponse(convert.toVO(currentDetail));
    }

    @SysLogs(title = "施工工作票新增", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesSwWorkTicketConstParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "施工工作票作废", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/cancel",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson cancel(@RequestBody MesSwWorkTicketConstParam param) {
        Boolean retVal = service.cancel(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @PostMapping(
        value = "/getSgLocationList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getSgLocationList(@RequestBody MesSwWorkTicketConstParam param) {
        return RespJson.buildSuccessResponse(service.getSgLocationList(param));
    }

    @PostMapping(
        value = "/getSgContentList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getSgContentList(@RequestBody MesSwWorkTicketConstParam param) {
        return RespJson.buildSuccessResponse(service.getSgContentList(param));
    }

    @PostMapping(
        value = "/getStatusList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getStatusList(@RequestBody MesSwWorkTicketConstParam param) {
        return RespJson.buildSuccessResponse(service.getStatusList(param));
    }

    @SysLogs(title = "施工工作票签字记录审批", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/approval",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson approval(@RequestBody MesSwWorkTicketConstSignUserParam param) {
        Boolean retVal = signUserService.approval(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

}
