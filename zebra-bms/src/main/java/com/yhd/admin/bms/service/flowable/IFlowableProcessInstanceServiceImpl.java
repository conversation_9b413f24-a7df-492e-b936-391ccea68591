package com.yhd.admin.bms.service.flowable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.bms.constant.FlowConstant;
import com.yhd.admin.bms.dao.flowable.IFlowableProcessInstanceDao;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.enums.flowable.CommentTypeEnum;
import com.yhd.admin.bms.domain.query.sys.UserParam;
import com.yhd.admin.bms.domain.vo.flowable.*;
import com.yhd.admin.bms.domain.vo.flowable.processinstance.DeleteFlowableProcessInstanceCmd;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sys.UserService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Activity;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class IFlowableProcessInstanceServiceImpl extends BaseProcessService
    implements IFlowableProcessInstanceService {
  @Resource private IFlowableBpmnModelService flowableBpmnModelService;
  @Resource private FlowProcessDiagramGenerator flowProcessDiagramGenerator;

  @Resource private IFlowableProcessInstanceDao flowableProcessInstanceDao;

  @Resource private IFlowableTaskService flowableTaskService;

  @Resource private UserService userService;

  @Override
  public RespJson<ProcessInstance> startProcessInstanceByKey(
      StartProcessInstanceVo processInstanceVo) {
    RespJson<ProcessInstance> returnVo = new RespJson<>();
    if (StringUtils.isNotBlank(processInstanceVo.getProcessDefinitionKey())
        && StringUtils.isNotBlank(processInstanceVo.getBusinessKey())
        && StringUtils.isNotBlank(processInstanceVo.getSystemSn())) {
      ProcessDefinition processDefinition =
          repositoryService
              .createProcessDefinitionQuery()
              .processDefinitionKey(processInstanceVo.getProcessDefinitionKey())
              .latestVersion()
              .singleResult();
      if (processDefinition != null && processDefinition.isSuspended()) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), "流程已被挂起,请先激活流程");
      }
      /** 1、设置变量 1.1、设置提交人字段为空字符串让其自动跳过 1.2、设置可以自动跳过 1.3、汇报线的参数设置 */
      // 1.1、设置提交人字段为空字符串让其自动跳过
      processInstanceVo.getVariables().put(FlowConstant.FLOW_SUBMITTER_VAR, "");
      // 1.2、设置可以自动跳过
      processInstanceVo.getVariables().put(FlowConstant.FLOWABLE_SKIP_EXPRESSION_ENABLED, true);
      // TODO 1.3、汇报线的参数设置
      // 2、当我们流程创建人和发起人
      String creator = processInstanceVo.getCurrentUserCode();
      if (StringUtils.isBlank(creator)) {
        creator = processInstanceVo.getCurrentUserCode();
        processInstanceVo.setCurrentUserCode(creator);
      }
      // 3.启动流程
      identityService.setAuthenticatedUserId(creator);
      ProcessInstance processInstance =
          runtimeService
              .createProcessInstanceBuilder()
              .processDefinitionKey(processInstanceVo.getProcessDefinitionKey().trim())
              .name(processInstanceVo.getFormName().trim())
              .businessKey(processInstanceVo.getBusinessKey().trim())
              .variables(processInstanceVo.getVariables())
              .tenantId(processInstanceVo.getSystemSn().trim())
              .start();
      returnVo.setData(processInstance);
      // 4.添加审批记录
      this.addComment(
          processInstanceVo.getCreator(),
          processInstance.getProcessInstanceId(),
          CommentTypeEnum.FQSP.toString(),
          "");
      // 5.TODO 推送消息数据

    }
    return returnVo;
  }

  @Override
  public RespJson<String> revokeProcess(RevokeProcessVo params) {
    RespJson<String> returnVo = RespJson.buildFailureResponse("撤回失败!");
    if (StringUtils.isNotBlank(params.getProcessInstanceId())) {
      ProcessInstance processInstance =
          runtimeService
              .createProcessInstanceQuery()
              .processInstanceId(params.getProcessInstanceId())
              .singleResult();
      if (processInstance != null) {
        // 1.添加撤回意见
        this.addComment(
            params.getUserCode(),
            params.getProcessInstanceId(),
            CommentTypeEnum.CH.toString(),
            params.getMessage());
        // 2.设置提交人
        runtimeService.setVariable(
            params.getProcessInstanceId(),
            FlowConstant.FLOW_SUBMITTER_VAR,
            processInstance.getStartUserId());
        // 3.执行撤回
        Activity disActivity =
            flowableBpmnModelService.findActivityByName(
                processInstance.getProcessDefinitionId(), FlowConstant.FLOW_SUBMITTER);
        // 4.删除运行和历史的节点信息
        this.deleteActivity(disActivity.getId(), params.getProcessInstanceId());
        // 5.执行跳转
        List<Execution> executions =
            runtimeService.createExecutionQuery().parentId(params.getProcessInstanceId()).list();
        List<String> executionIds = new ArrayList<>();
        executions.forEach(execution -> executionIds.add(execution.getId()));
        this.moveExecutionsToSingleActivityId(executionIds, disActivity.getId());
        returnVo = RespJson.buildSuccessResponse("撤回成功!");
      }
    } else {
      returnVo = RespJson.buildFailureResponse("流程实例id不能为空!");
    }
    return returnVo;
  }

  @Override
  public InputStream createImage(ProcessInstanceQueryVo param) {
    // 1.获取当前的流程实例
    ProcessInstance processInstance =
        runtimeService
            .createProcessInstanceQuery()
            .processInstanceId(param.getProcessInstanceId())
            .singleResult();
    String processDefinitionId = null;
    List<String> activeActivityIds = new ArrayList<>();
    List<String> highLightedFlows = new ArrayList<>();
    // 2.获取所有的历史轨迹线对象
    List<HistoricActivityInstance> historicSquenceFlows =
        historyService
            .createHistoricActivityInstanceQuery()
            .processInstanceId(param.getProcessInstanceId())
            .activityType(BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW)
            .list();
    historicSquenceFlows.forEach(
        historicActivityInstance -> highLightedFlows.add(historicActivityInstance.getActivityId()));
    // 3. 获取流程定义id和高亮的节点id
    if (processInstance != null) {
      // 3.1. 正在运行的流程实例
      processDefinitionId = processInstance.getProcessDefinitionId();
      activeActivityIds = runtimeService.getActiveActivityIds(param.getProcessInstanceId());
    } else {
      // 3.2. 已经结束的流程实例
      HistoricProcessInstance historicProcessInstance =
          historyService
              .createHistoricProcessInstanceQuery()
              .processInstanceId(param.getProcessInstanceId())
              .singleResult();
      processDefinitionId = historicProcessInstance.getProcessDefinitionId();
      // 3.3. 获取结束节点列表
      List<HistoricActivityInstance> historicEnds =
          historyService
              .createHistoricActivityInstanceQuery()
              .processInstanceId(param.getProcessInstanceId())
              .activityType(BpmnXMLConstants.ELEMENT_EVENT_END)
              .list();
      List<String> finalActiveActivityIds = activeActivityIds;
      historicEnds.forEach(
          historicActivityInstance ->
              finalActiveActivityIds.add(historicActivityInstance.getActivityId()));
    }
    // 4. 获取bpmnModel对象
    BpmnModel bpmnModel = flowableBpmnModelService.getBpmnModelByProcessDefId(processDefinitionId);
    // 5. 生成图片流
    InputStream inputStream =
        flowProcessDiagramGenerator.generateDiagram(bpmnModel, activeActivityIds, highLightedFlows);
    // 6. 转化成byte便于网络传输
    //    byte[] datas = IoUtil.readInputStream(inputStream, "image inputStream name");
    return inputStream;
  }

  @Override
  public PageRespJson<ProcessInstanceVo> getMyProcessInstances(ProcessInstanceQueryVo params) {

    int pageNum = params.getCurrent() == null ? 1 : params.getCurrent().intValue();
    int pageSize = params.getPageSize() == null ? 10 : params.getPageSize().intValue();
    Page<ProcessInstanceVo> page = new Page<>(pageNum, pageSize);
    IPage<ProcessInstanceVo> iPage = flowableProcessInstanceDao.getPagerModel(page, params);
    iPage
        .getRecords()
        .forEach(
            processInstanceVo -> {
              UserParam userParam = new UserParam();
              userParam.setAccountName(processInstanceVo.getApprover());
              UserDTO user = userService.getUser(userParam);
              processInstanceVo.setApprover(user.getName());
              this.setStateApprover(processInstanceVo);
            });
    return new PageRespJson<>(iPage);
  }

  @Override
  public RespJson<String> stopProcessInstanceById(EndProcessVo endVo) {
    RespJson<String> returnVo = null;
    ProcessInstance processInstance =
        runtimeService
            .createProcessInstanceQuery()
            .processInstanceId(endVo.getProcessInstanceId())
            .singleResult();
    if (processInstance != null) {

      // 1、添加审批记录
      this.addComment(
          endVo.getUserCode(),
          endVo.getProcessInstanceId(),
          endVo.getType() != null ? endVo.getType() : CommentTypeEnum.LCZZ.toString(),
          endVo.getMessage());
      List<EndEvent> endNodes =
          flowableBpmnModelService.findEndFlowElement(processInstance.getProcessDefinitionId());
      String endId = endNodes.get(0).getId();
      String processInstanceId = endVo.getProcessInstanceId();
      // 2、执行终止
      List<Execution> executions =
          runtimeService.createExecutionQuery().parentId(processInstanceId).list();
      List<String> executionIds = new ArrayList<>();
      executions.forEach(execution -> executionIds.add(execution.getId()));
      this.moveExecutionsToSingleActivityId(executionIds, endId);
      returnVo = RespJson.buildSuccessResponse("终止成功!");
    } else {
      throw new BMSException("error", "没有此任务，请确认!");
    }
    return returnVo;
  }

  @Override
  public RespJson<String> deleteProcessInstanceById(String processInstanceId) {
    RespJson<String> returnVo = null;
    long count =
        runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).count();
    if (count > 0) {
      DeleteFlowableProcessInstanceCmd cmd =
          new DeleteFlowableProcessInstanceCmd(processInstanceId, "删除流程实例", true);
      managementService.executeCommand(cmd);
      returnVo = RespJson.buildSuccessResponse("删除成功!");
    } else {
      historyService.deleteHistoricProcessInstance(processInstanceId);
      returnVo = RespJson.buildSuccessResponse("删除成功!");
    }
    return returnVo;
  }

  @Override
  public void deleteProcessInstanceByIdS(List<String> processInstanceIdS) {
    historyService.bulkDeleteHistoricProcessInstances(processInstanceIdS);
    runtimeService.bulkDeleteProcessInstances(processInstanceIdS, "删除流程实例");
  }

  /**
   * 设置状态和审批人
   *
   * @param processInstanceVo 参数
   */
  private void setStateApprover(ProcessInstanceVo processInstanceVo) {
    if (processInstanceVo.getEndTime() == null) {
      ProcessInstance processInstance =
          runtimeService
              .createProcessInstanceQuery()
              .processInstanceId(processInstanceVo.getProcessInstanceId())
              .singleResult();
      if (processInstance.isSuspended()) {
        processInstanceVo.setSuspensionState(FlowConstant.SUSPENSION_STATE);
      } else {
        processInstanceVo.setSuspensionState(FlowConstant.ACTIVATE_STATE);
      }
    }
    //        List<String> approvers =
    //            flowableTaskService.getApprovers(processInstanceVo.getProcessInstanceId());
    //        String userNames = this.createApprovers(approvers);
    //        processInstanceVo.setApprover(userNames);
    processInstanceVo.setTaskName(
        StringUtils.isEmpty(processInstanceVo.getTaskName())
            ? "已完成"
            : processInstanceVo.getTaskName());
  }

  /**
   * 组合审批人显示名称
   *
   * @param approvers 审批人列表
   * @return
   */
  private String createApprovers(List<String> approvers) {
    if (CollectionUtils.isNotEmpty(approvers)) {
      StringBuffer approverstr = new StringBuffer();
      StringBuffer finalApproverstr = approverstr;
      approvers.forEach(
          user -> {
            finalApproverstr.append(user).append(";");
          });
      if (approverstr.length() > 0) {
        approverstr = approverstr.deleteCharAt(approverstr.length() - 1);
      }
      return approverstr.toString();
    }
    return null;
  }
}
