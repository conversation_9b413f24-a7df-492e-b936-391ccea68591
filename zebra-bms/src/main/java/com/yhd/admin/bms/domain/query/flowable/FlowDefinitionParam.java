package com.yhd.admin.bms.domain.query.flowable;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class FlowDefinitionParam extends QueryParam implements Serializable, Cloneable {

  private String deploymentId;

  private Boolean skip;

  private Integer day;
}
