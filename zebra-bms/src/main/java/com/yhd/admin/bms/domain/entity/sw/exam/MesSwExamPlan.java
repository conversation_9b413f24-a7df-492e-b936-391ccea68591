package com.yhd.admin.bms.domain.entity.sw.exam;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全考试计划表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-01
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class MesSwExamPlan extends BaseEntity implements Serializable {

    /**
     * 考试试卷(组卷)主键id
     */
    private Long examPaperId;

    /**
     * 试卷标题
     */
    private String examTitle;

    /**
     * 试卷分类
     */
    private String examType;

    /**
     * 作者
     */
    private String author;

    /**
     * 计划状态code
     */
    private String planStatus;

    /**
     * 计划状态name
     */
    private String planStatusName;

    /**
     * 相关培训
     */
    private String trainingContent;

    /**
     * 考生来源
     */
    private String departmentId;

    @TableField(exist = false)
    private List<String> departmentIdList;

    @TableField(exist = false)
    private List<String> departmentNameList;

    private String departmentName;

    /**
     * 考试开始日期
     */
    private LocalDate examStartDate;
    /**
     * 考试结束日期
     */
    private LocalDate examEndDate;

    /**
     * 考试时长
     */
    private Long examDuration;

    /**
     * 题目乱序
     */
    private Boolean questionDisorder;

    /**
     * 答案乱序
     */
    private Boolean answerDisorder;

    /**
     * 抽考标记：0否，1是
     */
    private Boolean extract;
    /**
     * 抽考时间
     */
    private LocalDateTime extractTime;

    /**
     * 页面抽考操作按钮显示标记
     */
    @TableField(exist = false)
    private Boolean extractShow;

    /**
     * 考生
     */
    private String examStudentAccount;
    private String examStudentName;
    @TableField(exist = false)
    private List<String> examStudentAccountList;
    @TableField(exist = false)
    private List<String> examStudentNameList;
    /**
     * 考生人数
     */
    private Integer examNum;
    /**
     * 合格分数
     */
    private Integer passScore;
}
