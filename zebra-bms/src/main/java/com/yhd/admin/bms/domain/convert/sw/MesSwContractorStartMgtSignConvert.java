package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesSwContractorStartMgtSignDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwContractorStartMgtSign;
import com.yhd.admin.bms.domain.query.sw.MesSwContractorStartMgtSignParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwContractorStartMgtSignVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwContractorStartMgtSignConvert {
    MesSwContractorStartMgtSignVO toVO(MesSwContractorStartMgtSignDTO dto);

    MesSwContractorStartMgtSignDTO toDTO(MesSwContractorStartMgtSign entity);

    MesSwContractorStartMgtSign toEntity(MesSwContractorStartMgtSignParam param);

    List<MesSwContractorStartMgtSign> toListEntity(List<MesSwContractorStartMgtSignParam> approvalContentList);
}
