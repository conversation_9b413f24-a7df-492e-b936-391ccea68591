package com.yhd.admin.bms.domain.vo.sw.eqpt;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 厂领导抽查复检
 *
 * <AUTHOR>
 * @since 1.0.0 2024-08-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MesSwEquipmentInspectionSpotCheckVO extends BaseVO implements Serializable{
    /**
     * 设备巡检主键id
     */
    private String  inspectionId;

    /**
     * 设备编码
     */
    private String eqptNo;

    /**
     * 设备型号
     */
    private String eqptName;

    /**
     * 抽检周期-年月
     */
    private String checkCycle;

    /**
     * 抽检周期-周期code
     */
    private String checkWeekCode;

    /**
     * 抽检状态code
     */
    private String checkStatusCode;

    /**
     * 抽检状态
     */
    private String checkStatus;

    /**
     * 抽检时间
     */
    private LocalDateTime checkTime;

    /**
     * 抽检情况code
     */
    private String checkSituationCode;

    /**
     * 抽检情况
     */
    private String checkSituation;

    /**
     * 存在问题
     */
    private String existQuestion;

    /**
     * 现场照片
     */
    private List<JSONObject> photo;

    /**
     * 复检人code
     */
    private String checkPerson;

    /**
     * 设备巡检主键-编码
     */
    private String  eqptNoOrName;

    /**
     * 是否能检查标识（true:可以检查，false：不可检查）
     */
    private Boolean ifChecK;

    /**
     * 已检查数量
     */
    private Integer checkNum;

    /**
     * 抽检周期-年月周
     */
    private String checkCycleAndWeek;

    /**
     * 设备包机人json
     */
    private String eqptPersonJson;

    /**
     * 抽检时间字符串
     */
    private String checkTimeStr;

    /** 整改车间code */
    private Long zgWorkshopCode;

    /** 整改车间名称 */
    private String zgWorkshopName;
}
