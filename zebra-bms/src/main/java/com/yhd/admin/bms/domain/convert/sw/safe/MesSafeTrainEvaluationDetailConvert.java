package com.yhd.admin.bms.domain.convert.sw.safe;

import com.yhd.admin.bms.domain.dto.sw.safe.MesSafeTrainEvaluationDetailDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSafeTrainEvaluationDetail;
import com.yhd.admin.bms.domain.query.sw.safe.MesSafeTrainEvaluationDetailParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSafeTrainEvaluationDetailVO;
import org.mapstruct.Mapper;

/**
 * 课件评测表Convert
 *
 * <AUTHOR>
 * @since 2025-07-02 09:17:36
 */
@Mapper(componentModel = "spring")
public interface MesSafeTrainEvaluationDetailConvert {
MesSafeTrainEvaluationDetail toEntity(MesSafeTrainEvaluationDetailParam param);

MesSafeTrainEvaluationDetailDTO toDTO(MesSafeTrainEvaluationDetail entity);

MesSafeTrainEvaluationDetailVO toVO(MesSafeTrainEvaluationDetailDTO dto);
}

