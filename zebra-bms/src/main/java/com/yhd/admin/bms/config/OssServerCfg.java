package com.yhd.admin.bms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MinioCfg.java
 * @Description TODO
 * @createTime 2020年04月23日 14:06:00
 */
@Component
@ConfigurationProperties(value = "oss.server")
@Data
public class OssServerCfg {

    private String endpoint;

    private Integer port;

    private String accessKey;

    private String secretKey;

    private String bucketName;

}
