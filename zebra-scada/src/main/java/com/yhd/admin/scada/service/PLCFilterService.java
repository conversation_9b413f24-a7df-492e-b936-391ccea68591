package com.yhd.admin.scada.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.scada.domain.dto.PLCFilterDTO;
import com.yhd.admin.scada.domain.entity.MesPlcFilter;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/27 13:52
 */

public interface PLCFilterService extends IService<MesPlcFilter> {


    /**
     * 根据PLCID 查询对应的filter
     *
     * @param PLCId
     * @return
     */
    List<PLCFilterDTO> queryList(String PLCId);


}
