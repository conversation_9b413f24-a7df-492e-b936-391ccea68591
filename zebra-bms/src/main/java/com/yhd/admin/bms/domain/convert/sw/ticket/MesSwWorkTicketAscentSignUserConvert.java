package com.yhd.admin.bms.domain.convert.sw.ticket;


import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketAscentSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketAscentSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketAscentSignUserParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketAscentSignUserVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 登高作业工作票签字用户信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
@Mapper(componentModel = "spring")
public interface MesSwWorkTicketAscentSignUserConvert {

    MesSwWorkTicketAscentSignUser toEntity(MesSwWorkTicketAscentSignUserParam param);

    MesSwWorkTicketAscentSignUserVO toVO(MesSwWorkTicketAscentSignUserDTO dto);

    MesSwWorkTicketAscentSignUserDTO toDTO(MesSwWorkTicketAscentSignUser entity);

    List<MesSwWorkTicketAscentSignUserDTO> toDTOList(List<MesSwWorkTicketAscentSignUser> signUsers1);
}
