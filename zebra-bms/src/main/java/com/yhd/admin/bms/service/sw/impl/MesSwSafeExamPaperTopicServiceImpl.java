package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.MesSwSafeExamPaperTopicDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeExamPaperTopicConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperTopicDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeExamPaperTopic;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeQuestion;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperTopicParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesSwSafeExamPaperTopicService;
import com.yhd.admin.bms.service.sw.MesSwSafeQuestionService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/3 13:54
 * @Version 1.0
 */

@Service
@Slf4j
public class MesSwSafeExamPaperTopicServiceImpl
    extends ServiceImpl<MesSwSafeExamPaperTopicDao, MesSwSafeExamPaperTopic>
    implements MesSwSafeExamPaperTopicService {
    @Resource
    private MesSwSafeQuestionService questionService;
    @Resource
    private MesSwSafeExamPaperTopicConvert topicConvert;

    @Override
    public MesSwSafeExamPaperTopicDTO getQuestionByAdd(MesSwSafeExamPaperTopicParam param) {
        if (Objects.isNull(param.getQuestionId())){
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "缺少参数");
        }
        LambdaQueryWrapper<MesSwSafeExamPaperTopic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwSafeExamPaperTopic::getQuestionId,param.getQuestionId());
        List<MesSwSafeExamPaperTopic> topics = this.list(wrapper);
        if (CollectionUtils.isEmpty(topics)){
            MesSwSafeQuestion question = questionService.getById(param.getQuestionId());
            if (Objects.isNull(question)) {
                throw new BMSException(ResultStateEnum.FAIL.getCode(), "此题可能已被删除，请检查");
            }
            MesSwSafeExamPaperTopic newPaperTopic = new MesSwSafeExamPaperTopic();
            BeanUtils.copyProperties(question,newPaperTopic);
            MesSwSafeExamPaperTopicDTO topicDTO = topicConvert.toDTO(newPaperTopic);
            this.setUrl(topicDTO);
            return topicDTO;
        } else {
            MesSwSafeExamPaperTopic paperTopic = topics.get(0);
            MesSwSafeExamPaperTopicDTO topicDTO = topicConvert.toDTO(paperTopic);
            this.setUrl(topicDTO);
            return topicDTO;
        }
    }

    @Override
    public MesSwSafeExamPaperTopicDTO getQuestion(MesSwSafeExamPaperTopicParam param) {
        if (Objects.isNull(param.getId())){
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "缺少参数");
        }
        MesSwSafeExamPaperTopicDTO paperTopicDTO = topicConvert.toDTO(this.getById(param.getId()));
        this.setUrl(paperTopicDTO);
        return paperTopicDTO;
    }

    private void setUrl(MesSwSafeExamPaperTopicDTO topicDTO) {
        if (StringUtils.isNotBlank(topicDTO.getFileUrl())){
            String fileUrl = topicDTO.getFileUrl();
            String[] urlSplit = fileUrl.split("@&&@");
            List<String> fileUrlList = Arrays.asList(urlSplit);
            topicDTO.setFileUrlList(fileUrlList);
        }
        if (StringUtils.isNotBlank(topicDTO.getQuestionAnswer())){
            String questionAnswer = topicDTO.getQuestionAnswer();
            String[] answerSplit = questionAnswer.split("@&&@");
            List<String> questionAnswerList = Arrays.asList(answerSplit);
            topicDTO.setQuestionAnswerList(questionAnswerList);
        }
        if (StringUtils.isNotBlank(topicDTO.getCorrectAnswer())){
            String correctAnswer = topicDTO.getCorrectAnswer();
            String[] correctSplit = correctAnswer.split("@&&@");
            List<String> correctAnswerList = Arrays.asList(correctSplit);
            if (correctAnswerList.size() == 1){
                String answer = correctAnswerList.get(0);
                topicDTO.setCorrectAnswer(answer);
            } else {
                topicDTO.setCorrectAnswerList(correctAnswerList);
            }
        }
    }
}
