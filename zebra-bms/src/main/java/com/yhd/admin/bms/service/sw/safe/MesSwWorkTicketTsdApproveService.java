package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketTsdApproveDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketTsdApprove;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketTsdApproveParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketTsdSignUserParam;

import java.util.List;

/**
 * 外施工单位停送电工作票审批-业务层接口
 *
 * <AUTHOR>
 * @date 2024/6/15 10:50
 */
public interface MesSwWorkTicketTsdApproveService extends IService<MesSwWorkTicketTsdApprove> {

  /**
   * 根据条件查询分页列表
   *
   * @param param 查询参数
   * @return 外施工单位停送电工作票分页列表信息
   */
  IPage<MesSwWorkTicketTsdApproveDTO> pagingQuery(MesSwWorkTicketTsdApproveParam param);

  /**
   * 根据条件查询列表
   *
   * @param param 查询条件
   * @return 外施工单位停送电工作票列表信息
   */
  List<MesSwWorkTicketTsdApproveDTO> queryList(MesSwWorkTicketTsdApproveParam param);

  /**
   * 查询详情信息
   *
   * @param param 查询参数：主键id
   * @return 外施工单位停送电工作票详情信息
   */
  MesSwWorkTicketTsdApproveDTO getCurrentDetail(MesSwWorkTicketTsdApproveParam param);

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean add(MesSwWorkTicketTsdApproveParam param);

  /**
   * 送电申请
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean sdApply(MesSwWorkTicketTsdApproveParam param);

  /**
   * 作废
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean zf(MesSwWorkTicketTsdApproveParam param);

  /**
   * 审批
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean sp(MesSwWorkTicketTsdSignUserParam param);

  /**
   * 查询施工单位(承包商)列表
   *
   * @return 施工单位列表
   */
  List<String> getSgOrgNameList();

  /**
   * 查询施工项目列表
   *
   * @return 施工项目列表
   */
  List<String> getSgItemList();

  /**
   * 查询施工负责人列表
   *
   * @return 施工负责人列表
   */
  List<String> getSgUserNameList();
}
