package com.yhd.admin.bms.controller.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sys.SysLogConvert;
import com.yhd.admin.bms.domain.dto.sys.SysLogDTO;
import com.yhd.admin.bms.domain.query.sys.SysLogParam;
import com.yhd.admin.bms.domain.vo.sys.SysLogVO;
import com.yhd.admin.bms.service.sys.SysLogService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sysLog")
@Slf4j
public class SysLogController {

  @Resource private SysLogService service;

  @Resource private SysLogConvert convert;

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<SysLogVO> pagingQuery(@RequestBody SysLogParam queryParam) {
    IPage<SysLogDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson addSysLog(
      @RequestBody SysLogParam addParam, OAuth2Authentication authentication) {
    addParam.setCreatedBy(authentication.getName());
    service.add(addParam);
    return RespJson.buildSuccessResponse();
  }
}
