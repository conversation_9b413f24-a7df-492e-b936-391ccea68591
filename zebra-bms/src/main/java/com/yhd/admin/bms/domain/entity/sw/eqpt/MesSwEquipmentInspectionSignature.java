package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备巡检-签字表
 *
 * <AUTHOR>
 * @date 2023/12/01 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwEquipmentInspectionSignature implements Cloneable, Serializable {
  private String id;
  /** 设备巡检表主键ID */
  private String parentId;
  /** 签字人账号 */
  private String account;
  /** 签字人名称 */
  private String name;
  /** 签字图片地址 */
  @TableField(updateStrategy = FieldStrategy.IGNORED)
  private String fileUrl;
  /** 节点类型-字典 */
  private String nodeCode;
  /** 节点名称 */
  private String nodeName;
  /** 设备编号 */
  private String equipmentNo;
  /** 流程实例ID */
  private String processInstanceId;
  /** 审批意见 */
  private String opinion;
  /** 创建人 */
  @TableField(fill = FieldFill.INSERT)
  private String createdBy;
  /** 创建时间 */
  @TableField(updateStrategy = FieldStrategy.NEVER)
  private LocalDateTime createdTime;
  /** 更新人 */
  @TableField(fill = FieldFill.UPDATE)
  private String updatedBy;
  /** 更新时间 */
  @TableField(insertStrategy = FieldStrategy.NEVER)
  private LocalDateTime updatedTime;
}
