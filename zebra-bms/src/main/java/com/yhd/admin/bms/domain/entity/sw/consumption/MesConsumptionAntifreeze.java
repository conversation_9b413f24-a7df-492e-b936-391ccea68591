package com.yhd.admin.bms.domain.entity.sw.consumption;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 能耗管理-防冻液
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesConsumptionAntifreeze extends BaseEntity implements Cloneable, Serializable {
  /** 日期 */
  private LocalDate date;
  /** 防冻液——溜槽流量 */
  private BigDecimal chute;
  /** 防冻液——车皮流量 */
  private BigDecimal wagons;
}
