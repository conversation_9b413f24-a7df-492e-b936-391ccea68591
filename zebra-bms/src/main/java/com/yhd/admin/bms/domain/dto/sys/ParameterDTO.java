package com.yhd.admin.bms.domain.dto.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ParameterDTO.java
 * @Description TODO
 * @createTime 2020年05月12日 14:38:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ParameterDTO extends BaseDTO implements Serializable, Cloneable {

    /**
     * 参数KEY
     */
    private String paramKey;
    /**
     * 参数值
     */
    private String paramVal;
    /**
     * 状态;0否1是
     */
    private Boolean isEnable;
    /**
     * 参数类型
     */
    private String paramType;
    /**
     * 参数编号
     */
    private String paramNo;


    /**
     * 备注
     */
    private String remark;

    @Override
    public String toString() {
        return "SysParameter{" + "paramKey='" + paramKey + '\'' + ", paramVal='" + paramVal + '\''
            + ", isEnable="
            + isEnable + '}';
    }
}
