package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainTeacherEvaluateDetail;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainTeacherEvaluateDetailParam;

import java.util.List;

public interface MesSwSafeTrainTeacherEvaluateDetailService
    extends IService<MesSwSafeTrainTeacherEvaluateDetail> {

  /**
   * 根据条件查询教师评分详情列表
   *
   * @param param 查询参数
   * @return 教师评分详情
   */
  List<MesSwSafeTrainTeacherEvaluateDetail> queryList(
      MesSwSafeTrainTeacherEvaluateDetailParam param);
}
