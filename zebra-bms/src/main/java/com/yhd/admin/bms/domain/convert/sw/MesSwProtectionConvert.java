package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesSwProtectionDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwProtection;
import com.yhd.admin.bms.domain.query.sw.MesSwProtectionParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwProtectionVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesSwProtectionConvert {

    MesSwProtectionDTO toDTO(MesSwProtection mesSwProtection);

    MesSwProtectionVO toVO(MesSwProtectionDTO mesSwProtectionDTO);

    MesSwProtection toEntity(MesSwProtectionParam param);

}
