package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.query.sw.eqpt.EqptHealthParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.EqptHealthResultVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.EqptHealthVO;
import java.util.List;

/**
 * 设备健康评价
 *
 * <AUTHOR>
 * @date 2025/7/24 10:22
 */
public interface EqptHealthService {
  /**
   * 列表查询
   *
   * @param queryParam
   * @return
   */
  IPage<EqptHealthVO> pagingQuery(EqptHealthParam queryParam);

  /**
   * 运行统计
   *
   * @param queryParam
   * @return
   */
  EqptHealthResultVO getHealthResult(EqptHealthParam queryParam);

  /**
   * 获取配置的设备，设备类型去重集合,
   *
   * @param queryParam
   * @return
   */
  List<EqptHealthVO> getHealthDeviceType(EqptHealthParam queryParam);
}
