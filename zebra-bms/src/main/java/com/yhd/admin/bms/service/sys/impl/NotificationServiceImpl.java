package com.yhd.admin.bms.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.dao.sys.NotificationDao;
import com.yhd.admin.bms.domain.convert.sys.NotificationConvert;
import com.yhd.admin.bms.domain.entity.sys.SysNotification;
import com.yhd.admin.bms.domain.vo.sys.SysNotificationVO;
import com.yhd.admin.bms.service.sys.NotificationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NotificationServiceImpl.java
 */
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationDao, SysNotification> implements NotificationService {

    @Resource
    private NotificationConvert convert;

    @Override
    public Object queryList(String userName) {
        List<SysNotificationVO> resList = Lists.newArrayList();
        LambdaQueryChainWrapper<SysNotification> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.eq(SysNotification::getReceiveBy, userName);
        queryChain.eq(SysNotification::getReadme, 0);
        queryChain.orderByDesc(SysNotification::getCreatedTime);
        List<SysNotification> list = queryChain.list();
        Optional.ofNullable(list).ifPresent(e -> {
            e.forEach(item -> {
                resList.add(convert.toVO(convert.toDTO(item)));
            });
        });
        return resList;
    }

    @Override
    public Boolean read(SysNotification notify) {
        return super.baseMapper.updateById(notify) > 0;
    }

    @Override
    public Boolean readall(String userName) {
        QueryWrapper<SysNotification> update = new QueryWrapper<>();
        update.eq("receive_by", userName);
        SysNotification notification = new SysNotification();
        notification.setReadme(true);
        return super.baseMapper.update(notification, update) > 0;
    }

    @Override
    public Boolean readtype(String type, String userName) {
        QueryWrapper<SysNotification> update = new QueryWrapper<>();
        update.eq("receive_by", userName);
        update.eq("type", type);
        SysNotification notification = new SysNotification();
        notification.setReadme(true);
        return super.baseMapper.update(notification, update) > 0;
    }

    @Override
    public Boolean add(SysNotification notification) {
        return super.baseMapper.insert(notification) > 0;
    }
}
