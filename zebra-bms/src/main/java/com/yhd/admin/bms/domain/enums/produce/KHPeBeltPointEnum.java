package com.yhd.admin.bms.domain.enums.produce;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** 胶带机点位 */
@Getter
public enum KHPeBeltPointEnum {
  AMOUNT_301("XMSW_Raw_CON301_InstantCoalAmount", "301胶带机瞬时量"),
  DAY_301("XMSW_Lump_CON301_Info_ProductToday", "301胶带机日产量"),
  MONTH_301("XMSW_Lump_CON301_Info_ProductMonth", "301胶带机月产量"),
  YEAR_301("XMSW_Lump_CON301_Info_ProductYear", "301胶带机年产量"),

  AMOUNT_302("XMSW_Raw_CON302_InstantCoalAmount", "302胶带机瞬时量"),
  DAY_302("XMSW_Lump_CON302_Info_ProductToday", "302胶带机日产量"),
  MONTH_302("XMSW_Lump_CON302_Info_ProductMonth", "302胶带机月产量"),
  YEAR_302("XMSW_Lump_CON302_Info_ProductYear", "302胶带机年产量"),

  AMOUNT_201("XMSW_Raw_CON201_InstantCoalAmount", "201胶带机瞬时量"),
  DAY_201("XMSW_Raw_CON201_Info_ProductToday", "201胶带机日产量"),
  MONTH_201("XMSW_Raw_CON201_Info_ProductMonth", "201胶带机月产量"),
  YEAR_201("XMSW_Raw_CON201_Info_ProductYear", "201胶带机年产量"),

  AMOUNT_218("XMSW_Raw_CON218_InstantCoalAmount", "218胶带机瞬时量"),
  DAY_218("XMSW_Raw_CON218_Info_ProductToday", "218胶带机日产量"),
  MONTH_218("XMSW_Raw_CON218_Info_ProductMonth", "218胶带机月产量"),
  YEAR_218("XMSW_Raw_CON218_Info_ProductYear", "218胶带机年产量"),

  AMOUNT_701("XMSW_Raw_CON701_InstantCoalAmount", "701胶带机瞬时量"),
  DAY_701("XMSW_Siev_CON701_Info_ProductToday", "701胶带机日产量"),
  MONTH_701("XMSW_Siev_CON701_Info_ProductMonth", "701胶带机月产量"),
  YEAR_701("XMSW_Siev_CON701_Info_ProductYear", "701胶带机年产量"),

  AMOUNT_251("XMSW_Siev_CON251_InstantCoalAmount", "251胶带机瞬时量"),
  DAY_251("XMSW_Siev_CON251_Info_ProductToday", "251胶带机日产量"),
  MONTH_251("XMSW_Siev_CON251_Info_ProductMonth", "251胶带机月产量"),
  YEAR_251("XMSW_Siev_CON251_Info_ProductYear", "251胶带机年产量"),

  AMOUNT_702("XMSW_Lump_CON702_InstantCoalAmount1", "702胶带机瞬时量"),
  DAY_702("XMSW_Siev_CON702_Info_ProductToday", "702胶带机日产量"),
  MONTH_702("XMSW_Siev_CON702_Info_ProductMonth", "702胶带机月产量"),
  YEAR_702("XMSW_Siev_CON702_Info_ProductYear", "702胶带机年产量"),

  CON901_ProductToday("XMSW_Siev_CON901_Info_ProductToday", "901电厂皮带日产量，累计数值，每日18点置零"),
  ;

  private final String key;
  private final String desc;

  KHPeBeltPointEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }

  public static List<String> getKeyList() {
    KHPeBeltPointEnum[] values = KHPeBeltPointEnum.values();

    return Arrays.stream(values).map(KHPeBeltPointEnum::getKey).collect(Collectors.toList());
  }

  public static KHPeBeltPointEnum getEnumByKey(String key) {
    if (StringUtils.isBlank(key)) {
      return null;
    }
    KHPeBeltPointEnum[] values = KHPeBeltPointEnum.values();
    for (KHPeBeltPointEnum value : values) {
      if (value.getKey().equals(key)) {
        return value;
      }
    }
    return null;
  }
}
