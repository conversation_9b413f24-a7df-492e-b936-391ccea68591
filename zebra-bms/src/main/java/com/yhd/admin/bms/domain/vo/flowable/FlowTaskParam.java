package com.yhd.admin.bms.domain.vo.flowable;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FlowTaskParam {

  // 任务Id
  private String taskId;

  // 用户Id
  private String userId;

  // 任务类型
  private String commentType;
  // 任务意见
  private String comment;

  // 流程实例Id
  private String processInstanceId;

  // 节点
  private String targetKey;

  // 流程变量信息
  private Map<String, Object> values;

  // 审批人
  private String assignee;

  // 候选人
  private List<String> candidateUsers;

  // 审批组
  private List<String> candidateGroups;
}
