package com.yhd.admin.bms.domain.dto.sw;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 部件分类统计信息
 * <AUTHOR>
 */
@Data
public class MesLargePartStatisticDTO implements Serializable {
    /** 设备类型 */
    private String equipmentType;
    /** 部件分类 */
    private String partCategory;
    /** 单位 */
    private String unit;
    /** 总数量 */
    private Integer totalQuantity;
    /** 库存储备（新+旧） */
    private BigDecimal totalStock;
    /** 是否无储备报警 */
    private Boolean isAlarm = false;
}
