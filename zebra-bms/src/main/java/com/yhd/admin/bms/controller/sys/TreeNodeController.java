package com.yhd.admin.bms.controller.sys;

import com.yhd.admin.bms.domain.convert.sys.TreeNodeConvert;
import com.yhd.admin.bms.domain.query.sys.RegionParam;
import com.yhd.admin.bms.domain.vo.sys.TreeNode;
import com.yhd.admin.bms.service.sys.MenuService;
import com.yhd.admin.bms.service.sys.OrgService;
import com.yhd.admin.bms.service.sys.RegionService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName TreeNodeController.java @Description 树形结构
 * @createTime 2020年05月11日 14:44:00
 */
@RestController
@RequestMapping("/tree")
public class TreeNodeController {

  private final MenuService menuService;

  private final TreeNodeConvert convert;

  private final OrgService orgService;

  private final RegionService regionService;

  public TreeNodeController(
      MenuService menuService,
      TreeNodeConvert convert,
      OrgService orgService,
      RegionService regionService) {
    this.menuService = menuService;
    this.convert = convert;
    this.orgService = orgService;
    this.regionService = regionService;
  }

  @PostMapping(
      value = "/authority",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<TreeNode>> querySubMenuIncludeSelf() {
    return RespJson.buildSuccessResponse(convert.toTreeNode(menuService.querySubMenuById(0L)));
  }

  @PostMapping(
      value = "/orgTree",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<TreeNode>> queryOrgTree() {
    return RespJson.buildSuccessResponse(convert.orgToTreeNode(orgService.queryList(0L)));
  }

  @PostMapping(
      value = "/regionTree",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<TreeNode>> regionTree(@RequestBody RegionParam param) {
    Long id = param.getId() == null ? 1L : param.getId();
    return RespJson.buildSuccessResponse(convert.regionToTreeNode(regionService.getRegionTree(id)));
  }
}
