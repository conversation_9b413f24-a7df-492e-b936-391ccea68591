package com.yhd.admin.bms.service.sw.impl.eqpt;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentInspectionSpotCheckDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentInspectionSpotCheckConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionSpotCheckDTO;
import com.yhd.admin.bms.domain.dto.sys.OrgDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspection;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspectionSpotCheck;
import com.yhd.admin.bms.domain.entity.sys.MesNotice;
import com.yhd.admin.bms.domain.enums.NotifyEnum;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionSpotCheckParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentInspectionService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentInspectionSpotCheckService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.FileService;
import com.yhd.admin.bms.service.sys.OrgService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.bms.webscoket.service.WebsocketService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 厂领导抽查复检
 *
 * <AUTHOR>
 * @since 1.0.0 2024-08-19
 */
@Service
public class MesSwEquipmentInspectionSpotCheckServiceImpl
    extends ServiceImpl<MesSwEquipmentInspectionSpotCheckDao, MesSwEquipmentInspectionSpotCheck>
    implements MesSwEquipmentInspectionSpotCheckService {

  @Resource private MesSwEquipmentInspectionSpotCheckConvert convert;

  @Resource private MesSwEquipmentInspectionService inspectionService;

  @Resource private UserAccountService accountService;

  @Resource private WebsocketService websocketService;

  @Resource private DicService dicService;

  @Resource private FileService fileService;

  @Resource private MesSwEquipmentRepairService repairService;

  @Resource private OrgService orgService;

  /** 字典项-厂领导抽查复检-抽检状态 */
  public static final String LEADER_CHECK_STATUS = "LEADER_CHECK_STATUS";
  /** 字典项-厂领导抽查复检-抽检情况 */
  public static final String LEADER_CHECK_SITUATION = "LEADER_CHECK_SITUATION";
  /** 字典项-设备类型 */
  public static final String DEVICE_TYPE = "DEVICE_TYPE";

  /** 业务类型 */
  private static final String BUSINESS_TYPE = "巡检工单或厂领导复检";
  /** 附件类型 */
  private static final String FILE_TYPE = "现场照片";
  /** 厂长抽检台数 */
  public static final int CZ_CHECK_NUM = 10;
  /** 副厂长抽检台数 */
  public static final int FCZ_CHECK_NUM = 20;

  @Override
  public IPage<MesSwEquipmentInspectionSpotCheckDTO> pagingQuery(
      MesSwEquipmentInspectionSpotCheckParam queryParam) {
    IPage<MesSwEquipmentInspectionSpotCheck> iPage =
        new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<MesSwEquipmentInspectionSpotCheck> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 厂领导不选默认厂长
    if (StringUtils.isBlank(queryParam.getCheckPerson())) {
      List<UserAccountDTO> accounts = accountService.getCZListByPost("厂长");
      queryParam.setCheckPerson(accounts.get(0).getUsername());
    }
    queryChain.eq(MesSwEquipmentInspectionSpotCheck::getCheckPerson, queryParam.getCheckPerson());
    // 周期不选默认当前周
    if (StringUtils.isBlank(queryParam.getCheckCycle())
        && StringUtils.isBlank(queryParam.getCheckWeekCode())) {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
      String currentYearMonth = LocalDate.now().format(formatter);
      if (isWithinDate(
          LocalDate.now(),
          LocalDate.parse(currentYearMonth + "-01"),
          LocalDate.parse(currentYearMonth + "-07"))) {
        queryChain
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckCycle, currentYearMonth)
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckWeekCode, "1");
      } else if (isWithinDate(
          LocalDate.now(),
          LocalDate.parse(currentYearMonth + "-08"),
          LocalDate.parse(currentYearMonth + "-14"))) {
        queryChain
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckCycle, currentYearMonth)
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckWeekCode, "2");
      } else if (isWithinDate(
          LocalDate.now(),
          LocalDate.parse(currentYearMonth + "-15"),
          LocalDate.parse(currentYearMonth + "-21"))) {
        queryChain
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckCycle, currentYearMonth)
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckWeekCode, "3");
      } else {
        queryChain
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckCycle, currentYearMonth)
            .eq(MesSwEquipmentInspectionSpotCheck::getCheckWeekCode, "4");
      }
    } else {
      queryChain
          .eq(MesSwEquipmentInspectionSpotCheck::getCheckCycle, queryParam.getCheckCycle())
          .eq(MesSwEquipmentInspectionSpotCheck::getCheckWeekCode, queryParam.getCheckWeekCode());
    }
    queryChain.orderByDesc(MesSwEquipmentInspectionSpotCheck::getCreatedTime);
    IPage<MesSwEquipmentInspectionSpotCheckDTO> checkDTOIPage =
        queryChain.page(iPage).convert(convert::toDTO);
    List<MesSwEquipmentInspectionSpotCheckDTO> checkDTOS = checkDTOIPage.getRecords();
    long count =
        checkDTOS.stream().filter(checkDTO -> "已完成抽查复检".equals(checkDTO.getCheckStatus())).count();
    checkDTOS.forEach(
        checkDTO -> {
          // 拼接抽检周期
          String yearMonth = checkDTO.getCheckCycle().replace("-", "年");
          checkDTO.setCheckCycleAndWeek(yearMonth + "月 第" + checkDTO.getCheckWeekCode() + "周");
          // 拼接设备编码及型号
          checkDTO.setEqptNoOrName(checkDTO.getEqptNo() + " " + checkDTO.getEqptName());
          // 获取当前登录人
          String name = SecurityContextHolder.getContext().getAuthentication().getName();
          // 判断是否可检查
          checkDTO.setIfChecK(checkDTO.getCheckPerson().equals(name) ? true : false);
          // 获取已复检数量
          checkDTO.setCheckNum((int) count);
          // 拼接抽检时间
          if (checkDTO.getCheckTime() != null) {
            checkDTO.setCheckTimeStr(
                checkDTO.getCheckTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分")));
          }
        });
    return checkDTOIPage;
  }

  @Override
  public Boolean check(MesSwEquipmentInspectionSpotCheckParam param) {
    if ("1".equals(param.getCheckSituationCode())
        && StringUtils.isNotBlank(param.getExistQuestion())) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "设备无异常不能添加存在问题");
    }
    // 抽检情况填写后，抽检状态更新为已完成抽查复检
    if (StringUtils.isNotBlank(param.getCheckSituationCode())) {
      param.setCheckStatusCode("已完成抽查复检");
      param.setCheckStatus(dicService.transform(LEADER_CHECK_STATUS, param.getCheckStatusCode()));
    }
    fileService.insertFile(param.getId(), BUSINESS_TYPE, FILE_TYPE, param.getPhoto());
    // 抽检时间
    if (Objects.isNull(this.getById(param.getId()).getCheckTime())) {
      param.setCheckTime(LocalDateTime.now());
    }
    param.setCheckSituation(
        dicService.transform(LEADER_CHECK_SITUATION, param.getCheckSituationCode()));
    MesSwEquipmentInspectionSpotCheck spotCheck = convert.toEntity(param);
    boolean update = super.updateById(spotCheck);
    // 如果抽检存在问题，则提交检修提报
    if ("2".equals(spotCheck.getCheckSituationCode())) {
      // 调用检修提报接口
      MesSwEquipmentRepairParam repairParam = new MesSwEquipmentRepairParam();
      MesSwEquipmentInspectionSpotCheck newCheck = this.getById(spotCheck.getId());
      MesSwEquipmentInspection ins = inspectionService.getById(newCheck.getInspectionId());
      // 设备编码+型号
      repairParam.setEquipmentNoType(newCheck.getEqptNo() + " " + newCheck.getEqptName());
      // 提报人账号
      repairParam.setTbUserCode(newCheck.getCheckPerson());
      // 提报人姓名
      repairParam.setTbUserName(accountService.getNameByUserName(newCheck.getCheckPerson()));
      // 提报时间
      repairParam.setTbTime(LocalDateTime.now());
      // 提报车间编码
      for (OrgDTO orgDTO : orgService.getWorkshopList()) {
        if (orgDTO.getOrgName().equals(ins.getWorkShopName())) {
          repairParam.setTbWorkshopCode(orgDTO.getId());
        }
      }
      // 提报车间名称
      repairParam.setTbWorkshopName(ins.getWorkShopName());
      // 整改车间代码
      repairParam.setZgWorkshopCode(param.getZgWorkshopCode());
      // 整改车间名称
      repairParam.setZgWorkshopName(param.getZgWorkshopName());
      // 存在问题
      repairParam.setExistProblem(newCheck.getExistQuestion());
      // 现场照片
      repairParam.setTbPhoto(fileService.getFileList(newCheck.getId(), BUSINESS_TYPE, FILE_TYPE));
      repairService.addSpotCheck(repairParam);
    }
    return update;
  }

  @Override
  public MesSwEquipmentInspectionSpotCheckDTO getCurrentDetail(
      MesSwEquipmentInspectionSpotCheckParam param) {
    MesSwEquipmentInspectionSpotCheckDTO checkDTO = convert.toDTO(this.getById(param.getId()));
    String charterPerson = inspectionService.getById(checkDTO.getInspectionId()).getCharterPerson();
    checkDTO.setEqptPersonJson(charterPerson);
    // 拼接抽检周期
    String yearMonth = checkDTO.getCheckCycle().replace("-", "年");
    checkDTO.setCheckCycleAndWeek(yearMonth + "月 第" + checkDTO.getCheckWeekCode() + "周");
    // 拼接设备编码及型号
    checkDTO.setEqptNoOrName(checkDTO.getEqptNo() + " " + checkDTO.getEqptName());
    // 现场图片
    List<JSONObject> photo = fileService.getFileList(param.getId(), BUSINESS_TYPE, FILE_TYPE);
    checkDTO.setPhoto(photo);
    return checkDTO;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String savePush() {
    LocalDate date = LocalDate.now(); // 获取当前日期
    int day = date.getDayOfMonth(); // 获取当前日期的天数
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
    String currentYearMonth = LocalDate.now().format(formatter);
    LambdaQueryWrapper<MesSwEquipmentInspection> inspectionWrapper = new LambdaQueryWrapper<>();
    if (day <= 6) {
      inspectionWrapper
          .eq(MesSwEquipmentInspection::getThisInspection, currentYearMonth)
          .and(
              i ->
                  i.eq(MesSwEquipmentInspection::getThisInspectionPeriod, 1)
                      .or()
                      .eq(MesSwEquipmentInspection::getThisInspectionPeriod, 5));
    } else if (day > 6 && day <= 13) {
      inspectionWrapper
          .eq(MesSwEquipmentInspection::getThisInspection, currentYearMonth)
          .and(
              i ->
                  i.eq(MesSwEquipmentInspection::getThisInspectionPeriod, 2)
                      .or()
                      .eq(MesSwEquipmentInspection::getThisInspectionPeriod, 5));
    } else if (day > 13 && day < 20) {
      inspectionWrapper
          .eq(MesSwEquipmentInspection::getThisInspection, currentYearMonth)
          .and(
              i ->
                  i.eq(MesSwEquipmentInspection::getThisInspectionPeriod, 3)
                      .or()
                      .eq(MesSwEquipmentInspection::getThisInspectionPeriod, 6));
    } else {
      inspectionWrapper
          .eq(MesSwEquipmentInspection::getThisInspection, currentYearMonth)
          .and(
              i ->
                  i.eq(MesSwEquipmentInspection::getThisInspectionPeriod, 4)
                      .or()
                      .eq(MesSwEquipmentInspection::getThisInspectionPeriod, 6));
    }
    inspectionWrapper.eq(MesSwEquipmentInspection::getStatusCode, 9);
    inspectionWrapper.isNotNull(MesSwEquipmentInspection::getParentId);
    List<MesSwEquipmentInspection> inspections = inspectionService.list(inspectionWrapper);
    // 该月之前已经推送的复检集合
    LambdaQueryWrapper<MesSwEquipmentInspectionSpotCheck> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentInspectionSpotCheck::getCheckCycle, currentYearMonth);
    List<MesSwEquipmentInspectionSpotCheck> spotChecks = this.list(wrapper);
    // 本次新的复检集合
    List<MesSwEquipmentInspectionSpotCheck> spotCheckList = new ArrayList<>();
    // 删除之前已经推送的复检集合，避免重复推送
    inspections.removeIf(
        inspection ->
            spotChecks.stream()
                .anyMatch(spotCheck -> spotCheck.getInspectionId().equals(inspection.getId())));
    // 获取厂长、副厂长对应人员
    List<UserAccountDTO> czAccount = accountService.getCZListByPost("厂长");
    List<UserAccountDTO> fczAccounts = accountService.getCZListByPost("副厂长");
    if (czAccount.size() == 0 || fczAccounts.size() == 0) {
      return "没有厂长或副厂长账号";
    }
    int fczNum = fczAccounts.size();
    if (inspections.size() == 0) {
      return "本周期内没有已经完成巡检的工单";
    } else if (inspections.size() > 0 && inspections.size() <= 20) {
      for (int i = 0; i < inspections.size(); i++) {
        // 厂长
        if (i < CZ_CHECK_NUM) {
          spotCheckList.add(this.saveCZOrFCZ(inspections.get(i), czAccount.get(0), day));
        }
        // 副厂长
        int finalI = i;
        fczAccounts.forEach(
            account -> {
              spotCheckList.add(this.saveCZOrFCZ(inspections.get(finalI), account, day));
            });
      }
    } else if (inspections.size() > 20 && inspections.size() < fczNum * 20 + 10) {
      inspections
          .subList(0, CZ_CHECK_NUM)
          .forEach(
              inspection -> {
                // 厂长账号
                spotCheckList.add(this.saveCZOrFCZ(inspection, czAccount.get(0), day));
              });
      // 此时厂长分过后，剩余的巡检单数量大于副厂长的数量，则副厂长轮流获取
      int i = inspections.size() / FCZ_CHECK_NUM;
      if (i == 1) {
        // 此时代表只够第一个副厂长分，第二、三副厂长倒取值达到覆盖
        inspections
            .subList(0, FCZ_CHECK_NUM)
            .forEach(
                inspection -> {
                  // 副厂长一账号集合
                  spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(0), day));
                });
        inspections
            .subList(inspections.size() - FCZ_CHECK_NUM, inspections.size())
            .forEach(
                inspection -> {
                  // 副厂长二账号集合
                  spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(1), day));
                });
      } else if (i == 2) {
        // 此时代表第一、二个副厂长可以顺序取值，第三副厂长要倒取值
        inspections
            .subList(0, FCZ_CHECK_NUM)
            .forEach(
                inspection -> {
                  // 副厂长一账号集合
                  spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(0), day));
                });
        inspections
            .subList(FCZ_CHECK_NUM, FCZ_CHECK_NUM * 2)
            .forEach(
                inspection -> {
                  // 副厂长二账号集合
                  spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(1), day));
                });
      } else {
        // 此时代表减去厂长的10台才能达到覆盖，所以要从第11台开始取值，并且第三个副厂长要倒取值
        inspections
            .subList(CZ_CHECK_NUM, CZ_CHECK_NUM + FCZ_CHECK_NUM)
            .forEach(
                inspection -> {
                  // 副厂长一账号集合
                  spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(0), day));
                });
        inspections
            .subList(FCZ_CHECK_NUM + FCZ_CHECK_NUM, FCZ_CHECK_NUM + FCZ_CHECK_NUM * 2)
            .forEach(
                inspection -> {
                  // 副厂长二账号集合
                  spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(1), day));
                });
      }
      // 三种情况下，副厂长三都需要倒取值
      inspections
          .subList(inspections.size() - FCZ_CHECK_NUM, inspections.size())
          .forEach(
              inspection -> {
                // 副厂长三账号集合
                spotCheckList.add(this.saveCZOrFCZ(inspection, fczAccounts.get(2), day));
              });
    } else {
      for (int i = 0; i < inspections.size(); i++) {
        if (i < CZ_CHECK_NUM) {
          // 厂长账号
          spotCheckList.add(this.saveCZOrFCZ(inspections.get(i), czAccount.get(0), day));
        } else if (i < CZ_CHECK_NUM + FCZ_CHECK_NUM) {
          // 副厂长一账号集合
          spotCheckList.add(this.saveCZOrFCZ(inspections.get(i), fczAccounts.get(0), day));
        } else if (i < CZ_CHECK_NUM + FCZ_CHECK_NUM * 2) {
          // 副厂长二账号集合
          spotCheckList.add(this.saveCZOrFCZ(inspections.get(i), fczAccounts.get(1), day));
        } else if (i < CZ_CHECK_NUM + FCZ_CHECK_NUM * 3) {
          // 副厂长三账号集合
          spotCheckList.add(this.saveCZOrFCZ(inspections.get(i), fczAccounts.get(2), day));
        }
      }
    }
    boolean saveBatch = super.saveBatch(spotCheckList);
    if (saveBatch) {
      // 向领导推送对应的复检通知
      czAccount.addAll(fczAccounts);
      List<String> names =
          czAccount.stream().map(UserAccountDTO::getUsername).collect(Collectors.toList());
      names.forEach(
          name -> {
            MesNotice notice = this.sendMesNotice(name, date);
            websocketService.sendMessage(notice);
          });
    }
    return "推送完成";
  }

  @Override
  public List<UserAccountDTO> getCZListByPost() {
    List<UserAccountDTO> czList = accountService.getCZListByPost("厂长");
    List<UserAccountDTO> FCZList = accountService.getCZListByPost("副厂长");
    if (czList == null || FCZList == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "未获取到厂长或副厂长账号");
    }
    czList.addAll(FCZList);
    return czList;
  }

  private MesSwEquipmentInspectionSpotCheck saveCZOrFCZ(
      MesSwEquipmentInspection inspection, UserAccountDTO czAccount, int day) {
    MesSwEquipmentInspectionSpotCheck czSpotCheck = new MesSwEquipmentInspectionSpotCheck();
    // 厂长或副厂长账号
    czSpotCheck.setInspectionId(inspection.getId());
    czSpotCheck.setEqptNo(inspection.getEquipmentNo());
    String deviceName = dicService.transform(DEVICE_TYPE, inspection.getEquipmentType());
    czSpotCheck.setEqptName(deviceName);
    czSpotCheck.setCheckCycle(inspection.getThisInspection());
    String period = inspection.getThisInspectionPeriod();
    if ("1".equals(inspection.getThisInspectionPeriod())
        || "2".equals(inspection.getThisInspectionPeriod())
        || "3".equals(inspection.getThisInspectionPeriod())
        || "4".equals(inspection.getThisInspectionPeriod())) {
      czSpotCheck.setCheckWeekCode(period);
    }
    if ("5".equals(period) && day <= 6) {
      czSpotCheck.setCheckWeekCode("1");
    } else if ("5".equals(period) && day > 6 && day <= 13) {
      czSpotCheck.setCheckWeekCode("2");
    } else if ("6".equals(period) && day > 13 && day <= 20) {
      czSpotCheck.setCheckWeekCode("3");
    } else if ("6".equals(period) && day > 20) {
      czSpotCheck.setCheckWeekCode("4");
    }
    czSpotCheck.setCheckStatusCode("待检");
    czSpotCheck.setCheckStatus(dicService.transform(LEADER_CHECK_STATUS, "待检"));
    czSpotCheck.setCheckPerson(czAccount.getUsername());
    return czSpotCheck;
  }

  private MesNotice sendMesNotice(String user, LocalDate date) {
    MesNotice mesNotice = new MesNotice();
    mesNotice.setType(NotifyEnum.NOTIFICATION.getKey());
    mesNotice.setReadState(false);
    mesNotice.setPushState("1");
    mesNotice.setTitle("厂领导抽查复检通知");
    mesNotice.setTargetType(DicConstant.DIC_NOTICE_TYPE_EQUIPMENT_CHECK);
    mesNotice.setTargetTypeName(
        dicService.transform(
            DicConstant.DIC_NOTICE_TYPE, DicConstant.DIC_NOTICE_TYPE_EQUIPMENT_CHECK));
    mesNotice.setContent("请进行本周设备抽查复检");
    mesNotice.setJumpRoute("/equipment/spotCheck");
    mesNotice.setPriority("2");
    mesNotice.setIsDelete(false);
    mesNotice.setTargetId(null);
    mesNotice.setJumpParam(user + "*" + date);
    mesNotice.setReceiverAccountName(accountService.getNameByUserName(user));
    mesNotice.setReceiverAccount(user);
    return mesNotice;
  }

  /** 判断日期是否在开始、结束日期的区间内 */
  private Boolean isWithinDate(LocalDate dateToCheck, LocalDate startDate, LocalDate endDate) {
    return !dateToCheck.isBefore(startDate) && !dateToCheck.isAfter(endDate);
  }
}
