package com.yhd.admin.bms.domain.enums;

import lombok.Getter;

/**
 * 智能分选-亚控点位key枚举类
 *
 * <AUTHOR>
 * @date 2024/1/5 9:36
 */
@Getter
public enum KHSeparationPointKeyEnum {
  HF_310_Auto_MD_YM("HF_310_Auto_MD_YM", "智能模式：原煤"),
  HF_310_Auto_MD_JM("HF_310_Auto_MD_JM", "智能模式：精煤"),

  HF_374_AUTO_MD("HF_374_AUTO_MD", "新主洗：智能模式(精煤)和智能模式(原煤)"),
  HF_310_AUTO_MD("HF_310_AUTO_MD", "旧主洗：智能模式(精煤)和智能模式(原煤)"),

  Load_CON701_InstantCoalAmount("XMSW_Load_CON701_InstantCoalAmount", "精煤产量-取值701(旧主洗=新主洗)"),
  Load_CON802_InstantCoalAmount("XMSW_Load_CON802_InstantCoalAmount", "实际矸石产量-取值802(旧主洗=新主洗)"),
  CON701_Info_ProductToday("XMSW_Siev_CON701_Info_ProductToday", "精煤当日产量(旧主洗=新主洗)"),

  Lump_CON301_Ash("XMSW_Lump_CON301_Ash", "301原煤灰分"),
  Lump_CON701_Ash("XMSW_Lump_CON701_Ash", "701精煤灰分"),
  // 旧主洗
  Lump_CON302_InstantCoalAmount("XMSW_Lump_CON302_InstantCoalAmount", "原煤实时入洗量-取值302(旧主洗)"),
  Lump_CON302_Info_ProductToday("XMSW_Lump_CON302_Info_ProductToday", "原煤当日入洗量-取值302(旧主洗)"),
  Lump_DosingPump321_RunState("XMSW_Lump_DosingPump321_RunState", "321介质添加泵运行状态(旧主洗)"),
  Lump_MSeparator320_RunState("XMSW_Lump_MSeparator320_RunState", "320加介磁选机运行状态(旧主洗)"),
  Lump_DosingPump310_Level("XMSW_Lump_DosingPump310_Level", "310合介桶液位(旧主洗)"),
  Lump_Valve310_MValve("XMSW_Lump_Valve310_MValve", "310合介桶补水阀开度(旧主洗)"),
  Lump_DosingPump313_Level("XMSW_Lump_DosingPump313_Level", "313稀介桶液位(旧主洗)"),
  Lump_Valve313_MValve("XMSW_Lump_Valve313_MValve", "313稀介桶补水阀开度(旧主洗)"),
  Lump_Bucket310_Density("XMSW_Lump_Bucket310_Density", "310合介泵上密度(旧主洗)"),

  Lump_MSeparator314_Magnetic("XMSW_Lump_MSeparator314_Magnetic", "314磁选机(旧主洗)"),
  Lump_MSeparator315_Magnetic("XMSW_Lump_MSeparator315_Magnetic", "315磁选机(旧主洗)"),
  Lump_Bucket310_Magnetic("XMSW_Lump_Bucket310_Magnetic", "310分流箱磁性物含量计(旧主洗)"),

  // 新主洗
  Lump_CON301_InstantCoalAmount("XMSW_Lump_CON301_InstantCoalAmount", "原煤实时入洗量-取值301(新主洗)"),
  Lump_CON301_Info_ProductToday("XMSW_Lump_CON301_Info_ProductToday", "原煤当日入洗量-取值301(新主洗)"),
  Lump_DosingPump422_RunState("XMSW_Lump_DosingPump422_RunState", "422介质添加泵运行状态(新主洗)"),
  Lump_MSeparator397_RunState("XMSW_Lump_MSeparator397_RunState", "397加介磁选机(新主洗)"),
  Lump_DosingPump374_Level("XMSW_Lump_DosingPump374_Level", "374合介桶液位(新主洗)"),
  Lump_Valve374_MValve("XMSW_Lump_Valve374_MValve", "374合介桶补水阀开度/399分流箱分流阀开度(新主洗)"),
  Lump_DosingPump376_Level("XMSW_Lump_DosingPump376_Level", "376稀介桶液位(新主洗)"),
  Lump_Valve376_MValve("XMSW_Lump_Valve376_MValve", "376稀介桶补水阀开度(新主洗)"),
  Lump_Bucket374_Density("XMSW_Lump_Bucket374_Density", "374合介泵上密度(新主洗)"),

  Lump_MSeparator377_Magnetic("XMSW_Lump_MSeparator377_Magnetic", "377磁选机(新主洗)"),
  Lump_MSeparator378_Magnetic("XMSW_Lump_MSeparator378_Magnetic", "378磁选机(新主洗)"),
  Lump_Bucket374_Magnetic("XMSW_Lump_Bucket374_Magnetic", "374分流箱磁性物含量计(新主洗)"),
  ;

  private final String key;
  private final String desc;

  KHSeparationPointKeyEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }
}
