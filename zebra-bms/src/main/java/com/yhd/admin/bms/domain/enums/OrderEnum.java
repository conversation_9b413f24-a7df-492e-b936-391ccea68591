package com.yhd.admin.bms.domain.enums;

import com.baomidou.mybatisplus.core.metadata.OrderItem;

public enum OrderEnum {

    PEAK_QTY_DESCEND("peakQty_descend", OrderItem.desc("peak_qty")),
    PEAK_QTY_ASCEND("peakQty_ascend", OrderItem.asc("peak_qty")),
    NORMAL_QTY_ASCEND("normalQty_ascend", OrderItem.asc("normal_qty")),
    NORMAL_QTY_DESCEND("normalQty_descend", OrderItem.desc("normal_qty")),
    LOW_QTY_ASCEND("lowQty_ascend", OrderItem.asc("low_qty")),
    LOW_QTY_DESCEND("lowQty_descend", OrderItem.desc("low_qty")),
    /**
     * 备品备件
     */
    DEVICE_NO_DESCEND("deviceNo_descend", OrderItem.desc("device_No")),
    DEVICE_NO_ASCEND("deviceNo_ascend", OrderItem.asc("device_No")),
    ASSET_NO_DESCEND("assetNo_ascend", OrderItem.desc("asset_No")),
    ASSET_NO_ASCEND("assetNo_descend", OrderItem.asc("asset_No")),
    ;
    private String key;

    private OrderItem orderItem;

    OrderEnum(String key, OrderItem orderItem) {
        this.key = key;
        this.orderItem = orderItem;
    }

    public static OrderItem getOrderItem(String key) {
        for (OrderEnum order : values()) {
            if (order.key.equals(key)) {
                return order.orderItem;
            }
        }
        return PEAK_QTY_ASCEND.orderItem;
    }

}
