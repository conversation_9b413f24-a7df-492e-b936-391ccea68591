package com.yhd.admin.bms.service.sw.produce;

import com.yhd.admin.bms.domain.vo.sw.dashboard.TimeDataVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwPeAndTotalTimeVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwPeBeltDataVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwPeContrastVO;

import java.util.List;

/**
 * 生产效率分析-业务层接口
 *
 * <AUTHOR>
 * @date 2024/10/27 8:32
 */
public interface MesSwPeAnalysisService {

  /**
   * 获取生产效率趋势(近30天)
   *
   * @return 生产效率趋势列表
   */
  List<TimeDataVO> getDayTrendList();

  /**
   * 获取月生产效率列表(近12月)
   *
   * @return 月生产效率列表
   */
  List<TimeDataVO> getMonthList();

  /**
   * 获取年生产效率
   *
   * @return 月生产效率列表
   */
  MesSwPeContrastVO getYearCount();

  MesSwPeBeltDataVO getBeltData();

  /**
   * 获取生产效率及生产累计时间数据(首页使用)
   *
   * @return 生产效率及生产累计时间数据
   */
  MesSwPeAndTotalTimeVO getPeAndTotalTimeData();
}
