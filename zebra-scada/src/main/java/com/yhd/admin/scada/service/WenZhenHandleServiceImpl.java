package com.yhd.admin.scada.service;

import com.yhd.admin.scada.dao.MesSwEquipmentPointDataDao;
import com.yhd.admin.scada.domain.Enum.RedisKeyEnum;
import com.yhd.admin.scada.domain.dto.MesSwEquipmentPointDTO;
import com.yhd.admin.scada.domain.entity.MesSwEquipmentPointData;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.builtin.Variant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备资料-温振报警点位管理
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@Service
@Slf4j
public class WenZhenHandleServiceImpl implements WenZhenHandleService {

  @Resource private MesSwEquipmentPointDataDao valueDao;
  @Resource private RedisTemplate<String, Object> objectRedisTemplate;
  // 处理温振数据bms接口
  private final String HANDLER_DATA_URL = "/api/job/handlerWenZhenData";

  @Resource private RestTemplate restTemplate;

  @Value("${bms.url}")
  private String bmsUrl;

  @Override
  public void handleMonitored(ManagedDataItem item, DataValue value) {
    Variant val = value.getValue();
    MesSwEquipmentPointDTO pointDTO =
        (MesSwEquipmentPointDTO)
            objectRedisTemplate
                .opsForValue()
                .get(
                    String.format(
                        RedisKeyEnum.WEN_ZHEN.getKey(),
                        item.getReadValueId().getNodeId().toParseableString()));
    if (val.isNotNull()) {
      try {
        MesSwEquipmentPointData data = new MesSwEquipmentPointData();
        data.setPointId(pointDTO.getId());
        data.setPointAddress(pointDTO.getPointAddress());
        data.setPointValue(String.valueOf(val.getValue()));
        data.setValueTime(
            LocalDateTime.ofInstant(
                Instant.ofEpochMilli(value.getServerTime().getJavaTime()), ZoneId.systemDefault()));
        int result = valueDao.insert(data);
        if (result > 0) {
          StringBuffer httpurl = new StringBuffer(bmsUrl).append(HANDLER_DATA_URL);
          Map<String, Object> params = new HashMap<>(2);
          params.put("pointAddress", pointDTO.getPointAddress());
          params.put("id", data.getId());
          HttpHeaders headers = new HttpHeaders();
          headers.add("Content-Type", "application/json");
          HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, headers);
          ResponseEntity<Object> responseEntity =
              restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);
        }
      } catch (Exception e) {
        log.error(">>> insert：{},{}", e.toString());
      }
    }
  }
}
