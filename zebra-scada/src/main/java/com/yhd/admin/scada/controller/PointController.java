package com.yhd.admin.scada.controller;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.scada.boot.WenZhenCoalWashOPCUARunner;
import com.yhd.admin.scada.domain.param.PlcCtrlParam;
import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.Variant;
import org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.UInteger;
import org.eclipse.milo.opcua.stack.core.types.enumerated.TimestampsToReturn;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadValueId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * OPC UA 读取实时点位数据
 *
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/5 11:14
 */
@RestController
@RequestMapping("/point/rt")
public class PointController {

  private final OpcUaClient coalWashGOpcUaClient;
  private final WenZhenCoalWashOPCUARunner coalWashOPCUARunner;

  public PointController(
      @Qualifier("wenZhenOpcUaClient") OpcUaClient coalWashGOpcUaClient,
      WenZhenCoalWashOPCUARunner coalWashOPCUARunner) {
    this.coalWashGOpcUaClient = coalWashGOpcUaClient;
    this.coalWashOPCUARunner = coalWashOPCUARunner;
  }

  //    private final OpcUaClient client;
  //
  //    private final String prefix = "Siemens.S7-1500.";
  //
  //    public PointController( @Qualifier("PLCOpcUaClient") OpcUaClient client) {
  //        this.client = client;
  //    }
  //
  //    @PostMapping(value = "/value", consumes = MediaType.APPLICATION_JSON_VALUE,
  //        produces = MediaType.APPLICATION_JSON_VALUE)
  //    public RespJson<PointValueVO> readNodeValue(@RequestBody VariaNode variaNode) throws
  // UaException {
  //        UaVariableNode node = client.getAddressSpace().getVariableNode(new NodeId(2, prefix +
  // variaNode.getIdentifier()));
  //        DataValue value = node.readValue();
  //        PointValueVO pointValueVO = new PointValueVO(variaNode.getIdentifier(),
  // value.getValue().getValue(),
  //            value.getServerTime().getJavaTime());
  //        return RespJson.buildSuccessResponse(pointValueVO);
  //    }
  //
  //
  //    @PostMapping(value = "/values", consumes = MediaType.APPLICATION_JSON_VALUE,
  //        produces = MediaType.APPLICATION_JSON_VALUE)
  //    public RespJson<List<PointValueVO>> readNodeValues(@RequestBody List<String> nodes) throws
  // ExecutionException,
  //        InterruptedException {
  //        List<NodeId> nodeIds =
  //            nodes.stream().map(o -> new NodeId(2, prefix + o)).collect(Collectors.toList());
  //        CompletableFuture<List<DataValue>> dataValueCF = client.readValues(0,
  // TimestampsToReturn.Both, nodeIds);
  //        List<DataValue> dataValues = dataValueCF.get();
  //        List<PointValueVO> pointValueVOS = Lists.newArrayList();
  //        for (int i = 0; i < nodes.size(); i++) {
  //            DataValue dataValue = dataValues.get(i);
  //            pointValueVOS.add(new PointValueVO(nodes.get(i), dataValue.getValue().getValue(),
  //                dataValue.getServerTime().getJavaTime()));
  //        }
  //
  //        return RespJson.buildSuccessResponse(pointValueVOS);
  //    }

  @PostMapping(
      value = "/restart",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> readNodeValues() {
    try {
      coalWashGOpcUaClient.getSubscriptionManager().clearSubscriptions();
      coalWashOPCUARunner.run();
      return RespJson.buildSuccessResponse(true);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.toString());
    }
  }

  @PostMapping(value = "/read")
  public RespJson<?> read(@RequestBody PlcCtrlParam param) {
    try {
      String point = param.getPoint();
      // 使用 String.split() 将逗号分隔的字符串转换为数组
      String[] array = point.split(",");
      JSONObject json = new JSONObject();
      // 将数组转换为 List
      List<String> list = new ArrayList<>(Arrays.asList(array));
      int size = list.size();
      for (int i = 0; i < size; i++) {
        String item = list.get(i);
        // 读取节点数据
        NodeId nodeId = NodeId.parse(item);
        ReadValueId readValueId = new ReadValueId(nodeId, UInteger.valueOf(13), null, null);

        // 读取节点值
        CompletableFuture<List<DataValue>> future =
            coalWashGOpcUaClient.readValues(0.0, TimestampsToReturn.Both, List.of(nodeId));
        List<DataValue> dataValues = future.get();

        // 处理读取结果
        if (!dataValues.isEmpty()) {
          DataValue dataValue = dataValues.get(0);
          Variant value = dataValue.getValue();
          json.put(item, value.getValue());
        } else {
          json.put(item, null);
        }
      }
      return RespJson.buildSuccessResponse(json);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.toString());
    }
  }
}
