package com.yhd.admin.bms.service.sw.impl.goods;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.goods.MesSwGoodsPlanDao;
import com.yhd.admin.bms.domain.convert.sw.goods.MesSwGoodsPlanConvert;
import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsPlanDetailDTO;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsPlan;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsPlanDetail;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsStock;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsPlanDetailParam;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsPlanParam;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsStockParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.goods.MesSwGoodsSievePlateVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsPlanDetailService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsPlanService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsStockService;
import com.yhd.admin.bms.service.sys.StorageServices;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.common.poi.excel.WorkbookBuilder;
import com.yhd.admin.common.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 筛板物资计划-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/26 15:36
 */
@Service
public class MesSwGoodsPlanServiceImpl extends ServiceImpl<MesSwGoodsPlanDao, MesSwGoodsPlan>
    implements MesSwGoodsPlanService {
    private static final Logger logger = LoggerFactory.getLogger(MesSwGoodsPlanServiceImpl.class);

    private static final String defaultBucketName = "swcenter";

    private final StorageServices storageServices;
    private final MesSwGoodsPlanConvert goodsPlanConvert;
    private final MesSwGoodsPlanDetailService planDetailService;
    private final MesSwGoodsStockService stockService;
    private final UserAccountService accountService;

    public MesSwGoodsPlanServiceImpl(
        StorageServices storageServices,
        MesSwGoodsPlanConvert goodsPlanConvert,
        MesSwGoodsPlanDetailService planDetailService,
        MesSwGoodsStockService stockService,
        UserAccountService accountService) {
        this.storageServices = storageServices;
        this.goodsPlanConvert = goodsPlanConvert;
        this.planDetailService = planDetailService;
        this.stockService = stockService;
        this.accountService = accountService;
    }

    @Override
    public IPage<MesSwGoodsPlanDTO> pagingQuery(MesSwGoodsPlanParam param) {
        Page<MesSwGoodsPlan> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSwGoodsPlan> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        // 参数处理
        // 月份范围
        if (StringUtils.isNotBlank(param.getStartMonth())) {
            queryChain.ge(MesSwGoodsPlan::getMonth, param.getStartMonth());
        }
        if (StringUtils.isNotBlank(param.getEndMonth())) {
            queryChain.le(MesSwGoodsPlan::getMonth, param.getEndMonth());
        }
        // 排序
        queryChain.orderByDesc(MesSwGoodsPlan::getMonth).orderByDesc(MesSwGoodsPlan::getCreatedTime);
        IPage<MesSwGoodsPlanDTO> page = queryChain.page(iPage).convert(goodsPlanConvert::toDTO);
        page.getRecords()
            .forEach(
                v -> {
                    MesSwGoodsPlanDetailParam detailParam = new MesSwGoodsPlanDetailParam();
                    detailParam.setPlanId(v.getId());
                    List<MesSwGoodsPlanDetail> planDetails = planDetailService.queryList(detailParam);
                    v.setCateNum(planDetails.size());
                    UserAccountDTO accountDTO = accountService.currentDetail(v.getCreatedBy());
                    if (Objects.nonNull(accountDTO)){
                        v.setCreateName(accountDTO.getName());
                    }
                });
        return page;
    }

    @Override
    public String exportExcel(MesSwGoodsPlanParam param) {
        logger.debug("请求【导出物资计划excel】接口开始，参数：{}", param.getId());
        // 查询勾选导出计划的详情信息
        MesSwGoodsPlanDTO detail = this.getCurrentDetail(param);
        if (Objects.isNull(detail) || Objects.isNull(detail.getId())) {
            throw new BMSException("error", "未查询到计划详情信息，请检查");
        }
        String month = detail.getMonth();
        String[] split = month.split("-");

        String fileName =
            "洗选中心" + split[0] + "年" + split[1] + "月份月度材料计划-筛板" + IdWorker.getTimeId() + ".xls";

        Workbook workbook = null;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/vnd.ms-excel");
            // 获取模板
            File tplFile =
                ResourceUtils.getFile(
                    "classpath:template/export" + File.separator + "goods_plan_tpl.xls");
            // 读取工作簿
            workbook = WorkbookBuilder.getWorkBook(tplFile);
            // 读取工作簿第一个工作表-向工作表填充数据
            // SheetName
            String sheetName = split[0] + "年" + split[1] + "月";
            workbook.setSheetName(0, sheetName);
            Sheet sheet = workbook.getSheetAt(0);
            // 标题
            String title = split[0] + "年洗选中心" + split[1] + "月份物资需求计划";
            sheet.getRow(0).getCell(0).setCellValue(title);
            // 物资详情数据
            List<MesSwGoodsPlanDetailDTO> dataList = detail.getDetailList();
            if (CollectionUtils.isNotEmpty(dataList)) {
                // 获取工作簿单元格样式
                // 日期列单元格样式
                CellStyle dateCellStyle = sheet.getRow(2).getCell(9).getCellStyle();
                // 获取其他列单元格样式
                CellStyle cellStyle = sheet.getRow(2).getCell(2).getCellStyle();
                float heightInPoints = sheet.getRow(2).getHeightInPoints();
                // 填充数据
                int rowIndex = 2;
                Row row;
                Cell cell;
                for (MesSwGoodsPlanDetailDTO data : dataList) {
                    row = sheet.createRow(rowIndex);
                    row.setHeightInPoints(heightInPoints); // 行高
                    // 序号
                    cell = row.createCell(0);
                    cell.setCellValue(data.getId());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 单位名称
                    cell = row.createCell(1);
                    cell.setCellValue(StringUtils.isBlank(data.getOrgName()) ? "" : data.getOrgName());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // SPA编码
                    cell = row.createCell(2);
                    cell.setCellValue(StringUtils.isBlank(data.getSpa()) ? "" : data.getSpa());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 商品编码
                    cell = row.createCell(3);
                    cell.setCellValue(StringUtils.isBlank(data.getGoodsCode()) ? "" : data.getGoodsCode());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 备件系统描述
                    cell = row.createCell(4);
                    cell.setCellValue(StringUtils.isBlank(data.getGoodsDesc()) ? "" : data.getGoodsDesc());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 单位
                    cell = row.createCell(5);
                    cell.setCellValue(StringUtils.isBlank(data.getGoodsUnit()) ? "" : data.getGoodsUnit());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 数量
                    cell = row.createCell(6);
                    cell.setCellValue(data.getGoodsNum() == null ? "" : String.valueOf(data.getGoodsNum()));
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 单价
                    cell = row.createCell(7);
                    cell.setCellValue(data.getGoodsPrice() == null ? "" : String.valueOf(data.getGoodsPrice()));
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 总价
                    cell = row.createCell(8);
                    cell.setCellValue(data.getTalalValue() == null ? "" : String.valueOf(data.getTalalValue()));
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 到货日期
                    cell = row.createCell(9);
                    cell.setCellValue(
                        Objects.isNull(data.getArrivalDate()) ? "" : data.getArrivalDate().toString());
                    cell.setCellStyle(dateCellStyle); // 单元格样式
                    // 主机信息
                    cell = row.createCell(10);
                    cell.setCellValue(StringUtils.isBlank(data.getMainEngine()) ? "" : data.getMainEngine());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 用途
                    cell = row.createCell(11);
                    cell.setCellValue(StringUtils.isBlank(data.getPurpose()) ? "" : data.getPurpose());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 更换周期
                    cell = row.createCell(12);
                    cell.setCellValue(data.getReplaceCycle() == null ? "" : String.valueOf(data.getReplaceCycle()));
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 计划类型
                    cell = row.createCell(13);
                    cell.setCellValue(StringUtils.isBlank(data.getPlanType()) ? "" : data.getPlanType());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 本厂库存
                    cell = row.createCell(14);
                    cell.setCellValue(data.getStock() == null ? "" : String.valueOf(data.getStock()));
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 物资分类
                    cell = row.createCell(15);
                    cell.setCellValue(StringUtils.isBlank(data.getGoodsType()) ? "" : data.getGoodsType());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 设备类型
                    cell = row.createCell(16);
                    cell.setCellValue(StringUtils.isBlank(data.getEqptType()) ? "" : data.getEqptType());
                    cell.setCellStyle(cellStyle); // 单元格样式
                    // 备注
                    cell = row.createCell(17);
                    cell.setCellValue(StringUtils.isBlank(data.getRemark()) ? "" : data.getRemark());
                    cell.setCellStyle(cellStyle); // 单元格样式

                    rowIndex++;
                }
            }

            workbook.write(outputStream);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            storageServices.uploadObject(defaultBucketName, fileName, headers, inputStream);
            // 关闭文件流
            IOUtils.closeQuietly(inputStream);
        } catch (Exception e) {
            log.error("导出excel异常", e);
            throw new BMSException("error", "导出excel失败");
        } finally {
            if (workbook != null) {
                IOUtils.closeQuietly(workbook);
            }
        }

        return storageServices.getStorageUrl(fileName, defaultBucketName);
    }

    @Override
    public MesSwGoodsPlanDTO getCurrentDetail(MesSwGoodsPlanParam param) {
        logger.debug("请求【查询筛板物资计划详情】接口开始，参数：{}", param.getId());
        MesSwGoodsPlan entity = super.getById(param.getId());
        if (Objects.nonNull(entity) && Objects.nonNull(entity.getId())) {
            // 添加物资计划详情信息
            MesSwGoodsPlanDetailParam detailParam = new MesSwGoodsPlanDetailParam();
            detailParam.setPlanId(entity.getId());
            List<MesSwGoodsPlanDetail> planDetails = planDetailService.queryList(detailParam);
            entity.setDetailList(planDetails);
            entity.setCateNum(planDetails.size());
        }

        return goodsPlanConvert.toDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(MesSwGoodsPlanParam param) {
        logger.debug("请求【新增筛板物资计划】接口开始，表单参数：{}", param);
        MesSwGoodsPlan entity = goodsPlanConvert.toEntity(param);
        boolean save = super.save(entity);
        if (save) {
            // 插入计划物资详情
            if (CollectionUtils.isNotEmpty(entity.getDetailList())) {
                entity.getDetailList().forEach(v -> v.setPlanId(entity.getId()));

                boolean saveBatch = planDetailService.saveBatch(entity.getDetailList());
                if (saveBatch) {
                    logger.debug("批量插入物资计划详情表【成功】，新增筛板物资计划成功");
                }
            }
        }

        return save;
    }

    @Override
    public Boolean createPlan() {
        MesSwGoodsPlanParam plan = new MesSwGoodsPlanParam();
        // 计划表单信息
        // 当前月份
        String month = DateUtil.getSysYearMonth();
        plan.setMonth(month);
        String[] split = month.split("-");
        // 计划名称
        String planName = "洗选中心" + split[0] + "年" + split[1] + "月份月度材料计划";
        plan.setName(planName);
        // 计划物资详情列表
        List<MesSwGoodsPlanDetailParam> detailList = new ArrayList<>();
        // 筛板管理正在使用的物资，且库存<=预警
        List<MesSwGoodsStock> useGoodsList = stockService.getSievePlateUseGoodsList();
        List<MesSwGoodsStock> warnPlanGoods =
            useGoodsList.stream()
                .filter(v -> v.getGoodNum().compareTo(v.getWarnNum()) < 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(warnPlanGoods)) {
            for (MesSwGoodsStock goods : warnPlanGoods) {
                MesSwGoodsPlanDetailParam detailParam = new MesSwGoodsPlanDetailParam();
                // 根据物资id查询筛板情况
                MesSwGoodsStockParam stockParam = new MesSwGoodsStockParam();
                stockParam.setId(goods.getId());
                MesSwGoodsSievePlateVO sievePlate = stockService.getGoodsSievePlateById(stockParam);
                // 单位名称，默认上湾选煤厂
                detailParam.setOrgName("上湾选煤厂");
                // 备件系统描述:默认为物资名称（材料及尺寸）
                detailParam.setGoodsDesc(goods.getName());
                // 物资id
                detailParam.setGoodsId(goods.getId());
                // 物资单位
                detailParam.setGoodsUnit(goods.getUnitName());
                // 到货日期：默认生成日期向后推45天
                detailParam.setArrivalDate(DateUtil.minusDays(LocalDate.now(), -45));
                if (Objects.nonNull(sievePlate)) {
                    // 物资数量：物资筛上量（正在使用的量）的1/2后十位向上取整。如31取40，26.5取30
                    if (Objects.nonNull(sievePlate.getPlanNum())) {
                        detailParam.setGoodsNum(sievePlate.getPlanNum());
                    }
                    // 使用设备编码、用途:【316、317】振动筛共计【288】块筛板使用，月消耗更换【50】块
                    String purpose = "";
                    if (StringUtils.isNotBlank(sievePlate.getEqptText())
                        && Objects.nonNull(sievePlate.getOnSieveNum())
                        && Objects.nonNull(sievePlate.getMonthUseNum())) {
                        purpose =
                            purpose
                                + "【"
                                + sievePlate.getEqptText()
                                + "】振动筛共计"
                                + "【"
                                + sievePlate.getOnSieveNum()
                                + "】块筛板使用，月消耗更换"
                                + "【"
                                + sievePlate.getMonthUseNum()
                                + "】块";
                    }
                    detailParam.setPurpose(purpose);
                    // 更换周期：默认上次此物资提交计划的时间与本次提报日期的时间差。举例：提报周期【50】
                    detailParam.setReplaceCycle(sievePlate.getReplaceCycle());
                }
                // 计划类型
                detailParam.setPlanType("国能煤炭专区");
                // 本厂库存
                detailParam.setStock(goods.getGoodNum().longValue());
                // 物资分类:默认机械配件
                detailParam.setGoodsType("机械配件");
                // 设备类型：振动筛
                detailParam.setEqptType("振动筛");
                detailList.add(detailParam);
            }
        }
        plan.setDetailList(detailList);

        return this.add(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        if (!CollectionUtils.isEmpty(param.getId())) {
            param
                .getId()
                .forEach(
                    planId -> {
                        Map<String, Object> rmMap = new HashMap<>();
                        rmMap.put("plan_id", planId);
                        // 删除操作记录信息
                        planDetailService.removeByMap(rmMap);
                    });
        }
        return super.removeByIds(param.getId());
    }
}
