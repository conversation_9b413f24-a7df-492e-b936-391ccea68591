package com.yhd.admin.bms.domain.vo.sw.produce;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 生产效率-历史数据库-设备报警类型表
 *
 * <AUTHOR>
 * @Date 2025/4/24 14:19
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesEquipmentWarnTypeVO extends BaseVO {
    //报警类型
    private String warnType;
    //该类型条数
    private Integer count;
}
