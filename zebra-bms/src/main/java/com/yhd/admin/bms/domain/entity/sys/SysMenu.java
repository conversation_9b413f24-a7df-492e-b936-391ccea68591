package com.yhd.admin.bms.domain.entity.sys;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysMenu.java
 * @Description TODO
 * @createTime 2020年03月30日 09:57:00
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenu extends BaseEntity implements Serializable, Cloneable {

    /**
     * 父主键
     */
    private Long parentId;

    /**
     * 客户端ID
     */
    private String clientId;
    /**
     * 菜单标识
     */
    @TableField(value = "`key`")
    private String key;
    /**
     * 路径
     */
    private String path;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 国际化
     */
    private String locale;
    /**
     * 图标
     */
    private String icon;
    /**
     * 隐藏菜单;0:显示1:隐藏
     */
    private Boolean hideInMenu;
    /**
     * 隐藏子菜单;0:显示1:隐藏
     */
    private Boolean hideChildrenInMenu;
    /**
     * 权限
     */
    private String authority;

    /**
     * 类型;0：目录 1：菜单 2：按钮
     */
    private String type;
    /**
     * 排序
     */
    private Integer orderNum;
    /**
     * 层级
     */
    private Integer level;
}
