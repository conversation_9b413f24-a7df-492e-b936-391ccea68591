package com.yhd.admin.bms.controller.api.common;

import com.yhd.admin.bms.domain.enums.safe.TsdWorkTicketStatusEnum;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共信息-控制层
 *
 * <AUTHOR>
 * @date 2024/5/28 23:19
 */
@RestController
@RequestMapping("/api/common")
public class MasApiCommonController {

  /** 获取工作票状态列表 */
  @PostMapping(
      value = "/getWorkTicketStatusList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getWorkTicketStatusList() {
    List<String> descList = WorkTicketStatusEnum.getDescList();
    return RespJson.buildSuccessResponse(descList);
  }

  /** 获取外施工单位停送电工作票状态列表 */
  @PostMapping(
      value = "/getTsdWorkTicketStatusList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getTsdWorkTicketStatusList() {
    List<String> descList = TsdWorkTicketStatusEnum.getDescList();
    return RespJson.buildSuccessResponse(descList);
  }
}
