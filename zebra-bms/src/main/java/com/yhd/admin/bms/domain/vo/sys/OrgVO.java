package com.yhd.admin.bms.domain.vo.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgVO extends BaseVO {
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 父级ID
     */
    private Long parentId;
    /**
     * 父级名称
     */
    private String parentName;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 排序
     */
    private Long sortNum;

    private List<OrgVO> children;
}
