package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @<PERSON>
 * @Date 2025/7/1 14:44
 * @Version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafeQuestionNumParam extends QueryParam implements Cloneable, Serializable {
    /**
     *试题标题
     */
    private String title;
    /**
     *单选题数
     */
    private Integer oneNum;
    /**
     *多选题数
     */
    private Integer moreNum;
    /**
     *判断题数
     */
    private Integer ifNum;
}
