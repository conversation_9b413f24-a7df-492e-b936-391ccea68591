package com.yhd.admin.bms.service.sw.impl.load;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.load.LoadDayDao;
import com.yhd.admin.bms.domain.entity.sw.load.LoadDay;
import com.yhd.admin.bms.domain.vo.sw.load.LoadDayCountVO;
import com.yhd.admin.bms.service.sw.load.LoadDayService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日装车统计
 *
 * <AUTHOR>
 * @date 2025/1/4 13:07
 */
@Service
public class LoadDayServiceImpl extends ServiceImpl<LoadDayDao, LoadDay> implements LoadDayService {
  @Override
  public LoadDayCountVO getLoadDayCount() {
    LoadDayCountVO result = new LoadDayCountVO();
    List<LoadDay> list = this.list();
    if (CollectionUtils.isNotEmpty(list)) {
      list.forEach(
          v -> {
            if (v.getTexTitle().equals("日计划")) {
              result.setPlanNum(v.getTexValue());
            } else if (v.getTexTitle().equals("已完成")) {
              result.setFinishNum(v.getTexValue());
            } else if (v.getTexTitle().equals("未完成")) {
              result.setNotFinishNum(v.getTexValue());
            }
          });
    }

    return result;
  }
}
