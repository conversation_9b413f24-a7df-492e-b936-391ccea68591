package com.yhd.admin.common.domain.enums;

import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ExceptionCodeEnum.java
 * @Description
 * @createTime 2020年03月25日 14:34:00
 */
@Getter
@ToString
public enum ExceptionCodeEnum {
  /**
   * 用户不存在，请联系管理员 UsernameNotFoundException
   */
  USER_NAME_NOT_FOUND("User not found", "用户不存在，请联系管理员"),
  /**
   * 密码错误，请重新输入 BadCredentialsException
   */
  BAD_CREDENTIALS("Bad credentials", "密码错误，请重新输入"),
  /**
   * 账号过期，请联系管理员 AccountExpiredException
   */
  ACCOUNT_EXPIRED("User account has expired", "账号过期，请联系管理员"),
  /**
   * 账户被锁定，请联系管理员 LockedException
   */
  LOCKED("User account is locked", "账户被锁定，请联系管理员"),
  /**
   * 密码过期，请联系管理员 CredentialsExpiredException
   */
  CREDENTIALS_EXPIRED("User credentials have expired", "密码过期，请联系管理员"),
  /**
   * 账户被禁用，请联系管理员 DisabledException
   */
  DISABLED("User is disabled", "账户被禁用，请联系管理员"),

  OTHER("OTHER", "系统异常，请联系管理员");

  private String code;
  private String desc;

  private ExceptionCodeEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public static ExceptionCodeEnum getEnumByCode(String code) {
    for (ExceptionCodeEnum exception : ExceptionCodeEnum.values()) {
      if (StringUtils.equals(code, exception.code)) {
        return exception;
      }
    }
    return null;
  }
}
