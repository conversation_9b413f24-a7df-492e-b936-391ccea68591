package com.yhd.admin.passport.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.admin.passport.domain.entity.SysRole;
import com.yhd.admin.passport.domain.query.UserAccountParam;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleDao.java
 * @Description TODO 账户角色
 * @createTime 2020年03月20日 10:17:00
 */
public interface RoleDao extends BaseMapper<SysRole> {

  /**
   * 根据用户账户查询对应的角色
   *
   * @param param
   * @return
   */
  List<SysRole> selectRoleListByUser(UserAccountParam param);

  /**
   * 根据角色查询授权的菜单权限。
   *
   * @param rowList 角色 ID
   * @return 菜单ID
   */
  List<Long> selectMenuByRole(List<Long> rowList);

  /**
   * 根据菜单ID，查询自身节点和父节点权限。
   *
   * @param menuIdList 菜单ID集合
   * @return 菜单权限
   */
  List<String> selectAuthorityById(List<Long> menuIdList);
}
