package com.yhd.admin.bms.dao.flowable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.bms.domain.vo.flowable.ProcessInstanceQueryVo;
import com.yhd.admin.bms.domain.vo.flowable.ProcessInstanceVo;
import org.apache.ibatis.annotations.Param;

public interface IFlowableProcessInstanceDao {

  /**
   * 通过条件查询流程实例VO对象列表 查询我的发起流程
   *
   * @param page
   * @param params 参数
   * @return
   */
  public IPage<ProcessInstanceVo> getPagerModel(
      Page<ProcessInstanceVo> page, @Param("params") ProcessInstanceQueryVo params);
}
