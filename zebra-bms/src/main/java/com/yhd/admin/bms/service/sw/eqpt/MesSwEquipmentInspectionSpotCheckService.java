package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionSpotCheckDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspectionSpotCheck;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionSpotCheckParam;
import com.yhd.admin.common.domain.dto.UserAccountDTO;

import java.util.List;


/**
 * 厂领导抽查复检
 *
 * <AUTHOR>
 * @since 1.0.0 2024-08-19
 */
public interface MesSwEquipmentInspectionSpotCheckService extends IService<MesSwEquipmentInspectionSpotCheck> {

    IPage<MesSwEquipmentInspectionSpotCheckDTO> pagingQuery(MesSwEquipmentInspectionSpotCheckParam queryParam);

    Boolean check(MesSwEquipmentInspectionSpotCheckParam param);

    MesSwEquipmentInspectionSpotCheckDTO getCurrentDetail(MesSwEquipmentInspectionSpotCheckParam param);

    String savePush();

    List<UserAccountDTO> getCZListByPost();
}
