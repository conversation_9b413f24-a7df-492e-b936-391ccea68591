package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesCcmBbiDTO;
import com.yhd.admin.bms.domain.entity.sw.MesCcmBbi;
import com.yhd.admin.bms.domain.query.sw.MesCcmBbiParam;
import com.yhd.admin.bms.domain.vo.sw.MesCcmBbiVO;
import org.mapstruct.Mapper;


@Mapper(componentModel = "spring")
public interface MesCcmBbiConvert {
    MesCcmBbiVO toVO(MesCcmBbiDTO dto);

    MesCcmBbiDTO toDTO(MesCcmBbi entity);

    MesCcmBbi toEntity(MesCcmBbiParam param);

}
