package com.yhd.admin.bms.domain.convert.sw.ticket;

import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketLiftSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketLiftSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketLiftSignUserParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketLiftSignUserVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
* 吊装作业许可证-签字表
*
* <AUTHOR>
* @since 1.0.0 2024-10-28
*/
@Mapper(componentModel = "spring")
public interface MesSwWorkTicketLiftSignUserConvert {

    MesSwWorkTicketLiftSignUser toEntity(MesSwWorkTicketLiftSignUserParam param);

    MesSwWorkTicketLiftSignUserVO toVO(MesSwWorkTicketLiftSignUserDTO dto);

    MesSwWorkTicketLiftSignUserDTO toDTO(MesSwWorkTicketLiftSignUser entity);

    List<MesSwWorkTicketLiftSignUserDTO> toDTOs(List<MesSwWorkTicketLiftSignUser> entity);

    List<MesSwWorkTicketLiftSignUser> toEntitys(List<MesSwWorkTicketLiftSignUserParam> param);

    List<MesSwWorkTicketLiftSignUserVO> toVOs(List<MesSwWorkTicketLiftSignUserDTO> dto);

}
