package com.yhd.admin.bms.domain.vo.sys;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysDicDTO.java
 * @Description 字典表
 * @createTime 2020年05月20日 14:11:00
 */
public class DicVO extends BaseVO implements Serializable, Cloneable {

    /**
     * 字典名称
     */
    private String name;
    /**
     * 字典类别
     */
    private String category;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 排序
     */
    private Long orderNum;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return "SysDicDTO{" + "name='" + name + '\'' + ", category='" + category + '\'' + ", status="
            + status
            + ", orderNum='" + orderNum + '\'' + '}';
    }
}
