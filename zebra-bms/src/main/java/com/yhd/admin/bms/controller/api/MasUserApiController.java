package com.yhd.admin.bms.controller.api;

import com.yhd.admin.bms.domain.dto.sys.RoleDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.query.sys.UserAccountParam;
import com.yhd.admin.bms.domain.query.sys.UserParam;
import com.yhd.admin.bms.domain.vo.api.TsdUserParamVO;
import com.yhd.admin.bms.domain.vo.api.TsdUserVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sys.RoleService;
import com.yhd.admin.bms.service.sys.UserService;
import com.yhd.admin.common.domain.RespJson;
import liquibase.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/15 10:32
 */
@RestController
@RequestMapping("/api/user")
@Slf4j
public class MasUserApiController {
  @Autowired private ResourceServerTokenServices tokenServices;
  @Autowired private UserService userService;
  @Autowired private RoleService roleService;

  /** 根据token获取用户信息 */
  @PostMapping(
      value = "/getUser",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getLoginUser(@RequestBody TsdUserParamVO param) {
    String accessToken = param.getAccessToken();
    if (StringUtils.isBlank(accessToken)) {
      log.error("请求参数：accessToken，必填");
      throw new BMSException("error", "请求参数：accessToken，必填");
    }
    TsdUserVO tsdUserVO = new TsdUserVO();
    // 先根据token获取用户名称
    OAuth2Authentication authentication = tokenServices.loadAuthentication(accessToken);
    String username = authentication.getName();
    if (StringUtils.isBlank(username)) {
      log.error("token解析失败，获取的username为null");
      throw new BMSException("error", "token解析失败，获取的username为null");
    }

    // 查询用户停送电角色列表
    UserAccountParam accountParam = new UserAccountParam();
    accountParam.setUsername(username);
    List<RoleDTO> roleAllList = roleService.getRoleByUser(accountParam);
    List<String> roleList =
        roleAllList.stream()
            .map(RoleDTO::getRoleCode)
            .filter(roleCode -> roleCode.startsWith("TSD_"))
            .map(str -> "ROLE_" + str)
            .distinct()
            .collect(Collectors.toList());
    tsdUserVO.setTsdRoles(roleList);
    // 查询用户信息：用户名，密码
    UserParam userParam = new UserParam();
    userParam.setAccountName(username);
    UserDTO userDTO = userService.getUser(userParam);
    if (Objects.isNull(userDTO)) {
      log.error("用户名：{}，的用户数据不存在，请检查", username);
      throw new BMSException("error", "用户不存在，请检查l");
    }
    tsdUserVO.setUsername(username);
    // 提供给第三方的停送电操作密码密文
    tsdUserVO.setTsdOperatePassword(
        MD5Util.computeMD5(MD5Util.computeMD5(userDTO.getTsdOperatePassword()) + username));

    return RespJson.buildSuccessResponse(tsdUserVO);
  }

  /** 停送电操作密码校验 */
  @PostMapping(
      value = "/checkTsdPassword",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> checkTsdPassword(@RequestBody TsdUserParamVO param) {
    if (StringUtils.isBlank(param.getUsername())
        || StringUtils.isBlank(param.getTsdOperatePassword())) {
      log.error("请求参数不为空");
      throw new BMSException("error", "请求参数不为空");
    }
    boolean result = false;
    // 查询用户信息：用户名，密码
    UserParam userParam = new UserParam();
    userParam.setAccountName(param.getUsername());
    UserDTO userDTO = userService.getUser(userParam);
    if (Objects.isNull(userDTO)) {
      log.error("用户名：{}，的用户数据不存在，请检查", param.getUsername());
      throw new BMSException("error", "用户不存在，请检查l");
    }
    String password = param.getTsdOperatePassword();
    String md5Password = MD5Util.computeMD5(MD5Util.computeMD5(password));
    if (md5Password.equals(userDTO.getTsdOperatePassword())) {
      result = true;
    }

    return RespJson.buildSuccessResponse(result);
  }
}
