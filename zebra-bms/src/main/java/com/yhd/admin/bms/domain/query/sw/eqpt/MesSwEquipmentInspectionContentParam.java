package com.yhd.admin.bms.domain.query.sw.eqpt;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备巡检表-巡检内容的数据字段
 *
 * <AUTHOR>
 * @date 2023/12/01 18:22
 */
@Data
public class MesSwEquipmentInspectionContentParam implements Cloneable, Serializable {
  /** 序号 */
  private String num;
  /** 项目 */
  private String project;
  /** 项目-类型 */
  private String type;
  /** 巡检内容 */
  private String content;
  /** 标准 */
  private String criterion;
  /** 巡检周期 */
  private String period;
  /** 设备 */
  private String value;
  /** 单位 */
  private String unit;
  /** 工种code */
  private String nodeCode;
  /** 工种name */
  private String nodeName;
}
