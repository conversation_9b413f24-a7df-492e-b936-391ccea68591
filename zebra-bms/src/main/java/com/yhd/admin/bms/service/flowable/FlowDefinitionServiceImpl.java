package com.yhd.admin.bms.service.flowable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.bms.domain.convert.flowable.ProcessDefinitionConvert;
import com.yhd.admin.bms.domain.query.flowable.FlowDefinitionParam;
import com.yhd.admin.bms.domain.query.flowable.ModelParam;
import com.yhd.admin.bms.domain.vo.flowable.ProcessDefinitionVo;
import com.yhd.admin.bms.service.sys.StorageServices;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class FlowDefinitionServiceImpl implements FlowDefinitionService {
  @Resource private RepositoryService repositoryService;

  @Resource private ProcessDefinitionConvert processDefinitionConvert;

  @Resource private StorageServices storageServices;

  private static final String BPMN_FILE_SUFFIX = ".bpmn";

  @Override
  public IPage<ProcessDefinitionVo> pagingQuery(FlowDefinitionParam param) {
    int pageNum = param.getCurrent() == null ? 1 : param.getCurrent().intValue();
    int pageSize = param.getPageSize() == null ? 10 : param.getPageSize().intValue();
    IPage<ProcessDefinitionVo> page = new Page<>(pageNum, pageSize);
    List<ProcessDefinitionVo> result = new ArrayList<>();

    List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery().list();
    int listSize = list.size();
    int index = pageNum > 1 ? (pageNum - 1) * pageSize : 0;
    for (int i = 0; i < pageSize && index + i < listSize; i++) {
      ProcessDefinition item = list.get(index + i);
      result.add(processDefinitionConvert.toVO(item));
    }
    page.setRecords(result);
    page.setTotal(listSize);
    return page;
  }

  @Override
  public void importFile(ModelParam param) {
    InputStream in = null;
    try {
      in = storageServices.getObject("", param.getFile());
      // 添加隔离信息
      String tenantId = "flow";
      Deployment deploy =
          repositoryService
              .createDeployment()
              .addInputStream(param.getName() + BPMN_FILE_SUFFIX, in)
              .name(param.getName())
              .key(param.getKey())
              .category(param.getCategory())
              .tenantId(tenantId)
              .deploy();
      ProcessDefinition definition =
          repositoryService
              .createProcessDefinitionQuery()
              .deploymentId(deploy.getId())
              .singleResult();
      repositoryService.setProcessDefinitionCategory(definition.getId(), param.getCategory());
    } catch (Exception e) {
      log.error("导入失败:", e);
      e.printStackTrace();
      RespJson.buildSuccessResponse(e.getMessage());
    } finally {
      try {
        if (in != null) {
          in.close();
        }
      } catch (IOException e) {
        log.error("关闭流错误");
      }
    }
  }
}
