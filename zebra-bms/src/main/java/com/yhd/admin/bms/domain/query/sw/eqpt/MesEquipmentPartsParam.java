package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 设备健康中心-设备部件管理
 *
 * <AUTHOR>
 * @date 2025/4/24 10:22
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesEquipmentPartsParam extends QueryParam implements Cloneable, Serializable {

    /** 主键id列表 */
    private List<Long> ids;
    /** 设备id */
    private Long equipmentDataId;
    /** 设备编码+类别 */
    private String equipmentNoType;
    /** 设备名称 */
    private String equipmentName;
    /** 设备编码 */
    private String equipmentNo;
    /** 设备类型 */
    private String equipmentType;
    /** 设备类型代码 */
    private String equipmentTypeCode;
    /** 设备部件名称 */
    private String partsName;
    /** 规格型号 */
    private String model;
    /** 主要参数 */
    private String mainParameter;
    /** 功率（kw） */
    private String power;
    /** 生产厂家 */
    private String factory;
    /** 生产日期(yyyy-MM-dd) */
    private LocalDate produceDate;
    /** 更换日期 */
    private LocalDate changeDate;
    /** 国产进口 */
    private String domesticImports;
    /** 是否排除设备本身 */
    private Boolean excludeEquipment;

}
