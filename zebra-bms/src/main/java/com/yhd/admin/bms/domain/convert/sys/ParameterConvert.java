package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.ParameterDTO;
import com.yhd.admin.bms.domain.entity.sys.SysParameter;
import com.yhd.admin.bms.domain.query.sys.ParameterParam;
import com.yhd.admin.bms.domain.vo.sys.ParameterVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ParameterConvert.java
 * @Description TODO
 * @createTime 2020年05月12日 15:05:00
 */
@Mapper(componentModel = "spring")
public interface ParameterConvert {

    ParameterDTO toDTO(SysParameter parameter);

    ParameterDTO toDTO(ParameterParam parameter);

    SysParameter toEntity(ParameterDTO parameterDTO);

    SysParameter toEntity(ParameterParam parameterDTO);

    ParameterVO toVO(ParameterDTO parameterDTO);

}
