package com.yhd.admin.bms.service.flowable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.bms.constant.FlowConstant;
import com.yhd.admin.bms.dao.flowable.IFlowableTaskDao;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.enums.flowable.CommentTypeEnum;
import com.yhd.admin.bms.domain.query.sys.UserParam;
import com.yhd.admin.bms.domain.vo.flowable.*;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sys.UserService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import liquibase.pro.packaged.L;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

@Service
public class IFlowableTaskServiceImpl extends BaseProcessService implements IFlowableTaskService {
    @Resource
    private IFlowableBpmnModelService flowableBpmnModelService;
    @Resource
    private IFlowableTaskDao flowableTaskDao;
    @Resource
    private UserService userService;

    @Override
    @Transactional
    public RespJson<String> backToStepTask(BackTaskVo backTaskVo) {
        RespJson<String> returnVo = null;
        TaskEntity taskEntity =
            (TaskEntity) taskService.createTaskQuery().taskId(backTaskVo.getTaskId()).singleResult();
        // 1.把当前的节点设置为空
        if (taskEntity != null) {
            //      // 2.设置审批人
            //      taskEntity.setAssignee(backTaskVo.getUserCode());
            //      taskService.saveTask(taskEntity);
            // 3.添加驳回意见
            this.addComment(
                backTaskVo.getTaskId(),
                backTaskVo.getUserCode(),
                backTaskVo.getProcessInstanceId(),
                CommentTypeEnum.BH.toString(),
                backTaskVo.getMessage());
            // 4.处理提交人节点
            FlowNode distActivity =
                flowableBpmnModelService.findFlowNodeByActivityId(
                    taskEntity.getProcessDefinitionId(), backTaskVo.getDistFlowElementId());
            if (distActivity != null) {
                if (FlowConstant.FLOW_SUBMITTER.equals(distActivity.getName())) {
                    ProcessInstance processInstance =
                        runtimeService
                            .createProcessInstanceQuery()
                            .processInstanceId(taskEntity.getProcessInstanceId())
                            .singleResult();
                    runtimeService.setVariable(
                        backTaskVo.getProcessInstanceId(),
                        FlowConstant.FLOW_SUBMITTER_VAR,
                        processInstance.getStartUserId());
                }
                if (FlowConstant.FLOW_SUBMITTER_DIANGONG.equals(distActivity.getName())) {
                    ProcessInstance processInstance =
                        runtimeService
                            .createProcessInstanceQuery()
                            .processInstanceId(taskEntity.getProcessInstanceId())
                            .singleResult();
                    runtimeService.setVariable(
                        backTaskVo.getProcessInstanceId(),
                        FlowConstant.FLOW_SUBMITTER_VAR,
                        processInstance.getStartUserId());
                }
            }
            // 5.删除节点
            this.deleteActivity(backTaskVo.getDistFlowElementId(), taskEntity.getProcessInstanceId());
            List<String> executionIds = new ArrayList<>();
            // 6.判断节点是不是子流程内部的节点
            if (flowableBpmnModelService.checkActivitySubprocessByActivityId(
                taskEntity.getProcessDefinitionId(), backTaskVo.getDistFlowElementId())
                && flowableBpmnModelService.checkActivitySubprocessByActivityId(
                taskEntity.getProcessDefinitionId(), taskEntity.getTaskDefinitionKey())) {
                // 6.1 子流程内部驳回
                Execution executionTask =
                    runtimeService
                        .createExecutionQuery()
                        .executionId(taskEntity.getExecutionId())
                        .singleResult();
                String parentId = executionTask.getParentId();
                List<Execution> executions =
                    runtimeService.createExecutionQuery().parentId(parentId).list();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                this.moveExecutionsToSingleActivityId(executionIds, backTaskVo.getDistFlowElementId());
            } else {
                // 6.2 普通驳回
                List<Execution> executions =
                    runtimeService
                        .createExecutionQuery()
                        .parentId(taskEntity.getProcessInstanceId())
                        .list();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                this.moveExecutionsToSingleActivityId(executionIds, backTaskVo.getDistFlowElementId());
            }
            returnVo = RespJson.buildSuccessResponse("驳回成功");
        } else {
            returnVo = RespJson.buildFailureResponse("不存在任务实例,请确认!");
        }
        return returnVo;
    }

    @Override
    public List<FlowNodeVo> getBackNodesByProcessInstanceId(FlowTaskParam flowTaskParam) {
        List<FlowNodeVo> backNods = new ArrayList<>();
        TaskEntity taskEntity =
            (TaskEntity) taskService.createTaskQuery().taskId(flowTaskParam.getTaskId()).singleResult();
        String currActId = taskEntity.getTaskDefinitionKey();
        // 获取运行节点表中usertask
        String sql =
            "select t.* from ACT_RU_ACTINST t where t.ACT_TYPE_ = 'userTask' "
                + " and t.PROC_INST_ID_=#{processInstanceId} and t.END_TIME_ is not null ";
        List<ActivityInstance> activityInstances =
            runtimeService
                .createNativeActivityInstanceQuery()
                .sql(sql)
                .parameter("processInstanceId", flowTaskParam.getProcessInstanceId())
                .list();
        // 获取运行节点表的parallelGateway节点并出重
        sql =
            "SELECT any_value(t.ID_) AS ID_, any_value(t.REV_) AS REV_, any_value(t.PROC_DEF_ID_) AS PROC_DEF_ID_, any_value(t.PROC_INST_ID_) AS PROC_INST_ID_, "
                + " any_value(t.EXECUTION_ID_)AS EXECUTION_ID_, t.ACT_ID_ AS ACT_ID_, any_value(t.TASK_ID_) AS TASK_ID_, any_value(t.CALL_PROC_INST_ID_) AS CALL_PROC_INST_ID_, "
                + " any_value(t.ACT_NAME_) AS ACT_NAME_, any_value(t.ACT_TYPE_) AS ACT_TYPE_, any_value(t.ASSIGNEE_) AS ASSIGNEE_, "
                + " any_value(t.START_TIME_) AS START_TIME_, any_value(max(t.END_TIME_)) AS END_TIME_, any_value(t.DURATION_) AS DURATION_, any_value(t.DELETE_REASON_) AS DELETE_REASON_, any_value(t.TENANT_ID_) AS TENANT_ID_ "
                + " FROM  ACT_RU_ACTINST t WHERE t.ACT_TYPE_ = 'parallelGateway' AND t.PROC_INST_ID_ = #{processInstanceId} and t.END_TIME_ is not null"
                + " and t.ACT_ID_ <> #{actId} GROUP BY t.ACT_ID_";
        List<ActivityInstance> parallelGatewaies =
            runtimeService
                .createNativeActivityInstanceQuery()
                .sql(sql)
                .parameter("processInstanceId", flowTaskParam.getProcessInstanceId())
                .parameter("actId", currActId)
                .list();
        // 排序
        if (CollectionUtils.isNotEmpty(parallelGatewaies)) {
            activityInstances.addAll(parallelGatewaies);
            activityInstances.sort(Comparator.comparing(ActivityInstance::getEndTime));
        }
        // 分组节点
        int count = 0;
        Map<ActivityInstance, List<ActivityInstance>> parallelGatewayUserTasks = new HashMap<>();
        List<ActivityInstance> userTasks = new ArrayList<>();
        ActivityInstance currActivityInstance = null;
        for (ActivityInstance activityInstance : activityInstances) {
            if (BpmnXMLConstants.ELEMENT_GATEWAY_PARALLEL.equals(activityInstance.getActivityType())) {
                count++;
                if (count % 2 != 0) {
                    List<ActivityInstance> datas = new ArrayList<>();
                    currActivityInstance = activityInstance;
                    parallelGatewayUserTasks.put(currActivityInstance, datas);
                }
            }
            if (BpmnXMLConstants.ELEMENT_TASK_USER.equals(activityInstance.getActivityType())) {
                if (count % 2 == 0) {
                    userTasks.add(activityInstance);
                } else {
                    if (parallelGatewayUserTasks.containsKey(currActivityInstance)) {
                        parallelGatewayUserTasks.get(currActivityInstance).add(activityInstance);
                    }
                }
            }
        }
        // 组装人员名称
        List<HistoricTaskInstance> historicTaskInstances =
            historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(flowTaskParam.getProcessInstanceId())
                .finished()
                .list();
        ArrayList<HistoricTaskInstance> collect =
            historicTaskInstances.stream()
                .collect(
                    collectingAndThen(
                        toCollection(
                            () -> new TreeSet<>(Comparator.comparing(HistoricTaskInstance::getName))),
                        ArrayList::new));
        // TODO 多次转换
        List<HistoricTaskInstance> collectList =
            collect.stream()
                .filter(((HistoricTaskInstance h) -> h.getDeleteReason() == null))
                .collect(Collectors.toList());
        Map<String, List<HistoricTaskInstance>> taskInstanceMap = new HashMap<>();
        List<String> userCodes = new ArrayList<>();
        collectList.forEach(
            historicTaskInstance -> {
                userCodes.add(historicTaskInstance.getAssignee());
                String taskDefinitionKey = historicTaskInstance.getTaskDefinitionKey();
                if (taskInstanceMap.containsKey(historicTaskInstance.getTaskDefinitionKey())) {
                    taskInstanceMap.get(taskDefinitionKey).add(historicTaskInstance);
                } else {
                    List<HistoricTaskInstance> tasks = new ArrayList<>();
                    tasks.add(historicTaskInstance);
                    taskInstanceMap.put(taskDefinitionKey, tasks);
                }
            });
        //    List<User> userList = identityService.createUserQuery().userIds(userCodes).list();
        // TODO 组装usertask的数据
        Map<String, String> activityIdUserNames =
            this.getApplyers(flowTaskParam.getProcessInstanceId(), userCodes, taskInstanceMap);
        if (CollectionUtils.isNotEmpty(userTasks)) {
            userTasks.forEach(
                activityInstance -> {
                    FlowNodeVo node = new FlowNodeVo();
                    node.setNodeId(activityInstance.getActivityId());
                    node.setNodeName(activityInstance.getActivityName());
                    node.setEndTime(activityInstance.getEndTime());
                    node.setUserName(activityIdUserNames.get(activityInstance.getActivityId()));
                    backNods.add(node);
                });
        }
        // 组装会签节点数据
        if (MapUtils.isNotEmpty(taskInstanceMap)) {
            parallelGatewayUserTasks.forEach(
                (activity, activities) -> {
                    FlowNodeVo node = new FlowNodeVo();
                    node.setNodeId(activity.getActivityId());
                    node.setEndTime(activity.getEndTime());
                    StringBuffer nodeNames = new StringBuffer("会签:");
                    StringBuffer userNames = new StringBuffer("审批人员:");
                    if (CollectionUtils.isNotEmpty(activities)) {
                        activities.forEach(
                            activityInstance -> {
                                nodeNames.append(activityInstance.getActivityName()).append(",");
                                userNames
                                    .append(activityIdUserNames.get(activityInstance.getActivityId()))
                                    .append(",");
                            });
                        node.setNodeName(nodeNames.toString());
                        node.setUserName(userNames.toString());
                        backNods.add(node);
                    }
                });
        }
        // 去重合并
        List<FlowNodeVo> datas =
            backNods.stream()
                .collect(
                    collectingAndThen(
                        toCollection(
                            () -> new TreeSet<>(Comparator.comparing(nodeVo -> nodeVo.getNodeId()))),
                        ArrayList::new));

        // 排序
        datas.sort(Comparator.comparing(FlowNodeVo::getEndTime));
        return datas;
    }

    @Override
    public RespJson<String> complete(CompleteTaskVo params) {
        RespJson<String> returnVo = RespJson.buildSuccessResponse("审批成功");
        if (StringUtils.isNotBlank(params.getProcessInstanceId())
            && StringUtils.isNotBlank(params.getTaskId())) {
            // 1.查看当前任务是存在
            TaskEntity taskEntity =
                (TaskEntity) taskService.createTaskQuery().taskId(params.getTaskId()).singleResult();
            if (taskEntity != null) {
                String taskId = params.getTaskId();
                // 2.委派处理
                if (DelegationState.PENDING.equals(taskEntity.getDelegationState())) {
                    // 2.1生成历史记录
                    TaskEntity task = this.createSubTask(taskEntity, params.getUserCode());
                    taskService.complete(task.getId());
                    taskId = task.getId();
                    // 2.2执行委派
                    taskService.resolveTask(params.getTaskId(), params.getVariables());
                } else {
                    // todo 检修类停电管理
                    Map<String, Object> variables = new HashMap<>();
                    if (params.getCentraliController() != null) {
                        variables.put("centralized", params.getCentraliController());
                    }
                    List<String> userList = new ArrayList<>();
                    // 审批多实例
                    if (!CollectionUtils.isEmpty(params.getElectrician())) {
                        userList.addAll(params.getElectrician());
                        variables.put("userList", userList);
                    }
                    // todo 电气类停电管理
                    if (params.getEleManager() != null) {
                        variables.put("approvalman", params.getEleManager());
                    }
                    List<String> formUserList = new ArrayList<>();
                    // 审批多实例
                    if (!CollectionUtils.isEmpty(params.getDqelectrician())) {
                        formUserList.addAll(params.getDqelectrician());
                        variables.put("form_userList", formUserList);
                    }
                    params.setVariables(variables);

                    // 3.1修改执行人 其实我这里就相当于签收了
                    taskService.setAssignee(params.getTaskId(), params.getUserCode());
                    // 3.2执行任务
                    taskService.complete(params.getTaskId(), params.getVariables());
                    // 4.处理加签父任务
                    String parentTaskId = taskEntity.getParentTaskId();
                    if (StringUtils.isNotBlank(parentTaskId)) {
                        String tableName = managementService.getTableName(TaskEntity.class);
                        String sql =
                            "select count(1) from " + tableName + " where PARENT_TASK_ID_=#{parentTaskId}";
                        long subTaskCount =
                            taskService
                                .createNativeTaskQuery()
                                .sql(sql)
                                .parameter("parentTaskId", parentTaskId)
                                .count();
                        if (subTaskCount == 0) {
                            Task task = taskService.createTaskQuery().taskId(parentTaskId).singleResult();
                            // 处理前后加签的任务
                            taskService.resolveTask(parentTaskId);
                            if (FlowConstant.AFTER_ADDSIGN.equals(task.getScopeType())) {
                                taskService.complete(parentTaskId);
                            }
                        }
                    }
                }
                String type = params.getType() == null ? CommentTypeEnum.TY.toString() : params.getType();
                // 5.生成审批意见
                this.addComment(
                    taskId, params.getUserCode(), params.getProcessInstanceId(), type, params.getMessage());
            } else {
                returnVo = RespJson.buildFailureResponse("没有此任务，请确认!");
            }
        } else {
            returnVo = RespJson.buildFailureResponse("请输入正确的参数!");
        }
        return returnVo;
    }

    @Override
    public List<String> getApprovers(String processInstanceId) {
        List<String> users = new ArrayList<>();
        List<Task> list = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(
                task -> {
                    if (StringUtils.isNotBlank(task.getAssignee())) {
                        // 1.审批人ASSIGNEE_是用户id
                        //              User user =
                        //
                        // identityService.createUserQuery().userId(task.getAssignee()).singleResult();
                        //              if (user != null) {
                        users.add(task.getAssignee());
                        //              }
                        //              // 2.审批人ASSIGNEE_是组id
                        //              List<User> gusers =
                        //
                        // identityService.createUserQuery().memberOfGroup(task.getAssignee()).list();
                        //              if (CollectionUtils.isNotEmpty(gusers)) {
                        //                users.addAll();
                        //              }
                    } else {
                        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                        if (CollectionUtils.isNotEmpty(identityLinks)) {
                            identityLinks.forEach(
                                identityLink -> {
                                    // 3.审批人ASSIGNEE_为空,用户id
                                    if (StringUtils.isNotBlank(identityLink.getUserId())) {
                                        //                        User user =
                                        //                            identityService
                                        //                                .createUserQuery()
                                        //                                .userId(identityLink.getUserId())
                                        //                                .singleResult();
                                        //                        if (user != null) {
                                        users.add(identityLink.getUserId());
                                        //                        }
                                    } else {
                                        // 4.审批人ASSIGNEE_为空,组id
                                        //                        List<User> gusers =
                                        //                            identityService
                                        //                                .createUserQuery()
                                        //                                .memberOfGroup(identityLink.getGroupId())
                                        //                                .list();
                                        //                        if (CollectionUtils.isNotEmpty(gusers)) {
                                        //                        users.addAll(identityLink.getGroupId());
                                        //                        }
                                    }
                                });
                        }
                    }
                });
        }
        return users;
    }

    @Override
    public PageRespJson<TaskVo> getApplyingTasks(TaskQueryVo params) {
        int pageNum = params.getCurrent() == null ? 1 : params.getCurrent().intValue();
        int pageSize = params.getPageSize() == null ? 10 : params.getPageSize().intValue();
        Page<TaskVo> page = new Page<>(pageNum, pageSize);
        IPage<TaskVo> iPage = flowableTaskDao.getApplyingTasks(page, params);
        iPage
            .getRecords()
            .forEach(
                e -> {
                    if (e != null) {
                        UserParam userParam = new UserParam();
                        userParam.setAccountName(e.getStartUserName());
                        UserDTO user = userService.getUser(userParam);
                        e.setStartUserName(user.getName());
                    }
                });
        return new PageRespJson<>(iPage);
    }

    @Override
    public PageRespJson<TaskVo> getApplyedTasks(TaskQueryVo params) {
        int pageNum = params.getCurrent() == null ? 1 : params.getCurrent().intValue();
        int pageSize = params.getPageSize() == null ? 10 : params.getPageSize().intValue();
        Page<TaskVo> page = new Page<>(pageNum, pageSize);
        IPage<TaskVo> applyedTasks = flowableTaskDao.getApplyedTasks(page, params);
        applyedTasks
            .getRecords()
            .forEach(
                e -> {
                    if (e != null) {
                        UserParam userParam = new UserParam();
                        userParam.setAccountName(e.getStartUserName());
                        UserDTO user = userService.getUser(userParam);
                        if (!org.springframework.util.StringUtils.isEmpty(user)) {
                            e.setStartUserName(user.getName());
                        }
                    }
                });
        return new PageRespJson<>(applyedTasks);
    }

    @Override
    public Task getTask(String Assignee, String processInstanceId) {
        return taskService
            .createTaskQuery()
            .taskAssignee(Assignee)
            .processInstanceId(processInstanceId)
            .orderByTaskCreateTime()
            .desc()
            .singleResult();
    }

    @Override
    public Task getTaskByCandidate(String account, String processInstanceId) {
        return taskService
            .createTaskQuery()
            .processInstanceId(processInstanceId)
            .taskCandidateUser(account)
            .orderByTaskCreateTime()
            .desc()
            .singleResult();
    }

    @Override
    public Task getTask(String taskId) {
        return taskService.createTaskQuery().taskId(taskId).singleResult();
    }

    @Override
    public void complete(FlowTaskParam taskParam) {
        Task task = taskService.createTaskQuery().taskId(taskParam.getTaskId()).singleResult();
        if (Objects.isNull(task)) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "任务不存在或已经处理过，请检查！");
        }
        String type =
            taskParam.getCommentType() == null
                ? CommentTypeEnum.SP.toString()
                : taskParam.getCommentType();
        String comment =
            taskParam.getComment() == null ? CommentTypeEnum.SP.getName() : taskParam.getComment();
        // 5.生成审批意见
        this.addComment(
            taskParam.getTaskId(),
            taskParam.getUserId(),
            taskParam.getProcessInstanceId(),
            type,
            comment);
        taskService.complete(taskParam.getTaskId(), taskParam.getValues());
    }

    @Override
    public Task getCandidateTask(String account, String processInstanceId) {
        return taskService
            .createTaskQuery()
            .taskCandidateOrAssigned(account)
            .processInstanceId(processInstanceId)
            .orderByTaskCreateTime()
            .desc()
            .singleResult();
    }

    @Override
    public List<Task> getMyTask(String account, List<String> key) {
        return taskService.createTaskQuery().processDefinitionKeyIn(key).taskCandidateOrAssigned(account).list();
    }

    private Map<String, String> getApplyers(
        String instanceId,
        List<String> userList,
        Map<String, List<HistoricTaskInstance>> taskInstanceMap) {
        //    Map<String, User> userMap =
        //        userList.stream().collect(Collectors.toMap(User::getId, user -> user));
        Map<String, String> applyMap = new HashMap<>();
        ProcessInstance processInstance =
            runtimeService.createProcessInstanceQuery().processInstanceId(instanceId).singleResult();
        taskInstanceMap.forEach(
            (activityId, taskInstances) -> {
                StringBuffer applyers = new StringBuffer();
                StringBuffer finalApplyers = applyers;
                taskInstances.forEach(
                    taskInstance -> {
                        if (!taskInstance.getName().equals(FlowConstant.FLOW_SUBMITTER)) {
                            userList.stream()
                                .forEach(
                                    u -> {
                                        if (u != null) {
                                            if (StringUtils.indexOf(finalApplyers.toString(), u) == -1) {
                                                finalApplyers.append(u).append(",");
                                            }
                                        }
                                    });
                            //                  User user = userMap.get(taskInstance.getAssignee());
                            //                  if (user != null) {
                            //                    if (StringUtils.indexOf(finalApplyers.toString(),
                            // user.getDisplayName())
                            //                        == -1) {
                            //                      finalApplyers.append(user.getDisplayName()).append(",");
                            //                    }
                            //                  }
                        } else {
                            String startUserId = processInstance.getStartUserId();
                            //                  User user =
                            // identityService.createUserQuery().userId(startUserId).singleResult();
                            if (startUserId != null) {
                                finalApplyers.append(startUserId).append(",");
                            }
                        }
                        if (!taskInstance.getName().equals(FlowConstant.FLOW_SUBMITTER_DIANGONG)) {
                            userList.stream()
                                .forEach(
                                    u -> {
                                        if (u != null) {
                                            if (StringUtils.indexOf(finalApplyers.toString(), u) == -1) {
                                                finalApplyers.append(u).append(",");
                                            }
                                        }
                                    });
                            //                  User user = userMap.get(taskInstance.getAssignee());
                            //                  if (user != null) {
                            //                    if (StringUtils.indexOf(finalApplyers.toString(),
                            // user.getDisplayName())
                            //                        == -1) {
                            //                      finalApplyers.append(user.getDisplayName()).append(",");
                            //                    }
                            //                  }
                        } else {
                            String startUserId = processInstance.getStartUserId();
                            //                  User user =
                            // identityService.createUserQuery().userId(startUserId).singleResult();
                            if (startUserId != null) {
                                finalApplyers.append(startUserId).append(",");
                            }
                        }
                    });
                if (applyers.length() > 0) {
                    applyers = applyers.deleteCharAt(applyers.length() - 1);
                }
                applyMap.put(activityId, applyers.toString());
            });
        return applyMap;
    }
}
