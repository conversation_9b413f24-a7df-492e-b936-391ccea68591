package com.yhd.admin.bms.domain.entity.sw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 消防重点部位
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-10
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class MesSwFire extends BaseEntity implements Cloneable, Serializable {

    /**
     * 巡查时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime patrolTime;

    /**
     * 检查部位
     */
    private String checkPosition;

    /**
     * 巡查人
     */
    private String checker;

    /**
     * 巡查人账号
     */
    private String checkerCode;

    /**
     * 存在问题
     */
    private String dangerDesc;

    /**
     * 消防器材是否完好
     */
    private String fireEqpt;

    /**
     * 消防通道是否畅通
     */
    private String fireHidden;

    /**
     * 消防设施是否齐全
     */
    private String fireFacilities;

    /**
     * 隐患是否消除
     */
    private String hiddenTrouble;

    /**
     * 隐患消除时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime troubleTime;

    /**
     * 复查人
     */
    private String reviewer;

    /**
     * 复查人
     */
    private String reviewerCode;


}
