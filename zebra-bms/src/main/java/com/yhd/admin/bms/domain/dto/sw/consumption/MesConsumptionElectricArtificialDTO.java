package com.yhd.admin.bms.domain.dto.sw.consumption;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电量消耗-人工统计表
 *
 * <AUTHOR>
 * @date 2025/07/02 10:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesConsumptionElectricArtificialDTO extends BaseDTO implements Serializable {
  /** 年月 */
  private String month;

  /** 选煤厂用电量 */
  private BigDecimal coalPreparationQuantity;

  /** 选煤厂电价 */
  private BigDecimal coalPreparationPrice;

  /** 选煤厂用电金额 */
  private BigDecimal coalPreparationAmount;

  /** 电厂用电量 */
  private BigDecimal powerPlantQuantity;

  /** 电厂电价 */
  private BigDecimal powerPlantPrice;

  /** 电厂用电金额 */
  private BigDecimal powerPlantAmount;

  /** 用电量合计 */
  private BigDecimal totalQuantity;

  /** 用电金额合计 */
  private BigDecimal totalAmount;

  /** 入洗煤量 */
  private BigDecimal coalWash;

  /** 入洗吨煤电耗 */
  private BigDecimal coalWashConsumption;

  /** 洗选产量 */
  private BigDecimal washProduction;

  /** 产量吨煤电耗 */
  private BigDecimal washProductionConsumption;
}
