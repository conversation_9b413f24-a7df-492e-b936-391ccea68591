package com.yhd.admin.scada;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@EnableTransactionManagement
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication
@ServletComponentScan
@MapperScan(basePackages = {"com.yhd.admin.scada.dao"})
@EnableAsync
@EnableScheduling
public class ScadaApplication {

    public static void main(String[] args) {
        SpringApplication scada = new SpringApplication(ScadaApplication.class);
        scada.setBannerMode(Banner.Mode.OFF);
        scada.run(args);
    }

}
