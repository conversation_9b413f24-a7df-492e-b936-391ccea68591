package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesChecklistRcsudeDTO;
import com.yhd.admin.bms.domain.entity.sw.floatSink.MesChecklistRcsude;
import com.yhd.admin.bms.domain.query.sw.MesChecklistRcsudeParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.RawCoalCurveVO;

public interface ChecklistRcsudeService extends IService<MesChecklistRcsude> {
  /**
   * 分页查询。
   *
   * @param param 查询参数。
   * @return IPage<MesChecklistRcsudeDTO>
   */
  IPage<MesChecklistRcsudeDTO> pagingQuery(MesChecklistRcsudeParam param);

  /**
   * 新增原煤筛分沉浮实验。
   *
   * @param param 原煤筛分沉浮实验
   * @return true 成功，false 失败。
   */
  Boolean addRcsude(MesChecklistRcsudeParam param);

  /**
   * 修改原煤筛分沉浮实验。
   *
   * @param param 原煤筛分沉浮实验
   * @return true 成功，false 失败。
   */
  Boolean modifyRcsude(MesChecklistRcsudeParam param);

  /**
   * 批量删除 根据ID主键批量删除。
   *
   * @param batchParam [id]
   * @return 删除条数
   */
  Boolean removeBatch(BatchParam batchParam);

  /**
   * 根据ID查询详情
   *
   * @param param MesChecklistRcsudeParam
   * @return
   */
  MesChecklistRcsudeDTO getCurrentDetail(MesChecklistRcsudeParam param);

  RawCoalCurveVO getFloatSinkScSRVListByExpId(MesChecklistRcsudeParam param);
  //
  //    /**
  //     * 获取原煤可选性曲线的最新一条数据
  //     *
  //     * @return
  //     */
  //    RawCoalCurveVO getFloatSinkScSRVLast();
}
