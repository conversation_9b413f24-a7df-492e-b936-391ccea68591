package com.yhd.admin.bms.service.sw.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.dao.sw.MesSwUnsafeLedgerDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwUnsafeLedgerConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwUnsafeLedgerDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwUnsafeLedger;
import com.yhd.admin.bms.domain.query.sw.MesSwUnsafeLedgerParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.UserAccountParam;
import com.yhd.admin.bms.service.sw.MesSwUnsafeLedgerService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.FileService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class MesSwUnsafeLedgerServiceImpl
    extends ServiceImpl<MesSwUnsafeLedgerDao, MesSwUnsafeLedger>
    implements MesSwUnsafeLedgerService {

  @Resource private MesSwUnsafeLedgerConvert convert;
  @Resource private FileService fileService;
  @Resource private DicService dicService;
  @Resource private UserAccountService userAccountService;

  @Override
  public IPage<MesSwUnsafeLedgerDTO> pagingQuery(MesSwUnsafeLedgerParam queryParam) {
    Page<MesSwUnsafeLedger> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<MesSwUnsafeLedger> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    queryChain.like(
        StringUtils.isNoneBlank(queryParam.getInspectionName()),
        MesSwUnsafeLedger::getInspectionName,
        queryParam.getInspectionName());
    if (StringUtils.isNoneBlank(queryParam.getStartDate())
        && StringUtils.isNoneBlank(queryParam.getEndDate())) {
      queryChain.between(
          MesSwUnsafeLedger::getDate, queryParam.getStartDate(), queryParam.getEndDate());
    }
    queryChain.orderByDesc(MesSwUnsafeLedger::getDate, MesSwUnsafeLedger::getCreatedTime);

    IPage<MesSwUnsafeLedgerDTO> result = queryChain.page(page).convert(convert::toDTO);
    if (result != null && CollectionUtil.isNotEmpty(result.getRecords())) {
      List<MesSwUnsafeLedgerDTO> list = result.getRecords();
      for (MesSwUnsafeLedgerDTO item : list) {
        // 缴费凭证图片
        List<JSONObject> picturePayList =
            fileService.getFileList(
                item.getId(), DicConstant.UNSAFE_LEDGER, DicConstant.UNSAFE_LEDGER_PAY);
        item.setPicturePayList(picturePayList);
        // 视频监屏照片图片
        List<JSONObject> pictureMonitorList =
            fileService.getFileList(
                item.getId(), DicConstant.UNSAFE_LEDGER, DicConstant.UNSAFE_LEDGER_MONITOR);
        item.setPictureMonitorList(pictureMonitorList);
      }
    }

    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addOrModify(MesSwUnsafeLedgerParam queryParam) {
    MesSwUnsafeLedger entity = convert.toEntity(queryParam);
    // 班次
    entity.setClassesName(dicService.transform(DicConstant.CLASS, queryParam.getClasses()));
    // 风险等级
    entity.setRiskLevelName(
        dicService.transform(DicConstant.RISK_LEVEL, queryParam.getRiskLevel()));
    // 责任人
    entity.setChargeName(getUserName(queryParam.getChargePerson()));
    // 检查人
    entity.setExaminerName(getUserName(queryParam.getExaminer()));
    // 车间连带处罚责任人
    entity.setWorkshopPunisherName(getUserName(queryParam.getWorkshopPunisher()));
    // 厂领导连带处罚责任人
    entity.setLeaderPunisherName(getUserName(queryParam.getLeaderPunisher()));
    // 视频谈话责任人
    entity.setTalkChargeName(getUserName(queryParam.getTalkCharge()));
    boolean result = saveOrUpdate(entity);
    // 缴费凭证图片
    saveFilePicture(queryParam.getPicturePayList(), DicConstant.UNSAFE_LEDGER_PAY, entity);
    // 视频监屏照片图片
    saveFilePicture(queryParam.getPictureMonitorList(), DicConstant.UNSAFE_LEDGER_MONITOR, entity);
    return result;
  }

  /**
   * 保存图片
   *
   * @param fileList
   * @param entity
   */
  private void saveFilePicture(
      List<JSONObject> fileList, String fileType, MesSwUnsafeLedger entity) {
    if (CollectionUtil.isNotEmpty(fileList)) {
      fileService.insertFile(entity.getId(), DicConstant.UNSAFE_LEDGER, fileType, fileList);
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean removeBatch(BatchParam param) {
    if (CollectionUtil.isNotEmpty(param.getId())) {
      param.getId().stream()
          .forEach(
              id -> {
                // 删除附件
                fileService.removeFile(id, DicConstant.UNSAFE_LEDGER);
              });
    }
    return super.removeByIds(param.getId());
  }

  @Override
  public MesSwUnsafeLedgerDTO getCurrentDetail(MesSwUnsafeLedgerParam queryParam) {
    MesSwUnsafeLedgerDTO result = convert.toDTO(super.getById(queryParam.getId()));
    // 缴费凭证图片
    List<JSONObject> picturePayList =
        fileService.getFileList(
            queryParam.getId(), DicConstant.UNSAFE_LEDGER, DicConstant.UNSAFE_LEDGER_PAY);
    result.setPicturePayList(picturePayList);
    // 视频监屏照片图片
    List<JSONObject> pictureMonitorList =
        fileService.getFileList(
            queryParam.getId(), DicConstant.UNSAFE_LEDGER, DicConstant.UNSAFE_LEDGER_MONITOR);
    result.setPictureMonitorList(pictureMonitorList);
    return result;
  }

  private String getUserName(String userAccount) {
    if (StringUtils.isBlank(userAccount)) {
      return "";
    }
    UserAccountParam accountParam = new UserAccountParam();
    accountParam.setUsername(userAccount);
    // 查询账户
    Optional<UserAccountDTO> accountOptional = userAccountService.getUserAccount(accountParam);
    if (accountOptional.isPresent()) {
      userAccount = accountOptional.get().getName();
    }
    return userAccount;
  }
}
