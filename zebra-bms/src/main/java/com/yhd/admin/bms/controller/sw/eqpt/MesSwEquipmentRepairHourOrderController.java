package com.yhd.admin.bms.controller.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentRepairHourOrderConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentRepairHourOrderDTO;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairHourOrderParam;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairHourOrderService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备检修-设备检修工时记录
 *
 * <AUTHOR>
 * @date 2025/7/14 10:22
 */
@RestController
@RequestMapping("/equipment/repairHour")
@Slf4j
public class MesSwEquipmentRepairHourOrderController
    extends BaseController<
        MesSwEquipmentRepairHourOrderConvert, MesSwEquipmentRepairHourOrderService> {

  public MesSwEquipmentRepairHourOrderController(
      MesSwEquipmentRepairHourOrderConvert convert, MesSwEquipmentRepairHourOrderService service) {
    super(convert, service);
  }

  /**
   * 设备巡检列表
   *
   * @param queryParam
   * @return
   * @throws Exception
   */
  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwEquipmentRepairHourOrderDTO> pagingQuery(
      @RequestBody MesSwEquipmentRepairHourOrderParam queryParam) throws Exception {
    IPage<MesSwEquipmentRepairHourOrderDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage);
  }

  /**
   * 新增设备检修工时记录
   *
   * @param queryParam
   * @return
   */
  @PostMapping(
      value = "/saveAdd",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> add(@RequestBody MesSwEquipmentRepairHourOrderParam queryParam) {
    try {
      Boolean retVal = service.saveAdd(queryParam);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 编辑设备检修工时记录
   *
   * @param queryParam
   * @return
   */
  @PostMapping(
      value = "/saveModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> saveModify(@RequestBody MesSwEquipmentRepairHourOrderParam queryParam) {
    try {
      Boolean retVal = service.saveModify(queryParam);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 查看设备检修工时记录
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MesSwEquipmentRepairHourOrderDTO> getCurrentDetail(
      @RequestBody MesSwEquipmentRepairHourOrderParam param) {
    try {
      return RespJson.buildSuccessResponse(service.getCurrentDetail(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 删除
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/remove",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> remove(@RequestBody MesSwEquipmentRepairHourOrderParam param) {
    try {
      return RespJson.buildSuccessResponse(service.remove(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
