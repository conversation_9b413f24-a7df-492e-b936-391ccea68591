package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesSwWorkTicketConstDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConst;
import com.yhd.admin.bms.domain.query.sw.MesSwWorkTicketConstParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwWorkTicketConstVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/5/29 9:02
 * @Version 1.0
 *
 * 施工工作票
 */

@Mapper(componentModel = "spring")
public interface MesSwWorkTicketConstConvert{
    MesSwWorkTicketConst toEntity(MesSwWorkTicketConstParam param);

    MesSwWorkTicketConst dtoToEntity(MesSwWorkTicketConstDTO dto);

    MesSwWorkTicketConstVO toVO(MesSwWorkTicketConstDTO dto);

    MesSwWorkTicketConstDTO toDTO(MesSwWorkTicketConst entity);
}
