package com.yhd.admin.bms.dao.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.admin.bms.domain.entity.sys.SysMenu;
import com.yhd.admin.bms.domain.query.sys.RoleParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MenuDao.java
 * @Description 菜单操作类。
 * @createTime 2020年03月30日 10:02:00
 */

public interface MenuDao extends BaseMapper<SysMenu> {

    /**
     * 根据角色ID查询菜单
     *
     * @param param 角色查询参数
     * @return {@link List<SysMenu>}
     */
    List<SysMenu> selectMenuByRole(RoleParam param);

    /**
     * 根据角色批量查询对应的菜单。
     *
     * @param roleIds
     * @return
     */
    List<SysMenu> selectBatchMenuByRole(List<Long> roleIds);

    /**
     * 根据菜单ID，查询自身节点和父节点。
     *
     * @param menuIds 菜单ID
     * @return 菜单
     */
    List<SysMenu> findParentById(@Param("list") List<Long> list, @Param("clientId") String clientId);

    /**
     * 根据ID查询所有子节点包含自身节点。
     *
     * @param id 菜单ID
     * @return
     */
    List<SysMenu> selectChildren(Long id);
}
