package com.yhd.admin.bms.config.userdetails;

import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

public class AdminUserDetail extends User {
  private final UserDTO userInfo;

  public AdminUserDetail(
      String username,
      String password,
      boolean enabled,
      boolean accountNonExpired,
      boolean credentialsNonExpired,
      boolean accountNonLocked,
      Collection<? extends GrantedAuthority> authorities,
      UserDTO userInfo) {
    super(
        username,
        password,
        enabled,
        accountNonExpired,
        credentialsNonExpired,
        accountNonLocked,
        authorities);
    this.userInfo = userInfo;
  }

  public UserDTO getUserInfo() {
    return userInfo;
  }
}
