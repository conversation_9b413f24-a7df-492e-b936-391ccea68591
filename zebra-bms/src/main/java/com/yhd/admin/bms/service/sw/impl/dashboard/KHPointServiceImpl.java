package com.yhd.admin.bms.service.sw.impl.dashboard;

import KHThrift.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.domain.query.sw.HstTimeStatsParam;
import com.yhd.admin.bms.domain.vo.sw.dashboard.KHPointDataVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.dashboard.KHPointService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 亚控点位-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/10/2 9:38
 */
@Service
@Slf4j
public class KHPointServiceImpl implements KHPointService {
    private static final Logger logger = LoggerFactory.getLogger(KHPointServiceImpl.class);

    @Resource
    private com.yhd.admin.bms.config.khserver.KHClient KHClient;

    @Override
    public List<KHPointDataVO> queryPointList(HstTimeStatsParam param) {
        String point = param.getPoint();
        if (com.yhd.admin.common.utils.StringUtils.isBlank(point)) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询参数变量名称不能为空，请检查！");
        }
        Integer interval = param.getInterval();
        if (interval == null) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询参数采样间隔不能为空，请检查！");
        }
        LocalDateTime startTime = param.getStartTime();
        LocalDateTime endTime = param.getEndTime();
        if (startTime == null || endTime == null) {
            endTime = LocalDateTime.now();
            startTime = endTime.minusHours(2);
        }
        ThriftDataCriteria Criteria = new ThriftDataCriteria();
        Criteria.StartTime = new ThriftTimeStamp();
        Criteria.EndTime = new ThriftTimeStamp();
        // 第三方接口参数是int型
        Criteria.StartTime.Seconds = (int) startTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.StartTime.Millisecs = 0;
        Criteria.EndTime.Seconds = (int) endTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.EndTime.Millisecs = 0;
        Criteria.DigitalAsString = true;
        Criteria.TagNames = new ArrayList<>();
        Criteria.TagNames.add(point);
        Criteria.RowCount = 1000000;
        Criteria.SamplingMode = khbrokerConstants.THRIFT_KSAM_STEPPED;
        Criteria.DataVersion = khbrokerConstants.THRIFT_KDAV_ALL;
        Criteria.SamplingInterval = 1000L * interval;

        List<KDBDataRecordset> dataPointList = KHClient.OpenRecordset(Criteria);
        List<KHPointDataVO> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(dataPointList)) {
            return result;
        }
        KDBDataRecordset dataPoint = dataPointList.get(0);
        if (dataPoint == null) {
            return result;
        }
        List<KDBDataProperties> recordList = dataPoint.DataRecords;
        if (CollectionUtil.isEmpty(recordList)) {
            return result;
        }
        for (KDBDataProperties itemRecord : recordList) {
            KHPointDataVO item = kdbDataConvert(itemRecord);
            item.setPoint(point);
            item.setTime(DateUtil.longSecondToLocalDateTime(itemRecord.TimeStamp.Seconds));
            if (null == item.getValue()) {
                item.setValue(BigDecimal.ZERO);
            }
            result.add(item);
        }

        return result;
    }

    @Override
    public KHPointDataVO getPointValue(String pointName) {
        logger.debug("获取：{}-点位数据开始，时间：{}", pointName, LocalDateTime.now());
        if (StringUtils.isNotBlank(pointName)) {
            List<KDBDataProperties> dataPropertiesList = KHClient.getCurrentValue(pointName);
            if (!CollectionUtil.isEmpty(dataPropertiesList)) {
                KDBDataProperties dataProperties = dataPropertiesList.get(0);
                if (null != dataProperties) {
                    KHPointDataVO pointDataVO = kdbDataConvert(dataProperties);
                    pointDataVO.setPoint(pointName);
                    return pointDataVO;
                }
            }
        }

        return null;
    }

    @Override
    public List<KHPointDataVO> getPointValue(List<String> pointNames) {
        List<KHPointDataVO> result = Lists.newArrayList();
        if (!CollectionUtil.isEmpty(pointNames)) {
            List<KDBDataProperties> dataPropertiesList = KHClient.getCurrentValue(pointNames);
            if (!CollectionUtil.isEmpty(dataPropertiesList)) {
                for (int i = 0; i < dataPropertiesList.size(); i++) {
                    KHPointDataVO dataVO = kdbDataConvert(dataPropertiesList.get(i));
                    dataVO.setPoint(pointNames.get(i));
                    result.add(dataVO);
                }
            }
        }

        return result;
    }

    @Override
    public KHPointDataVO getPointBooleanValue(String pointName) {
        logger.debug("获取：{}-点位数据开始，时间：{}", pointName, LocalDateTime.now());
        KHPointDataVO result = new KHPointDataVO();
        if (StringUtils.isNotBlank(pointName)) {
            List<KDBDataProperties> dataPropertiesList = KHClient.getCurrentValue(pointName);
            if (!CollectionUtil.isEmpty(dataPropertiesList)) {
                KDBDataProperties dataProperties = dataPropertiesList.get(0);
                result.setPoint(pointName);
                // 时间
                result.setTime(DateUtil.longSecondToLocalDateTime(dataProperties.TimeStamp.Seconds));
                // 值
                KDBValue.DataValue dataValue = dataProperties.Value.Value;
                if (dataValue != null) {
                    boolean bitVal = dataValue.bitVal;
                    logger.debug(pointName + "-点位数据值：{}", bitVal);

                    result.setValue(bitVal);
                }
            }
        }

        return result;
    }

    @Override
    public Map<String, BigDecimal> getConsumptionByParams(String date, List<String> pointNames) {
        Map<String, BigDecimal> result = new HashMap<>();
        if (CollectionUtil.isEmpty(pointNames)) {
            return result;
        }
        // 初始化结果，默认为null
        Map<String, BigDecimal> emptyResult = new HashMap<>();
        pointNames.stream()
            .forEach(
                e -> {
                    emptyResult.put(e, null);
                });

        if (StringUtils.isBlank(date)) {
            return emptyResult;
        }
        LocalDate localDate = LocalDate.parse(date);
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 前一个时间段区间
        LocalDateTime startTime1 = null;
        LocalDateTime endTime1 = null;

        // 后一个时间段区间
        LocalDateTime startTime2 = null;
        LocalDateTime endTime2 = null;

        // 判断输入的日期是否在今天之前
        if (localDate.isBefore(today)) {
            startTime1 = LocalDateTime.of(localDate, LocalTime.MIN);
            endTime1 = LocalDateTime.of(localDate, LocalTime.MAX);
            // 第2天的时间区间
            startTime2 = LocalDateTime.of(localDate.plusDays(1), LocalTime.MIN);
            endTime2 = LocalDateTime.of(localDate.plusDays(1), LocalTime.MAX);
        } else if (localDate.isEqual(today)) {
            // 查询当天时间
            startTime1 = LocalDateTime.of(today, LocalTime.MIN);
            endTime1 = LocalDateTime.now();
        } else {
            // 查询未来时间
            return emptyResult;
        }

        // 遍历查询的点位，用来赋值
        LocalDateTime finalStartTime1 = startTime1;
        LocalDateTime finalEndTime1 = endTime1;
        LocalDateTime finalStartTime2 = startTime2;
        LocalDateTime finalEndTime2 = endTime2;
        pointNames.stream()
            .forEach(
                e -> {
                    // 查询第一时间段的值，开始值
                    List<KHPointDataVO> beforeList =
                        getPointsFirstValue(finalStartTime1, finalEndTime1, e);
                    // 查询第二时间段的值，结束值
                    List<KHPointDataVO> afterList = null;
                    if (localDate.isBefore(today)) {
                        afterList = getPointsFirstValue(finalStartTime2, finalEndTime2, e);
                    } else if (localDate.isEqual(today)) {
                        // 查询当天时间
                        afterList = this.getPointValue(Arrays.asList(e));
                    }
                    if (CollectionUtil.isEmpty(beforeList) || CollectionUtil.isEmpty(afterList)) {
                        result.put(e, null);
                        return;
                    }
                    Object beforeValue = beforeList.get(0).getValue();
                    Object afterValue = afterList.get(0).getValue();

                    if (beforeValue == null || afterValue == null) {
                        result.put(e, null);
                        return;
                    }
                    // 消耗量=结束值 减去 开始值
                    BigDecimal value =
                        NumberUtil.sub(
                            NumberUtil.toBigDecimal(afterValue.toString()),
                            NumberUtil.toBigDecimal(beforeValue.toString()));
                    result.put(e, value);
                });
        return result;
    }

    @Override
    public Map<String, BigDecimal> getCoalProductionByParams(String date, List<String> pointNames) {
        Map<String, BigDecimal> result = new HashMap<>();
        if (CollectionUtil.isEmpty(pointNames)) {
            return result;
        }
        // 初始化结果，默认为null
        Map<String, BigDecimal> emptyResult = new HashMap<>();
        pointNames.stream()
            .forEach(
                e -> {
                    emptyResult.put(e, null);
                });
        if (StringUtils.isBlank(date)) {
            return emptyResult;
        }
        LocalDate localDate = LocalDate.parse(date);
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 前一个时间段区间
        LocalDateTime startTime1 = null;
        LocalDateTime endTime1 = null;

        // 判断输入的日期是否在今天之前
        if (localDate.isBefore(today)) {
            endTime1 =
                LocalDateTime.parse(
                    date + " 17:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            startTime1 = endTime1.minusMinutes(30);
        } else if (localDate.isEqual(today)) {
            // 查询当天时间
            endTime1 = LocalDateTime.now();
            if (endTime1.isAfter(endTime1.withHour(17).withMinute(59).withSecond(59))) {
                endTime1 = endTime1.withHour(17).withMinute(59).withSecond(59);
            }
            startTime1 = endTime1.minusMinutes(30);
        } else {
            // 查询未来时间
            return emptyResult;
        }
        Map<String, List<KDBDataProperties>> pointDataMap =
            getPointsValueList(startTime1, endTime1, pointNames);

        if (CollectionUtil.isEmpty(pointDataMap)) {
            return emptyResult;
        }
        // 遍历查询的点位，用来赋值
        pointNames.stream()
            .forEach(
                e -> {
                    List<KDBDataProperties> dataList = pointDataMap.get(e);
                    if (CollectionUtil.isEmpty(dataList)) {
                        result.put(e, null);
                        return;
                    }
                    // 取最后一个值
                    KDBDataProperties data = dataList.get(dataList.size() - 1);
                    Object value = kdbDataConvert(data).getValue();
                    if (value == null) {
                        result.put(e, null);
                        return;
                    }
                    result.put(e, NumberUtil.toBigDecimal(value.toString()));
                });
        return result;
    }

    @Override
    public Map<String, List<KHPointDataVO>> queryPointListByStepped(HstTimeStatsParam param) {
        Map<String, List<KHPointDataVO>> result = new HashMap<>();
        List<String> pointNames = param.getPointList();
        if (CollUtil.isEmpty(pointNames)) {
            return result;
        }
        // 初始化结果，默认为null
        Map<String, List<KHPointDataVO>> emptyResult = new HashMap<>();
        pointNames.stream()
            .forEach(
                e -> {
                    emptyResult.put(e, null);
                });
        // 默认间隔60秒
        Integer interval = param.getInterval();
        if (interval == null) {
            interval = 60;
        }
        LocalDateTime startTime = param.getStartTime();
        LocalDateTime endTime = param.getEndTime();
        if (startTime == null || endTime == null) {
            endTime = LocalDateTime.now();
            startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        }
        ThriftDataCriteria Criteria = new ThriftDataCriteria();
        Criteria.StartTime = new ThriftTimeStamp();
        Criteria.EndTime = new ThriftTimeStamp();
        // 第三方接口参数是int型
        Criteria.StartTime.Seconds = (int) startTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.StartTime.Millisecs = 0;
        Criteria.EndTime.Seconds = (int) endTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.EndTime.Millisecs = 0;
        Criteria.DigitalAsString = true;
        Criteria.TagNames = new ArrayList<>();
        Criteria.TagNames.addAll(pointNames);
        Criteria.RowCount = 1000000;
        Criteria.SamplingMode = khbrokerConstants.THRIFT_KSAM_STEPPED;
        Criteria.DataVersion = khbrokerConstants.THRIFT_KDAV_ALL;
        Criteria.SamplingInterval = 1000L * interval;

        List<KDBDataRecordset> dataPointList = KHClient.OpenRecordset(Criteria);
        if (CollectionUtil.isEmpty(dataPointList)) {
            return emptyResult;
        }
        dataPointList.forEach(
            e -> {
                List<KDBDataProperties> recordList = e.DataRecords;
                if (CollectionUtil.isEmpty(recordList)) {
                    result.put(e.TagName, null);
                } else {
                    int size = recordList.size();
                    List<KHPointDataVO> dataList = Lists.newArrayList();
                    for (int i = 0; i < size; i++) {
                        KHPointDataVO dataVO = kdbDataConvert(recordList.get(i));
                        dataVO.setPoint(e.TagName);
                        dataList.add(dataVO);
                    }
                    result.put(e.TagName, dataList);
                }
            });
        return result;
    }

    /**
     * 查询时间区间内的第一个值，只能一个个点位去查
     *
     * @param startTime
     * @param endTime
     * @param pointName
     * @return
     */
    private List<KHPointDataVO> getPointsFirstValue(
        LocalDateTime startTime, LocalDateTime endTime, String pointName) {
        ThriftDataCriteria Criteria = new ThriftDataCriteria();
        Criteria.StartTime = new ThriftTimeStamp();
        Criteria.EndTime = new ThriftTimeStamp();
        // 第三方接口参数是int型
        Criteria.StartTime.Seconds = (int) startTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.StartTime.Millisecs = 0;
        Criteria.EndTime.Seconds = (int) endTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.EndTime.Millisecs = 0;
        Criteria.DigitalAsString = true;
        Criteria.TagNames = new ArrayList<>();
        Criteria.TagNames.add(pointName);
        Criteria.RowCount = 1;
        Criteria.SamplingMode = khbrokerConstants.THRIFT_KSAM_RAW_BY_NUMBER;
        Criteria.DataVersion = khbrokerConstants.THRIFT_KDAV_ALL;
        List<KDBDataRecordset> dataPointList = KHClient.OpenRecordset(Criteria);

        List<KHPointDataVO> result = Lists.newArrayList();
        if (!CollectionUtil.isEmpty(dataPointList)) {
            for (int i = 0; i < dataPointList.size(); i++) {
                KDBDataRecordset dataPoint = dataPointList.get(i);
                KHPointDataVO dataVO = null;
                if (CollectionUtil.isNotEmpty(dataPoint.DataRecords)) {
                    dataVO = kdbDataConvert(dataPoint.DataRecords.get(0));
                } else {
                    // 查不到，默认为null
                    dataVO = new KHPointDataVO();
                    dataVO.setValue(null);
                }
                dataVO.setPoint(dataPoint.TagName);
                result.add(dataVO);
            }
        }
        return result;
    }

    /**
     * 查询时间区间内的所有值
     *
     * @param startTime
     * @param endTime
     * @param pointNames
     * @return key-点位，value-数据集
     */
    private Map<String, List<KDBDataProperties>> getPointsValueList(
        LocalDateTime startTime, LocalDateTime endTime, List<String> pointNames) {
        ThriftDataCriteria Criteria = new ThriftDataCriteria();
        Criteria.StartTime = new ThriftTimeStamp();
        Criteria.EndTime = new ThriftTimeStamp();
        // 第三方接口参数是int型
        Criteria.StartTime.Seconds = (int) startTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.StartTime.Millisecs = 0;
        Criteria.EndTime.Seconds = (int) endTime.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
        Criteria.EndTime.Millisecs = 0;
        Criteria.DigitalAsString = true;
        Criteria.TagNames = new ArrayList<>();
        Criteria.TagNames.addAll(pointNames);
        Criteria.RowCount = 1000000;
        Criteria.SamplingMode = khbrokerConstants.THRIFT_KSAM_RAW_BY_TIME;
        Criteria.DataVersion = khbrokerConstants.THRIFT_KDAV_ALL;
        List<KDBDataRecordset> dataPointList = KHClient.OpenRecordset(Criteria);

        Map<String, List<KDBDataProperties>> result = new HashMap<>();
        if (CollectionUtil.isEmpty(dataPointList)) {
            return result;
        }
        dataPointList.forEach(
            e -> {
                result.put(e.TagName, e.DataRecords);
            });
        return result;
    }

    /**
     * 亚控数据转换
     *
     * @param dataProperties 亚控数据
     * @return KHTimeSeriesVO
     */
    private KHPointDataVO kdbDataConvert(KDBDataProperties dataProperties) {
        KHPointDataVO result = new KHPointDataVO();
        // 时间
        result.setTime(DateUtil.longSecondToLocalDateTime(dataProperties.TimeStamp.Seconds));
        // 数据类型
        short dataType = dataProperties.Value.DataType;
        // 值
        KDBValue.DataValue dataValue = dataProperties.Value.Value;
        String formatData = null;
        if (dataValue != null) {

            if (1 == dataType) { // boolean bitVal
                result.setValue(dataValue.bitVal);
                return result;
            } else if (4 == dataType) { // short i2Va
                formatData = dataValue.i2Val + "";
            } else if (5 == dataType) { // short ui2Val
                formatData = dataValue.ui2Val + "";
            } else if (6 == dataType) { // int i4Val
                formatData = dataValue.i4Val + "";
            } else if (7 == dataType) { // int ui4Val
                formatData = dataValue.ui4Val + "";
            } else if (8 == dataType) { // long i8Val
                formatData = dataValue.i8Val + "";
            } else if (9 == dataType) { // long ui8Val
                formatData = dataValue.ui8Val + "";
            } else if (10 == dataType) { // float r4Val
                formatData = String.format("%f", dataValue.r4Val);
            } else if (11 == dataType) { // double r8Val
                formatData = String.format("%f", dataValue.r8Val);
            } else if (12 == dataType) { // String strVal
                formatData = dataValue.strVal;
            } else if (13 == dataType) { // String wstrVal
                formatData = dataValue.wstrVal;
            }
            result.setValue(formatData);
        }

        return result;
    }
}
