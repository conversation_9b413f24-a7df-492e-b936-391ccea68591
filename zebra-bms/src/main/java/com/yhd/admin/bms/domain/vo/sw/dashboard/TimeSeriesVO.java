package com.yhd.admin.bms.domain.vo.sw.dashboard;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
public class TimeSeriesVO implements Cloneable, Serializable {

    /** 时间点 */
    @Alias("时间")
    private LocalDateTime time;

    /** 数值 */
    @Alias("值")
    private Object value;

    public TimeSeriesVO() {}

    public TimeSeriesVO(LocalDateTime time, BigDecimal value) {
      this.time = time;
      this.value = value;
    }

    public TimeSeriesVO(LocalDateTime time, Object value) {
        this.time = time;
        this.value = value;
    }

}
