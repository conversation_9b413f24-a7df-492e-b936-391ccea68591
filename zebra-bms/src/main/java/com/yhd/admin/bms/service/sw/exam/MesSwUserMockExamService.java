package com.yhd.admin.bms.service.sw.exam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwUserMockExamDTO;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwUserMockExamParam;

/**
 * 用户模拟考试-业务层接口
 *
 * <AUTHOR>
 * @date 2024/11/1 9:02
 */
public interface MesSwUserMockExamService {

  IPage<MesSwUserMockExamDTO> pagingQuery(MesSwUserMockExamParam param);

  /**
   * 查询用户模拟考试试卷详情
   *
   * @param param 试卷id
   * @return 模拟考试试卷详情信息
   */
  MesSwUserMockExamDTO getUserMockPaperDetail(MesSwUserMockExamParam param);

  /**
   * 查询用户模拟考试试卷详情
   *
   * @param paperId 试卷id
   * @return 模拟考试试卷详情信息
   */
  MesSwUserMockExamDTO getUserMockPaperDetail(Long paperId);

  /**
   * 开始考试
   *
   * @param paperId 试卷id
   * @return true成功，false失败
   */
  Boolean startExam(Long paperId);

  /**
   * 交卷/结束考试
   *
   * @param param 试卷id
   * @return true成功，false失败
   */
  Boolean endExam(MesSwUserMockExamParam param);
}
