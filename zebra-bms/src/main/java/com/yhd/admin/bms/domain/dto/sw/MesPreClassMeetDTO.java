package com.yhd.admin.bms.domain.dto.sw;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 班前会记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MesPreClassMeetDTO extends BaseDTO implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;
    /**
     * 内容json
     */
    private String contentJson;
    /**
     * 时间
     */
    private String meetTime;
    /**
     * 车间
     */
    private String workshop;
    /**
     * 班组
     */
    private String teamGroup;
    /**
     * 交班人
     */
    private String jbr;
    /**
     * 交班人姓名
     */
    private String jbrName;
    /**
     * 接班人
     */
    private String successor;
    /**
     * 接班人姓名
     */
    private String successorName;
    /**
     * 主持人
     */
    private String host;
    /**
     * 主持人姓名
     */
    private String hostName;
    /**
     * 记录人
     */
    private String recorder;
    /**
     * 记录人姓名
     */
    private String recorderName;
    /**
     * 地点
     */
    private String address;
    /**
     * 参会人员
     */
    private List<String> confereeUser;
    /**
     * 参会人员
     */
    private String conferee;
    /**
     * 上班工作完成情况-生产完成情况
     */
    private String productionStatus;
    /**
     * 上班工作完成情况-设备运行情况
     */
    private String equipStatus;
    /**
     * 上班工作完成情况-气体检测结果
     */
    private String gasResult;
    /**
     * 上班工作完成情况-领导安排工作落实情况
     */
    private String workStatus;
    /**
     * 设备点检情况
     */
    private String spotCheckStatus;
    /**
     * 本班工作安排情况
     */
    private String workArrangement;
    /**
     * 风险源辨识及评估
     */
    private String risk;
    /**
     * 每日一题
     */
    private String dayQuestion;
    /**
     * 传达上级文件、会议精神
     */
    private String conveyInfo;
    /**
     * 厂部领导安排工作
     */
    private String arrangeWork;
    /**
     * 针对安排工作有无合理化建议
     */
    private String suggest;
    /**
     * 附件信息列表
     */
    private List<JSONObject> fileList;

}
