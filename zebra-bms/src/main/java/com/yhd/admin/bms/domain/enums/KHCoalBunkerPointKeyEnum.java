package com.yhd.admin.bms.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 煤仓煤量-亚控点位key枚举类
 *
 * <AUTHOR>
 * @date 2024/10/25 9:36
 */
@Getter
public enum KHCoalBunkerPointKeyEnum {
  // 原煤仓点位
  Raw_BUROW1_BinPositionT("XMSW_Raw_BUROW1_BinPositionT", "原煤一号仓煤量"),
  Raw_BUROW2_BinPositionT("XMSW_Raw_BUROW2_BinPositionT", "原煤二号仓煤量"),
  Raw_BUROW3_BinPositionT("XMSW_Raw_BUROW3_BinPositionT", "原煤三号仓煤量"),
  // 产品仓
  Load_BUPRO01_BinPositionT("XMSW_Load_BUPRO01_BinPositionT", "产品一号仓煤量"),
  Load_BUPRO02_BinPositionT("XMSW_Load_BUPRO02_BinPositionT", "产品二号仓煤量"),
  Load_BUPRO03_BinPositionT("XMSW_Load_BUPRO03_BinPositionT", "产品三号仓煤量"),
  Load_BUPRO04_BinPositionT("XMSW_Load_BUPRO04_BinPositionT", "产品四号仓煤量"),

  // 耙位
  XMSW_Slurry_Thickener601_ThickValue("XMSW_Slurry_Thickener601_ThickValue", "601耙位"),
  XMSW_Slurry_Thickener602_ThickValue("XMSW_Slurry_Thickener602_ThickValue", "602耙位"),
  XMSW_Slurry_Thickener412_ThickValue("XMSW_Slurry_Thickener412_ThickValue", "412耙位"),
  ;

  private final String key;
  private final String desc;

  KHCoalBunkerPointKeyEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }

  public static List<String> getKeyList() {
    KHCoalBunkerPointKeyEnum[] values = KHCoalBunkerPointKeyEnum.values();

    return Arrays.stream(values).map(KHCoalBunkerPointKeyEnum::getKey).collect(Collectors.toList());
  }

  public static KHCoalBunkerPointKeyEnum getEnumByKey(String key) {
    if (StringUtils.isBlank(key)) {
      return null;
    }
    KHCoalBunkerPointKeyEnum[] values = KHCoalBunkerPointKeyEnum.values();
    for (KHCoalBunkerPointKeyEnum value : values) {
      if (value.getKey().equals(key)) {
        return value;
      }
    }
    return null;
  }
}
