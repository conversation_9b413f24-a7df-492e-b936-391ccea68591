package com.yhd.admin.scada.domain.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/8/12 15:29
 */

@Data
@ToString

public class AlarmDTO implements Cloneable, Serializable {

    private String id;
    /**
     * 告警名称
     */
    private String alarmName;
    /**
     * 告警来源
     */
    private String source;
    /**
     * 告警设备
     */
    private String deviceNo;
    /**
     * 告警设备名称
     */
    private String deviceName;
    /**
     * 告警发生时间
     */
    private Long happendTime;
    /**
     * 状态;0未处理1下发任务2直接关闭
     */
    private String status;
    /**
     * 告警级别
     */
    private String level;
    /**
     * 任务编号
     */
    private String tkNo;

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
