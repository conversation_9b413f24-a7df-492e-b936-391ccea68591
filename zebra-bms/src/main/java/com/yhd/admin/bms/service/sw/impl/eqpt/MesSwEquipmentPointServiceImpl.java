package com.yhd.admin.bms.service.sw.impl.eqpt;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentPointDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentPointConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentPointDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentPoint;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentPointParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentPointService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 设备资料-温振报警点位管理
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@Service
public class MesSwEquipmentPointServiceImpl
    extends ServiceImpl<MesSwEquipmentPointDao, MesSwEquipmentPoint>
    implements MesSwEquipmentPointService {

  @Resource private MesSwEquipmentPointConvert convert;
  @Resource private DicService dicService;

  @Resource private RestTemplate restTemplate;

  @Value("${scada.url}")
  private String scadaUrl;
  // scada重新订阅
  private final String HANDLER_DATA_URL = "/point/rt/restart";

  @Override
  public IPage<MesSwEquipmentPointDTO> pagingQuery(MesSwEquipmentPointParam param) {
    Page<MesSwEquipmentPoint> iPage = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MesSwEquipmentPoint> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 查询参数
    // 设备编码及类别
    if (StringUtils.isNotBlank(param.getEquipmentNoType())) {
      queryChain.eq(MesSwEquipmentPoint::getEquipmentNoType, param.getEquipmentNoType());
    }
    // 类型
    if (StringUtils.isNotBlank(param.getTypeCode())) {
      queryChain.eq(MesSwEquipmentPoint::getTypeCode, param.getTypeCode());
    }
    // 判断点位名称
    if (StringUtils.isNotBlank(param.getPointName())) {
      queryChain.like(MesSwEquipmentPoint::getPointName, param.getPointName());
    }
    // 判断点位
    if (Objects.nonNull(param.getPointAddress())) {
      queryChain.like(MesSwEquipmentPoint::getPointAddress, param.getPointAddress());
    }

    // 排序
    queryChain.orderByDesc(MesSwEquipmentPoint::getCreatedTime);

    IPage<MesSwEquipmentPoint> page = queryChain.page(iPage);
    return page.convert(convert::toDTO);
  }

  @Override
  public MesSwEquipmentPointDTO getCurrentDetail(MesSwEquipmentPointParam param) {
    if (param.getId() == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    MesSwEquipmentPoint entity = super.getById(param.getId());
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "该点位已经不存在，请检查！");
    }
    return convert.toDTO(entity);
  }

  @Override
  public Boolean add(MesSwEquipmentPointParam param) {
    String equipmentNoType = param.getEquipmentNoType();
    Long equipmentDataId = param.getEquipmentDataId();
    String typeCode = param.getTypeCode();
    String pointName = param.getPointName();
    String pointAddress = param.getPointAddress();
    if (StringUtils.isBlank(equipmentNoType)
        || equipmentDataId == null
        || StringUtils.isBlank(typeCode)
        || StringUtils.isBlank(pointAddress)
        || StringUtils.isBlank(pointName)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    param.setTypeName(dicService.transform(DicConstant.DIC_POINT_TYPE, typeCode));
    MesSwEquipmentPoint entity = convert.toEntity(param);

    LambdaQueryWrapper<MesSwEquipmentPoint> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentPoint::getPointAddress, pointAddress);
    if (param.getId() != null && param.getId() != -1) {
      wrapper.ne(MesSwEquipmentPoint::getId, param.getId());
    }
    int num = this.count(wrapper);
    if (num > 0) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), pointAddress + "该点位已经存在，不能重复添加，请检查！");
    }
    boolean result = saveOrUpdate(entity);
    StringBuffer httpurl = new StringBuffer(scadaUrl).append(HANDLER_DATA_URL);
    Map<String, Object> params = new HashMap<>(1);
    HttpHeaders headers = new HttpHeaders();
    headers.add("Content-Type", "application/json");
    HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, headers);
    ResponseEntity<Object> responseEntity =
        restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);
    return result;
  }

  @Override
  public Boolean remove(MesSwEquipmentPointParam param) {
    if (param.getId() == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "参数为空，请检查！");
    }
    MesSwEquipmentPoint entity = super.getById(param.getId());
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "该点位已经不存在，请检查！");
    }
    boolean result = super.removeById(param.getId());
    StringBuffer httpurl = new StringBuffer(scadaUrl).append(HANDLER_DATA_URL);
    Map<String, Object> params = new HashMap<>(1);
    HttpHeaders headers = new HttpHeaders();
    headers.add("Content-Type", "application/json");
    HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, headers);
    ResponseEntity<Object> responseEntity =
        restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);
    return result;
  }
}
