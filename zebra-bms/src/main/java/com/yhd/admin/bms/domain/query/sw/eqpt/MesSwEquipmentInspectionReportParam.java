package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentInspectionReportParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = -146698747820093464L;
  /** 设备巡检表主键ID */
  private String parentId;

  /** 存在问题 */
  private String existQuestion;

  /** 照片 */
  private List<JSONObject> photo;

  /** 填报人账号 */
  private String account;

  /** 整改车间code */
  private Long zgWorkshopCode;

  /** 整改车间名称 */
  private String zgWorkshopName;
}
