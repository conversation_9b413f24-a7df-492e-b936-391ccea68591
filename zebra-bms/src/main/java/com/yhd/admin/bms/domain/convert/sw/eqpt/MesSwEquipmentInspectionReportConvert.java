package com.yhd.admin.bms.domain.convert.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionReportDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspectionReport;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionReportParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionReportVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
* 设备巡检检修填报
*
* <AUTHOR>
* @since 1.0.0 2024-08-19
*/
@Mapper(componentModel = "spring")
public interface MesSwEquipmentInspectionReportConvert {

    MesSwEquipmentInspectionReport toEntity(MesSwEquipmentInspectionReportParam param);

    MesSwEquipmentInspectionReportVO toVO(MesSwEquipmentInspectionReportDTO dto);

    MesSwEquipmentInspectionReportDTO toDTO(MesSwEquipmentInspectionReport entity);

    List<MesSwEquipmentInspectionReport> toEntityList(List<MesSwEquipmentInspectionReportParam> paramList);

    List<MesSwEquipmentInspectionReportVO> toVO(List<MesSwEquipmentInspectionReportDTO> dto);

    List<MesSwEquipmentInspectionReportDTO> toDTOList(List<MesSwEquipmentInspectionReport> entity);



}
