package com.yhd.admin.bms.domain.vo.sw.produce;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @<PERSON>
 * @Date 2025/4/23 14:21
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesEquipmentWarnTimeVO extends BaseVO {
    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
