package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.RegionDTO;
import com.yhd.admin.bms.domain.entity.sys.MesRegion;
import com.yhd.admin.bms.domain.query.sys.RegionParam;
import com.yhd.admin.bms.domain.vo.sys.RegionVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 数据转换
 * @date 2021/4/22 13:54
 */

@Mapper(componentModel = "spring")
public interface RegionConvert {

    MesRegion transformParamToEntity(RegionParam param);

    @Mapping(ignore = true, target = "children")
    RegionDTO transformEntityToDTO(MesRegion region);

    RegionVO transformDTOToVO(RegionDTO dto);


}
