package com.yhd.admin.bms.service.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentSievePlateRecordCountDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentSievePlateRecordDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentSievePlateRecord;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentSievePlateRecordParam;

import java.util.List;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
public interface MesSwEquipmentSievePlateRecordService
    extends IService<MesSwEquipmentSievePlateRecord> {

    IPage<MesSwEquipmentSievePlateRecordDTO> pagingQuery(MesSwEquipmentSievePlateRecordParam param);

    /**
     * 筛板寿命统计
     *
     * @param param
     * @return
     */
    List<MesSwEquipmentSievePlateRecordDTO> statisticsScrapPlate(
        MesSwEquipmentSievePlateRecordParam param);

    /**
     * 筛板更换记录-更换原因统计
     */
    MesSwEquipmentSievePlateRecordCountDTO getQueryRecordCount(MesSwEquipmentSievePlateRecordParam param);

    /**
     * 获取当前正在使用的材质及尺寸集合
     */
    List<MesSwEquipmentSievePlateRecordDTO> getListByMaterial(MesSwEquipmentSievePlateRecordParam param);
}
