package com.yhd.admin.bms.domain.vo.sw.goods;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 筛板物资计划
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwGoodsPlanVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 5935271974454162646L;
    /**
     * 计划月份
     */
    private String month;
    /**
     * 名称
     */
    private String name;
    /**
     * 计划物资种类数量
     */
    private Integer cateNum;
    /**
     * 操作人
     */
    private String createName;

    /**
     * 详情列表
     */
    private List<MesSwGoodsPlanDetailVO> detailList;
}
