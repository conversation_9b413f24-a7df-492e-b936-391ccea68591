package com.yhd.admin.bms.service.sw.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.constant.FlowConstant;
import com.yhd.admin.bms.dao.sw.MesSwSafeHiddenDangerDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafeHiddenDangerConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeHiddenDangerDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeHiddenDanger;
import com.yhd.admin.bms.domain.enums.flowable.CommentTypeEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeHiddenDangerParam;
import com.yhd.admin.bms.domain.query.sys.UserAccountParam;
import com.yhd.admin.bms.domain.vo.flowable.EndProcessVo;
import com.yhd.admin.bms.domain.vo.flowable.FlowTaskParam;
import com.yhd.admin.bms.domain.vo.flowable.StartProcessInstanceVo;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.flowable.IFlowableProcessInstanceService;
import com.yhd.admin.bms.service.flowable.IFlowableTaskService;
import com.yhd.admin.bms.service.sw.MesSwSafeHiddenDangerService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.FileService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class MesSwSafeHiddenDangerServiceImpl
    extends ServiceImpl<MesSwSafeHiddenDangerDao, MesSwSafeHiddenDanger>
    implements MesSwSafeHiddenDangerService {

  @Resource private MesSwSafeHiddenDangerConvert convert;
  @Resource private FileService fileService;
  @Resource private DicService dicService;
  @Resource private UserAccountService userAccountService;
  @Resource private IFlowableProcessInstanceService flowableProcessInstanceService;
  @Resource private IFlowableTaskService flowableTaskService;

  @Override
  public IPage<MesSwSafeHiddenDangerDTO> pagingQuery(MesSwSafeHiddenDangerParam queryParam) {
    Page<MesSwSafeHiddenDanger> page =
        new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<MesSwSafeHiddenDanger> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 检查日期
    if (queryParam.getStartDate() != null && queryParam.getEndDate() != null) {
      queryChain.between(
          MesSwSafeHiddenDanger::getCheckDate, queryParam.getStartDate(), queryParam.getEndDate());
    }
    // 检查类型
    queryChain.eq(
        StringUtils.isNoneBlank(queryParam.getCheckType()),
        MesSwSafeHiddenDanger::getCheckType,
        queryParam.getCheckType());
    // 整改负责人
    queryChain.like(
        StringUtils.isNoneBlank(queryParam.getRectifyChargeName()),
        MesSwSafeHiddenDanger::getRectifyChargeName,
        queryParam.getRectifyChargeName());
    // 当前整改状态
    queryChain.eq(
        StringUtils.isNoneBlank(queryParam.getStatusType()),
        MesSwSafeHiddenDanger::getStatusType,
        queryParam.getStatusType());
    // 责任部门/车间
    queryChain.eq(
        StringUtils.isNoneBlank(queryParam.getDutyDepType()),
        MesSwSafeHiddenDanger::getDutyDepType,
        queryParam.getDutyDepType());
    queryChain.orderByDesc(
        MesSwSafeHiddenDanger::getCheckDate, MesSwSafeHiddenDanger::getCreatedTime);
    return queryChain.page(page).convert(convert::toDTO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MesSwSafeHiddenDangerParam queryParam) {
    String rectifyCharge = queryParam.getRectifyCharge();
    if (StringUtils.isBlank(rectifyCharge)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "整改责任人为空，请检查！");
    }
    // 发起流程
    String proposer = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    StartProcessInstanceVo startProcessInstanceVo = new StartProcessInstanceVo();
    startProcessInstanceVo.setCreator(proposer);
    startProcessInstanceVo.setCurrentUserCode(proposer);
    startProcessInstanceVo.setFormName(FlowConstant.FLOW_SW_SAFE_INVESTIGATE_NAME);
    startProcessInstanceVo.setSystemSn("flow");
    startProcessInstanceVo.setProcessDefinitionKey(FlowConstant.FLOW_SW_SAFE_INVESTIGATE_KEY);
    startProcessInstanceVo.setBusinessKey(FlowConstant.FLOW_SW_SAFE_INVESTIGATE_KEY);
    Map<String, Object> variables = new HashMap<>();
    // 流程发起人
    variables.put("initiator", proposer);
    // 集控员
    variables.put("rectifyCharge", rectifyCharge);
    startProcessInstanceVo.setVariables(variables);
    RespJson<ProcessInstance> returnJson = new RespJson<>();
    // 实例ID
    String processInstanceId = null;
    try {
      returnJson = flowableProcessInstanceService.startProcessInstanceByKey(startProcessInstanceVo);
      processInstanceId = returnJson.getData().getProcessInstanceId();
    } catch (Exception e) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), e.getMessage());
    }
    queryParam.setProcessInstanceId(processInstanceId);
    // 检查类型
    queryParam.setCheckTypeName(
        dicService.transform(DicConstant.SAFE_CHECK, queryParam.getCheckType()));
    // 责任部门/车间
    queryParam.setDutyDepName(
        dicService.transform(DicConstant.SAFE_DUTY_WORKSHOP, queryParam.getDutyDepType()));
    // 严重程度
    queryParam.setSeverityName(
        dicService.transform(DicConstant.SAFE_SEVERITY, queryParam.getSeverityType()));
    // 整改期限
    queryParam.setDeadlineName(
        dicService.transform(DicConstant.SAFE_DEADLINE, queryParam.getDeadlineType()));
    // 整改责任人
    queryParam.setRectifyChargeName(getUserName(queryParam.getRectifyCharge()));
    // 当前整改状态，默认“整改中”
    String statusName =
        dicService.transform(DicConstant.SAFE_RECTIFY_STATUS, DicConstant.SAFE_RECTIFY_STATUS_ONE);
    queryParam.setStatusType(DicConstant.SAFE_RECTIFY_STATUS_ONE);
    queryParam.setStatusName(statusName);
    MesSwSafeHiddenDanger entity = convert.toEntity(queryParam);
    boolean result = saveOrUpdate(entity);
    // 图片
    saveFilePicture(
        queryParam.getPictureList(), DicConstant.SAFE_HIDDEN_DANGER_ONE_PICTURE, entity);
    // 视频
    saveFileVideo(queryParam.getVideoList(), DicConstant.SAFE_HIDDEN_DANGER_ONE_VIDEO, entity);
    return result;
  }

  /**
   * 保存图片
   *
   * @param fileList
   * @param entity
   */
  private void saveFilePicture(
      List<JSONObject> fileList, String fileType, MesSwSafeHiddenDanger entity) {
    if (CollectionUtil.isNotEmpty(fileList)) {
      fileService.insertFile(entity.getId(), DicConstant.SAFE_HIDDEN_DANGER, fileType, fileList);
    }
  }

  /**
   * 保存视频
   *
   * @param fileList
   * @param entity
   */
  private void saveFileVideo(
      List<JSONObject> fileList, String fileType, MesSwSafeHiddenDanger entity) {
    if (CollectionUtil.isNotEmpty(fileList)) {
      fileService.insertFile(entity.getId(), DicConstant.SAFE_HIDDEN_DANGER, fileType, fileList);
    }
  }

  @Override
  public MesSwSafeHiddenDangerDTO getCurrentDetail(MesSwSafeHiddenDangerParam param) {
    Long id = param.getId();
    String processInstanceId = param.getProcessInstanceId();
    if (org.springframework.util.StringUtils.isEmpty(processInstanceId)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "接口参数缺少，请检查！");
    }
    MesSwSafeHiddenDanger entity = null;
    if (StringUtils.isNotNull(id)) {
      // 详情页面进入，可以有id传来
      entity = super.getById(id);
    } else {
      // 任务看板页面进入，没有id传来
      LambdaQueryWrapper<MesSwSafeHiddenDanger> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(MesSwSafeHiddenDanger::getProcessInstanceId, processInstanceId);
      List<MesSwSafeHiddenDanger> list = this.list(wrapper);
      if (!CollectionUtils.isEmpty(list)) {
        entity = list.get(0);
        id = entity.getId();
      }
    }
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询不到该数据，请检查！");
    }
    MesSwSafeHiddenDangerDTO result = convert.toDTO(entity);
    // 当前登录人
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    // 获取taskId
    Task task = flowableTaskService.getTask(account, processInstanceId);
    if (task != null) result.setTaskId(task.getId());
    // 新建的图片和视频
    List<JSONObject> pictureList =
        fileService.getFileList(
            param.getId(),
            DicConstant.SAFE_HIDDEN_DANGER,
            DicConstant.SAFE_HIDDEN_DANGER_ONE_PICTURE);
    result.setPictureList(pictureList);
    List<JSONObject> videoList =
        fileService.getFileList(
            param.getId(),
            DicConstant.SAFE_HIDDEN_DANGER,
            DicConstant.SAFE_HIDDEN_DANGER_ONE_VIDEO);
    result.setVideoList(videoList);
    // 整改的图片和视频
    List<JSONObject> pictureRectifyList =
        fileService.getFileList(
            param.getId(),
            DicConstant.SAFE_HIDDEN_DANGER,
            DicConstant.SAFE_HIDDEN_DANGER_TWO_PICTURE);
    result.setPictureRectifyList(pictureRectifyList);
    List<JSONObject> videoRectifyList =
        fileService.getFileList(
            param.getId(),
            DicConstant.SAFE_HIDDEN_DANGER,
            DicConstant.SAFE_HIDDEN_DANGER_TWO_VIDEO);
    result.setVideoRectifyList(videoRectifyList);

    return result;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean complete(MesSwSafeHiddenDangerParam param) {
    String taskId = param.getTaskId();
    String processInstanceId = param.getProcessInstanceId();
    if (StringUtils.isBlank(processInstanceId)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "流程参数不能为空，请检查！");
    }
    if (StringUtils.isBlank(taskId)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "流程参数为空，任务不存在或已经处理过，请检查！");
    }
    Task task = flowableTaskService.getTask(taskId);
    if (task == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "任务不存在或已经处理过，请检查！");
    }
    Long id = param.getId();
    MesSwSafeHiddenDanger entity = super.getById(id);
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询不到数据，请检查！");
    }
    FlowTaskParam taskParam = new FlowTaskParam();
    taskParam.setTaskId(taskId);
    // 当前登录人
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    taskParam.setUserId(account);
    taskParam.setProcessInstanceId(processInstanceId);

    Map<String, Object> variables = new HashMap<>();
    // 数据库里的“处理人”为空，参数传来的“处理人”不为空
    if (StringUtils.isBlank(entity.getHandler())) {
      if (StringUtils.isBlank(param.getHandler())) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), "处理人员不能为空，请检查！");
      }
      // 流程节点-责任人指派，塞值：处理人员名称
      entity.setHandlerName(getUserName(param.getHandler()));
      entity.setHandler(param.getHandler());
      saveOrUpdate(entity);
      // 处理人
      variables.put("handler", param.getHandler());
      taskParam.setValues(variables);
    } else if (StringUtils.isBlank(entity.getRectifyFinish())) {
      // 数据库里的“完成整改”为空，参数传来的“完成整改”不为空
      if (StringUtils.isBlank(param.getRectifyFinish())
          || StringUtils.isBlank(param.getRectifyExplain())) {
        throw new BMSException(ResultStateEnum.FAIL.getCode(), "完成整改或整改情况不能为空，请检查！");
      }
      entity.setRectifyFinish(param.getRectifyFinish());
      entity.setRectifyExplain(param.getRectifyExplain());
      saveOrUpdate(entity);
      // 图片
      saveFilePicture(param.getPictureList(), DicConstant.SAFE_HIDDEN_DANGER_TWO_PICTURE, entity);
      // 视频
      saveFileVideo(param.getVideoList(), DicConstant.SAFE_HIDDEN_DANGER_TWO_VIDEO, entity);
    }
    flowableTaskService.complete(taskParam);
    return true;
  }

  /**
   * 作废
   *
   * @param param
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public RespJson<String> revokeProcess(MesSwSafeHiddenDangerParam param) {
    String processInstanceId = param.getProcessInstanceId();
    if (StringUtils.isBlank(processInstanceId)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "流程参数不能为空，请检查！");
    }
    Long id = param.getId();
    MesSwSafeHiddenDanger entity = null;
    if (StringUtils.isNotNull(id)) {
      // 详情页面进入，可以有id传来
      entity = super.getById(id);
    } else {
      // 任务看板页面进入，没有id传来
      LambdaQueryWrapper<MesSwSafeHiddenDanger> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(MesSwSafeHiddenDanger::getProcessInstanceId, processInstanceId);
      List<MesSwSafeHiddenDanger> list = this.list(wrapper);
      if (!CollectionUtils.isEmpty(list)) {
        entity = list.get(0);
      }
    }
    if (entity == null) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询不到该数据，请检查！");
    }
    EndProcessVo paramProcess = new EndProcessVo();
    paramProcess.setProcessInstanceId(processInstanceId);
    // 当前登录人
    String account = UserContextHolder.getUserDetail().getUserInfo().getAccountName();
    paramProcess.setUserCode(account);
    paramProcess.setMessage(CommentTypeEnum.LCZZ.getName());
    // 如果，是否整改完成，字段为空，则是终止操作，当前整改状态是：已终止
    String statusName =
        dicService.transform(DicConstant.SAFE_RECTIFY_STATUS, DicConstant.SAFE_RECTIFY_STATUS_ZERO);
    entity.setStatusType(DicConstant.SAFE_RECTIFY_STATUS_ZERO);
    entity.setStatusName(statusName);
    this.saveOrUpdate(entity);
    return flowableProcessInstanceService.stopProcessInstanceById(paramProcess);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String stopSafeHiddenDanger() {
    LambdaQueryWrapper<MesSwSafeHiddenDanger> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwSafeHiddenDanger::getStatusType, DicConstant.SAFE_RECTIFY_STATUS_ONE);
    wrapper.le(MesSwSafeHiddenDanger::getDeadlineTime, LocalDateTime.now());
    List<MesSwSafeHiddenDanger> list = this.list(wrapper);
    EndProcessVo paramProcess = null;
    StringBuffer id = new StringBuffer();
    if (!CollectionUtils.isEmpty(list)) {
      for (MesSwSafeHiddenDanger entity : list) {
        // 默认处理结果
        entity.setRectifyFinish(DicConstant.SAFE_RECTIFY_FINISH_NO);
        entity.setRectifyExplain("超时未处理");
        // 任务超时或整改未完成，当前整改状态是：遗留问题
        String statusName =
            dicService.transform(
                DicConstant.SAFE_RECTIFY_STATUS, DicConstant.SAFE_RECTIFY_STATUS_EIGHT);
        entity.setStatusType(DicConstant.SAFE_RECTIFY_STATUS_EIGHT);
        entity.setStatusName(statusName);

        paramProcess = new EndProcessVo();
        paramProcess.setProcessInstanceId(entity.getProcessInstanceId());
        paramProcess.setUserCode("_admin");
        paramProcess.setMessage("超时未处理");
        // 返回id串
        id.append(",").append(entity.getId());
        try {
          flowableProcessInstanceService.stopProcessInstanceById(paramProcess);
        } catch (Exception e) {
          id.append(",").append(e.getMessage());
        } finally {

        }
      }
      this.saveOrUpdateBatch(list);
    }
    return id.toString();
  }

  private String getUserName(String userAccount) {
    if (StringUtils.isBlank(userAccount)) {
      return "";
    }
    UserAccountParam accountParam = new UserAccountParam();
    accountParam.setUsername(userAccount);
    // 查询账户
    Optional<UserAccountDTO> accountOptional = userAccountService.getUserAccount(accountParam);
    if (accountOptional.isPresent()) {
      userAccount = accountOptional.get().getName();
    }
    return userAccount;
  }
}
