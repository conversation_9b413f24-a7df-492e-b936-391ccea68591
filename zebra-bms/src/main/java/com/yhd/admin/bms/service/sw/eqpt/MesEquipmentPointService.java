package com.yhd.admin.bms.service.sw.eqpt;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesEquipmentPointDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesEquipmentPoint;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesEquipmentPointParam;

import java.util.List;

/**
 * 设备健康中心-设备点位管理
 *
 * <AUTHOR>
 * @date 2025/4/23 10:22
 */
public interface MesEquipmentPointService extends IService<MesEquipmentPoint> {

    /**
     * 根据条件查询分页列表
     *
     * @param param 查询参数
     * @return 设备点位分页列表信息
     */
    IPage<MesEquipmentPointDTO> pagingQuery(MesEquipmentPointParam param);

    /**
     * 查询详情信息
     *
     * @param param 查询参数：主键id
     * @return 设备点位详情信息
     */
    MesEquipmentPointDTO getCurrentDetail(MesEquipmentPointParam param);

    /**
     * 新增或修改
     *
     * @param param 设备点位参数
     * @return true成功，false失败
     */
    Boolean addOrModify(MesEquipmentPointParam param);

    /**
     * 根据条件查询列表
     *
     * @param param 查询条件
     * @return 设备点位列表信息
     */
    List<MesEquipmentPointDTO> queryList(MesEquipmentPointParam param);

    /**
     * 批量删除
     *
     * @param param 主键id列表
     * @return true成功，false失败
     */
    Boolean removeBatch(MesEquipmentPointParam param);

    /**
     * 校验点位地址唯一性
     *
     * @param param 设备点位参数
     * @return true唯一，false重复
     */
    Boolean isOnly(MesEquipmentPointParam param);
}
