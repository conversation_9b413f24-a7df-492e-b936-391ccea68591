package com.yhd.admin.bms.domain.query.sw.exam;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPaperQuestionUserAnswerTypeGroupDTO;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户考试信息
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwUserExamParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 7041798856203505680L;
  /** 开始日期(yyyy-MM-dd) */
  private LocalDate startDate;
  /** 结束日期(yyyy-MM-dd) */
  private LocalDate endDate;

  /** 用户账号 */
  private String accountName;
  /** 用户姓名 */
  private String realName;
  /** 考试计划表主键id */
  private Long examPlanId;
  //06.30修改
  /**
   * 计划考试开始日期
   */
  private LocalDate examPlanStartDate;
  /**
   * 计划考试结束日期
   */
  private LocalDate examPlanEndDate;
  /** 计划考试结束时间 */
  private LocalDateTime examPlanEndTime;
  /** 考试警报时间 */
  private LocalDateTime examWarnTime;
  /** 计划考试时长(分钟) */
  private Long examTimeLength;
  /** 考试试卷(组卷)主键id */
  private Long examPaperId;
  /** 考试试卷标题 */
  private String paperTitle;
  /** 考试试卷分类 */
  private String paperClassify;
  /** 考试状态：0未开始、1进行中、2已完成 */
  private String examStatus;

  private String examStatusName;
  /** 考试开始时间 */
  private LocalDateTime examStartTime;
  /** 考试结束时间 */
  private LocalDateTime examEndTime;
  /** 试卷总分 */
  private Integer paperScore;
  /** 用户最终得分 */
  private Integer userScore;
  /** 试卷题目数量 */
  private Integer questionCount;
  /** 做题时间(秒) */
  private Long doTime;
  /** 相关培训内容 */
  private String trainingContent;
  /** 考试学员组织编码 */
  private Long departmentId;
  /** 考试学员组织名称 */
  private String department;

  /** 试卷题目列表 */
  @TableField(exist = false)
  private List<MesSwExamPaperQuestionUserAnswerParam> paperQuestionList;
  /** 试卷题目题型分组列表 */
  @TableField(exist = false)
  private List<MesSwExamPaperQuestionUserAnswerTypeGroupDTO> typeGroupQuestionList;
  //06.30修改
  /**
   * 合格分数
   */
  private Integer passScore;
}
