package com.yhd.admin.bms.domain.convert.sw.ticket;

import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserDTO;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpaceSignUser;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketConfinedSpaceSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserParam;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserVO;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketConfinedSpaceSignUserVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
* 受限空间许可证-签字表
*
* <AUTHOR>
* @since 1.0.0 2024-10-29
*/
@Mapper(componentModel = "spring")
public interface MesSwWorkTicketConfinedSpaceSignUserConvert {

    MesSwWorkTicketConfinedSpaceSignUser toEntity(MesSwWorkTicketConfinedSpaceSignUserParam param);

    MesSwWorkTicketConfinedSpaceSignUserVO toVO(MesSwWorkTicketConfinedSpaceSignUserDTO dto);

    MesSwWorkTicketConfinedSpaceSignUserDTO toDTO(MesSwWorkTicketConfinedSpaceSignUser entity);

    List<MesSwWorkTicketConfinedSpaceSignUserDTO> toDTOs(List<MesSwWorkTicketConfinedSpaceSignUser> entity);

    List<MesSwWorkTicketConfinedSpaceSignUser> toEntitys(List<MesSwWorkTicketConfinedSpaceSignUserParam> param);

    List<MesSwWorkTicketConfinedSpaceSignUserVO> toVOs(List<MesSwWorkTicketConfinedSpaceSignUserDTO> dto);

}
