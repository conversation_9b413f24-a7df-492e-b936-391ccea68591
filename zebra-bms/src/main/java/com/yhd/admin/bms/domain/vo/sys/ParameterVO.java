package com.yhd.admin.bms.domain.vo.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ParameterVO.java
 * @Description TODO
 * @createTime 2020年05月12日 14:52:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ParameterVO extends BaseVO implements Cloneable, Serializable {

    /**
     * 参数KEY
     */
    private String paramKey;
    /**
     * 参数值
     */
    private String paramVal;
    /**
     * 状态;0否1是
     */
    private Boolean isEnable;
    /**
     * 参数类型
     */
    private String paramType;
    /**
     * 参数编号
     */
    private String paramNo;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String toString() {
        return "ParameterVO{" + "paramKey='" + paramKey + '\'' + ", paramVal='" + paramVal + '\''
            + ", isEnable="
            + isEnable + '}';
    }
}
