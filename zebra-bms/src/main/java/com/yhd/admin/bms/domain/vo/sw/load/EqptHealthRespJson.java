package com.yhd.admin.bms.domain.vo.sw.load;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 设备健康中心-返回json
 * <AUTHOR>
 * @Date 2025/3/6 9:19
 * @Version 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EqptHealthRespJson {
    /** 响应码：1 成功、0 失败 */
    private String code;
    /** 响应消息 */
    private String msg;
    /** 数据集 */
    private Object data;
}
