package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.dto.sw.MesCoalBinStatusDTO;
import com.yhd.admin.bms.domain.query.sw.MesCoalBinStatusParam;

import java.util.List;

/**
 * 煤仓情状表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14 16:31:40
 */
public interface MesCoalBinStatusService {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MesCoalBinStatusDTO> pagingQuery(MesCoalBinStatusParam param);

    List<MesCoalBinStatusDTO> getLatestByNames();

    /**
     * 新增或修改数据
     *
     * @param param
     * @return
     */
    Boolean addOrModify(MesCoalBinStatusParam param);

    Boolean addBatch(MesCoalBinStatusParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param
     * @return
     */
    MesCoalBinStatusDTO getCurrentDetails(MesCoalBinStatusParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MesCoalBinStatusParam param);

}
