package com.yhd.admin.bms.service.sw.impl.exam;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.exam.MesSwUserExamDao;
import com.yhd.admin.bms.domain.convert.sw.exam.MesSwExamPaperQuestionUserAnswerConvert;
import com.yhd.admin.bms.domain.convert.sw.exam.MesSwUserExamConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperDTO;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperTopicDTO;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPaperQuestionUserAnswerDTO;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPaperQuestionUserAnswerTypeGroupDTO;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwUserExamDTO;
import com.yhd.admin.bms.domain.dto.sys.DicItemDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwExamPaperQuestion;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwExamPaperQuestionUserAnswer;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwExamPlan;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwUserExam;
import com.yhd.admin.bms.domain.entity.sys.MesNotice;
import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.enums.NotifyEnum;
import com.yhd.admin.bms.domain.enums.safe.ExamPaperQuestionTypeEnum;
import com.yhd.admin.bms.domain.enums.safe.ExamStatusEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperParam;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwExamPlanParam;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwUserExamParam;
import com.yhd.admin.bms.domain.vo.sw.exam.QuestionAnswerOptionVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesSwSafeExamPaperService;
import com.yhd.admin.bms.service.sw.exam.MesSwExamPaperQuestionService;
import com.yhd.admin.bms.service.sw.exam.MesSwExamPaperQuestionUserAnswerService;
import com.yhd.admin.bms.service.sw.exam.MesSwExamPlanService;
import com.yhd.admin.bms.service.sw.exam.MesSwUserExamService;
import com.yhd.admin.bms.service.sys.DicItemService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.bms.webscoket.service.WebsocketService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.common.utils.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 我的考试-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/3/4 19:30
 */
@Service
public class MesSwUserExamServiceImpl extends ServiceImpl<MesSwUserExamDao, MesSwUserExam>
    implements MesSwUserExamService {
    private static final Logger logger = LoggerFactory.getLogger(MesSwUserExamServiceImpl.class);

    @Autowired
    private MesSwExamPlanService examPlanService;
    @Autowired
    private MesSwUserExamConvert userExamConvert;
    @Autowired
    private MesSwExamPaperQuestionUserAnswerService questionUserAnswerService;
    @Autowired
    private MesSwExamPaperQuestionService paperQuestionService;
    @Autowired
    private DicItemService dicItemService;
    @Autowired
    private MesSwSafeExamPaperService examPaperService;
    @Autowired
    private WebsocketService websocketService;
    @Autowired
    private MesSwExamPaperQuestionUserAnswerConvert examPaperQuestionUserAnswerConvert;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private UserAccountService accountService;
    @Autowired
    private MesSwExamPaperQuestionUserAnswerService userAnswerService;

    // 答案选项标题
    private static final List<String> ABC =
        Arrays.asList(
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean initUserExam(MesSwExamPlanParam examPlanParam) {
        logger.debug("请求调用【初始化用户考试】接口开始，请求参数：{}", examPlanParam);
        //06.30修改
        List<UserAccountDTO> userList = new ArrayList<>();
        //判断是第一次初始化，还是重考初始化（重考时，重考账号非空）
        if (StringUtils.isNotBlank(examPlanParam.getAgainAccount())) {
            //代表重考，userList只有这个人
            UserAccountDTO accountDTO = accountService.currentDetail(examPlanParam.getAgainAccount());
            userList.add(accountDTO);
        } else {
            // 获取缓存中，考生列表数据
            HashOperations<String, String, List<UserAccountDTO>> hashOperations =
                redisTemplate.opsForHash();
            userList =
                hashOperations.get(
                    BMSRedisKeyEnum.PLAN_EXAM_USER.getKey(), examPlanParam.getId().toString());
            if (CollectionUtil.isEmpty(userList)) {
                throw new BMSException("error", "考生来源列表为空，请检查");
            }
            // 删除redis缓存考生列表数据
            hashOperations.delete(
                BMSRedisKeyEnum.PLAN_EXAM_USER.getKey(), examPlanParam.getId().toString());
        }
        // 查询考试计划信息
        MesSwExamPlanDTO examPlan = examPlanService.getCurrentDetail(examPlanParam);
        if (Objects.isNull(examPlan)) {
            throw new BMSException("error", "考试计划不存在，请检查");
        }
//        // 查询此计划考试信息是否生成
//        List<MesSwUserExam> swUserExams = this.queryListByUserPlanId(examPlanParam.getId());
//        if (!CollectionUtil.isEmpty(swUserExams)) {
//            logger.error("数据库已存在计划id={}，的用户考试数据", examPlanParam.getId());
//            throw new BMSException("error", "考试计划已发布，请检查");
//        }

        // 查询组好的试卷信息
        MesSwSafeExamPaperParam paperParam = new MesSwSafeExamPaperParam();
        paperParam.setId(examPlan.getExamPaperId());
        MesSwSafeExamPaperDTO examPaper = examPaperService.getCurrentDetail(paperParam);
        if (Objects.isNull(examPaper)) {
            throw new BMSException("error", "考试计划选中试卷已不存在，请检查");
        }
        List<MesSwSafeExamPaperTopicDTO> topicList = examPaper.getTopicList();
        if (CollectionUtil.isEmpty(topicList)) {
            throw new BMSException("error", "考试计划选中试卷试题列表不存在，请检查");
        }
        //06.30修改
        // 1.先查出老试卷
        LambdaQueryWrapper<MesSwExamPaperQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwExamPaperQuestion::getExamPlanId, examPlanParam.getId());
        List<MesSwExamPaperQuestion> paperQuestionList = paperQuestionService.list(wrapper);
        if (CollectionUtil.isEmpty(paperQuestionList)) {
            //没有老试卷，代表初始化新试卷
            AtomicInteger questionNum = new AtomicInteger();
            topicList.forEach(
                topic -> {
                    MesSwExamPaperQuestion paperQuestion = new MesSwExamPaperQuestion();
                    paperQuestion.setSerialNum((questionNum.getAndIncrement() + 1) + "");
                    paperQuestion.setExamPlanId(examPlanParam.getId()); // 考试计划表id
                    paperQuestion.setExamTimeLength(examPlan.getExamDuration()); // 计划考试时长
                    paperQuestion.setPaperTitle(examPlan.getExamTitle()); // 试卷标题
                    paperQuestion.setPaperClassify(examPlan.getExamType()); // 试卷类型
                    paperQuestion.setQuestionType(topic.getQuestionType()); // 题型
                    paperQuestion.setQuestionContent(topic.getQuestionContent()); // 试题内容
                    paperQuestion.setDifficultyType(topic.getDifficultyType()); // 难度类型
                    paperQuestion.setQuestionTitle(topic.getTitle()); // 试题标题
                    paperQuestion.setQuestionAnswerOption(topic.getQuestionAnswer()); // 试题答案选项
                    paperQuestion.setQuestionRightAnswer(topic.getCorrectAnswer()); // 试题正确答案
                    paperQuestion.setFileUrl(topic.getFileUrl()); // 试题附件
                    paperQuestion.setFileType(topic.getFileType()); // 试题类型
                    paperQuestion.setQuestionSetter(topic.getQuestionSetter()); // 出题人
                    paperQuestion.setQuestionSetterName(topic.getQuestionSetterName()); // 出题人名称
                    paperQuestion.setTrainingContent(topic.getTrainingContent()); // 培训内容
                    paperQuestion.setQuestionScore(topic.getScore());
                    paperQuestionList.add(paperQuestion);
                });
            paperQuestionService.saveBatch(paperQuestionList);
        }
        // 筛选判断题
        List<MesSwExamPaperQuestion> judgeChoiceList =
            paperQuestionList.stream()
                .filter(
                    question ->
                        ExamPaperQuestionTypeEnum.JUDGE_CHOICE
                            .getCode()
                            .equals(question.getQuestionType()))
                .collect(Collectors.toList());
        // 筛选单项选择题
        List<MesSwExamPaperQuestion> singleChoiceList =
            paperQuestionList.stream()
                .filter(
                    question ->
                        ExamPaperQuestionTypeEnum.SINGLE_CHOICE
                            .getCode()
                            .equals(question.getQuestionType()))
                .collect(Collectors.toList());
        // 多项选择题
        List<MesSwExamPaperQuestion> multiChoiceList =
            paperQuestionList.stream()
                .filter(
                    question ->
                        ExamPaperQuestionTypeEnum.MULTI_CHOICE
                            .getCode()
                            .equals(question.getQuestionType()))
                .collect(Collectors.toList());

        // 2. 生成用户考试表数据、
        List<MesSwUserExam> userExamList = Lists.newArrayList();
        // 题型顺序按数据字典正排序
        List<DicItemDTO> itemList = dicItemService.queryDicItemByDicId("EXAM_QUESTION_TYPE");
        if (!CollectionUtil.isEmpty(userList)) {
            userList.forEach(
                user -> {
                    MesSwUserExam userExam = new MesSwUserExam();
                    userExam.setAccountName(user.getUsername()); // 用户账号
                    userExam.setRealName(user.getName()); // 用户姓名
                    userExam.setExamPlanId(examPlan.getId()); // 考试计划表主键
                    userExam.setExamPlanStartDate(examPlan.getExamStartDate()); // 计划考试开始日期
                    userExam.setExamPlanEndDate(examPlan.getExamEndDate()); // 计划考试结束日期
                    userExam.setExamTimeLength(examPlan.getExamDuration()); // 考试时长
                    userExam.setExamPaperId(examPlan.getExamPaperId()); // 考试试卷(组卷)主键id
                    userExam.setPaperTitle(examPlan.getExamTitle()); // 试卷标题
                    userExam.setPaperClassify(examPlan.getExamType()); // 试卷分类
                    userExam.setPaperScore(examPaper.getPaperScore()); // 试卷总分
                    //06.30修改,添加合格分数字段
                    userExam.setPassScore(examPlan.getPassScore()); // 合格分数

                    userExam.setQuestionCount(topicList.size()); // 试题总数
                    userExam.setTrainingContent(
                        StringUtils.isNotBlank(examPlan.getTrainingContent())
                            ? examPlan.getTrainingContent()
                            : ""); // 培训内容
                    userExam.setDepartmentId(user.getDepartmentId()); // 组织id
                    userExam.setDepartment(user.getDepartment()); // 组织名称
                    userExamList.add(userExam);
                });
        }
        super.saveBatch(userExamList);

        // 3. 生成用户试卷题目表数据
        List<MesSwExamPaperQuestionUserAnswer> nowPaperQuestion = Lists.newArrayList();
        userExamList.forEach(
            userExam -> {
                // 按照考试计划规则生成用户试卷题目列表
                AtomicInteger nowQuestionNum = new AtomicInteger();
                itemList.forEach(
                    item -> {
                        if (item.getVal().equals(ExamPaperQuestionTypeEnum.JUDGE_CHOICE.getCode())) {
                            // 判断题生成考试试卷数据处理
                            if (!CollectionUtil.isEmpty(judgeChoiceList)) {
                                if (examPlan.getQuestionDisorder()) { // 题目乱序
                                    Collections.shuffle(judgeChoiceList);
                                    Collections.shuffle(judgeChoiceList);
                                }
                                judgeChoiceList.forEach(
                                    judgeChoice -> {
                                        MesSwExamPaperQuestionUserAnswer question =
                                            new MesSwExamPaperQuestionUserAnswer();
                                        question.setUserExamId(userExam.getId()); // 用户考试表主键
                                        question.setPaperQuestionId(judgeChoice.getId()); // 计划考试试卷题目表主键id
                                        question.setPaperTitle(judgeChoice.getPaperTitle()); // 考试试卷标题
                                        question.setSerialNum((nowQuestionNum.getAndIncrement() + 1)); // 试卷序列号
                                        question.setPaperClassify(judgeChoice.getPaperClassify()); // 考试试卷分类
                                        question.setQuestionType(judgeChoice.getQuestionType()); // 试卷题型
                                        question.setQuestionContent(judgeChoice.getQuestionContent()); // 试题题目内容
                                        question.setDifficultyType(judgeChoice.getDifficultyType()); // 试题难度
                                        question.setQuestionTitle(judgeChoice.getQuestionTitle()); // 试题标题
                                        // 答案选项处理
                                        List<QuestionAnswerOptionVO> optionList = Lists.newArrayList();
                                        String option = judgeChoice.getQuestionAnswerOption();
                                        String rightAnswerContent = judgeChoice.getQuestionRightAnswer();
                                        StringBuilder rightAnswerOption = new StringBuilder();
                                        String[] optionArray = option.split("@&&@");
                                        for (int i = 0; i < optionArray.length; i++) {
                                            QuestionAnswerOptionVO optionVO = new QuestionAnswerOptionVO();
                                            optionVO.setSequence(i);
                                            optionVO.setOptionTitle(ABC.get(i));
                                            optionVO.setOptionContent(optionArray[i]);
                                            if (optionArray[i].equals(rightAnswerContent)) { // 匹配正确答案，正确答案内容匹配选项都正确
                                                optionVO.setCorrect("0");
                                                rightAnswerOption.append(ABC.get(i));
                                            } else {
                                                optionVO.setCorrect("1");
                                            }
                                            optionList.add(optionVO);
                                        }

                                        question.setQuestionAnswerOption(JSON.toJSONString(optionList));
                                        question.setQuestionRightAnswer(rightAnswerOption.toString());
                                        question.setFileUrl(judgeChoice.getFileUrl()); // 试题附件
                                        question.setFileType(judgeChoice.getFileType()); // 附件类型
                                        question.setQuestionSetter(judgeChoice.getQuestionSetter());
                                        question.setQuestionSetterName(judgeChoice.getQuestionSetterName());
                                        question.setTrainingContent(judgeChoice.getTrainingContent());
                                        question.setQuestionScore(judgeChoice.getQuestionScore());

                                        nowPaperQuestion.add(question);
                                    });
                            }
                        } else if (item.getVal()
                            .equals(ExamPaperQuestionTypeEnum.SINGLE_CHOICE.getCode())) {
                            if (!CollectionUtil.isEmpty(singleChoiceList)) {
                                if (examPlan.getQuestionDisorder()) { // 题目乱序
                                    Collections.shuffle(singleChoiceList);
                                    Collections.shuffle(singleChoiceList);
                                }
                                singleChoiceList.forEach(
                                    singleChoice -> {
                                        MesSwExamPaperQuestionUserAnswer question =
                                            new MesSwExamPaperQuestionUserAnswer();
                                        question.setUserExamId(userExam.getId()); // 用户考试表主键
                                        question.setPaperQuestionId(singleChoice.getId()); // 计划考试试卷题目表主键id
                                        question.setPaperTitle(singleChoice.getPaperTitle()); // 考试试卷标题
                                        question.setSerialNum((nowQuestionNum.getAndIncrement() + 1)); // 试卷序列号
                                        question.setPaperClassify(singleChoice.getPaperClassify()); // 考试试卷分类
                                        question.setQuestionType(singleChoice.getQuestionType()); // 试卷题型
                                        question.setQuestionContent(singleChoice.getQuestionContent()); // 试题题目内容
                                        question.setDifficultyType(singleChoice.getDifficultyType()); // 试题难度
                                        question.setQuestionTitle(singleChoice.getQuestionTitle()); // 试题标题
                                        // 答案选项处理
                                        List<QuestionAnswerOptionVO> optionList = Lists.newArrayList();
                                        String option = singleChoice.getQuestionAnswerOption();
                                        String rightAnswerContent = singleChoice.getQuestionRightAnswer();
                                        String[] rightAnswerArray = rightAnswerContent.split("@&&@");

                                        StringBuilder rightAnswerOption = new StringBuilder();
                                        String[] optionArray = option.split("@&&@");
                                        List<String> collect =
                                            Arrays.stream(optionArray).collect(Collectors.toList());
                                        if (examPlan.getAnswerDisorder()) { // 答案选项乱序
                                            Collections.shuffle(collect);
                                            Collections.shuffle(collect);
                                        }
                                        for (int i = 0; i < collect.size(); i++) {
                                            QuestionAnswerOptionVO optionVO = new QuestionAnswerOptionVO();
                                            optionVO.setSequence(i);
                                            optionVO.setOptionTitle(ABC.get(i));
                                            optionVO.setOptionContent(collect.get(i));
                                            if (ArrayUtil.contains(
                                                rightAnswerArray, collect.get(i))) { // 匹配正确答案，正确答案内容匹配选项都正确
                                                optionVO.setCorrect("0");
                                                rightAnswerOption.append(ABC.get(i));
                                            } else {
                                                optionVO.setCorrect("1");
                                            }
                                            optionList.add(optionVO);
                                        }

                                        question.setQuestionAnswerOption(JSON.toJSONString(optionList));
                                        question.setQuestionRightAnswer(rightAnswerOption.toString());
                                        question.setFileUrl(singleChoice.getFileUrl()); // 试题附件
                                        question.setFileType(singleChoice.getFileType()); // 附件类型
                                        question.setQuestionSetter(singleChoice.getQuestionSetter());
                                        question.setQuestionSetterName(singleChoice.getQuestionSetterName());
                                        question.setTrainingContent(singleChoice.getTrainingContent());
                                        question.setQuestionScore(singleChoice.getQuestionScore());

                                        nowPaperQuestion.add(question);
                                    });
                            }

                        } else if (item.getVal().equals(ExamPaperQuestionTypeEnum.MULTI_CHOICE.getCode())) {
                            if (!CollectionUtil.isEmpty(multiChoiceList)) {
                                if (examPlan.getQuestionDisorder()) { // 题目乱序
                                    Collections.shuffle(multiChoiceList);
                                    Collections.shuffle(multiChoiceList);
                                }
                                multiChoiceList.forEach(
                                    multiChoice -> {
                                        MesSwExamPaperQuestionUserAnswer question =
                                            new MesSwExamPaperQuestionUserAnswer();
                                        question.setUserExamId(userExam.getId()); // 用户考试表主键
                                        question.setPaperQuestionId(multiChoice.getId()); // 计划考试试卷题目表主键id
                                        question.setPaperTitle(multiChoice.getPaperTitle()); // 考试试卷标题
                                        question.setSerialNum((nowQuestionNum.getAndIncrement() + 1)); // 试卷序列号
                                        question.setPaperClassify(multiChoice.getPaperClassify()); // 考试试卷分类
                                        question.setQuestionType(multiChoice.getQuestionType()); // 试卷题型
                                        question.setQuestionContent(multiChoice.getQuestionContent()); // 试题题目内容
                                        question.setDifficultyType(multiChoice.getDifficultyType()); // 试题难度
                                        question.setQuestionTitle(multiChoice.getQuestionTitle()); // 试题标题
                                        // 答案选项处理
                                        List<QuestionAnswerOptionVO> optionList = Lists.newArrayList();
                                        String option = multiChoice.getQuestionAnswerOption();
                                        String rightAnswerContent = multiChoice.getQuestionRightAnswer();
                                        String[] rightAnswerArray = rightAnswerContent.split("@&&@");

                                        StringBuilder rightAnswerOption = new StringBuilder();
                                        String[] optionArray = option.split("@&&@");
                                        List<String> collect =
                                            Arrays.stream(optionArray).collect(Collectors.toList());
                                        if (examPlan.getAnswerDisorder()) { // 答案选项乱序
                                            Collections.shuffle(collect);
                                            Collections.shuffle(collect);
                                        }
                                        for (int i = 0; i < collect.size(); i++) {
                                            QuestionAnswerOptionVO optionVO = new QuestionAnswerOptionVO();
                                            optionVO.setSequence(i);
                                            optionVO.setOptionTitle(ABC.get(i));
                                            optionVO.setOptionContent(collect.get(i));
                                            if (ArrayUtil.contains(
                                                rightAnswerArray, collect.get(i))) { // 匹配正确答案，正确答案内容匹配选项都正确
                                                optionVO.setCorrect("0");
                                                rightAnswerOption.append(ABC.get(i));
                                            } else {
                                                optionVO.setCorrect("1");
                                            }
                                            optionList.add(optionVO);
                                        }

                                        question.setQuestionAnswerOption(JSON.toJSONString(optionList));
                                        question.setQuestionRightAnswer(rightAnswerOption.toString());
                                        question.setFileUrl(multiChoice.getFileUrl()); // 试题附件
                                        question.setFileType(multiChoice.getFileType()); // 附件类型
                                        question.setQuestionSetter(multiChoice.getQuestionSetter());
                                        question.setQuestionSetterName(multiChoice.getQuestionSetterName());
                                        question.setTrainingContent(multiChoice.getTrainingContent());
                                        question.setQuestionScore(multiChoice.getQuestionScore());

                                        nowPaperQuestion.add(question);
                                    });
                            }
                        }
                    });
            });
        questionUserAnswerService.saveBatch(nowPaperQuestion);
        logger.debug("发布考试计划，,计划ID={}，初始化生成用户考试信息成功！！！", examPlanParam.getId());
        //06.30修改，重考不需要发送通知，只有第一次初始化时才发送通知
        if (StringUtils.isBlank(examPlanParam.getAgainAccount())) {
            this.sendUserExamMsg(userList);
        }
        // 发送安全考试通知
        logger.debug("考试 计划：{}，的安全考试消息发送成功！！！，考生数量：{}", examPlanParam.getId(), userList.size());

        return true;
    }

    @Override
    public IPage<MesSwUserExamDTO> pagingQuery(MesSwUserExamParam param) {
        UserDTO userInfo = UserContextHolder.getUserInfo();
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
            throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
        }
        logger.debug("用户账号：{}，请求【根据条件查询我的考试分页列表】接口开始", userInfo.getAccountName());
        LambdaQueryChainWrapper<MesSwUserExam> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        // 用户账号
        queryChain.eq(MesSwUserExam::getAccountName, userInfo.getAccountName());
        // 考试日期区间
        if (Objects.nonNull(param.getStartDate()) && Objects.nonNull(param.getEndDate())) {
            queryChain.between(MesSwUserExam::getExamPlanStartDate, param.getStartDate(), param.getEndDate())
                .or().between(MesSwUserExam::getExamPlanEndDate, param.getStartDate(), param.getEndDate());
        }
        // 排序
        queryChain
            .orderByDesc(MesSwUserExam::getCreatedTime);
        // 查询所有记录
        List<MesSwUserExam> allRecords = queryChain.list();
        //根据培训计划分组
        Map<Long, List<MesSwUserExam>> examMap = allRecords.stream().collect(Collectors.groupingBy(MesSwUserExam::getExamPlanId));
        //每组只保留最新的一条数据
        List<MesSwUserExam> distinctRecords = examMap.values().stream().map(list -> list.stream().max(Comparator.comparing(MesSwUserExam::getCreatedTime)).orElse(null)).collect(Collectors.toList());
        distinctRecords.forEach(v -> {
            //代表有重考，并且最新一次的考试没有分数
            if (v.getUserScore() == null && examMap.get(v.getExamPlanId()).size() > 1) {
                //将分数赋值为上一张考试的分数，也就是第二条数据
                List<MesSwUserExam> list = examMap.get(v.getExamPlanId());
                if (list != null && list.size() > 0) {
                    v.setUserScore(list.get(1).getUserScore());
                }
            }
            //计算是否合格
            if (v.getUserScore() != null && v.getPassScore() != null) {
                v.setIfPass(v.getUserScore() >= v.getPassScore());
            }
        });
        distinctRecords.sort(Comparator.comparing(MesSwUserExam::getCreatedTime).reversed());
        // 分页操作
        int currentPage = Math.toIntExact(param.getCurrent());
        int pageSize = Math.toIntExact(param.getPageSize());
        int totalRecords = distinctRecords.size();
        int totalPages = (totalRecords + pageSize - 1) / pageSize;
        if (currentPage > totalPages) {
            currentPage = totalPages;
        }
        int fromIndex = (currentPage - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalRecords);
        List<MesSwUserExam> paginatedRecords = distinctRecords.subList(fromIndex, toIndex);
        // 构造分页对象
        Page<MesSwUserExam> page = new Page<>(currentPage, pageSize, totalRecords);
        page.setRecords(paginatedRecords);
        return page.convert(userExamConvert::toDTO);
//        Page<MesSwUserExam> page = new Page<>(param.getCurrent(), param.getPageSize());
//        LambdaQueryChainWrapper<MesSwUserExam> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
//        // 用户账号
//        queryChain.eq(MesSwUserExam::getAccountName, userInfo.getAccountName());
//        // 考试日期区间
//        // 06.30修改
//        if (Objects.nonNull(param.getStartDate())) {
//            queryChain.between(MesSwUserExam::getExamPlanStartDate, param.getStartDate(), param.getEndDate())
//                .or().between(MesSwUserExam::getExamPlanEndDate, param.getStartDate(), param.getEndDate());
//        }
//        // 排序
//        queryChain
//            .orderByDesc(MesSwUserExam::getExamEndTime)
//            .orderByDesc(MesSwUserExam::getExamPlanStartDate);
//
//        // 考试状态翻译
//        Page<MesSwUserExam> pageData = queryChain.page(page);
//        pageData.getRecords().forEach(
//            v ->
//                v.setExamStatusName(
//                    Objects.requireNonNull(ExamStatusEnum.getStatusEnumByCode(v.getExamStatus()))
//                        .getCode()));
//        return pageData.convert(userExamConvert::toDTO);
    }

    @Override
    public List<MesSwUserExam> queryList(MesSwUserExamParam param) {
        LambdaQueryWrapper<MesSwUserExam> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getTrainingContent())) {
            // 安全检测中心看板
            wrapper.eq(MesSwUserExam::getTrainingContent, param.getTrainingContent());
            wrapper.isNotNull(MesSwUserExam::getUserScore);
            wrapper.orderByDesc(MesSwUserExam::getUserScore);
        }

        wrapper.eq(
            Objects.nonNull(param.getExamPlanId()),
            MesSwUserExam::getExamPlanId,
            param.getExamPlanId());
        // 考试状态
        wrapper.eq(
            StringUtils.isNotBlank(param.getExamStatus()),
            MesSwUserExam::getExamStatus,
            param.getExamStatus());

        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<MesSwUserExam> queryListByUserPlanId(Long planId) {
        if (planId == null) {
            return null;
        }

        MesSwUserExamParam param = new MesSwUserExamParam();
        param.setExamPlanId(planId);

        return this.queryList(param);
    }

    @Override
    public MesSwUserExamDTO getUserExamPaperDetail(MesSwUserExamParam param) {
        if (StringUtils.isBlank(param.getAccountName())) {
            UserDTO userInfo = UserContextHolder.getUserInfo();
            if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
                throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
            }

            param.setAccountName(userInfo.getAccountName());
        }
        logger.debug(
            "用户账号：{}，请求【查询我的考试试卷详情信息】接口开始，请求参数：用户考试表主键id={}", param.getAccountName(), param.getId());

        MesSwUserExam userExam = super.getById(param.getId());
        //06.30修改，共用代码剥离
//        if (Objects.nonNull(userExam)) {
//            // 考试状态翻译
//            userExam.setExamStatusName(
//                Objects.requireNonNull(ExamStatusEnum.getStatusEnumByCode(userExam.getExamStatus()))
//                    .getMsg());
//            // 添加试卷题目列表信息
//            List<MesSwExamPaperQuestionUserAnswer> questionUserAnswerList =
//                questionUserAnswerService.queryListByUserExamId(param.getId());
//            userExam.setPaperQuestionList(questionUserAnswerList);
//            // 添加试卷题目按题型分组列表信息
//            List<MesSwExamPaperQuestionUserAnswerTypeGroupDTO> typeGroupQuestionList =
//                Lists.newArrayList();
//            List<DicItemDTO> questionTypeList = dicItemService.queryDicItemByDicId("EXAM_QUESTION_TYPE");
//            questionTypeList.forEach(
//                v -> {
//                    MesSwExamPaperQuestionUserAnswerTypeGroupDTO typeGroupQuestion =
//                        new MesSwExamPaperQuestionUserAnswerTypeGroupDTO();
//                    List<MesSwExamPaperQuestionUserAnswer> collectList =
//                        questionUserAnswerList.stream()
//                            .filter(c -> v.getVal().equals(c.getQuestionType()))
//                            .collect(Collectors.toList());
//                    typeGroupQuestion.setQuestionType(v.getVal());
//                    typeGroupQuestion.setQuestionTypeName(v.getCode());
//                    typeGroupQuestion.setPaperQuestionList(
//                        examPaperQuestionUserAnswerConvert.toDTO(collectList));
//                    typeGroupQuestionList.add(typeGroupQuestion);
//                });
//            userExam.setTypeGroupQuestionList(typeGroupQuestionList);
//        }
//        return userExamConvert.toDTO(userExam);
        return this.getPaper(userExam);
    }

    private MesSwUserExamDTO getPaper(MesSwUserExam userExam) {
        if (Objects.nonNull(userExam)) {
            // 考试状态翻译
            userExam.setExamStatusName(
                Objects.requireNonNull(ExamStatusEnum.getStatusEnumByCode(userExam.getExamStatus()))
                    .getMsg());
            // 添加试卷题目列表信息
            List<MesSwExamPaperQuestionUserAnswer> questionUserAnswerList =
                questionUserAnswerService.queryListByUserExamId(userExam.getId());
            userExam.setPaperQuestionList(questionUserAnswerList);
            // 添加试卷题目按题型分组列表信息
            List<MesSwExamPaperQuestionUserAnswerTypeGroupDTO> typeGroupQuestionList =
                Lists.newArrayList();
            List<DicItemDTO> questionTypeList = dicItemService.queryDicItemByDicId("EXAM_QUESTION_TYPE");
            questionTypeList.forEach(
                v -> {
                    MesSwExamPaperQuestionUserAnswerTypeGroupDTO typeGroupQuestion =
                        new MesSwExamPaperQuestionUserAnswerTypeGroupDTO();
                    List<MesSwExamPaperQuestionUserAnswer> collectList =
                        questionUserAnswerList.stream()
                            .filter(c -> v.getVal().equals(c.getQuestionType()))
                            .collect(Collectors.toList());
                    typeGroupQuestion.setQuestionType(v.getVal());
                    typeGroupQuestion.setQuestionTypeName(v.getCode());
                    typeGroupQuestion.setPaperQuestionList(
                        examPaperQuestionUserAnswerConvert.toDTO(collectList));
                    typeGroupQuestionList.add(typeGroupQuestion);
                });
            userExam.setTypeGroupQuestionList(typeGroupQuestionList);
        }
        return userExamConvert.toDTO(userExam);
    }

    @Override
    public MesSwUserExamDTO getUserExamPaperDetail(Long userExamId) {
        if (null == userExamId) {
            return null;
        }
        MesSwUserExamParam param = new MesSwUserExamParam();
        param.setId(userExamId);

        return this.getUserExamPaperDetail(param);
    }

    @Override
    public List<MesSwUserExamDTO> getUserExamPapers(MesSwUserExamParam param) {
        if (StringUtils.isBlank(param.getAccountName())) {
            UserDTO userInfo = UserContextHolder.getUserInfo();
            if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
                throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
            }
            param.setAccountName(userInfo.getAccountName());
        }
        //06.30修改，原来一场考试只能考一次，现在可以考多次,所有一场考试计划对应多条考试记录
        logger.debug(
            "用户账号：{}，请求【查询我的考试试卷详情信息】接口开始，请求参数：用户考试对应计划id={}", param.getAccountName(), param.getExamPlanId());
        if (Objects.isNull(param.getExamPlanId())) {
            throw new BMSException("error", "缺少参数！");
        }
        LambdaQueryWrapper<MesSwUserExam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwUserExam::getExamPlanId, param.getExamPlanId());
        wrapper.eq(MesSwUserExam::getAccountName, param.getAccountName());
        wrapper.eq(MesSwUserExam::getExamStatus, ExamStatusEnum.FINISHED.getCode());
        wrapper.orderByDesc(MesSwUserExam::getExamStartTime);
        List<MesSwUserExam> userExamList = this.list(wrapper);
        if (CollectionUtils.isNotEmpty(userExamList)) {
            userExamList.forEach(userExam -> {
                this.getPaper(userExam);
                //计算准确率
                Integer rightCount =
                    userAnswerService
                        .lambdaQuery()
                        .eq(MesSwExamPaperQuestionUserAnswer::getUserExamId, userExam.getId())
                        .eq(MesSwExamPaperQuestionUserAnswer::getDoRight, 0)
                        .count();
                if (rightCount >= 0 && userExam.getQuestionCount() > 0) {
                    userExam.setAccuracy(
                        NumberUtil.mul(
                            NumberUtil.div(rightCount.doubleValue(), userExam.getQuestionCount().doubleValue()),
                            100));
                }
                //计算是否合格
                if (userExam.getUserScore() != null && userExam.getPassScore() != null) {
                    userExam.setIfPass(userExam.getUserScore() >= userExam.getPassScore());
                }
            });
        }
        return userExamConvert.toDTOList(userExamList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startExam(Long userExamId) {
        UserDTO userInfo = UserContextHolder.getUserInfo();
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
            throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
        }
        logger.debug("用户账号：{}，请求【开始考试】接口开始，请求参数：用户考试表主键id={}", userInfo.getAccountName(), userExamId);
        // 查询用户此场考试信息
        MesSwUserExamDTO examPaperDetail = this.getUserExamPaperDetail(userExamId);
        if (Objects.isNull(examPaperDetail)) {
            throw new BMSException("error", "暂未查询到此场考试信息，请检查");
        }
        if (ExamStatusEnum.DOING.getCode().equals(examPaperDetail.getExamStatus())) {
            throw new BMSException("error", "此场考试已开始，请刷新页面");
        }
        if (ExamStatusEnum.FINISHED.getCode().equals(examPaperDetail.getExamStatus())) {
            throw new BMSException("error", "此场考试已结束，请检查");
        }

        // redisson分布式锁key
        String redisLockKey = "START_EXAM_LOCK" + userInfo.getAccountName() + "_" + userExamId;
        RLock redissonLock = redissonClient.getLock(redisLockKey);
        if (redissonLock.tryLock()) {
            try {
                // 点击开始考试，更新用户考试表开始考试时间、计划结束时间、考试状态
                LocalDateTime nowTime = LocalDateTime.now();
                examPaperDetail.setExamStartTime(nowTime);
                examPaperDetail.setExamPlanEndTime(
                    nowTime.plusMinutes(examPaperDetail.getExamTimeLength()));
                // 报警时长(秒)
                long warnTimeLength = examPaperDetail.getExamTimeLength() * 6 * 9;
                examPaperDetail.setExamWarnTime(nowTime.plusSeconds(warnTimeLength));
                examPaperDetail.setExamStatus(ExamStatusEnum.DOING.getCode());

                // 更新表数据
                boolean update = super.updateById(userExamConvert.toEntity(examPaperDetail));
                if (update) {
                    // 用户考试试卷题目列表存入redis
                    logger.debug("用户考试试卷题目列表存入redis");
                    HashOperations<String, String, List<MesSwExamPaperQuestionUserAnswer>> hashOperations =
                        redisTemplate.opsForHash();
                    hashOperations.put(
                        BMSRedisKeyEnum.USER_EXAM_QUESTION.getKey(),
                        userInfo.getAccountName() + "_" + userExamId,
                        examPaperQuestionUserAnswerConvert.dtoToEntity(
                            examPaperDetail.getPaperQuestionList()));
                    //          Map<String, List<MesSwExamPaperQuestionUserAnswer>> answerMap = new
                    // HashMap<>();
                    //          answerMap.put(
                    //              userInfo.getAccountName() + "_" + userExamId,
                    //              examPaperQuestionUserAnswerConvert.dtoToEntity(
                    //                  examPaperDetail.getPaperQuestionList()));
                    //          hashOperations.putAll(BMSRedisKeyEnum.USER_EXAM_QUESTION.getKey(), answerMap);
                    //
                    //          // 设置过期时间
                    //          long expireTime = examPaperDetail.getExamTimeLength() * 60 + 10;
                    //          hashOperations
                    //              .getOperations()
                    //              .expire(BMSRedisKeyEnum.USER_EXAM_QUESTION.getKey(), expireTime,
                    // TimeUnit.SECONDS);
                }
            } finally {
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                    redissonLock.unlock();
                }
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean endExam(MesSwUserExamParam param) {
        // 1. redisson锁控制交卷重复提交
        // 2. 更新表数据：用户考试表考试结束时间、考试状态、做题用时、最终得分；
        // 结束考试：更新用户考试表考试结束时间、考试状态、做题用时、最终得分；
        // 试卷题目表：题目正确人数累加统计、已完成答题人数累加统计
        // 用户考试题目答案表：用户答案、得分、是否正确
        // 3. 删除redis缓存
        if (StringUtils.isBlank(param.getAccountName())) {
            UserDTO userInfo = UserContextHolder.getUserInfo();
            if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getAccountName())) {
                throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
            }

            param.setAccountName(userInfo.getAccountName());
        }

        logger.debug("用户账号：{}，请求【结束考试】接口开始，请求参数：用户考试主键id={}", param.getAccountName(), param.getId());
        // 查询用户此场考试信息
        MesSwUserExamDTO examPaperDetail = this.getUserExamPaperDetail(param.getId());
        if (Objects.isNull(examPaperDetail)) {
            throw new BMSException("error", "暂未查询到此场考试信息，请检查");
        }
        if (ExamStatusEnum.FINISHED.getCode().equals(examPaperDetail.getExamStatus())) {
            throw new BMSException("error", "此场考试已交卷成功，请勿重复提交");
        }
        // redisson分布式锁key
        String redisLockKey = "END_EXAM_LOCK" + param.getAccountName() + "_" + param.getId();
        RLock redissonLock = redissonClient.getLock(redisLockKey);
        if (redissonLock.tryLock()) {
            try {
                LocalDateTime nowTime = LocalDateTime.now();
                examPaperDetail.setExamEndTime(nowTime);
                examPaperDetail.setExamStatus(ExamStatusEnum.FINISHED.getCode());
                Duration duration = Duration.between(examPaperDetail.getExamStartTime(), nowTime);
                examPaperDetail.setDoTime(duration.toSeconds());
                // 查询计划考试试卷题目列表
                List<MesSwExamPaperQuestion> paperQuestionList =
                    paperQuestionService.queryListByPlanId(examPaperDetail.getExamPlanId());

                // 计算最终得分
                int userScore = 0; // 用户最终得分
                List<MesSwExamPaperQuestionUserAnswerDTO> questionAnswerList =
                    examPaperDetail.getPaperQuestionList();
                for (MesSwExamPaperQuestionUserAnswerDTO question : questionAnswerList) {
                    String userAnswer = question.getUserAnswer();
                    if (StringUtils.isBlank(userAnswer)) { // 用户未作答
                        question.setDoRight("1"); // 是否正确
                        question.setUserScore(0); // 题目得分
                        //如果之前正确过，需要删除
                        paperQuestionList.forEach(
                            v -> {
                                if (Objects.equals(v.getId(), question.getPaperQuestionId())) {
                                    //先获取当前正确账号集合
                                    String count = v.getRightCount();
                                    if (StringUtils.isNotBlank(count)) {
                                        if (count.contains(param.getAccountName() + "@@")) {
                                            v.setRightCount( count.replace(param.getAccountName() + "@@", ""));
                                        }
                                    }
                                }
                            });
                    } else {
                        if (userAnswer.equals(question.getQuestionRightAnswer())) { // 用户答案正确
                            question.setDoRight("0"); // 是否正确
                            question.setUserScore(question.getQuestionScore()); // 题目得分
                            userScore = userScore + question.getQuestionScore();
                            // 计划考试试卷题目正确累加
                            paperQuestionList.forEach(
                                v -> {
                                    if (Objects.equals(v.getId(), question.getPaperQuestionId())) {
                                        //先获取当前正确账号集合
                                        String count = v.getRightCount();
                                        if (StringUtils.isNotBlank(count)) {
                                            if (!count.contains(param.getAccountName() + "@@")) {
                                                v.setRightCount(count + param.getAccountName() + "@@");
                                            }
                                        } else {
                                            v.setRightCount(param.getAccountName() + "@@");
                                        }
//                                      v.setRightCount(v.getRightCount() + 1);
                                    }
                                });
                        } else { // 用户答案错误
                            question.setDoRight("1"); // 是否正确
                            question.setUserScore(0); // 题目得分
                            //如果之前正确过，需要删除
                            paperQuestionList.forEach(
                                v -> {
                                    if (Objects.equals(v.getId(), question.getPaperQuestionId())) {
                                        //先获取当前正确账号集合
                                        String count = v.getRightCount();
                                        if (StringUtils.isNotBlank(count)) {
                                            if (count.contains(param.getAccountName() + "@@")) {
                                                v.setRightCount(count.replace(param.getAccountName() + "@@", ""));
                                            }
                                        }

                                    }
                                });
                        }
                    }
                }
                // 用户最终得分
                examPaperDetail.setUserScore(userScore);
                // 06.30修改，首次累加答题人数
                paperQuestionList.forEach(v -> {
                    LambdaQueryWrapper<MesSwUserExam> examWrapper = new LambdaQueryWrapper<>();
                    examWrapper.eq(MesSwUserExam::getExamPlanId, examPaperDetail.getExamPlanId());
                    examWrapper.eq(MesSwUserExam::getAccountName, examPaperDetail.getAccountName());
                    //只有一条记录，代表首次考，此次答题人数累加
                    if (this.list(examWrapper).size() == 1) {
                        v.setFinishCount(v.getFinishCount() + 1);
                    }
                });
                // 计划考试试卷题目表，已完成答题人数累加
//                paperQuestionList.forEach(v -> v.setFinishCount(v.getFinishCount() + 1));
                // 更新表操作
                super.updateById(userExamConvert.toEntity(examPaperDetail));
                paperQuestionService.updateBatchById(paperQuestionList);
                questionUserAnswerService.updateBatchById(
                    examPaperQuestionUserAnswerConvert.dtoToEntity(questionAnswerList));
                logger.debug("用户账号：{}，交卷/结束考试，更新表信息成功", param.getAccountName());

                // 删除redis中用户考试答题列表数据
                redisTemplate
                    .opsForHash()
                    .delete(
                        BMSRedisKeyEnum.USER_EXAM_QUESTION.getKey(),
                        param.getAccountName() + "_" + param.getId());
                //06.30修改
                //判断是否合格，不合格重新初始化考试
                if (userScore < examPaperDetail.getPassScore()) {
                    MesSwExamPlanParam planParam = new MesSwExamPlanParam();
                    planParam.setId(examPaperDetail.getExamPlanId());
                    planParam.setAgainAccount(examPaperDetail.getAccountName());
                    this.initUserExam(planParam);
                }
            } catch (Exception e) {
                logger.error("用户账号：{}，请求【交卷/结束考试】接口异常", param.getAccountName(), e);
                return false;
            } finally {
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                    redissonLock.unlock();
                }
            }
        }

        return true;
    }

    @Override
    public void processingExpiredKey(String expiredKey) {
        logger.debug("要处理过期了");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void timeOutAutoEndExam() {
        // 查询所有正在进行状态的考试
        MesSwUserExamParam doingListParam = new MesSwUserExamParam();
        doingListParam.setExamStatus(ExamStatusEnum.DOING.getCode());
        List<MesSwUserExam> userExamList = this.queryList(doingListParam);
        if (!CollectionUtil.isEmpty(userExamList)) {
            userExamList.forEach(
                userExam -> {
                    LocalDateTime nowTime = LocalDateTime.now();
                    Duration duration = Duration.between(userExam.getExamEndTime(), nowTime);
                    if (duration.toSeconds() > 10) {
                        MesSwUserExamParam endExamParam = new MesSwUserExamParam();
                        endExamParam.setId(userExam.getId());
                        endExamParam.setAccountName(userExam.getAccountName());
                        this.endExam(endExamParam);
                        logger.debug(
                            "用户账号：{}，用户考试表id：{}，考试已超时，自动结束考试，当前时间：{}",
                            userExam.getId(),
                            userExam.getAccountName(),
                            nowTime);
                    }
                });
        }
        // 查询已抽考的计划超时考试
        List<MesSwExamPlan> planList = examPlanService.list();
        List<MesSwExamPlan> extractList =
            planList.stream()
                .filter(
                    v ->
                        Objects.nonNull(v.getExtractTime())
                            && Duration.between(v.getExtractTime(), LocalDateTime.now()).toSeconds()
                            > 24 * 60 * 60)
                .collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(extractList)) {
            extractList.forEach(
                extract -> {
                    List<MesSwUserExam> extractExamList = this.queryListByUserPlanId(extract.getId());
                    extractExamList.forEach(
                        data -> {
                            MesSwUserExamParam endExamParam = new MesSwUserExamParam();
                            endExamParam.setId(data.getId());
                            endExamParam.setAccountName(data.getAccountName());
                            this.endExam(endExamParam);
                            logger.debug(
                                "用户账号：{}，用户考试表id：{}，考试抽考已超时，自动结束考试，当前时间：{}",
                                data.getId(),
                                data.getAccountName(),
                                LocalDateTime.now());
                        });
                });
        }
    }

    @Override
    public void sendUserExamMsg(List<UserAccountDTO> userList) {
        if (!CollectionUtil.isEmpty(userList)) {
            userList.forEach(
                user -> {
                    MesNotice mesNotice = new MesNotice();
                    mesNotice.setType(NotifyEnum.NOTIFICATION.getKey());
                    mesNotice.setReceiverAccount(user.getUsername());
                    mesNotice.setReceiverAccountName(user.getName());
                    mesNotice.setTitle(NotifyEnum.USER_SAFE_EXAM.getKey());
                    mesNotice.setContent(NotifyEnum.USER_SAFE_EXAM.getDesc());
                    mesNotice.setJumpRoute("/safety/safetyMonitor/myExam");
                    mesNotice.setTargetType("USER_SAFE_EXAM");
                    mesNotice.setTargetTypeName(NotifyEnum.USER_SAFE_EXAM.getKey());
                    mesNotice.setPriority("2");
                    websocketService.sendMessage(mesNotice);
                });
        }
    }

    @Override
    public Integer planExamUserCount(MesSwExamPlanParam param) {
        int result = 0;
        List<MesSwUserExam> userExamList = this.queryListByUserPlanId(param.getId());
        List<MesSwUserExam> finishExamList =
            userExamList.stream()
                .filter(v -> ExamStatusEnum.FINISHED.getCode().equals(v.getExamStatus()))
                .collect(Collectors.toList());
        if (userExamList.size() > finishExamList.size()) {
            throw new BMSException("error", "待全部考生完成考试再进行抽考");
        }
        if (!CollectionUtil.isEmpty(userExamList)) {
            result = userExamList.size();
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> extractExam(MesSwExamPlanParam param) {
        List<MesSwUserExam> userExamList = this.queryListByUserPlanId(param.getId());
        List<MesSwUserExam> finishExamList =
            userExamList.stream()
                .filter(v -> ExamStatusEnum.FINISHED.getCode().equals(v.getExamStatus()))
                .collect(Collectors.toList());
        if (userExamList.size() > finishExamList.size()) {
            throw new BMSException("error", "待全部考生完成考试再进行抽考");
        }
        // 随机抽取处理
        Collections.shuffle(userExamList);
        List<MesSwUserExam> extractExamList =
            userExamList.stream().limit(param.getExtractUserCount()).collect(Collectors.toList());
        // 获取抽考人员姓名列表
        List<String> nameList =
            extractExamList.stream().map(MesSwUserExam::getRealName).collect(Collectors.toList());

        extractExamList.forEach(
            v -> {
                // 用户考试试卷题目答案恢复未答题
                List<MesSwExamPaperQuestionUserAnswer> userAnswers =
                    questionUserAnswerService.queryListByUserExamId(v.getId());
                userAnswers.forEach(
                    answer -> {
                        answer.setUserAnswer(null);
                        answer.setUserScore(null);
                        answer.setDoRight(null);
                    });
                questionUserAnswerService.saveOrUpdateBatch(userAnswers);
                // 用户考试状态变更为未开始
                v.setExamStatus(ExamStatusEnum.NOT_START.getCode());
                v.setExamPlanEndTime(null);
                v.setExamWarnTime(null);
                v.setExamStartTime(null);
                v.setExamEndTime(null);
                v.setUserScore(null);
                v.setDoTime(null);
            });

        boolean updateBatch = this.saveOrUpdateBatch(extractExamList);
        if (updateBatch) {
            // 计划表变更抽考标记
            MesSwExamPlan plan = examPlanService.getById(param.getId());
            plan.setExtract(true);
            plan.setExtractTime(LocalDateTime.now());
            examPlanService.updateById(plan);
        }
        // 发送抽考通知
        extractExamList.forEach(
            v -> {
                MesNotice mesNotice = new MesNotice();
                mesNotice.setType(NotifyEnum.NOTIFICATION.getKey());
                mesNotice.setReceiverAccount(v.getAccountName());
                mesNotice.setReceiverAccountName(v.getRealName());
                mesNotice.setTitle(NotifyEnum.USER_SAFE_EXAM.getKey());
                mesNotice.setContent("您有一场安全考试抽考消息，请查收");
                mesNotice.setJumpRoute("/safety/safetyMonitor/testCenter/myExam");
                mesNotice.setTargetType("USER_SAFE_EXAM");
                mesNotice.setTargetTypeName(NotifyEnum.USER_SAFE_EXAM.getKey());
                mesNotice.setPriority("2");
                websocketService.sendMessage(mesNotice);
            });

        return nameList;
    }
}
