package com.yhd.admin.bms.service.sw.impl.eqpt;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentRepairYlDao;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairYl;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairYlService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备检修工单整改复核记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-12 15:09:32
 */
@Service
public class MesSwEquipmentRepairYlServiceImpl extends ServiceImpl<MesSwEquipmentRepairYlDao, MesSwEquipmentRepairYl> implements MesSwEquipmentRepairYlService {


    @Override
    public MesSwEquipmentRepairYl getTodoByRepairId(Long id) {
        LambdaQueryWrapper<MesSwEquipmentRepairYl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwEquipmentRepairYl::getRepairId, id);
        wrapper.eq(MesSwEquipmentRepairYl::getClResult, "0");

        List<MesSwEquipmentRepairYl> ylList = baseMapper.selectList(wrapper);

        return CollectionUtils.isNotEmpty(ylList) ? ylList.get(0) : null;
    }


    @Override
    public MesSwEquipmentRepairYl getFinishByRepairId(Long repairId) {
        LambdaQueryWrapper<MesSwEquipmentRepairYl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwEquipmentRepairYl::getRepairId, repairId);
        wrapper.ne(MesSwEquipmentRepairYl::getClResult, "0");

        wrapper.orderByDesc(MesSwEquipmentRepairYl::getReceiveTime);

        List<MesSwEquipmentRepairYl> ylList = baseMapper.selectList(wrapper);

        return CollectionUtils.isNotEmpty(ylList) ? ylList.get(0) : null;
    }
}
