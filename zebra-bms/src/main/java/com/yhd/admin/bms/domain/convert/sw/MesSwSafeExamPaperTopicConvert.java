package com.yhd.admin.bms.domain.convert.sw;


import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperTopicDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeExamPaperTopic;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperTopicParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwSafeExamPaperTopicVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/1 16:47
 * @Version 1.0
 */

@Mapper(componentModel = "spring")
public interface MesSwSafeExamPaperTopicConvert {
    MesSwSafeExamPaperTopic toEntity(MesSwSafeExamPaperTopicParam param);

    List<MesSwSafeExamPaperTopic> toEntityList(List<MesSwSafeExamPaperTopicParam> params);

    MesSwSafeExamPaperTopicVO toVO(MesSwSafeExamPaperTopicDTO dto);

    List<MesSwSafeExamPaperTopicVO> toVOList(List<MesSwSafeExamPaperTopicDTO> dtoList);

    MesSwSafeExamPaperTopicDTO toDTO(MesSwSafeExamPaperTopic entity);

    List<MesSwSafeExamPaperTopicDTO> toDTOList(List<MesSwSafeExamPaperTopic> entityList);
}
