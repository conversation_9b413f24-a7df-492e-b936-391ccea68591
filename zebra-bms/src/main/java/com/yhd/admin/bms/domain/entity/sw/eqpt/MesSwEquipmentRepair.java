package com.yhd.admin.bms.domain.entity.sw.eqpt;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检修
 *
 * <AUTHOR>
 * @date 2024/8/19 15:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentRepair extends BaseEntity implements Serializable {
  private static final long serialVersionUID = 954096478615648770L;

  /////////////////////////////////////////////////////////////////
  // 工单基本提报信息
  /** 设备资料id */
  private Long equipmentDataId;
  /** 设备编码+类别 */
  private String equipmentNoType;
  /** 存在问题 */
  private String existProblem;
  /** 提报类型：新增提报、巡检提报、抽检提报、缺陷提报 */
  private String tbType;
  /** 提报人账号 */
  private String tbUserCode;
  /** 提报人姓名 */
  private String tbUserName;
  /** 提报时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime tbTime;
  /** 提报车间code */
  private Long tbWorkshopCode;
  /** 提报车间名称 */
  private String tbWorkshopName;
  /** 提报现场照片 */
  @TableField(exist = false)
  private List<JSONObject> tbPhoto;
  /** 工单状态名称：0已完成,1转检修待处理,2已检修待审核,3检修审核驳回,-1已关闭 */
  private String statusName;
  /** 工单状态编码 */
  private String statusCode;
  /** 工单关闭时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime closeTime;
  /** 关闭原因 */
  private String closeReason;
  /** 关闭人账号 */
  private String closeUserCode;
  /** 关闭人姓名 */
  private String closeUserName;
//  /** 提报审核人账号 */
//  private String tbAuditUserCode;
//  /** 提报审核人姓名 */
//  private String tbAuditUserName;
  /** 整改车间code */
  private Long zgWorkshopCode;
  /** 整改车间名称 */
  private String zgWorkshopName;
//  /** 问题复核人账号 */
//  private String tbCheckUserCode;
//  /** 问题复核人姓名 */
//  private String tbCheckUserName;
  /** 整改人账号 */
  private String zgUserCode;
  /** 整改人姓名 */
  private String zgUserName;
//  /** 整改审核人账号 */
//  private String zgAuditUserCode;
//  /** 整改审核人姓名 */
//  private String zgAuditUserName;
  /** 整改复核人账号 */
  private String zgCheckUserCode;
  /** 整改复核人姓名 */
  private String zgCheckUserName;
  /** 遗留审核人账号 */
  private String ylAuditUserCode;
  /** 遗留审核人姓名 */
  private String ylAuditUserName;
  /** 审核人账号 */
  private String ylCheckUserCode;
  /** 审核人姓名 */
  private String ylCheckUserName;

  /////////////////////////////////////////////////////////////////
//  /** 提报车间主任审核信息 */
//  @TableField(exist = false)
//  private MesSwEquipmentRepairTbAudit tbAudit;
  /////////////////////////////////////////////////////////////////
//  /** 整改车间复核提报信息 */
//  @TableField(exist = false)
//  private MesSwEquipmentRepairTbCheck tbCheck;
  /////////////////////////////////////////////////////////////////
  /** 检修整改信息 */
  @TableField(exist = false)
  private MesSwEquipmentRepairZg repairZg;
  /////////////////////////////////////////////////////////////////
//  /** 整改审核信息-整改车间主任审核 */
//  @TableField(exist = false)
//  private MesSwEquipmentRepairZgAudit zgAudit;
  /////////////////////////////////////////////////////////////////
  /** 整改复核信息-提报车间主任复核 */
  @TableField(exist = false)
  private MesSwEquipmentRepairZgCheck zgCheck;
  /** 遗留信息-厂领导复核 */
  @TableField(exist = false)
  private MesSwEquipmentRepairYl repairYl;

  /** 是否拥有处理权限 */
  @TableField(exist = false)
  private Boolean isCanCl = false;
  /** 是否拥有终止工单权限 */
  @TableField(exist = false)
  private Boolean isCanClose = false;
  /** 是否拥有长期遗留转检修待处理权限 */
  @TableField(exist = false)
  private Boolean isCanZjx = false;
}
