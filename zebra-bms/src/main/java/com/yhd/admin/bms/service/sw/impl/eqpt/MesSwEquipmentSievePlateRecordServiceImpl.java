package com.yhd.admin.bms.service.sw.impl.eqpt;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentSievePlateRecordDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentSievePlateConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentSievePlateDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentSievePlateRecordCountDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentSievePlateRecordDTO;
import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsStockDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentData;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentSievePlate;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentSievePlateRecord;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsCategory;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsStock;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentSievePlateRecordParam;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsStockParam;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentDataService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentSievePlateRecordService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentSievePlateService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsCategoryService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsStockService;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
@Service
public class MesSwEquipmentSievePlateRecordServiceImpl
    extends ServiceImpl<MesSwEquipmentSievePlateRecordDao, MesSwEquipmentSievePlateRecord>
    implements MesSwEquipmentSievePlateRecordService {

    @Resource
    private MesSwEquipmentSievePlateConvert convert;
    @Resource
    private MesSwGoodsStockService mesSwGoodsStockService;
    @Resource
    private MesSwEquipmentDataService equipmentDataService;
    @Resource
    private MesSwEquipmentSievePlateService sievePlateService;
    @Resource
    private MesSwGoodsCategoryService goodsCategoryService;

    @Override
    public IPage<MesSwEquipmentSievePlateRecordDTO> pagingQuery(
        MesSwEquipmentSievePlateRecordParam param) {
        Page<MesSwEquipmentSievePlateRecord> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSwEquipmentSievePlateRecord> queryChain =
            new LambdaQueryChainWrapper<>(baseMapper);
        // 更换日期
        if (param.getStartDate() != null && param.getEndDate() != null) {
            queryChain.between(
                MesSwEquipmentSievePlateRecord::getDate, param.getStartDate(), param.getEndDate());
        }
        // 报废日期
        if (param.getScrapStartDate() != null && param.getScrapEndDate() != null) {
            queryChain.between(
                MesSwEquipmentSievePlateRecord::getScrapTime,
                LocalDateTime.of(param.getScrapStartDate(), LocalTime.MIN),
                LocalDateTime.of(param.getScrapEndDate(), LocalTime.MAX));
        }
        //更换原因
        if (StringUtils.isNotBlank(param.getChangeReasonCode())) {
            queryChain.eq(MesSwEquipmentSievePlateRecord::getChangeReasonCode, param.getChangeReasonCode());
        }
        // 筛面名称
        if (StringUtils.isNotBlank(param.getName())) {
            queryChain.eq(MesSwEquipmentSievePlateRecord::getPlateName, param.getName());
            List<Long> plateIds =
                sievePlateService
                    .lambdaQuery()
                    .select(MesSwEquipmentSievePlate::getId)
                    .eq(MesSwEquipmentSievePlate::getName, param.getName())
                    .eq(param.getColumn() != null, MesSwEquipmentSievePlate::getColNum, param.getColumn())
                    .eq(param.getRow() != null, MesSwEquipmentSievePlate::getRowNum, param.getRow())
                    .eq(MesSwEquipmentSievePlate::getName, param.getName())
                    .list()
                    .stream()
                    .map(MesSwEquipmentSievePlate::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(plateIds)) {
                // 防止后续为空查询，会报错
                plateIds = new ArrayList<>();
                plateIds.add(-10000l);
            }
            List<String> newPlateIds = new ArrayList<>();
            plateIds.forEach(id -> newPlateIds.add(id.toString() + "&"));
            newPlateIds.forEach(id -> {
                queryChain.like(MesSwEquipmentSievePlateRecord::getSievePlateId, id);
                //如果不是最后一个，则拼接.or()
                if (!id.equals(newPlateIds.get(newPlateIds.size() - 1))) {
                    queryChain.or();
                }
            });
        }
        // 材质及尺寸
        if (StringUtils.isNotBlank(param.getMaterialName())) {
            queryChain.like(MesSwEquipmentSievePlateRecord::getMaterialName, param.getMaterialName());
        }
        // 材质分类
        if (param.getCategoryCode() != null) {
            List<Long> categoryIDS = goodsCategoryService.getSubCateIdList(param.getCategoryCode());
            if (CollUtil.isEmpty(categoryIDS)) {
                // 防止后续为空查询，会报错
                categoryIDS = new ArrayList<>();
                categoryIDS.add(-10000l);
            }
            List<Long> goodIds =
                mesSwGoodsStockService
                    .lambdaQuery()
                    .select(MesSwGoodsStock::getId)
                    .in(MesSwGoodsStock::getCategoryCode, categoryIDS)
                    .list()
                    .stream()
                    .map(MesSwGoodsStock::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(goodIds)) {
                // 防止后续为空查询，会报错
                goodIds = new ArrayList<>();
                goodIds.add(-10000l);
            }
            queryChain.in(MesSwEquipmentSievePlateRecord::getMaterialCode, goodIds);
        }
        // 设备编号
        if (StringUtils.isNotBlank(param.getEquipmentNo())) {
            List<Long> eqpIds =
                equipmentDataService
                    .lambdaQuery()
                    .select(MesSwEquipmentData::getId)
                    .like(
                        StringUtils.isNotBlank(param.getEquipmentNo()),
                        MesSwEquipmentData::getNo,
                        param.getEquipmentNo())
                    .list()
                    .stream()
                    .map(MesSwEquipmentData::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(eqpIds)) {
                eqpIds = new ArrayList<>();
                // 防止后续为空查询，会报错
                eqpIds.add(-10000l);
            }
            queryChain.in(MesSwEquipmentSievePlateRecord::getEquipmentDataId, eqpIds);
        }
        if (StringUtils.isNotBlank(param.getIfUse())) {
            if ("是".equals(param.getIfUse())) {
                //查询records中day为空的数据
                queryChain.isNull(MesSwEquipmentSievePlateRecord::getDay);
            } else if ("否".equals(param.getIfUse())) {
                queryChain.isNotNull(MesSwEquipmentSievePlateRecord::getDay);
            }
        }
        queryChain.orderByDesc(
            MesSwEquipmentSievePlateRecord::getDate, MesSwEquipmentSievePlateRecord::getCreatedTime);
        IPage<MesSwEquipmentSievePlateRecord> iPage = queryChain.page(page);
        List<MesSwEquipmentSievePlateRecord> records = iPage.getRecords();
        iPage.setRecords(records);
        return iPage.convert(
            e -> {
                MesSwEquipmentSievePlateRecordDTO dto = convert.toRecordDTO(e);
                Long equipmentDataId = dto.getEquipmentDataId();
                if (equipmentDataId != null) {
                    MesSwEquipmentData equipmentPO = equipmentDataService.getById(equipmentDataId);
                    if (equipmentPO != null) {
                        dto.setEquipmentNo(equipmentPO.getNo());
                        dto.setEquipmentName(equipmentPO.getName());
                    }
                }
                String goodId = dto.getMaterialCode();
                if (StringUtils.isNotBlank(goodId)) {
                    MesSwGoodsStockParam stockParam = new MesSwGoodsStockParam();
                    stockParam.setId(Long.valueOf(goodId));
                    MesSwGoodsStockDTO goodsDTO = mesSwGoodsStockService.getCurrentDetail(stockParam);
                    if (goodsDTO != null) {
                        dto.setCategoryName(goodsDTO.getCategoryName());
                    }
                }
//                //需求变更后，sievePlateId为id通过&拼接，getById查不出数据，所以弃用。
//                String sievePlateId = dto.getSievePlateId();
//                if (sievePlateId != null) {
//                    MesSwEquipmentSievePlate sievePlatePO = sievePlateService.getById(Integer.valueOf(sievePlateId));
//                    if (sievePlatePO != null) {
//                        dto.setColumn(sievePlatePO.getColNum());
//                        dto.setRow(sievePlatePO.getRowNum());
//                    }
//                }
//                if (dto.getDay() != null && dto.getDate() != null) {
//                    dto.setScrapDate(dto.getDate().plusDays(dto.getDay()));
//                }
                if (dto.getDay() == null) {
                    Duration duration =
                        Duration.between(
                            dto.getDate().atStartOfDay(), LocalDate.now().atStartOfDay());
                    if (Objects.nonNull(duration)) {
                        Integer usingDay = Integer.valueOf(String.valueOf(duration.toDays()));
                        dto.setUsingDay(usingDay);
                    }
                }
                return dto;
            });
    }

    @Override
    public List<MesSwEquipmentSievePlateRecordDTO> statisticsScrapPlate(
        MesSwEquipmentSievePlateRecordParam param) {
        LocalDate now = LocalDate.now();
        //startDate为当月第一天
        LocalDate startDate = now.withDayOfMonth(1);
        //endDate为当月最后一天
        LocalDate endDate = now.withDayOfMonth(now.lengthOfMonth());
        if (param.getStartDate() != null && param.getEndDate() != null)  {
            startDate = param.getStartDate();
            endDate = param.getEndDate();
        }
//        Integer day = param.getDay();
//        if (day == null) {
//            // 默认统计近30天的
//            day = 30;
//        }
//        LocalDate today = LocalDate.now();
//        LocalDate before = today.minusDays(day);
        // 查询所有更换下来的筛板
        List<MesSwEquipmentSievePlateRecord> plateRecords =
            this.lambdaQuery()
                .isNotNull(MesSwEquipmentSievePlateRecord::getDay)
                .isNotNull(MesSwEquipmentSievePlateRecord::getMaterialCode)
                .list();
        List<MesSwEquipmentSievePlateRecordDTO> plateRecordList = new ArrayList<>();
        if (CollUtil.isEmpty(plateRecords)) {
            return plateRecordList;
        }
        LocalDate finalEndDate = endDate;
        LocalDate finalStartDate = startDate;
        plateRecords.stream()
            .forEach(
                e -> {
                    LocalDate beforeDate = e.getDate();
                    Integer dayItem = e.getDay();
                    if (beforeDate == null || dayItem == null) {
                        return;
                    }
                    // 算出报废时间
                    LocalDate afterDate = beforeDate.plusDays(dayItem);
                    boolean isInRange =
                        (afterDate.isEqual(finalStartDate) || afterDate.isAfter(finalStartDate))
                            && (afterDate.isEqual(finalEndDate) || afterDate.isBefore(finalEndDate));
                    // 报废时间，在统计时间内
                    if (isInRange) {
                        MesSwEquipmentSievePlateRecordDTO dto = convert.toRecordDTO(e);
                        String sievePlateId = dto.getSievePlateId();
                        if (sievePlateId != null) {
                            MesSwEquipmentSievePlate sievePlatePO = sievePlateService.getById(sievePlateId);
                            if (sievePlatePO != null) {
                                dto.setColumn(sievePlatePO.getColNum());
                                dto.setRow(sievePlatePO.getRowNum());
                            }
                        }
                        String materialCode = dto.getMaterialCode();
                        MesSwGoodsStock goodsPO =
                            mesSwGoodsStockService.getById(Long.valueOf(materialCode));
                        if (goodsPO == null || goodsPO.getCategoryCode() == null) {
                            return;
                        }
                        Long code = goodsPO.getCategoryCode();
                        MesSwGoodsCategory category = goodsCategoryService.getById(code);
                        if (category == null || StringUtils.isBlank(category.getName())) {
                            return;
                        }
                        dto.setCategoryName(category.getName());
                        plateRecordList.add(dto);
                    }
                });
        if (CollUtil.isEmpty(plateRecordList)) {
            return plateRecordList;
        }
        Map<String, List<MesSwEquipmentSievePlateRecordDTO>> resultMap =
            CollStreamUtil.groupByKey(
                plateRecordList, MesSwEquipmentSievePlateRecordDTO::getCategoryName);
        if (CollUtil.isEmpty(resultMap)) {
            return plateRecordList;
        }
        String firstName = "1~7行";
        String secondName = "其他位置";
        String allName = "全部";
        List<MesSwEquipmentSievePlateRecordDTO> result = new ArrayList<>();
        resultMap.forEach(
            (key, value) -> {
                if (CollUtil.isEmpty(value)) {
                    return;
                }
                // 1-7行的数据
                List<MesSwEquipmentSievePlateRecordDTO> first =
                    value.stream()
                        .filter(bean -> bean.getRow() != null && bean.getRow() >= 0 && bean.getRow() <= 6)
                        .filter(bean -> bean.getDay() != null)
                        .collect(Collectors.toList());
                MesSwEquipmentSievePlateRecordDTO item = new MesSwEquipmentSievePlateRecordDTO();
                if (CollUtil.isNotEmpty(first)) {
                    int size = first.size();
                    int total =
                        first.stream()
                            .filter(bean -> bean.getDay() != null) // 确保 day 不为空
                            .mapToInt(MesSwEquipmentSievePlateRecordDTO::getDay) // 获取 day 的值并转换为原始 int
                            .sum();
                    int firstAverage = (int) Math.round((double) total / size);
                    item.setDay(firstAverage);
                } else {
                    item.setDay(0);
                }
                item.setMaterialName(key);
                item.setOriginalMaterialName(firstName);
                result.add(item);

                // 其余行的数据
                List<MesSwEquipmentSievePlateRecordDTO> second =
                    value.stream()
                        .filter(bean -> bean.getRow() != null && bean.getRow() > 6)
                        .filter(bean -> bean.getDay() != null)
                        .collect(Collectors.toList());
                MesSwEquipmentSievePlateRecordDTO item2 = new MesSwEquipmentSievePlateRecordDTO();
                if (CollUtil.isNotEmpty(second)) {
                    int size = second.size();
                    int total =
                        second.stream()
                            .filter(bean -> bean.getDay() != null) // 确保 day 不为空
                            .mapToInt(MesSwEquipmentSievePlateRecordDTO::getDay) // 获取 day 的值并转换为原始 int
                            .sum();
                    int secondAverage = (int) Math.round((double) total / size);
                    item2.setDay(secondAverage);
                } else {
                    item2.setDay(0);
                }
                item2.setMaterialName(key);
                item2.setOriginalMaterialName(secondName);
                result.add(item2);

                // 全部的数据
                List<MesSwEquipmentSievePlateRecordDTO> all =
                    value.stream().filter(bean -> bean.getDay() != null).collect(Collectors.toList());
                MesSwEquipmentSievePlateRecordDTO item3 = new MesSwEquipmentSievePlateRecordDTO();
                if (CollUtil.isNotEmpty(all)) {
                    int size = all.size();
                    int total =
                        all.stream()
                            .filter(bean -> bean.getDay() != null) // 确保 day 不为空
                            .mapToInt(MesSwEquipmentSievePlateRecordDTO::getDay) // 获取 day 的值并转换为原始 int
                            .sum();
                    int allAverage = (int) Math.round((double) total / size);
                    item3.setDay(allAverage);
                } else {
                    item3.setDay(0);
                }
                item3.setMaterialName(key);
                item3.setOriginalMaterialName(allName);
                result.add(item3);
            });
        return result;
    }

    @Override
    public MesSwEquipmentSievePlateRecordCountDTO getQueryRecordCount(MesSwEquipmentSievePlateRecordParam param) {
        LambdaQueryWrapper<MesSwEquipmentSievePlateRecord> countWrapper =
            new LambdaQueryWrapper<>();
        // 更换日期
        if (param.getStartDate() != null && param.getEndDate() != null) {
            countWrapper.between(
                MesSwEquipmentSievePlateRecord::getDate, param.getStartDate(), param.getEndDate());
        }
        // 报废日期
        if (param.getScrapStartDate() != null && param.getScrapEndDate() != null) {
            countWrapper.between(
                MesSwEquipmentSievePlateRecord::getScrapTime,
                LocalDateTime.of(param.getScrapStartDate(), LocalTime.MIN),
                LocalDateTime.of(param.getScrapEndDate(), LocalTime.MAX));
        }
        //更换原因
        if (StringUtils.isNotBlank(param.getChangeReasonCode())) {
            countWrapper.eq(MesSwEquipmentSievePlateRecord::getChangeReasonCode, param.getChangeReasonCode());
        }
        // 筛面名称
        if (StringUtils.isNotBlank(param.getName())) {
            countWrapper.eq(MesSwEquipmentSievePlateRecord::getPlateName, param.getName());
            List<Long> plateIds =
                sievePlateService
                    .lambdaQuery()
                    .select(MesSwEquipmentSievePlate::getId)
                    .eq(MesSwEquipmentSievePlate::getName, param.getName())
                    .eq(param.getColumn() != null, MesSwEquipmentSievePlate::getColNum, param.getColumn())
                    .eq(param.getRow() != null, MesSwEquipmentSievePlate::getRowNum, param.getRow())
                    .eq(MesSwEquipmentSievePlate::getName, param.getName())
                    .list()
                    .stream()
                    .map(MesSwEquipmentSievePlate::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(plateIds)) {
                // 防止后续为空查询，会报错
                plateIds = new ArrayList<>();
                plateIds.add(-10000l);
            }
            List<String> newPlateIds = new ArrayList<>();
            plateIds.forEach(id -> newPlateIds.add(id.toString() + "&"));
            newPlateIds.forEach(id -> {
                countWrapper.like(MesSwEquipmentSievePlateRecord::getSievePlateId, id);
                //如果不是最后一个，则拼接.or()
                if (!id.equals(newPlateIds.get(newPlateIds.size() - 1))) {
                    countWrapper.or();
                }
            });
        }
        // 材质及尺寸
        if (StringUtils.isNotBlank(param.getMaterialName())) {
            countWrapper.like(MesSwEquipmentSievePlateRecord::getMaterialName, param.getMaterialName());
        }
        // 材质分类
        if (param.getCategoryCode() != null) {
            List<Long> categoryIDS = goodsCategoryService.getSubCateIdList(param.getCategoryCode());
            if (CollUtil.isEmpty(categoryIDS)) {
                // 防止后续为空查询，会报错
                categoryIDS = new ArrayList<>();
                categoryIDS.add(-10000l);
            }
            List<Long> goodIds =
                mesSwGoodsStockService
                    .lambdaQuery()
                    .select(MesSwGoodsStock::getId)
                    .in(MesSwGoodsStock::getCategoryCode, categoryIDS)
                    .list()
                    .stream()
                    .map(MesSwGoodsStock::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(goodIds)) {
                // 防止后续为空查询，会报错
                goodIds = new ArrayList<>();
                goodIds.add(-10000l);
            }
            countWrapper.in(MesSwEquipmentSievePlateRecord::getMaterialCode, goodIds);
        }
        // 设备编号
        if (StringUtils.isNotBlank(param.getEquipmentNo())) {
            List<Long> eqpIds =
                equipmentDataService
                    .lambdaQuery()
                    .select(MesSwEquipmentData::getId)
                    .like(
                        StringUtils.isNotBlank(param.getEquipmentNo()),
                        MesSwEquipmentData::getNo,
                        param.getEquipmentNo())
                    .list()
                    .stream()
                    .map(MesSwEquipmentData::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(eqpIds)) {
                eqpIds = new ArrayList<>();
                // 防止后续为空查询，会报错
                eqpIds.add(-10000l);
            }
            countWrapper.in(MesSwEquipmentSievePlateRecord::getEquipmentDataId, eqpIds);
        }
        countWrapper.orderByDesc(
            MesSwEquipmentSievePlateRecord::getDate, MesSwEquipmentSievePlateRecord::getCreatedTime);
        List<MesSwEquipmentSievePlateRecord> list = this.list(countWrapper);
        if (StringUtils.isNotBlank(param.getIfUse())) {
            if ("是".equals(param.getIfUse())) {
                //查询records中day为空的数据
                list = list.stream().filter(record -> record.getDay() == null).collect(Collectors.toList());
            } else if ("否".equals(param.getIfUse())) {
                list = list.stream().filter(record -> record.getDay() != null).collect(Collectors.toList());
            }
        }
        MesSwEquipmentSievePlateRecordCountDTO dto = new MesSwEquipmentSievePlateRecordCountDTO();
        if (!CollUtil.isEmpty(list)) {
            dto.setTotalNum(list.size());
            //根据更换原因进行分组
            Map<String, List<MesSwEquipmentSievePlateRecord>> reasonCode = list.stream().collect(Collectors.groupingBy(MesSwEquipmentSievePlateRecord::getChangeReasonCode));
            if (!CollUtil.isEmpty(reasonCode)) {
                dto.setDeformNum(reasonCode.computeIfAbsent("1", k -> Collections.emptyList()).size());
                dto.setAdjustNum(reasonCode.computeIfAbsent("2", k -> Collections.emptyList()).size());
                dto.setOtherNum(reasonCode.computeIfAbsent("3", k -> Collections.emptyList()).size());
            }
        }
        return dto;
    }

    @Override
    public List<MesSwEquipmentSievePlateRecordDTO> getListByMaterial(MesSwEquipmentSievePlateRecordParam param) {
        //正在使用的所有筛板
        LambdaQueryWrapper<MesSwEquipmentSievePlateRecord> recordWrapper = new LambdaQueryWrapper<>();
        //recordWrapper.isNull(MesSwEquipmentSievePlateRecord::getDay);
        List<MesSwEquipmentSievePlateRecord> records = this.list(recordWrapper);
        //根据materialCode进行分组
        Map<String, List<MesSwEquipmentSievePlateRecord>> recordMap = records.stream()
            .filter(record -> record.getMaterialCode() != null)
            .collect(Collectors.groupingBy(MesSwEquipmentSievePlateRecord::getMaterialCode));
        List<MesSwEquipmentSievePlateRecordDTO> recordList = new ArrayList<>();
        //获取每个value的第一条数据
        if (!CollectionUtils.isEmpty(recordMap)) {
            recordMap.forEach(
                (k, v) -> {
                    MesSwEquipmentSievePlateRecordDTO recordDTO = new MesSwEquipmentSievePlateRecordDTO();
                    recordDTO.setMaterialCode(k);
                    MesSwGoodsStock goodsStock = mesSwGoodsStockService.getById(k);
                    if (goodsStock != null) {
                        recordDTO.setMaterialName(goodsStock.getName());
                    }
                    recordList.add(recordDTO);
                });
        }
        return recordList;
    }
}
