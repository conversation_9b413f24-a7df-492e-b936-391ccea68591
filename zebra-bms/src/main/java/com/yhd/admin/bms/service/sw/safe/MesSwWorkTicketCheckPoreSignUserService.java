package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketCheckPoreSignUserDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwWorkTicketCheckPoreSignUser;
import java.util.List;

public interface MesSwWorkTicketCheckPoreSignUserService
    extends IService<MesSwWorkTicketCheckPoreSignUser> {

  /**
   * 根据条件查询列表
   *
   * @param ticketId 参数
   * @return 列表信息
   */
  List<MesSwWorkTicketCheckPoreSignUserDTO> queryListByTicketId(Long ticketId);
}
