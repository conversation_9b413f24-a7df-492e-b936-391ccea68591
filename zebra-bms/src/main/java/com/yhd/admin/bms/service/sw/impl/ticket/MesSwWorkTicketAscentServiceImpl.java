package com.yhd.admin.bms.service.sw.impl.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.ticket.MesSwWorkTicketAscentDao;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketAscentConvert;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketAscentSignUserConvert;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketAscentDTO;
import com.yhd.admin.bms.domain.dto.sys.DicItemDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketAscent;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketAscentSignUser;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketAscentParam;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketAscentSignUserParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketAscentVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketAscentService;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketAscentSignUserService;
import com.yhd.admin.bms.service.sys.DicItemService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 登高作业工作票
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
@Service
public class MesSwWorkTicketAscentServiceImpl
    extends ServiceImpl<MesSwWorkTicketAscentDao, MesSwWorkTicketAscent>
    implements MesSwWorkTicketAscentService {

  @Resource private MesSwWorkTicketAscentConvert convert;

  @Resource private MesSwWorkTicketAscentSignUserService signUserService;

  @Resource private MesSwWorkTicketAscentSignUserConvert signUserConvert;

  @Resource private DicItemService dicItemService;

  @Override
  public IPage<MesSwWorkTicketAscentDTO> pagingQuery(MesSwWorkTicketAscentParam queryParam) {
    Page<MesSwWorkTicketAscent> page =
        new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<MesSwWorkTicketAscent> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 时间
    if (!StringUtils.isEmpty(queryParam.getStartTime())
        && !StringUtils.isEmpty(queryParam.getEndTime())) {
      queryChain.between(
          MesSwWorkTicketAscent::getSgStartTime,
          queryParam.getStartTime(),
          queryParam.getEndTime());
    }
    // 申请单位
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getSgUnit()),
        MesSwWorkTicketAscent::getSgUnit,
        queryParam.getSgUnit());
    // 申请人
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getApplyUserName()),
        MesSwWorkTicketAscent::getApplyUserName,
        queryParam.getApplyUserName());
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getApplyUserCode()),
        MesSwWorkTicketAscent::getApplyUserCode,
        queryParam.getApplyUserCode());
    // 作业地点
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getSgLocation()),
        MesSwWorkTicketAscent::getSgLocation,
        queryParam.getSgLocation());
    // 工作票状态
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getStatusCode()),
        MesSwWorkTicketAscent::getStatusCode,
        queryParam.getStatusCode());
    queryChain.eq(
        !StringUtils.isEmpty(queryParam.getStatusName()),
        MesSwWorkTicketAscent::getStatusName,
        queryParam.getStatusName());
    queryChain.orderByDesc(MesSwWorkTicketAscent::getCreatedTime);
    List<MesSwWorkTicketAscent> records = queryChain.page(page).getRecords();
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    records.forEach(
        e -> {
          if (!Objects.equals(e.getStatusCode(), WorkTicketStatusEnum.ZF.getCode())
              && !Objects.equals(e.getStatusCode(), WorkTicketStatusEnum.WC.getCode())) {
            // 查询所有审批人员
            List<String> stringList =
                signUserService
                    .lambdaQuery()
                    .eq(MesSwWorkTicketAscentSignUser::getTicketId, e.getId())
                    .list()
                    .stream()
                    .filter(
                        u -> {
                          return Boolean.FALSE.equals(u.getIsSign());
                        })
                    .map(MesSwWorkTicketAscentSignUser::getUserCode)
                    .collect(Collectors.toList());
            // 当前用户在stringList中,有审批权限
            e.setIsAuthority(stringList.contains(username));
            // 计划施工时间
            String startTime =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(e.getSgStartTime());
            String endTime =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(e.getSgEndTime());
            e.setSgTimes(startTime + "~" + endTime);
          } else {
            e.setIsAuthority(Boolean.FALSE);
          }
        });
    return queryChain.page(page).setRecords(records).convert(convert::toDTO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MesSwWorkTicketAscentParam param) {
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    MesSwWorkTicketAscent entity = convert.toEntity(param);
    entity.setTjTime(LocalDateTime.now());
    entity.setStatusCode(WorkTicketStatusEnum.SPZ.getCode());
    entity.setStatusName(WorkTicketStatusEnum.SPZ.getDesc());
    entity.setTjUserCode(username);
    boolean saveResult = super.save(entity);
    if (!saveResult) {
      return false;
    }
    List<MesSwWorkTicketAscentSignUserParam> ascentSignUserParams = new ArrayList<>();

    // 厂站审批
    List<MesSwWorkTicketAscentSignUserParam> signUserList1 = param.getSignUserList1();
    processSignUserList(signUserList1, entity.getId(), "厂站审批");
    ascentSignUserParams.addAll(signUserList1);

    // 作业人员贯彻签字
    List<MesSwWorkTicketAscentSignUserParam> signUserList2 = param.getSignUserList2();
    processSignUserList(signUserList2, entity.getId(), "贯彻签字");
    ascentSignUserParams.addAll(signUserList2);
    signUserService.saveBatch(convert.toEntityList(ascentSignUserParams));
    return true;
  }

  private void processSignUserList(
      List<MesSwWorkTicketAscentSignUserParam> signUserList, Long ticketId, String type) {
    if (signUserList != null) {
      signUserList.forEach(
          e -> {
            e.setTicketId(ticketId);
            e.setType(type);
            e.setIsSign(false);
          });
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean modify(MesSwWorkTicketAscentParam param) {
    // 获取当前用户
    List<MesSwWorkTicketAscentSignUserParam> ascentSignUserParams = new ArrayList<>();
    MesSwWorkTicketAscent entity = convert.toEntity(param);
    boolean b = super.updateById(entity);
    // 厂站审批
    List<MesSwWorkTicketAscentSignUserParam> signUserList1 = param.getSignUserList1();
    signUserList1.forEach(
        e -> {
          e.setSignTime(LocalDateTime.now());
          e.setIsSign(true);
        });
    // 作业人员贯彻签字
    List<MesSwWorkTicketAscentSignUserParam> signUserList2 = param.getSignUserList2();
    signUserList2.forEach(
        e -> {
          e.setSignTime(LocalDateTime.now());
          e.setIsSign(true);
        });
    ascentSignUserParams.addAll(signUserList1);
    ascentSignUserParams.addAll(signUserList2);
    signUserService.updateBatchById(convert.toEntityList(ascentSignUserParams));
    return b;
  }

  @Override
  public MesSwWorkTicketAscentDTO getCurrentDetail(MesSwWorkTicketAscentParam param) {
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    List<MesSwWorkTicketAscentSignUser> objects1 = Lists.newArrayList();
    List<MesSwWorkTicketAscentSignUser> objects2 = Lists.newArrayList();
    MesSwWorkTicketAscent byId = super.getById(param.getId());
    List<MesSwWorkTicketAscentSignUser> list =
        signUserService
            .lambdaQuery()
            .eq(MesSwWorkTicketAscentSignUser::getTicketId, byId.getId())
            .list();
    // 厂站审批
    List<MesSwWorkTicketAscentSignUser> collect1 =
        list.stream().filter(e -> "厂站审批".equals(e.getType())).collect(Collectors.toList());
    collect1.forEach(
        e -> {
          e.setIsAuthority(username.equals(e.getUserCode()) && Boolean.FALSE.equals(e.getIsSign()));
        });
    // 作业人员贯彻签字
    List<MesSwWorkTicketAscentSignUser> collect2 =
        list.stream().filter(e -> "贯彻签字".equals(e.getType())).collect(Collectors.toList());
    collect2.forEach(
        e -> {
          e.setIsAuthority(username.equals(e.getUserCode()) && Boolean.FALSE.equals(e.getIsSign()));
        });
    objects1.addAll(collect1);
    objects2.addAll(collect2);
    byId.setSignUserList1(collect1);
    byId.setSignUserList2(collect2);
    return convert.toDTO(byId);
  }

  /**
   * @param param
   * @return @Description:审批签字
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean approve(MesSwWorkTicketAscentSignUserParam param) {
    // 获取当前用户
    String username = UserContextHolder.getUserDetail().getUsername();
    if (null != param.getId()) {
      MesSwWorkTicketAscentSignUser byId =
          signUserService
              .lambdaQuery()
              .eq(MesSwWorkTicketAscentSignUser::getId, param.getId())
              .eq(MesSwWorkTicketAscentSignUser::getUserCode, username)
              .one();
      Objects.requireNonNull(byId, "数据异常,请联系管理员");
      if (Boolean.TRUE.equals(byId.getIsSign())) {
        throw new BMSException("error", "该用户已签字");
      }
      // 作废
      if (WorkTicketStatusEnum.ZF
          .getCode()
          .equals(super.getById(param.getTicketId()).getStatusCode())) {
        throw new BMSException("error", "该单子已经作废");
      }
    }
    List<MesSwWorkTicketAscentSignUser> swWorkTicketAscentSignUsers =
        signUserService
            .lambdaQuery()
            .eq(MesSwWorkTicketAscentSignUser::getTicketId, param.getTicketId())
            .list()
            .stream()
            .filter(
                w -> {
                  return null != w.getUserCode() && Boolean.FALSE.equals(w.getIsSign());
                })
            .collect(Collectors.toList());
    if (swWorkTicketAscentSignUsers.size() == 1) {
      MesSwWorkTicketAscent mesSwWorkTicketAscent = super.getById(param.getTicketId());
      if (!"作业人员".equals(param.getUserType())) {
        mesSwWorkTicketAscent.setStatusCode(WorkTicketStatusEnum.WC.getCode());
        mesSwWorkTicketAscent.setStatusName(WorkTicketStatusEnum.WC.getDesc());
        super.updateById(mesSwWorkTicketAscent);
      }
    }
    MesSwWorkTicketAscentSignUser entity = signUserConvert.toEntity(param);
    entity.setIsSign(true);
    entity.setSignTime(LocalDateTime.now());
    // 查询用户签字信息
    return signUserService.saveOrUpdate(entity);
  }

  /**
   * @param param
   * @return @Description:作废工作票接口
   */
  @Override
  public Boolean cancel(MesSwWorkTicketAscentParam param) {
    // 获取当前用户
    MesSwWorkTicketAscent mesSwWorkTicketAscent = super.getById(param.getId());
    Objects.requireNonNull(mesSwWorkTicketAscent, "数据异常,请联系管理员");
    if (WorkTicketStatusEnum.ZF.getCode().equals(mesSwWorkTicketAscent.getStatusCode())) {
      throw new BMSException("error", "该单子已经作废");
    }
    MesSwWorkTicketAscent entity = convert.toEntity(param);
    entity.setStatusCode(WorkTicketStatusEnum.ZF.getCode());
    entity.setStatusName(WorkTicketStatusEnum.ZF.getDesc());
    return this.updateById(entity);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean removeBatch(BatchParam param) {
    return super.removeByIds(param.getId());
  }

  /**
   * @return @Description:查询字典项
   */
  @Override
  public MesSwWorkTicketAscentDTO getTypeList() {
    MesSwWorkTicketAscentDTO mesSwWorkTicketAscentDTO = new MesSwWorkTicketAscentDTO();
    List<MesSwWorkTicketAscentSignUser> signUsers1 = Lists.newArrayList();
    List<MesSwWorkTicketAscentSignUser> signUsers2 = Lists.newArrayList();
    // 登高厂站审批
    List<DicItemDTO> cz = dicItemService.queryDicItemByDicId("CZ");
    cz.forEach(
        e -> {
          MesSwWorkTicketAscentSignUser mesSwWorkTicketAscentSignUser =
              new MesSwWorkTicketAscentSignUser();
          mesSwWorkTicketAscentSignUser.setUserType(e.getVal());
          signUsers1.add(mesSwWorkTicketAscentSignUser);
        });
    // 登高作业人员贯彻签字
    List<DicItemDTO> zyqz = dicItemService.queryDicItemByDicId("ZYQZ");
    zyqz.forEach(
        e -> {
          MesSwWorkTicketAscentSignUser mesSwWorkTicketAscentSignUser =
              new MesSwWorkTicketAscentSignUser();
          mesSwWorkTicketAscentSignUser.setUserType(e.getVal());
          signUsers2.add(mesSwWorkTicketAscentSignUser);
        });
    mesSwWorkTicketAscentDTO.setSignUserList1(signUserConvert.toDTOList(signUsers1));
    mesSwWorkTicketAscentDTO.setSignUserList2(signUserConvert.toDTOList(signUsers2));
    return mesSwWorkTicketAscentDTO;
  }

  @Override
  public List<MesSwWorkTicketAscentVO> getDataByList() {
    return convert.toVOList(this.list());
  }
}
