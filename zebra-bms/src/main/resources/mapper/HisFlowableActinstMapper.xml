<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yhd.admin.bms.dao.flowable.IHisFlowableActinstDao">
    <delete id="deleteHisActinstsByIds" parameterType="java.util.List">
        delete from ACT_HI_ACTINST where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
