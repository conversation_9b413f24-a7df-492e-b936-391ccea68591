package com.yhd.admin.bms.domain.entity.sw;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Date 2023/10/10 16:23
 **/

@Data
@EqualsAndHashCode(callSuper = true)
public class MesSwFirePumpRecord extends BaseEntity implements Serializable, Cloneable{
    /**
     * 测试时间
     */
    private LocalDateTime testTime;
    /**
     * 测试人code
     */
    private String testPeopleCode;
    /**
     * 测试人
     */
    private String testPeople;
    /**
     * 是否完好（0是 1否）
     */
    private int testStatus;
    /**
     * 备注
     */
    private String testNotes;
}
