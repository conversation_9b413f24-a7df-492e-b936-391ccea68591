package com.yhd.admin.bms.domain.vo.sw;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/** 消防水幕检查记录 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwFireWaterVO extends BaseVO implements Cloneable, Serializable {
  /** 车间编码 */
  private String workshopCode;
  /** 车间名称 */
  private String workshopName;
  /** 测试时间;年月日时分 */
  private String testTime;
  /** 详细情况;JSON数组 */
  private String detailInfo;
}
