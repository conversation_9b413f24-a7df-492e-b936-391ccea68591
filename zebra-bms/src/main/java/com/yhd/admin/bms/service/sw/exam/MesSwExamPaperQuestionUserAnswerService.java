package com.yhd.admin.bms.service.sw.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPaperQuestionUserAnswerDTO;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwExamPaperQuestionUserAnswer;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwExamPaperQuestionUserAnswerParam;

import java.util.List;

/**
 * 用户考试试卷题目答案-业务层接口
 *
 * <AUTHOR>
 * @date 2024/3/5 9:37
 */
public interface MesSwExamPaperQuestionUserAnswerService
    extends IService<MesSwExamPaperQuestionUserAnswer> {

  /**
   * 根据条件查询试卷题目答案列表
   *
   * @param param 查询条件
   * @return 试卷题目答案列表
   */
  List<MesSwExamPaperQuestionUserAnswer> queryList(MesSwExamPaperQuestionUserAnswerParam param);

  /**
   * 根据用户考试主键id查询试卷题目答案列表
   *
   * @param userExamId 用户考试主键id
   * @return 试卷题目答案列表
   */
  List<MesSwExamPaperQuestionUserAnswer> queryListByUserExamId(Long userExamId);

  /**
   * 查询试卷题目详情信息
   *
   * @param param 参数：试卷题目答案表主键id
   * @return 试卷题目详情
   */
  MesSwExamPaperQuestionUserAnswerDTO getPaperQuestionDetail(
      MesSwExamPaperQuestionUserAnswerParam param);

  /**
   * 保存用户做题记录
   *
   * @param param 用户做题信息
   * @return true成功，false失败
   */
  Boolean saveUserDoQuestion(MesSwExamPaperQuestionUserAnswerParam param);
}
