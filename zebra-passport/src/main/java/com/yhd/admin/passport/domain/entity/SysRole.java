package com.yhd.admin.passport.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role")
public class SysRole extends BaseEntity implements Cloneable, Serializable {

  /**
   * 角色名称
   */
  private String roleName;
  /**
   * 角色编码
   */
  private String roleCode;
  /**
   * 角色状态
   */
  private Boolean isEnable;
}
