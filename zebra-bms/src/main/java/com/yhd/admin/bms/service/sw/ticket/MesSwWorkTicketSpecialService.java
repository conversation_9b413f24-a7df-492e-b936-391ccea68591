package com.yhd.admin.bms.service.sw.ticket;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketSpecialDTO;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketSpecial;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketSpecialParam;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketSpecialSignUserParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.ticket.MesSwWorkTicketSpecialVO;

import java.util.List;

/**
 * 特殊作业工作票
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-29
 */
public interface MesSwWorkTicketSpecialService extends IService<MesSwWorkTicketSpecial> {

    IPage<MesSwWorkTicketSpecialDTO> pagingQuery(MesSwWorkTicketSpecialParam queryParam);

    Boolean add(MesSwWorkTicketSpecialParam param);

    Boolean modify(MesSwWorkTicketSpecialParam param);

    Boolean removeBatch(BatchParam param);

    MesSwWorkTicketSpecialDTO getCurrentDetail(MesSwWorkTicketSpecialParam param);

    MesSwWorkTicketSpecialDTO getTypeList();

    List<MesSwWorkTicketSpecialVO> getDataByList();

    Boolean cancel(MesSwWorkTicketSpecialParam param);

    Boolean approve(MesSwWorkTicketSpecialSignUserParam param);
}
