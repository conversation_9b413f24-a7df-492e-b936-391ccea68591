package com.yhd.admin.bms.domain.query.sw.consumption;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电量消耗统计表
 *
 * <AUTHOR>
 * @date 2025/1/10 14:49
 */
@Data
public class MesConsumptionElectricParam implements Serializable {
  private static final long serialVersionUID = -7138735884430994819L;

  /** 统计日期 */
  private String statsDate;
  /** 新高压配电室1#进线 */
  private BigDecimal xgy1;
  /** 新高压配电室2#进线 */
  private BigDecimal xgy2;
  /** 旧高压1号进线 */
  private BigDecimal jgy1;
  /** 旧高压2号进线 */
  private BigDecimal jgy2;
}
