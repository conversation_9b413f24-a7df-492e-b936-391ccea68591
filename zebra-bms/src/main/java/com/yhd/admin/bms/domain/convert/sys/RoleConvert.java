package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.RoleDTO;
import com.yhd.admin.bms.domain.entity.sys.SysRole;
import com.yhd.admin.bms.domain.query.sys.RoleParam;
import com.yhd.admin.bms.domain.vo.sys.RoleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleConvert.java
 * @Description TODO
 * @createTime 2020年03月30日 16:49:00
 */
@Mapper(componentModel = "spring")
public interface RoleConvert {

    @Mapping(target = "authority", ignore = true)
    RoleDTO toDTO(SysRole role);

    RoleVO toVO(RoleDTO roleDTO);

    List<RoleVO> toVO(List<RoleDTO> roleDTO);

    List<RoleDTO> toDTO(List<SysRole> roleList);

    SysRole toEntity(RoleDTO roleDTO);

    SysRole toEntity(RoleParam roleParam);

}
