package com.yhd.admin.bms.controller.sw.consumption;

import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionWaterDTO;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionWaterParam;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionWaterService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 能耗管理-水耗
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
@RestController
@RequestMapping(value = "/consumption/water")
public class MesWaterConsumptionController {

  @Resource private MesConsumptionWaterService service;

  /**
   * 水耗管理的列表
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/queryList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MesConsumptionWaterDTO>> queryList(
      @RequestBody MesConsumptionWaterParam param) {
    try {
      List<MesConsumptionWaterDTO> result = service.queryList(param);
      return RespJson.buildSuccessResponse(result);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 详情接口
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MesConsumptionWaterDTO>> getCurrentDetail(
      @RequestBody MesConsumptionWaterParam param) {
    try {
      List<MesConsumptionWaterDTO> result = service.getCurrentDetail(param);
      return RespJson.buildSuccessResponse(result);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
