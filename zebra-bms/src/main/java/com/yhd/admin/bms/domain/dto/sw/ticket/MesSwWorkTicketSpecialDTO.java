package com.yhd.admin.bms.domain.dto.sw.ticket;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 特殊作业工作票
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketSpecialDTO extends BaseDTO implements Cloneable, Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 施工地点
     */
    private String sgLocation;

    /**
     * 作业内容
     */
    private String sgContent;

    /**
     * 计划作业开始时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime sgStartTime;

    /**
     * 计划作业结束时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime sgEndTime;

    /**
     * 工作票提交时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime tjTime;

    /**
     * 工作票提交人code
     */
    private String tjUserCode;

    /**
     * 工作票提交人姓名
     */
    private String tjUserName;

    /**
     * 编制人code
     */
    private String bzUserCode;

    /**
     * 编制人name
     */
    private String bzUserName;

    /**
     * 安检员code
     */
    private String safeUserCode;

    /**
     * 安检员姓名
     */
    private String safeUserName;

    /**
     * 现场负责人code
     */
    private String jsUserCode;

    /**
     * 现场负责人姓名
     */
    private String jsUserName;

    /**
     * 瓦解员code
     */
    private String wjUserCode;

    /**
     * 瓦解员姓名
     */
    private String wjUserName;

    /**
     * 作业人员code
     */
    private String zyUserCode;

    /**
     * 作业人员姓名
     */
    private String zyUserName;

    /**
     * 跟班领导code
     */
    private String gbUserCode;

    /**
     * 跟班领导姓名
     */
    private String gbUserName;

    /**
     * 工作票状态code：01审批中、02审批完成、03作废
     */
    private String statusCode;

    /**
     * 工作票状态名称
     */
    private String statusName;


    //审批部门/领导

    private List<MesSwWorkTicketSpecialSignUserDTO> signUserList;

    /**
     * 是否有权限审批
     */

    private Boolean isAuthority;


    /**
     * 实际施工开始时间(yyyy-MM-dd hh:mm)
     */

    private String sgTimes;
}
