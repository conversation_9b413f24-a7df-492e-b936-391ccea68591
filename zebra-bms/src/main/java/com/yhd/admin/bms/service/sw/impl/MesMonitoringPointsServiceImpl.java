package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.MesMonitoringPointsDao;
import com.yhd.admin.bms.domain.convert.sw.MesMonitoringPointsConvert;
import com.yhd.admin.bms.domain.dto.sw.MesMonitoringPointsDTO;
import com.yhd.admin.bms.domain.entity.sw.MesMonitoringPoints;
import com.yhd.admin.bms.domain.query.sw.MesMonitoringPointsParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.service.sw.MesMonitoringPointsService;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @Date 2024/6/28 15:29 @Version 1.0
 */
@Service
public class MesMonitoringPointsServiceImpl
    extends ServiceImpl<MesMonitoringPointsDao, MesMonitoringPoints>
    implements MesMonitoringPointsService {
  @Resource private MesMonitoringPointsConvert convert;

  /**
   * 分页查询
   *
   * @param queryParam {@link MesMonitoringPointsParam}
   * @return IPage<MesMonitoringPointsDTO>
   */
  @Override
  public IPage<MesMonitoringPointsDTO> pagingQuery(MesMonitoringPointsParam queryParam) {
    Page<MesMonitoringPoints> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<MesMonitoringPoints> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 监控点位名称
    if (StringUtils.isNotBlank(queryParam.getName())) {
      queryChain.like(MesMonitoringPoints::getName, queryParam.getName());
    }
    // 所属区域
    if (StringUtils.isNotBlank(queryParam.getLocation())) {
      queryChain.like(MesMonitoringPoints::getLocation, queryParam.getLocation());
    }
    queryChain.orderByDesc(MesMonitoringPoints::getCreatedTime);
    return queryChain.page(page).convert(convert::toDTO);
  }

  /**
   * 根据ID查询详情
   *
   * @param queryParam {@link MesMonitoringPointsParam}
   * @return {@link MesMonitoringPointsDTO}
   */
  @Override
  public MesMonitoringPointsDTO getCurrentDetail(MesMonitoringPointsParam queryParam) {
    // 主表
    MesMonitoringPointsDTO MesMonitoringPointsDTO =
        convert.toDTO(super.getById(queryParam.getId()));
    return MesMonitoringPointsDTO;
  }

  /**
   * 根据ID进行新增或者修改
   *
   * @param queryParam {@link MesMonitoringPointsParam}
   * @return true 成功，false 失败
   */
  @Override
  public Boolean addOrModify(MesMonitoringPointsParam queryParam) {
    MesMonitoringPoints entity = convert.toEntity(queryParam);
    return saveOrUpdate(entity);
  }

  /**
   * 批量删除
   *
   * @param param {@link BatchParam}
   * @return true 成功，false 失败
   */
  @Override
  public Boolean removeBatch(BatchParam param) {
    return super.removeByIds(param.getId());
  }
}
