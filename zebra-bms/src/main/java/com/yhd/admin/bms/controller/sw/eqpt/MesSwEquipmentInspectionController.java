package com.yhd.admin.bms.controller.sw.eqpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentInspectionConvert;
import com.yhd.admin.bms.domain.convert.sys.DicItemConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionPageDTO;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionParam;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionPageVO;
import com.yhd.admin.bms.domain.vo.sw.eqpt.MesSwEquipmentInspectionVO;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentInspectionService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备巡检
 *
 * <AUTHOR>
 * @date 2023/12/01 18:22
 */
@RestController
@RequestMapping("/equipment/inspection")
@Slf4j
public class MesSwEquipmentInspectionController
    extends BaseController<MesSwEquipmentInspectionConvert, MesSwEquipmentInspectionService> {

  @Resource private DicItemConvert dicItemConvert;

  public MesSwEquipmentInspectionController(
      MesSwEquipmentInspectionConvert convert, MesSwEquipmentInspectionService service) {
    super(convert, service);
  }

  /**
   * 设备巡检列表
   *
   * @param queryParam
   * @return
   * @throws Exception
   */
  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwEquipmentInspectionPageVO> pagingQuery(
      @RequestBody MesSwEquipmentInspectionParam queryParam) throws Exception {
    IPage<MesSwEquipmentInspectionPageDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toPageVO));
  }

  /**
   * 新增设备巡检
   *
   * @param queryParam
   * @return
   * @throws Exception
   */
  @SysLogs(title = "设备巡检新增", businessType = BusinessType.INSERT)
  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson add(@RequestBody MesSwEquipmentInspectionParam queryParam) throws Exception {
    try {
      Boolean retVal = service.add(queryParam);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 查看设备巡检-子表（单个设备）
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetail(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 巡检签字
   *
   * @param param
   * @return
   */
  @SysLogs(title = "巡检签字", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/sign",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson sign(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(service.sign(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 查看设备巡检-总表（设备汇总）
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/getCurrentDetailTotal",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetailTotal(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(service.getCurrentDetailTotal(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 承包人签字
   *
   * @param param
   * @return
   */
  @SysLogs(title = "承包人签字", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/leaderSign",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson leaderSign(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(service.leaderSign(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 删除
   *
   * @param param
   * @return
   */
  @SysLogs(title = "删除设备巡检", businessType = BusinessType.DELETE)
  @PostMapping(
      value = "/remove",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson remove(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(service.remove(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
  /**
   * 导出
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/exportExcel",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson exportExcel(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(service.exportExcel(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 批量导出
   *
   * @param param
   * @return
   */
  @PostMapping(
      value = "/exportExcelBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson exportExcelBatch(@RequestBody MesSwEquipmentInspectionParam param) {
    try {
      return RespJson.buildSuccessResponse(service.exportExcelBatch(param));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/querySelectValue",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson queryDicItemByDicId(@RequestBody MesSwEquipmentInspectionParam queryParam) {
    try {
      return RespJson.buildSuccessResponse(
          dicItemConvert.toVO(service.querySelectValue(queryParam)));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @SysLogs(title = "驳回签字", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/reject",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson reject(@RequestBody MesSwEquipmentInspectionParam queryParam) {
    try {
      return RespJson.buildSuccessResponse(service.reject(queryParam));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 查看自动创建工单的开关
   *
   * @return
   */
  @PostMapping(
      value = "/getAutoCreate",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getAutoCreate() {
    return RespJson.buildSuccessResponse(service.getAutoCreate());
  }

  /**
   * 设置自动创建工单的开关
   *
   * @return
   */
  @SysLogs(title = "设置自动创建工单的开关", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/saveAutoCreate",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson saveAutoCreate(@RequestBody MesSwEquipmentInspectionParam queryParam) {
    try {
      return service.saveAutoCreate(queryParam);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 开启补签
   *
   * @return
   */
  @SysLogs(title = "开启补签", businessType = BusinessType.UPDATE)
  @PostMapping(
      value = "/saveReplenishSign",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson saveReplenishSign(@RequestBody MesSwEquipmentInspectionParam queryParam) {
    try {
      return RespJson.buildSuccessResponse(service.saveReplenishSign(queryParam));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  /**
   * 设备编号，巡检巡检子单
   *
   * @param queryParam
   * @return
   */
  @PostMapping(
      value = "/pagingChildrenQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwEquipmentInspectionVO> pagingChildrenQuery(
      @RequestBody MesSwEquipmentInspectionParam queryParam) {
    IPage<MesSwEquipmentInspectionDTO> iPage = service.pagingChildrenQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }
}
