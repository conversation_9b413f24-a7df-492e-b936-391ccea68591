package com.yhd.admin.bms.controller.flowable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.flowable.GroupConvert;
import com.yhd.admin.bms.domain.dto.flowable.GroupDTO;
import com.yhd.admin.bms.domain.entity.sys.SysUserAccount;
import com.yhd.admin.bms.domain.query.flowable.GroupParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.flowable.GroupVO;
import com.yhd.admin.bms.service.flowable.GroupService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/13 19:12
 * @Description: 用户组
 * @Version 1.0
 */

@RestController
@RequestMapping("/group")
public class FlowGroupController {

    @Resource
    private GroupService groupService;

    @Resource
    private GroupConvert groupConvert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<GroupVO> pagingQuery(@RequestBody GroupParam param) {
        IPage<GroupDTO> iPage = groupService.pagingQuery(param);
        return new PageRespJson<>(iPage.convert(groupConvert::toVO));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "新增组", businessType = BusinessType.INSERT)
    public RespJson add(
        @RequestBody GroupParam queryParam) {
        return RespJson.buildSuccessResponse(groupService.add(queryParam));
    }

    @PostMapping(
        value = "/update",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "更新组", businessType = BusinessType.UPDATE)
    public RespJson update(
        @RequestBody GroupParam queryParam) {
        return RespJson.buildSuccessResponse(groupService.update(queryParam));
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "删除组", businessType = BusinessType.DELETE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        return RespJson.buildSuccessResponse(groupService.removeBatch(param));
    }

    @PostMapping(value = "/addUserGroups/{groupId}", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    @SysLogs(title = "添加用户组", businessType = BusinessType.INSERT)
    public RespJson addUserGroups(@PathVariable String groupId, @RequestBody List<SysUserAccount> users) {
        return RespJson.buildSuccessResponse(groupService.addUserGroupsByGroup(groupId, users));
    }
}
