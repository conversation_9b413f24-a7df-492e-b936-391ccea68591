package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备资料-温振报警点位数据
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentPointDataParam extends QueryParam implements Cloneable, Serializable {
  private Long id;
  private Long pointId;
  private LocalDateTime valueTime;
  /** 点位 */
  private String pointAddress;
  /** 点位值 */
  private String pointValue;
  /** 缺陷Id */
  private Long flawId;
  /** 报警状态：warn_no，不报警；warn_low，低于报警；warn_high，高于报警； */
  private String warnStatus;
  /** 处理状态：为空，没有处理，ok，已处理 */
  private String handlerStatus;
}
