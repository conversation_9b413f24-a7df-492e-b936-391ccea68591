package com.yhd.admin.bms.controller.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.controller.sys.BaseController;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwContractorStaffAdmissionConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwContractorStaffAdmissionDTO;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwContractorStaffAdmissionParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwContractorStaffAdmissionVO;
import com.yhd.admin.bms.service.sw.safe.MesSwContractorStaffAdmissionService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 承包商员工入场管理
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@RestController
@RequestMapping("/contractor/admission")
@Slf4j
public class MesSwContractorStaffAdmissionController
    extends BaseController<
        MesSwContractorStaffAdmissionConvert, MesSwContractorStaffAdmissionService> {

  public MesSwContractorStaffAdmissionController(
      MesSwContractorStaffAdmissionConvert convert, MesSwContractorStaffAdmissionService service) {
    super(convert, service);
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwContractorStaffAdmissionVO> pagingQuery(
      @RequestBody MesSwContractorStaffAdmissionParam queryParam) {
    IPage<MesSwContractorStaffAdmissionDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getCurrentDetail(@RequestBody MesSwContractorStaffAdmissionParam param) {
    try {
      return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/getTemplate",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson getTemplate() {
    try {
      return RespJson.buildSuccessResponse(convert.toTemplateVOS(service.getTemplate()));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/saveTemplate",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson saveTemplate(@RequestBody MesSwContractorStaffAdmissionParam param) {
    try {
      Boolean retVal = service.saveTemplate(param);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/saveOrder",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson saveOrder(@RequestBody MesSwContractorStaffAdmissionParam param) {
    try {
      Boolean retVal = service.saveOrder(param);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/saveApproval",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson saveApproval(@RequestBody MesSwContractorStaffAdmissionParam param) {
    try {
      Boolean retVal = service.saveApproval(param);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

  @PostMapping(
      value = "/remove",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson remove(@RequestBody MesSwContractorStaffAdmissionParam param) {
    try {
      Boolean retVal = service.remove(param);
      return RespJson.buildSuccessResponse(retVal);
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
