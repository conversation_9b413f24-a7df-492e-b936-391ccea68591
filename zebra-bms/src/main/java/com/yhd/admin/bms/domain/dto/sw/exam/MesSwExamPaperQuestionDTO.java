package com.yhd.admin.bms.domain.dto.sw.exam;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 用户考试试卷题目信息
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwExamPaperQuestionDTO extends BaseDTO implements Serializable {
  private static final long serialVersionUID = 7906102024019954165L;

  /** 考试计划表主键id */
  private Long examPlanId;
  /** 计划考试时长 */
  private Long examTimeLength;
  /** 考试试卷标题 */
  private String paperTitle;
  /** 试卷序列号 */
  private String serialNum;
  /** 考试试卷分类 */
  private String paperClassify;
  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  private String questionType;
  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  private String questionTypeName;
  /** 试题题目内容 */
  private String questionContent;
  /** 试题难度：1-低，2-中，3-高 */
  private String difficultyType;
  /** 试题难度：1-低，2-中，3-高 */
  private String difficultyTypeName;
  /** 试题标题 */
  private String questionTitle;
  /** 试题题目答案选项 */
  private String questionAnswerOption;

  private List<String> questionAnswerList;
  /** 试题题目正确答案 */
  private String questionRightAnswer;

  private List<String> correctAnswerList;
  /** 试题题目附件 */
  private String fileUrl;

  private List<String> fileUrlList;
  /** 附件类型 */
  private String fileType;
  /** 出题人账号 */
  private String questionSetter;
  /** 出题人姓名 */
  private String questionSetterName;
  /** 相关培训内容 */
  private String trainingContent;
  /** 试题题目分数 */
  private Integer questionScore;
  /** 答题正确人数 */
  private String rightCount;
  /** 已完成答题人数 */
  private Integer finishCount;
}
