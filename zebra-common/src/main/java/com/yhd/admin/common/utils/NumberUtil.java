package com.yhd.admin.common.utils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数字处理工具类
 *
 * <AUTHOR>
 * @date 2022/9/1 9:35
 */
public class NumberUtil implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 这个类不能实例化 */
  private NumberUtil() {}

  /** 默认精度: 2位小数 */
  public static final int DEFAULT_SCALE = 2;
  /** int类型数值100 */
  public static final int INT_HUNDRED = 100;
  /** double类型数值100 */
  public static final double DOUBLE_HUNDRED = 100d;
  /** 默认百分比数据format */
  public static final String DEFAULT_PERCENTAGE_FORMAT = "##.00%";

  /**
   * 提供精确的加法运算
   *
   * @param v1 数据v1
   * @param v2 数据v2
   * @return 数据相加之和
   */
  public static double add(double v1, double v2) {
    BigDecimal b1 = new BigDecimal(Double.toString(v1));
    BigDecimal b2 = new BigDecimal(Double.toString(v2));
    return b1.add(b2).doubleValue();
  }

  /**
   * 提供精确的加法运算
   *
   * @param v1 数据v1
   * @param v2 数据v2
   * @param vs 数据vs
   * @return 数据相加之和
   */
  public static double add(double v1, double v2, double... vs) {
    double result = add(v1, v2);
    for (double item : vs) {
      result = add(result, item);
    }

    return result;
  }

  /**
   * 提供精确的减法运算
   *
   * @param v1 被减数
   * @param v2 减数
   * @return 两个参数的差 v1 - v2
   */
  public static double sub(double v1, double v2) {
    BigDecimal b1 = new BigDecimal(Double.toString(v1));
    BigDecimal b2 = new BigDecimal(Double.toString(v2));
    return b1.subtract(b2).doubleValue();
  }

  /**
   * 提供精确的减法运算 保留指定位数
   *
   * @param v1 被减数
   * @param v2 减数
   * @return 两个参数的差 v1 - v2
   */
  public static double subScale(double v1, double v2, int scale) {
    BigDecimal b1 = new BigDecimal(Double.toString(v1));
    BigDecimal b2 = new BigDecimal(Double.toString(v2));
    return b1.subtract(b2).divide(new BigDecimal(1), scale, RoundingMode.HALF_UP).doubleValue();
  }

  /**
   * 提供精确的乘法运算
   *
   * @param v1 被乘数
   * @param v2 乘数
   * @return 两个参数的积 v1 * v2
   */
  public static double mul(double v1, double v2) {
    BigDecimal b1 = new BigDecimal(Double.toString(v1));
    BigDecimal b2 = new BigDecimal(Double.toString(v2));
    return b1.multiply(b2).doubleValue();
  }

  /**
   * 提供精确的乘法运算
   *
   * @param v1 数据v1
   * @param v2 数据v2
   * @param vs 数据vs
   * @return 数据相加之和
   */
  public static double mul(double v1, double v2, double... vs) {
    double result = mul(v1, v2);
    for (double item : vs) {
      result = mul(result, item);
    }

    return result;
  }

  /**
   * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到 小数点以后2位，以后的数字四舍五入
   *
   * @param v1 被除数
   * @param v2 除数
   * @return 两个参数的商 v1 / v2
   */
  public static double div(double v1, double v2) {
    return div(v1, v2, DEFAULT_SCALE);
  }

  /**
   * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入
   *
   * @param v1 被除数
   * @param v2 除数
   * @param scale 表示表示需要精确到小数点以后几位。
   * @return 两个参数的商 v1 / v2
   */
  public static double div(double v1, double v2, int scale) {
    if (scale < 0) {
      throw new IllegalArgumentException("小数位数必须是正整数或零");
    }
    if (isZero(v1)) {
      return BigDecimal.ZERO.doubleValue();
    }
    BigDecimal b1 = new BigDecimal(Double.toString(v1));
    BigDecimal b2 = new BigDecimal(Double.toString(v2));

    return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
  }

  /**
   * 提供精确的小数位四舍五入处理(默认保留2位)
   *
   * @param v 需要四舍五入的数字
   * @return 四舍五入后的结果
   */
  public static double round(double v) {
    return round(v, DEFAULT_SCALE);
  }

  /**
   * 提供精确的小数位四舍五入处理
   *
   * @param v 需要四舍五入的数字
   * @param scale 小数点后保留几位
   * @return 四舍五入后的结果
   */
  public static double round(double v, int scale) {
    if (scale < 0) {
      throw new IllegalArgumentException("小数位数必须是正整数或零");
    }
    BigDecimal b = new BigDecimal(Double.toString(v));
    BigDecimal one = new BigDecimal("1");

    return b.divide(one, scale, RoundingMode.HALF_UP).doubleValue();
  }

  /**
   * 判断数字是否等于0
   *
   * @param number 数字
   * @return true/false
   */
  public static boolean isZero(double number) {
    return 0 == new BigDecimal(number).compareTo(BigDecimal.ZERO);
  }

  /**
   * 判断数字是否等于0
   *
   * @param number 数字
   * @return true/false
   */
  public static boolean isZero(BigDecimal number) {
    return 0 == number.compareTo(BigDecimal.ZERO);
  }

  /**
   * 获取较昨日增长数值百分比(默认保留2位小数) 公式：(今日数据 - 昨日数据) * 100 / 昨日数据 四舍五入
   *
   * @param number1 今日数据
   * @param number2 昨日数据
   * @return 结果
   */
  public static double getGrowthPercentage(double number1, double number2) {
    return getGrowthPercentage(number1, number2, DEFAULT_SCALE);
  }

  /**
   * 获取较昨日增长数值百分比 公式：(今日数据 - 昨日数据) * 100 / 昨日数据 四舍五入
   *
   * @param number1 今日数据
   * @param number2 昨日数据
   * @param scale 精确位数
   * @return 结果
   */
  public static double getGrowthPercentage(double number1, double number2, int scale) {
    double result = 0d;
    // 昨日数据为0,今日数据不为0,增长率为100%
    if (!isZero(number1) && isZero(number2)) {
      result = DOUBLE_HUNDRED;
    } else if (!isZero(number1) && !isZero(number2)) {
      result = div(mul(sub(number1, number2), INT_HUNDRED), number2, scale);
    }

    return result;
  }

  /**
   * 去除数据逗号分隔符
   *
   * @param number 数字字符串
   * @return String
   */
  public static String removeComma(String number) {
    if (number.contains(",")) {
      number = number.replace(",", "");
    }
    return number;
  }

  /**
   * 去除整数小数点
   *
   * @param number 数字字符串
   * @return String
   */
  public static String removeDecimalPoint(String number) {
    if (number.indexOf(".") > 0) {
      String[] split = number.split("\\.");
      number = split[0];
    }

    return number;
  }

  public static int roundUpToTenDigit(double num) {
    // 将数字转换为整数部分（舍弃小数部分）
    int intNum = (int) num;
    // 获取十位数字
    int tenDigit = (intNum / 10) * 10;

    // 如果原数字的小数部分不为0或原数字不为整数且个位数不为0，则十位数字加10
    if ((num - intNum) != 0 || (intNum % 10 != 0)) {
      tenDigit += 10;
    }

    return tenDigit;
  }
}
