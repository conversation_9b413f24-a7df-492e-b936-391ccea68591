package com.yhd.admin.bms.domain.dto.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备资料-温振报警点位管理
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwEquipmentPointDTO extends BaseDTO implements Serializable {
  /** 设备资料id */
  private Long equipmentDataId;
  /** 设备编码+类别 */
  private String equipmentNoType;
  /** 类型 */
  private String typeCode;
  /** 类型名称 */
  private String typeName;
  /** 判断点位名称 */
  private String pointName;
  /** 判断点位 */
  private String pointAddress;
  /** 警告下限点位 */
  private String warnDown;
  /** 警告上限点位 */
  private String warnUp;
  /** 危险下限点位 */
  private String riskDown;
  /** 危险上限点位 */
  private String riskUp;
}
