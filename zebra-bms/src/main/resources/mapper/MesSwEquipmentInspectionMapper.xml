<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentInspectionDao">

    <select id="pagingQuery" resultType="com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionPageDTO"
            parameterType="com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionParam">
        SELECT
            id                      AS  id,
            parent_id               AS  parentId,
            equipment_name          AS  equipmentName,
            classification_shop     AS  classificationShop,
            this_period             AS  thisPeriod,
            created_date            AS  createdDateDate,
            created_by              AS  createdBy,
            status_code             AS  statusCode,
            status_name             AS  statusName,
            equipment_no            AS  equipmentNo,
            equipment_power         AS  equipmentPower,
            process_instance_id     AS  processInstanceId
        FROM tb_mes_sw_equipment_inspection
        <where>
            <if test="param.startDate != null and param.endDate != null">
                AND created_date &gt;= #{param.startDate}
                AND created_date &lt;= #{param.endDate}
            </if>
            <if test="param.equipmentType != null and param.equipmentType != ''">
                AND equipment_type = #{param.equipmentType}
            </if>
            <if test="param.thisInspection != null and param.thisInspection != ''">
                AND this_inspection = #{param.thisInspection}
            </if>
            <if test="param.statusCode != null and param.statusCode != ''">
                AND status_code = #{param.statusCode}
            </if>
            <if test="param.classificationShop != null and param.classificationShop != ''">
                AND classification_shop like CONCAT('%',#{param.classificationShop},'%')
            </if>
            <if test="param.IDS != null and param.IDS.size() &gt; 0">
                AND id in
                <foreach collection="param.IDS" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </if>
            AND parent_id IS NULL
        </where>
        ORDER BY created_time DESC
    </select>

</mapper>
