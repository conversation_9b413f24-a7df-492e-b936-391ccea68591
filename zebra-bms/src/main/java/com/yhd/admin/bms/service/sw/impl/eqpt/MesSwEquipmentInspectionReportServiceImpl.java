package com.yhd.admin.bms.service.sw.impl.eqpt;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentInspectionReportDao;
import com.yhd.admin.bms.domain.convert.sw.eqpt.MesSwEquipmentInspectionReportConvert;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentDataDTO;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionReportDTO;
import com.yhd.admin.bms.domain.dto.sys.OrgDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspection;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspectionReport;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionReportParam;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentRepairParam;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentDataService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentInspectionReportService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentInspectionService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairService;
import com.yhd.admin.bms.service.sys.FileService;
import com.yhd.admin.bms.service.sys.OrgService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备巡检检修填报
 *
 * <AUTHOR>
 * @since 1.0.0 2024-08-19
 */
@Service
public class MesSwEquipmentInspectionReportServiceImpl
    extends ServiceImpl<MesSwEquipmentInspectionReportDao, MesSwEquipmentInspectionReport>
    implements MesSwEquipmentInspectionReportService {

  @Resource private MesSwEquipmentInspectionReportConvert convert;
  @Resource private FileService fileService;
  @Resource private MesSwEquipmentRepairService repairService;
  @Resource private MesSwEquipmentInspectionService inspectionService;
  @Resource private UserAccountService userService;
  @Resource private OrgService orgService;
  @Resource private MesSwEquipmentDataService dataService;
  /** 业务类型 */
  private static final String BUSINESS_TYPE = "巡检工单或厂领导复检";
  /** 附件类型 */
  private static final String FILE_TYPE = "现场照片";

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(List<MesSwEquipmentInspectionReportParam> paramList) {
    // 筛选出无id,为新增的数据
    List<MesSwEquipmentInspectionReportParam> newParams =
        paramList.stream().filter(param -> param.getId() == null).collect(Collectors.toList());
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String account = authentication.getName();
    boolean save = false;
    if (newParams.size() > 0) {
      newParams.forEach(
          param -> {
            // set当前登录人账号
            param.setAccount(account);
            // 整改车间名称
            for (OrgDTO orgDTO : orgService.getWorkshopList()) {
                if (orgDTO.getId().equals(param.getZgWorkshopCode())) {
                    param.setZgWorkshopName(orgDTO.getOrgName());
                }
            }
          });
      List<MesSwEquipmentInspectionReport> reports = convert.toEntityList(newParams);
      save = super.saveBatch(reports);
      if (save) {
        reports.forEach(
            report -> {
              // 添加照片附件
              fileService.insertFile(report.getId(), BUSINESS_TYPE, FILE_TYPE, report.getPhoto());
              // 调用检修提报接口
              MesSwEquipmentRepairParam repairParam = new MesSwEquipmentRepairParam();
              MesSwEquipmentInspection ins = inspectionService.getById(report.getParentId());
              // 设备id
              MesSwEquipmentDataDTO mesSwEquipmentDataDTO = dataService.getDetailByNo(ins.getEquipmentNo());
              repairParam.setEquipmentDataId(mesSwEquipmentDataDTO.getId());
              // 设备编码+类别
              repairParam.setEquipmentNoType(ins.getEquipmentNo() + " " + ins.getEquipmentName());
              // 提报人账号
              repairParam.setTbUserCode(account);
              // 提报人姓名
              repairParam.setTbUserName(userService.getNameByUserName(account));
              // 提报时间
              repairParam.setTbTime(LocalDateTime.now());
              // 提报车间编码
              for (OrgDTO orgDTO : orgService.getWorkshopList()) {
                if (orgDTO.getOrgName().equals(ins.getWorkShopName())) {
                  repairParam.setTbWorkshopCode(orgDTO.getId());
                }
              }
              // 提报车间名称
              repairParam.setTbWorkshopName(ins.getWorkShopName());
              // 整改车间名称
              repairParam.setZgWorkshopName(report.getZgWorkshopName());
              // 整改车间编码
              repairParam.setZgWorkshopCode(report.getZgWorkshopCode());
              // 存在问题
              repairParam.setExistProblem(report.getExistQuestion());
              // 现场照片
              repairParam.setTbPhoto(report.getPhoto());
              repairService.addXJ(repairParam);
            });
      }
    }
    return save;
  }

  @Override
  public List<MesSwEquipmentInspectionReportDTO> queryListByParentId(String id) {
    LambdaQueryWrapper<MesSwEquipmentInspectionReport> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentInspectionReport::getParentId, id);
    List<MesSwEquipmentInspectionReportDTO> reportDTOS = convert.toDTOList(list(wrapper));
    reportDTOS.forEach(
        reportDTO -> {
          reportDTO.setPhoto(fileService.getFileList(reportDTO.getId(), BUSINESS_TYPE, FILE_TYPE));
        });
    return reportDTOS;
  }
}
