package com.yhd.admin.common.constant;

/**
 * 符号和特殊符号常用类
 *
 * <AUTHOR>
 * @date 2024/8/15 14:01
 */
public class SymbolConstant {

  /** 符号：点 */
  public static final String SPOT = ".";

  /** 符号：双斜杠 */
  public static final String DOUBLE_BACKSLASH = "\\";

  /** 符号：冒号 */
  public static final String COLON = ":";

  /** 符号：逗号 */
  public static final String COMMA = ",";

  /** 符号：左花括号 } */
  public static final String LEFT_CURLY_BRACKET = "{";

  /** 符号：右花括号 } */
  public static final String RIGHT_CURLY_BRACKET = "}";

  /** 符号：井号 # */
  public static final String WELL_NUMBER = "#";

  /** 符号：单斜杠 */
  public static final String SINGLE_SLASH = "/";

  /** 符号：双斜杠 */
  public static final String DOUBLE_SLASH = "//";

  /** 符号：感叹号 */
  public static final String EXCLAMATORY_MARK = "!";

  /** 符号：下划线 */
  public static final String UNDERLINE = "_";

  /** 符号：单引号 */
  public static final String SINGLE_QUOTATION_MARK = "'";

  /** 符号：星号 */
  public static final String ASTERISK = "*";

  /** 符号：百分号 */
  public static final String PERCENT_SIGN = "%";

  /** 符号：美元 $ */
  public static final String DOLLAR = "$";

  /** 符号：和 & */
  public static final String AND = "&";

  /** 符号：../ */
  public static final String SPOT_SINGLE_SLASH = "../";

  /** 符号：..\\ */
  public static final String SPOT_DOUBLE_BACKSLASH = "..\\";

  /** 系统变量前缀 #{ */
  public static final String SYS_VAR_PREFIX = "#{";

  /** 符号 {{ */
  public static final String DOUBLE_LEFT_CURLY_BRACKET = "{{";

  /** 符号：[ */
  public static final String SQUARE_BRACKETS_LEFT = "[";
  /** 符号：] */
  public static final String SQUARE_BRACKETS_RIGHT = "]";

  /** 拼接字符串符号 分号 ; */
  public static final String SEMICOLON = ";";
}
