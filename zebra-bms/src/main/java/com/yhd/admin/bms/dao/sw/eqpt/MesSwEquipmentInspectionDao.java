package com.yhd.admin.bms.dao.sw.eqpt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.bms.domain.dto.sw.eqpt.MesSwEquipmentInspectionPageDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentInspection;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesSwEquipmentInspectionParam;
import org.apache.ibatis.annotations.Param;

public interface MesSwEquipmentInspectionDao extends BaseMapper<MesSwEquipmentInspection> {
  /**
   * 设备巡检列表查询接口
   *
   * @param page
   * @param param
   * @return
   */
  Page<MesSwEquipmentInspectionPageDTO> pagingQuery(
      Page<MesSwEquipmentInspectionPageDTO> page,
      @Param("param") MesSwEquipmentInspectionParam param);
}
