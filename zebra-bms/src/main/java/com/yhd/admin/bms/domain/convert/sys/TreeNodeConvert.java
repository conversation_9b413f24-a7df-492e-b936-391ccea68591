package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsCategoryDTO;
import com.yhd.admin.bms.domain.dto.sys.MenuDTO;
import com.yhd.admin.bms.domain.dto.sys.OrgDTO;
import com.yhd.admin.bms.domain.dto.sys.RegionDTO;
import com.yhd.admin.bms.domain.vo.sys.TreeNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName TreeNode.java @Description TODO
 * @createTime 2020年05月11日 14:42:00
 */
@Mapper(
    componentModel = "spring",
    uses = {Transform.class})
public interface TreeNodeConvert {

  @Mappings({
    @Mapping(source = "id", target = "key"),
    @Mapping(source = "id", target = "value"),
    @Mapping(source = "name", target = "title"),
    @Mapping(
        target = "isLeaf",
        source = "children",
        qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
    @Mapping(target = "disabled", source = "type")
  })
  TreeNode toTreeNode(MenuDTO menuDTO);

  List<TreeNode> toTreeNode(List<MenuDTO> menuDTOList);

  @Mappings({
    @Mapping(source = "id", target = "key"),
    @Mapping(source = "id", target = "value"),
    @Mapping(source = "orgName", target = "title"),
    @Mapping(
        target = "isLeaf",
        source = "children",
        qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
    @Mapping(
        source = "status",
        target = "disabled",
        qualifiedByName = {"Transform", "negDisabled"})
  })
  TreeNode orgToTreeNode(OrgDTO orgDTO);

  @Mappings({
    @Mapping(source = "id", target = "key"),
    @Mapping(source = "id", target = "value"),
    @Mapping(source = "regionName", target = "title"),
    @Mapping(
        target = "isLeaf",
        source = "children",
        qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
    @Mapping(
        source = "status",
        target = "disabled",
        qualifiedByName = {"Transform", "negDisabled"})
  })
  TreeNode regionToTreeNode(RegionDTO regionDTO);

  List<TreeNode> regionToTreeNode(List<RegionDTO> regionDTO);

  List<TreeNode> orgToTreeNode(List<OrgDTO> orgDTOList);

  @Mappings({
    @Mapping(source = "id", target = "key"),
    @Mapping(source = "id", target = "value"),
    @Mapping(source = "name", target = "title"),
    @Mapping(
        target = "isLeaf",
        source = "children",
        qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
    @Mapping(
        source = "status",
        target = "disabled",
        qualifiedByName = {"Transform", "negDisabled"})
  })
  TreeNode goodsToTreeNode(MesSwGoodsCategoryDTO goodsDTO);

  List<TreeNode> goodsToTreeNode(List<MesSwGoodsCategoryDTO> goodsDTOList);
}
