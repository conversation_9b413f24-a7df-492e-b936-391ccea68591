package com.yhd.admin.bms.service.sw.impl.eqpt;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.eqpt.MesSwEquipmentRepairTbAuditDao;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentRepairTbAudit;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentRepairTbAuditService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备检修提报审核记录表-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/2/10 11:20
 */
@Service
public class MesSwEquipmentRepairTbAuditServiceImpl
    extends ServiceImpl<MesSwEquipmentRepairTbAuditDao, MesSwEquipmentRepairTbAudit>
    implements MesSwEquipmentRepairTbAuditService {
  @Override
  public MesSwEquipmentRepairTbAudit getTodoByRepairId(Long repairId) {
    LambdaQueryWrapper<MesSwEquipmentRepairTbAudit> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentRepairTbAudit::getRepairId, repairId);
    wrapper.eq(MesSwEquipmentRepairTbAudit::getClResult, "0");

    List<MesSwEquipmentRepairTbAudit> tbAudits = baseMapper.selectList(wrapper);

    return CollectionUtils.isNotEmpty(tbAudits) ? tbAudits.get(0) : null;
  }

  @Override
  public MesSwEquipmentRepairTbAudit getFinishByRepairId(Long repairId) {
    LambdaQueryWrapper<MesSwEquipmentRepairTbAudit> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MesSwEquipmentRepairTbAudit::getRepairId, repairId);
    wrapper.ne(MesSwEquipmentRepairTbAudit::getClResult, "0");

    wrapper.orderByDesc(MesSwEquipmentRepairTbAudit::getReceiveTime);

    List<MesSwEquipmentRepairTbAudit> tbAudits = baseMapper.selectList(wrapper);

    return CollectionUtils.isNotEmpty(tbAudits) ? tbAudits.get(0) : null;
  }
}
