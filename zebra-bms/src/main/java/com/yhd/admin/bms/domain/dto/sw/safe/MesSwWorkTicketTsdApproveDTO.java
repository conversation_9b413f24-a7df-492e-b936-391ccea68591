package com.yhd.admin.bms.domain.dto.sw.safe;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/** 外施工单位停送电审批工作票 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwWorkTicketTsdApproveDTO extends BaseDTO {

  private static final long serialVersionUID = 9153749872706958963L;

  /** 施工单位名称(承包商) */
  private String sgOrgName;
  /** 施工单位编码(承包商) */
  private Long sgOrgCode;
  /** 施工单位负责人code */
  private String sgUserCode;
  /** 施工单位负责人姓名 */
  private String sgUserName;
  /** 施工项目 */
  private String sgItem;
  /** 联系电话 */
  private Long telephone;
  /** 作业人数 */
  private Long workUserCount;
  /** 申请停电开始时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime tdStartTime;
  /** 申请停电结束时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime tdEndTime;
  /** 申请送电时间(yyyy-MM-dd hh:mm:ss) */
  private LocalDateTime sdTime;
  /** 工作票状态code：01停电审批中、02停电审批通过、03送电审批中、04送电审批通过、05已作废 */
  private String statusCode;
  /** 工作票状态名称 */
  private String statusName;

  /** 申请停电时间区间字符串 */
  private String tdTimeStr;
  /** 是否能审批：true可以，false不可以 */
  private Boolean isCanSp = false;
  /** 是否能作废：true可以，false不可以 */
  private Boolean isCanZf = false;
  /** 是否能申请送电：true可以，false不可以 */
  private Boolean isCanSd = false;
  /** 停电审批用户类型用户列表 */
  private List<MesSwWorkTicketTsdSignUserDTO> userTdTypeList;
  /** 送电审批用户类型用户列表 */
  private List<MesSwWorkTicketTsdSignUserDTO> userSdTypeList;

  /** 设备列表 */
  private List<MesSwWorkTicketTsdEquipmentDTO> equipmentList;
}
