package com.yhd.admin.bms.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *原煤加工信息-亚控点位key枚举类
 *
 * <AUTHOR>
 * @Date 2025/3/4 17:22 @Version 1.0
 */
@Getter
public enum KHRawCoalProcessingKeyEnum {
//    CON201Amount("XMSW_Raw_CON201_InstantCoalAmount", "201胶带机瞬时量"),
    CON201Today("XMSW_Raw_CON201_Info_ProductToday", "201胶带机日产量"),
    CON201Month("XMSW_Raw_CON201_Info_ProductMonth", "201胶带机月产量"),
    CON201Year("XMSW_Raw_CON201_Info_ProductYear", "201胶带机年产量"),

//    CON218Amount("XMSW_Raw_CON218_InstantCoalAmount", "218胶带机瞬时量"),
    CON218Today("XMSW_Raw_CON218_Info_ProductToday", "218胶带机日产量"),
    CON218Month("XMSW_Raw_CON218_Info_ProductMonth", "218胶带机月产量"),
    CON218Year("XMSW_Raw_CON218_Info_ProductYear", "218胶带机年产量"),

//    CON301Amount("XMSW_Raw_CON301_InstantCoalAmount", "301胶带机瞬时量"),
    CON301Today("XMSW_Lump_CON301_Info_ProductToday", "301胶带机日产量"),
    CON301Month("XMSW_Lump_CON301_Info_ProductMonth", "301胶带机月产量"),
    CON301Year("XMSW_Lump_CON301_Info_ProductYear", "301胶带机年产量"),

//    CON302Amount("XMSW_Raw_CON302_InstantCoalAmount", "302胶带机瞬时量"),
    CON302Today("XMSW_Lump_CON302_Info_ProductToday", "302胶带机日产量"),
    CON302Month("XMSW_Lump_CON302_Info_ProductMonth", "302胶带机月产量"),
    CON302Year("XMSW_Lump_CON302_Info_ProductYear", "302胶带机年产量"),

//    CON701Amount("XMSW_Raw_CON701_InstantCoalAmount", "701胶带机瞬时量"),
    CON701Today("XMSW_Siev_CON701_Info_ProductToday", "701胶带机日产量"),
    CON701Month("XMSW_Siev_CON701_Info_ProductMonth", "701胶带机月产量"),
    CON701Year("XMSW_Siev_CON701_Info_ProductYear", "701胶带机年产量"),

//    CON702Amount("XMSW_Lump_CON702_InstantCoalAmount", "702胶带机瞬时量"),
    CON702Today("XMSW_Siev_CON702_Info_ProductToday", "702胶带机日产量"),
    CON702Month("XMSW_Siev_CON702_Info_ProductMonth", "702胶带机月产量"),
    CON702Year("XMSW_Siev_CON702_Info_ProductYear", "702胶带机年产量"),

    CON703Today("XMSW_Siev_CON703_Info_ProductToday", "703胶带机日产量"),
    CON703Month("XMSW_Siev_CON703_Info_ProductMonth", "703胶带机月产量"),
    CON703Year("XMSW_Siev_CON703_Info_ProductYear", "703胶带机年产量"),

    CON706Today("XMSW_Siev_CON706_Info_ProductToday", "706胶带机日产量"),
    CON706Month("XMSW_Siev_CON706_Info_ProductMonth", "706胶带机月产量"),
    CON706Year("XMSW_Siev_CON706_Info_ProductYear", "706胶带机年产量"),

    CON901Today("XMSW_Siev_CON901_Info_ProductToday", "901胶带机日产量"),
    CON901Month("XMSW_Siev_CON901_Info_ProductMonth", "901胶带机月产量"),
    CON901Year("XMSW_Siev_CON901_Info_ProductYear", "901胶带机年产量"),
    ;

    private final String key;
    private final String desc;

    KHRawCoalProcessingKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static List<String> getKeyList() {
        KHRawCoalProcessingKeyEnum[] values = KHRawCoalProcessingKeyEnum.values();

        return Arrays.stream(values).map(KHRawCoalProcessingKeyEnum::getKey).collect(Collectors.toList());
    }

    public static KHRawCoalProcessingKeyEnum getEnumByKey(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        KHRawCoalProcessingKeyEnum[] values = KHRawCoalProcessingKeyEnum.values();
        for (KHRawCoalProcessingKeyEnum value : values) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }
}
