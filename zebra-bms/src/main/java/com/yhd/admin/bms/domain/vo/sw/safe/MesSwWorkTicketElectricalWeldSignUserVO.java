package com.yhd.admin.bms.domain.vo.sw.safe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 电气焊工作票签字用户信息 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwWorkTicketElectricalWeldSignUserVO extends BaseVO {

  /** 检修作业工作票主键id */
  private Long ticketId;

  /** 表格类型 */
  private String type;

  /** 签字用户类型 */
  private String userType;

  /** 用户账号 */
  private String userCode;

  /** 用户姓名 */
  private String userName;

  /** 签字图片url */
  private String signUrl;

  /** 审批意见 */
  private String spOpinion;

  /** 是否完成签字：0false,1true */
  private Boolean isSign;

  /** 当前账号是否可以签字：true 可以 false 不可以 */
  @TableField(exist = false)
  private Boolean isCurrent = false;
}
