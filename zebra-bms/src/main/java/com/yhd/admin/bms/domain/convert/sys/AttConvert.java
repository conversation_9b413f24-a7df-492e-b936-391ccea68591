package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.AttachmentDTO;
import com.yhd.admin.bms.domain.entity.sys.SysAttachment;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AttConvert {

    SysAttachment toEntity(AttachmentDTO dto);

    AttachmentDTO toDTO(SysAttachment entity);

    List<SysAttachment> toEntity(List<AttachmentDTO> dtoList);

    List<AttachmentDTO> toDTO(List<SysAttachment> entityList);

}
