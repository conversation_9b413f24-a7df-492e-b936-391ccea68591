package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class MesChecklistRcsudeParam extends QueryParam {

  private Long id;

  /** 煤样名称 */
  private String coalSampleName;
  /** 采样地点 */
  private String samplingLocation;
  /** 煤样质量 */
  private Double samplingQty;
  /** 采样时间 */
  private LocalDate samplingTime;
  /** 煤样灰分 */
  private Double coalAsh;
  /** 实验日期 */
  private LocalDate exptDate;
  /** 实验类型 */
  private String exptTypes;
  /** 实验数据 */
  private String exptData;
  /** 实验数据2 */
  private String exptData2;
  /** 单位负责人 */
  private String charge;
  /** 审核人 */
  private String reviewer;
  /** 制表 */
  private String lister;
  /** 实验人员 */
  private String experimenter;

  /** 采样时间 开始 */
  private LocalDate samplingStartDate;

  /** 采样时间 结束 */
  private LocalDate samplingEndDate;

  /** 实验日期 */
  private LocalDate exptStartDate;

  /** 实验日期 */
  private LocalDate exptEndDate;

  /** 实验数据缓存KEY */
  private String expId;

  /** 实验数据缓存KEY */
  private String fileName;
}
