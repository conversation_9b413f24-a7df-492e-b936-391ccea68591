package com.yhd.admin.passport.auth.config;

import com.yhd.admin.passport.auth.handler.AuthAccessDeniedHandler;
import com.yhd.admin.passport.auth.handler.AuthExceptionEntryPoint;
import com.yhd.admin.passport.auth.handler.AuthOAuth2ResponseExceptionTranslator;
import com.yhd.admin.passport.auth.jwt.JwtTokenEnhancer;
import com.yhd.admin.passport.auth.token.CustomTokenServices;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.approval.ApprovalStore;
import org.springframework.security.oauth2.provider.approval.JdbcApprovalStore;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.code.JdbcAuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.web.authentication.rememberme.JdbcTokenRepositoryImpl;
import org.springframework.security.web.authentication.rememberme.PersistentTokenRepository;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAuthorizationServer
public class AuthServerConfig extends AuthorizationServerConfigurerAdapter {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserDetailsService userDetailsService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AuthAccessDeniedHandler authAccessDeniedHandler;

    @Autowired
    private AuthExceptionEntryPoint authExceptionEntryPoint;

    @Autowired
    private RedisConnectionFactory redisConnectionFactor;

    @Autowired
    private AuthOAuth2ResponseExceptionTranslator translator;

    @Bean
    public PasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public ClientDetailsService jdbcClientDetailsService() {
        return new JdbcClientDetailsService(dataSource);
    }

    // token 保存策略，指你生成的 Token 要往哪里存储
    // 有四种，JdbcTokenStore ， InMemoryTokenStore， JwkTokenStore，JwtTokenStore, RedisTokenStore
    @Bean
    public TokenStore tokenStore() {
        RedisTokenStore redisTokenStore = new RedisTokenStore(redisConnectionFactor);
        redisTokenStore.setPrefix("OAUTH2:");
        return redisTokenStore;
    }

    @Bean
    public PersistentTokenRepository persistentTokenRepository() {
        JdbcTokenRepositoryImpl tokenRepository = new JdbcTokenRepositoryImpl();
        tokenRepository.setDataSource(dataSource);
        return tokenRepository;
    }

    @Bean
    public ApprovalStore approvalStore() {
        return new JdbcApprovalStore(dataSource);
    }

    @Bean
    public AuthorizationCodeServices authorizationCodeServices() {
        return new JdbcAuthorizationCodeServices(dataSource);
    }

    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.jdbc(dataSource).passwordEncoder(passwordEncoder).clients(jdbcClientDetailsService());
    }

    // 令牌管理
    @Bean("customTokenServices")
    public AuthorizationServerTokenServices tokenServices() {
        CustomTokenServices tokenServices = new CustomTokenServices();
        // token 保存策略
        tokenServices.setTokenStore(tokenStore());
        // 支持刷新模式
        tokenServices.setSupportRefreshToken(true);
        // 客户端信息来源
        tokenServices.setClientDetailsService(jdbcClientDetailsService());
        // 用户详情服务
        tokenServices.setUserDetailsService(userDetailsService);
        // token 有效期自定义设置，默认 12 小时
        tokenServices.setAccessTokenValiditySeconds(60 * 60 * 24);
        // refresh token 有效期自定义设置，默认 30 天
        tokenServices.setRefreshTokenValiditySeconds(60 * 60 * 24 * 7);
        tokenServices.setTokenEnhancer(new JwtTokenEnhancer());

        return tokenServices;
    }

    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {

        endpoints
            .authenticationManager(authenticationManager)
            .tokenServices(tokenServices())
            .approvalStore(approvalStore())
            .userDetailsService(userDetailsService)
            .exceptionTranslator(translator)
            //        .tokenGranter()
            .authorizationCodeServices(authorizationCodeServices());
    }

    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) {
        security
            .tokenKeyAccess("isAuthentication()")
            .checkTokenAccess("permitAll()")
            .allowFormAuthenticationForClients()
            .accessDeniedHandler(authAccessDeniedHandler)
            .authenticationEntryPoint(authExceptionEntryPoint);
    }
}
