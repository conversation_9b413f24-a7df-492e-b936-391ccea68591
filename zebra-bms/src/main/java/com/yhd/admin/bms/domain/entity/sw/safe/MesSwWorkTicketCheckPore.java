package com.yhd.admin.bms.domain.entity.sw.safe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 检修作业工作票 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwWorkTicketCheckPore extends BaseEntity {

  /** 作业单位 */
  private String workUnit;

  /** 作业地点 */
  private String workLocation;

  /** 作业内容 */
  private String workContent;

  /** 计划施工时间-开始 */
  private String sgStartTime;

  /** 计划施工时间-结束 */
  private String sgEndTime;

  /** 作业负责人账号 */
  private String chargeCode;

  /** 作业负责人名称 */
  private String chargeName;

  /** 现场负责人账号 */
  private String xcCode;

  /** 现场负责人名称 */
  private String xcName;

  /** 安全负责人账号 */
  private String safeCode;

  /** 安全负责人名称 */
  private String safeName;

  /** 技术负责人账号 */
  private String jsCode;

  /** 技术负责人名称 */
  private String jsName;

  /** 工作票状态code：01审批中、02已完成、03已作废 */
  private String statusCode;

  /** 工作票状态名称 */
  private String statusName;

  /** 是否能审批：true可以，false不可以 */
  @TableField(exist = false)
  private Boolean isCanSp = false;

  /** 是否能作废：true可以，false不可以 */
  @TableField(exist = false)
  private Boolean isCanZf = false;

  /** 是否能贯彻签字：true可以，false不可以 */
  @TableField(exist = false)
  private Boolean isCanGcSign = false;
}
