package com.yhd.admin.bms.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ExceptionEnum.java @Description TODO 异常编码，定义4位编码，如果1000～1999为一组，不同模块采用不同组，不要交叉使用。
 * @createTime 2020年04月01日 15:08:00
 */
@Getter
public enum ExceptionEnum {
    // User 用户类 【1000-1999】

    USER_NAME_NULL("1000", "账户名为空，请检查"), USER_ID_NULL("1001", "用户ID为空，请检查"),
    USER_NAME_ID_NULL("1002", "用户ID和账户名为空，请检查"), DIC_ID_CATEGORY_NULL("9000", "字典项分类ID和分类编码都为空，请检查"),
    USER_PASSWD_NOT_MATCH("1003", "原始密码不匹配"), USER_NOT_LOGIN("1004", "用户未登录，请检查"), NON_DATA("1004", "暂无数据"),
    // 年度计划

    PRODUCT_PLAN_CROSS("2000", "生产计划存在时间点交叉"),

    // 工作日报
    PRODUCT_PLAN_SEQ_EMPTY("3000", "班组日报SEQ不能为空"),

    PRODUCT_STRUCTURE_FORECAST("4000", "当前日期已存在记录"),
    // 库存管理【5000-5999】
    STOCK_GOODS_EXIST_ERROR("5000", "物资已存在"),

    STOCK_GOODS_NUM_ERROR("5001", "物资数量不足，不可出库"),

    REQUIRED_ERROR("10001", "请确认必填项！"), DATA_NOT_EXIST("10002", "数据不存在！"), CF_ERROR("10003", "重复操作！"),
    WQ_ERROR("10004", "无权限操作！");

    private final String code;
    private final String desc;

    ExceptionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
