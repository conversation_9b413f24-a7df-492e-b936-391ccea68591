package com.yhd.admin.bms.domain.vo.sw.ticket;

import com.yhd.admin.bms.domain.dto.sw.ticket.MesSwWorkTicketLiftSignUserDTO;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import com.yhd.admin.bms.domain.vo.sys.UserAccountVo;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 受限空间许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-29
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesSwWorkTicketConfinedSpaceVO extends BaseVO implements Cloneable, Serializable {

	/**
	* 许可证编号
	*/
	private String ticketNo;

	/**
	* 申请单位
	*/
	private String applyUnit;

	/**
	* 编制日期
	*/
	private LocalDate orgDate;

	/**
	* 受限空间名称
	*/
	private String spaceName;

	/**
	* 作业内容
	*/
	private String workContent;

    /**
     * 许可证有效日期-拼接
     */
    private String ticketStartOrEndTime;

	/**
	* 许可证有效日期-开始日期
	*/
	private LocalDateTime ticketStartTime;

	/**
	* 许可证有效日期-结束日期
	*/
	private LocalDateTime ticketEndTime;

	/**
	* 需办理其他许可证
	*/
	private String ticketPermit;

    /**
     * 需办理其他许可证集合
     */
    private List<String> ticketPermitList;

	/**
	* 进入受限空间人员
	*/
	private String enterSpaceUser;

	/**
	* 现场监护人员
	*/
	private String sceneSafeUser;

	/**
	* 主要危害因素
	*/
	private String ticketDanger;

    /**
     * 主要危害因素集合
     */
    private List<String> ticketDangerList;

	/**
	* 补充安全技术措施
	*/
	private String otherSafeCheck;

	/**
	* 措施贯彻签字确认
	*/
	private String measureSign;

    /**
     * 措施贯彻签字确认集合
     */
    private List<String> measureSignList;

	/**
	* 许可证状态code
	*/
	private String statusCode;

	/**
	* 许可证状态name
	*/
	private String statusName;

    /**
     * 工作票审批用户列表
     */
    private List<MesSwWorkTicketConfinedSpaceSignUserVO> userSpTypeList;

    /**
     * 责任车间人员集合
     */
    private List<UserAccountVo> dutyWorkshopUsers;

    /**
     * 安监员集合
     */
    private List<UserAccountVo> safeUsers;

    /**
     * 技术员集合
     */
    private List<UserAccountVo> techUsers;

    /**
     * 值班集合
     */
    private List<UserAccountVo> leadUsers;

    /**
     * 是否能审批
     */
    private Boolean isCanSp;

}
