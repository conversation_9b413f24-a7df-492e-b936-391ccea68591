package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainSignDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainStudentSignDTO;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainSignParam;

import java.util.List;

/**
 * 安全培训签到-业务层接口
 *
 * <AUTHOR>
 * @date 2024/1/4 17:07
 */
public interface MesSwSafeTrainSignService {

  /**
   * 根据条件查询培训签到分页列表
   *
   * @param param 请求参数
   * @return 培训签到分页列表
   */
  IPage<MesSwSafeTrainSignDTO> pagingQuery(MesSwSafeTrainSignParam param);

  /**
   * 初始化待签到的课程、学员信息(培训计划审批完成调用)
   *
   * @param planId 培训计划表主键id
   * @return true成功、false失败
   */
  Boolean initTodoSignCourseAndStudent(Long planId);

  /**
   * 查询安全培训签到详情信息
   *
   * @param param 参数：计划id&培训日期
   * @return 安全培训签到详情
   */
  MesSwSafeTrainSignDTO getCurrentDetail(MesSwSafeTrainSignParam param);

  /**
   * 教师签到
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean teacherSign(MesSwSafeTrainSignParam param);

  /**
   * 学员签到
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean studentSign(MesSwSafeTrainSignParam param);

  /**
   * 教师发起/停止签到
   *
   * @param param 参数
   * @return true成功，false失败
   */
  Boolean startEndSign(MesSwSafeTrainSignParam param);

  /**
   * 根据培训计划id查询学员列表
   *
   * @param planId 培训计划id
   * @return 学员列表
   */
  List<MesSwSafeTrainStudentSignDTO> getTrainStudentList(Long planId);
}
