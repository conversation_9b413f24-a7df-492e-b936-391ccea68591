package com.yhd.admin.scada.pgsql;

import com.yhd.admin.scada.dao.PointValueDao;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.domain.entity.MeasPointsVal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import com.yhd.admin.scada.opc.ua.HandleService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.builtin.Variant;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class HandlePgServiceImpl implements HandleService {
  @Resource private PointValueDao valueDao;

  @Override
  public void handleMonitored(ManagedDataItem item, DataValue value, PLCPointDTO point) {
    Variant val = value.getValue();
    if (point.getDb() && val.isNotNull()) {
      MeasPointsVal points =
          new MeasPointsVal(
              LocalDateTime.ofInstant(
                  Instant.ofEpochMilli(value.getServerTime().getJavaTime()),
                  ZoneId.systemDefault()),
              point.getPointAddress(),
              point.getKwpAddress(),
              String.valueOf(val.getValue()));
      valueDao.insert(points);
    }
  }
}
