package com.yhd.admin.common.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class DateUtil {

  private static final String DEFAULT_FORMAT = "yyyy-MM-dd";

  /**
   * 当前时间
   *
   * @return
   */
  public static LocalDate now() {
    return LocalDate.now();
  }

  /**
   * 某一时间之前 n天
   *
   * @return
   */
  public static LocalDate minusDays(LocalDate date, Integer n) {
    return date.minusDays(n);
  }

  /**
   * 获取两个日期之间所有的日期
   *
   * @param start
   * @param end
   * @return
   */
  public static List<String> dateBetween(LocalDate start, LocalDate end) {
    List<LocalDate> dates = start.datesUntil(end).collect(Collectors.toList());
    dates.add(end);
    return dates.stream().map(LocalDate::toString).collect(Collectors.toList());
  }

  public static List<LocalDate> dateBetweenToLocalDate(LocalDate start, LocalDate end) {
    List<LocalDate> dates = start.datesUntil(end).collect(Collectors.toList());
    dates.add(end);
    return dates;
  }

  /**
   * 获取两个日期月份区间
   *
   * @param start
   * @param end
   * @return
   */
  public static List<String> dateMonthBetween(String start, String end) {
    List<String> dates = Lists.newArrayList();
    LocalDate startDate = LocalDate.parse(start + "-01");
    LocalDate endDate = LocalDate.parse(end + "-01");
    long distance = ChronoUnit.MONTHS.between(startDate, endDate);
    dates.add(start);
    if (distance > 0) {
      for (int i = 1; i <= distance; i++) {
        LocalDate date = startDate.plusMonths(i);
        dates.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM")));
      }
    }
    return dates;
  }

  /**
   * 获取两个日期年份区间
   *
   * @param startTime
   * @param endTime
   * @return
   */
  public static List<String> dateYearBetween(String startTime, String endTime) {
    List<String> res = new ArrayList<>();
    DateFormat dateFormat = new SimpleDateFormat("yyyy");

    try {
      Date start = dateFormat.parse(startTime);
      Date end = dateFormat.parse(endTime);
      Calendar tempStart = Calendar.getInstance();
      tempStart.setTime(start);
      Calendar tempEnd = Calendar.getInstance();
      tempEnd.setTime(end);
      tempEnd.add(Calendar.YEAR, 1); // 日期加1(包含结束)
      while (tempStart.before(tempEnd)) {
        String year = dateFormat.format(tempStart.getTime());
        res.add(year);
        tempStart.add(Calendar.YEAR, 1);
      }
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return res;
  }

  /**
   * 获取年月
   *
   * @param date
   * @return
   */
  public static String getYearMonth(LocalDate date) {
    return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
  }

  /**
   * 获取系统月份
   *
   * @return
   */
  public static String getSysYearMonth() {
    SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM");
    Calendar calast = Calendar.getInstance();
    String last = sm.format(calast.getTime());
    return last;
  }

  /**
   * 格式化日期
   *
   * @param date 日期对象
   * @return 日期字符串
   */
  public static String formatDate(Date date) {
    return formatDate(date, DEFAULT_FORMAT);
  }

  /**
   * 格式化日期
   *
   * @param localDate 日期对象
   * @return 日期字符串
   */
  public static String formatDate(LocalDate localDate) {
    return formatDate(localDate, DEFAULT_FORMAT);
  }

  public static String formatDate(LocalDate localDat, String format) {
    DateTimeFormatter dtf = DateTimeFormatter.ofPattern(format);
    return localDat.format(dtf);
  }

  /**
   * 格式化日期
   *
   * @param date 日期对象
   * @return 日期字符串
   */
  public static String formatDate(Date date, String format) {
    SimpleDateFormat f = new SimpleDateFormat(format);
    return f.format(date);
  }

  /**
   * 获取本月第一天日期
   *
   * @date 2022/9/7 15:56
   * @return 本月第一天日期
   */
  public static String getCurrentMonthFirst() {
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.MONTH, 0);
    calendar.set(Calendar.DAY_OF_MONTH, 1);

    return formatDate(calendar.getTime());
  }

  /**
   * 获取某年第一天日期
   *
   * @param year 年份
   * @return Date
   */
  public static String getYearFirst(int year) {
    Calendar calendar = Calendar.getInstance();
    calendar.clear();
    calendar.set(Calendar.YEAR, year);

    return formatDate(calendar.getTime());
  }

  /**
   * 获取本年第一天日期
   *
   * @date 2022/9/7 15:56
   * @return String
   */
  public static String getCurrentYearFirst() {
    return new SimpleDateFormat("yyyy").format(new Date()) + "-01-01";
  }

  /**
   * 获取月的第一天
   *
   * @param month 月份(yyyy-MM)
   */
  public static String getMonthBeginDate(String month) {
    String result = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(sdf.parse(month + "-01"));
      calendar.add(Calendar.MONTH, 0);
      calendar.set(Calendar.DAY_OF_MONTH, 1);
      result = sdf.format(calendar.getTime());
    } catch (ParseException e) {
      log.error("日期格式转换异常", e);
    }
    return result;
  }

  /**
   * 获取月的最后一天
   *
   * @param month 月份(yyyy-MM)
   */
  public static String getMonthEndDate(String month) {
    String result = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(sdf.parse(month + "-01"));
      calendar.add(Calendar.MONTH, 0);
      calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
      result = sdf.format(calendar.getTime());
    } catch (ParseException e) {
      log.error("日期格式转换异常", e);
    }
    return result;
  }

  /**
   * 秒级时间戳转LocalDateTime
   *
   * @param longEpochSeconds 秒级时间戳
   * @return LocalDateTime
   */
  public static LocalDateTime longSecondToLocalDateTime(int longEpochSeconds) {
    // 将秒级时间戳转换为 Instant
    Instant instant = Instant.ofEpochSecond(longEpochSeconds);
    // 使用默认时区转换为 LocalDateTime
    return instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
  }

  /**
   * 计算本月剩余天数
   *
   * @return 天数
   */
  public static Integer remainingDaysInMonth() {
    Calendar calendar = Calendar.getInstance();
    int today = calendar.get(Calendar.DAY_OF_MONTH); // 获取今天是本月的第几天

    // 设置calendar的日期为本月最后一天
    calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
    int lastDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取本月最后一天是第几天

    return lastDay - today; // 计算剩余天数
  }

  /**
   * 计算本年剩余天数
   *
   * @return 天数
   */
  public static Integer remainingDaysInYear() {
    Calendar calendar = Calendar.getInstance();
    int currentMonth = calendar.get(Calendar.MONTH);

    // 判断当前月份之后的月份总天数
    int remainingDaysInYear = 365 - calendar.get(Calendar.DAY_OF_YEAR);
    // 如果是二月份之后，并且是闰年，需要加一天
    if (currentMonth > 1 && isLeapYear(calendar.get(Calendar.YEAR))) {
      remainingDaysInYear = remainingDaysInYear + 1;
    }

    return remainingDaysInYear;
  }

  // 判断是否为闰年
  public static boolean isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  /**
   * 获取当前月的前几个月份
   *
   * @param n 偏移个数
   * @return String
   */
  public static String minusMonth(Integer n) {
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
    Date date = new Date();
    Calendar calendar = Calendar.getInstance();
    // 设置为当前时间
    calendar.setTime(date);
    calendar.add(Calendar.MONTH, n);
    date = calendar.getTime();
    return format.format(date);
  }
}
