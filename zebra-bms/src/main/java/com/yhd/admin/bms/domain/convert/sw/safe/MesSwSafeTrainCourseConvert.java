package com.yhd.admin.bms.domain.convert.sw.safe;

import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainCourseDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainCourse;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainCourseParam;
import com.yhd.admin.bms.domain.vo.sw.safe.MesSwSafeTrainCourseVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwSafeTrainCourseConvert {
  MesSwSafeTrainCourse toEntity(MesSwSafeTrainCourseParam param);

  MesSwSafeTrainCourse toEntity(MesSwSafeTrainCourseDTO dto);

  MesSwSafeTrainCourseDTO toDTO(MesSwSafeTrainCourse entity);

  List<MesSwSafeTrainCourseDTO> toDTO(List<MesSwSafeTrainCourse> entityList);

  MesSwSafeTrainCourseVO toVO(MesSwSafeTrainCourseDTO dto);

  List<MesSwSafeTrainCourseVO> toVO(List<MesSwSafeTrainCourseDTO> dtoList);
}
