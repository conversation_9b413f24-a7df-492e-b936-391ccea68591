package com.yhd.admin.bms.service.sw.impl.goods;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.goods.MesSwGoodsStockDao;
import com.yhd.admin.bms.domain.convert.sw.goods.MesSwGoodsStockConvert;
import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsStockDTO;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentData;
import com.yhd.admin.bms.domain.entity.sw.eqpt.MesSwEquipmentSievePlateRecord;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsCategory;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsPlanDetail;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsStock;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsStockParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.goods.MesSwGoodsSievePlateVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentDataService;
import com.yhd.admin.bms.service.sw.eqpt.MesSwEquipmentSievePlateRecordService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsCategoryService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsPlanDetailService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsStockOperateService;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsStockService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.utils.DateUtil;
import com.yhd.admin.common.utils.NumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MesSwGoodsStockServiceImpl extends ServiceImpl<MesSwGoodsStockDao, MesSwGoodsStock>
    implements MesSwGoodsStockService {

    private static final String UNIT = "SW_GOODS_UNIT";
    /**筛板孔径*/
    private static final String GOODS_SIEVE_PLATE_APERTURE = "GOODS_SIEVE_PLATE_APERTURE";
    /**筛板尺寸*/
    private static final String GOODS_SIEVE_PLATE_SIZE = "GOODS_SIEVE_PLATE_SIZE";

    @Resource
    private MesSwGoodsStockConvert convert;

    @Resource
    private DicService dicService;

    @Resource
    private UserAccountService accountService;

    @Resource
    private MesSwGoodsStockOperateService stockOperateService;

    @Resource
    private MesSwGoodsCategoryService categoryService;

    @Resource
    private MesSwEquipmentSievePlateRecordService sievePlateRecordService;
    @Resource
    private MesSwEquipmentDataService equipmentDataService;
    @Resource
    private MesSwGoodsPlanDetailService planDetailService;

    @Override
    public IPage<MesSwGoodsStockDTO> pagingQuery(MesSwGoodsStockParam queryParam) {
        Page<MesSwGoodsStock> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwGoodsStock> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain
            .like(
                StringUtils.isNotBlank(queryParam.getName()),
                MesSwGoodsStock::getName,
                queryParam.getName())
            .like(
                StringUtils.isNotBlank(queryParam.getChargeName()),
                MesSwGoodsStock::getChargeName,
                queryParam.getChargeName());
        if (Objects.nonNull(queryParam.getCategoryCode())) {
            // 查询分类id及子级分类id
            List<Long> subCateIdList = categoryService.getSubCateIdList(queryParam.getCategoryCode());
            queryChain.in(MesSwGoodsStock::getCategoryCode, subCateIdList);
        }
        queryChain.orderByDesc(MesSwGoodsStock::getCreatedTime);

        IPage<MesSwGoodsStock> page = queryChain.page(iPage);
        page.getRecords().forEach(this::goodsDataHandle);

        return page.convert(convert::toDTO);
    }

    @Override
    public Boolean add(MesSwGoodsStockParam param) {
        if (StringUtils.isBlank(param.getUnitCode())
            || Objects.isNull(param.getCategoryCode())
            || StringUtils.isBlank(param.getName())
            || StringUtils.isBlank(param.getChargeAccount())
            || Objects.isNull(param.getGoodNum())
            || StringUtils.isBlank(param.getApertureCode())
            || StringUtils.isBlank(param.getSizeCode())
            || param.getGoodNum().doubleValue() < 0) {
            throw new BMSException(ExceptionEnum.REQUIRED_ERROR);
        }
        // 物资单位
        param.setUnitName(dicService.transform(UNIT, param.getUnitCode()));
        // 筛板孔径
        param.setApertureName(dicService.transform(GOODS_SIEVE_PLATE_APERTURE, param.getApertureCode()));
        // 筛板尺寸
        param.setSizeName(dicService.transform(GOODS_SIEVE_PLATE_SIZE, param.getSizeCode()));
        // 负责人名称
        param.setChargeName(accountService.getNameByUserName(param.getChargeAccount()));
        MesSwGoodsStock entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MesSwGoodsStockParam param) {
        if (Objects.isNull(param.getId())) {
            throw new BMSException("error", "id不能为空");
        }
        LambdaQueryWrapper<MesSwEquipmentSievePlateRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwEquipmentSievePlateRecord::getMaterialCode, param.getId());
        List<MesSwEquipmentSievePlateRecord> records = sievePlateRecordService.list(wrapper);
        // 物资单位
        param.setUnitName(dicService.transform(UNIT, param.getUnitCode()));
        // 同步修改同步修改筛板更换表中的孔径物资类别
        if (param.getCategoryCode() != null) {
            records.forEach(record -> {
                record.setCategoryCode(param.getCategoryCode());
            });
            sievePlateRecordService.updateBatchById(records);
        }
        // 筛板孔径
        if (StringUtils.isNotBlank(param.getApertureCode())) {
            param.setApertureName(dicService.transform(GOODS_SIEVE_PLATE_APERTURE, param.getApertureCode()));
            //同步修改筛板更换表中的孔径code
            if (param.getApertureCode() != null) {
                records.forEach(record -> {
                    record.setApertureCode(param.getApertureCode());
                });
                sievePlateRecordService.updateBatchById(records);
            }
        }
        // 筛板尺寸
        if (StringUtils.isNotBlank(param.getSizeCode())) {
            param.setSizeName(dicService.transform(GOODS_SIEVE_PLATE_SIZE, param.getSizeCode()));
        }
        // 负责人名称
        param.setChargeName(accountService.getNameByUserName(param.getChargeAccount()));
        MesSwGoodsStock entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MesSwGoodsStockDTO getCurrentDetail(MesSwGoodsStockParam param) {
        MesSwGoodsStock entity = super.getById(param.getId());
        goodsDataHandle(entity);
        return convert.toDTO(entity);
    }

    @Override
    public List<MesSwGoodsStockDTO> getList(MesSwGoodsStockParam param) {
        LambdaQueryChainWrapper<MesSwGoodsStock> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        if (Objects.nonNull(param.getCategoryCode())) {
            // 查询分类id及子级分类id
            List<Long> subCateIdList = categoryService.getSubCateIdList(param.getCategoryCode());
            queryChain.in(MesSwGoodsStock::getCategoryCode, subCateIdList);
        }
        queryChain.orderByDesc(MesSwGoodsStock::getCreatedTime);

        List<MesSwGoodsStock> list = queryChain.list();
        list.forEach(this::goodsDataHandle);

        return convert.toDTOS(list);
    }

    @Override
    public List<MesSwGoodsStock> getSievePlateUseGoodsList() {
        return baseMapper.getSievePlateUseGoodsList();
    }

    @Override
    public MesSwGoodsSievePlateVO getGoodsSievePlateById(MesSwGoodsStockParam param) {
        MesSwGoodsSievePlateVO result = new MesSwGoodsSievePlateVO();
        result.setGoodsId(param.getId());
        // 查询该物资被筛板使用列表
        LambdaQueryWrapper<MesSwEquipmentSievePlateRecord> spQuery = new LambdaQueryWrapper<>();
        spQuery
            .eq(MesSwEquipmentSievePlateRecord::getMaterialCode, param.getId())
            .isNull(MesSwEquipmentSievePlateRecord::getDay);
        List<MesSwEquipmentSievePlateRecord> sievePlateList = sievePlateRecordService.list(spQuery);
        // 筛上量
        int onSieveNum = sievePlateList.size();
        result.setOnSieveNum(onSieveNum);
        // 计划物资数量：筛上量的1/2后十位向上取整，如31取40，26.5取30
        double useCount = (double) onSieveNum / 2;
        result.setPlanNum(NumberUtil.roundUpToTenDigit(useCount));
        // 查询物资被那些设备使用
        List<String> eqptList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sievePlateList)) {
            Map<Long, Long> eqptGroupMap =
                sievePlateList.stream()
                    .filter(f -> Objects.nonNull(f.getEquipmentDataId()))
                    .collect(
                        Collectors.groupingBy(
                            MesSwEquipmentSievePlateRecord::getEquipmentDataId, Collectors.counting()));
            eqptGroupMap.forEach(
                (k, v) -> {
                    MesSwEquipmentData eqptData = equipmentDataService.getById(k);
                    if (Objects.nonNull(eqptData)) {
                        eqptList.add(eqptData.getNo());
                    }
                });
        }
        if (!CollectionUtils.isEmpty(eqptList)) {
            result.setEqptText(String.join(",", eqptList));
        }
        // 上月消耗量
        Integer monthCount =
            sievePlateRecordService
                .lambdaQuery()
                .eq(MesSwEquipmentSievePlateRecord::getMaterialCode, param.getId())
                .like(
                    MesSwEquipmentSievePlateRecord::getDate,
                    LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM")))
                .count();
        result.setMonthUseNum(monthCount);
        // 更换周期
        LambdaQueryWrapper<MesSwGoodsPlanDetail> detailQuery = new LambdaQueryWrapper<>();
        detailQuery.eq(MesSwGoodsPlanDetail::getGoodsId, param.getId());
        detailQuery.orderByDesc(MesSwGoodsPlanDetail::getCreatedTime);
        List<MesSwGoodsPlanDetail> planDetails = planDetailService.list(detailQuery);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(planDetails)) {
            MesSwGoodsPlanDetail detail = planDetails.get(0);
            // 上次此物资提交计划的时间与本次提报日期的时间差
            List<String> between =
                DateUtil.dateBetween(detail.getCreatedTime().toLocalDate(), LocalDate.now());
            result.setReplaceCycle(between.size());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        if (!CollectionUtils.isEmpty(param.getId())) {
            param
                .getId()
                .forEach(
                    goodsId -> {
                        Map<String, Object> rmMap = new HashMap<>();
                        rmMap.put("goods_id", goodsId);
                        // 删除操作记录信息
                        stockOperateService.removeByMap(rmMap);
                    });
        }
        return super.removeByIds(param.getId());
    }

    /**
     * 物资数据处理
     *
     * @param entity 物资
     */
    private void goodsDataHandle(MesSwGoodsStock entity) {
        if (Objects.nonNull(entity)) {
            String categoryName = "";
            // 物资分类展示数据处理
            MesSwGoodsCategory category = categoryService.getById(entity.getCategoryCode());
            categoryName = category.getName();
            if (category.getLevel() == 2) {
                MesSwGoodsCategory pCategory = categoryService.getById(category.getParentId());
                categoryName = pCategory.getName() + "-" + category.getName();
            }
            entity.setCategoryName(categoryName);
            // 查询该物资被筛板使用列表
            LambdaQueryWrapper<MesSwEquipmentSievePlateRecord> spQuery = new LambdaQueryWrapper<>();
            spQuery
                .eq(MesSwEquipmentSievePlateRecord::getMaterialCode, entity.getId())
                .isNull(MesSwEquipmentSievePlateRecord::getDay);
            List<MesSwEquipmentSievePlateRecord> sievePlateList = sievePlateRecordService.list(spQuery);
            // 筛上量
            int onSieveNum = sievePlateList.size();
            if (onSieveNum != 0) { // 存在筛上量，预警数量取筛上量的1/6，向上取整
                double ceil = Math.ceil((double) onSieveNum / 6);
                entity.setWarnNum(BigDecimal.valueOf(ceil));
            }
        }
    }
}
