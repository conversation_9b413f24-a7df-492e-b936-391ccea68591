package com.yhd.admin.bms.domain.dto.sw.eqpt;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwEquipmentSievePlateRecordDTO extends BaseDTO implements Serializable {
    /**
     * 筛板id
     */
    private String sievePlateId;

    /**
     * 设备资料id
     */
    private Long equipmentDataId;
    /**
     * 更换日期
     */
    private LocalDate date;
    /**
     * 报废时间
     */
    private LocalDateTime scrapTime;
//    /**
//     * 报废日期 = 更换日期+使用天数
//     */
//    private LocalDate scrapDate;
    /**
     * 是否为盲板
     */
    private String blindPlate;
    /**
     * 原材质及尺寸编码
     */
    private String originalMaterialCode;
    /**
     * 原材质及尺寸名称
     */
    private String originalMaterialName;
    /**
     * 材质及尺寸编码
     */
    private String materialCode;
    /**
     * 材质及尺寸名称
     */
    private String materialName;
    /**
     * 更换原因code
     */
    private String changeReasonCode;
    /**
     * 更换原因名称
     */
    private String changeReasonName;
    /**
     * 使用天数
     */
    private Integer day;
    /**
     * 正在使用天数
     */
    private Integer usingDay;
    /**
     * 更换人account
     */
    private String changerAccount;
    /**
     * 更换人名称
     */
    private String changerName;

    /**
     * 列号
     */
    private Integer column;
    /**
     * 行号
     */
    private Integer row;

    /**
     * 筛板名称
     */
    private String plateName;

    /**
     * 颜色
     */
    private String color;

    /**
     * 设备编码
     */
    private String equipmentNo;
    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 材质
     */
    private Long categoryCode;
    /**
     * 材质name
     */
    private String categoryName;
    /**
     * 孔径
     */
    private String apertureCode;

    /**
     * 行数据
     */
    private String rowNums;
    /**
     * 列号
     */
    private Integer colNum;
}
