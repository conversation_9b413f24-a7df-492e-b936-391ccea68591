package com.yhd.admin.bms.domain.dto.sw.exam;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 模拟试卷题目题型分组信息
 *
 * <AUTHOR>
 * @date 2024/3/5 10:28
 */
@Data
public class MesSwMockExamPaperQuestionUserAnswerTypeGroupDTO implements Serializable {
  private static final long serialVersionUID = 8404933825756875862L;

  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  private String questionType;
  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  private String questionTypeName;

  /** 模拟试卷题目列表 */
  private List<MesSwMockExamPaperQuestionUserAnswerDTO> paperQuestionList;
}
