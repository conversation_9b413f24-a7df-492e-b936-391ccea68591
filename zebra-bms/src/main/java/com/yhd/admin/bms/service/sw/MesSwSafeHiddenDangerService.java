package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeHiddenDangerDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeHiddenDanger;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeHiddenDangerParam;
import com.yhd.admin.common.domain.RespJson;

/** 安全隐患检查记录 */
public interface MesSwSafeHiddenDangerService extends IService<MesSwSafeHiddenDanger> {

  /**
   * 分页查询
   *
   * @param queryParam {@link MesSwSafeHiddenDangerParam}
   * @return IPage<MesSwSafeHiddenDangerDTO>
   */
  IPage<MesSwSafeHiddenDangerDTO> pagingQuery(MesSwSafeHiddenDangerParam queryParam);

  /**
   * 根据ID进行新增或者修改
   *
   * @param queryParam {@link MesSwSafeHiddenDangerParam}
   * @return true 成功，false 失败
   */
  Boolean add(MesSwSafeHiddenDangerParam queryParam);

  /**
   * 根据ID查询详情
   *
   * @param queryParam {@link MesSwSafeHiddenDangerParam}
   * @return {@link MesSwSafeHiddenDangerDTO}
   */
  MesSwSafeHiddenDangerDTO getCurrentDetail(MesSwSafeHiddenDangerParam queryParam);

  Boolean complete(MesSwSafeHiddenDangerParam param);

  RespJson<String> revokeProcess(MesSwSafeHiddenDangerParam param);

  String stopSafeHiddenDanger();
}
