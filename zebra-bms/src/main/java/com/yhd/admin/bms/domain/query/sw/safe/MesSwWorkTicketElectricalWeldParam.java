package com.yhd.admin.bms.domain.query.sw.safe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** 电气焊工作票 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwWorkTicketElectricalWeldParam extends QueryParam {
  /** 编制日期 */
  private String date;

  /** 编制部门 */
  private String dept;

  /** 编制人账号 */
  private String organizationCode;

  /** 编制人名称 */
  private String organizationName;

  /** 施焊地点 */
  private String workLocation;

  /** 作业内容 */
  private String workContent;

  /** 计划施工时间-开始 */
  private String sgStartTime;

  /** 计划施工时间-结束 */
  private String sgEndTime;

  /** 现场负责人账号 */
  private String xcCode;

  /** 现场负责人名称 */
  private String xcName;

  /** 安监人账号 */
  private String safeCode;

  /** 安监人名称 */
  private String safeName;

  /** 施焊人账号 */
  private String shCode;

  /** 施焊人名称 */
  private String shName;

  /** 喷洒水人账号 */
  private String pssCode;

  /** 喷洒水人名称 */
  private String pssName;

  /** 组织贯彻人账号 */
  private String zzgcCode;

  /** 组织贯彻人名称 */
  private String zzgcName;

  /** 焊后值班人账号 */
  private String hhzbCode;

  /** 焊后值班人名称 */
  private String hhzbName;

  /** 值班领导人账号 */
  private String zbCode;

  /** 值班领导人名称 */
  private String zbName;

  /** 工作票状态code：审批中 已完成审批 已完成作业 已作废 */
  private String statusCode;

  /** 工作票状态名称 */
  private String statusName;

  @TableField(exist = false)
  private List<MesSwWorkTicketElectricalWeldSignUserParam> branchList;

  @TableField(exist = false)
  private List<MesSwWorkTicketElectricalWeldSignUserParam> workList;

  private String startTime;
  private String endTime;
}
