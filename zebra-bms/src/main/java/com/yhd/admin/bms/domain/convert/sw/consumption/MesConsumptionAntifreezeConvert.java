package com.yhd.admin.bms.domain.convert.sw.consumption;

import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionAntifreezeDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionAntifreeze;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionAntifreezeParam;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesConsumptionAntifreezeConvert {

  MesConsumptionAntifreeze toEntity(MesConsumptionAntifreezeParam param);

  MesConsumptionAntifreezeDTO toDTO(MesConsumptionAntifreeze entity);
}
