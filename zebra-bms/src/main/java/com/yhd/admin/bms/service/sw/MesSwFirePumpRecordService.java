package com.yhd.admin.bms.service.sw;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.MesSwEarlyMeetingDTO;
import com.yhd.admin.bms.domain.dto.sw.MesSwFirePumpRecordDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwEarlyMeeting;
import com.yhd.admin.bms.domain.entity.sw.MesSwFirePumpRecord;
import com.yhd.admin.bms.domain.query.sw.MesSwEarlyMeetingParam;
import com.yhd.admin.bms.domain.query.sw.MesSwFirePumpRecordParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Date 2023/10/10 16:37
 **/
public interface MesSwFirePumpRecordService extends IService<MesSwFirePumpRecord> {

    IPage<MesSwFirePumpRecordDTO> pagingQuery(MesSwFirePumpRecordParam queryParam);

    MesSwFirePumpRecordDTO getCurrentDetail(MesSwFirePumpRecordParam queryParam);

    boolean add(MesSwFirePumpRecordParam queryParam);

    boolean modify(MesSwFirePumpRecordParam queryParam);

    Boolean removeBatch(BatchParam queryParam);
}
