package com.yhd.admin.bms.domain.vo.sw.safe;

import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainTeacherEvaluateDetail;
import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 安全培训评价
 *
 * <AUTHOR>
 * @date 2024/3/14 11:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwSafeTrainEvaluateVO extends BaseVO implements Serializable {
  private static final long serialVersionUID = 3918297017908432212L;

  /** 培训计划主键id */
  private Long planId;
  /** 学员账号 */
  private String student;
  /** 学员姓名 */
  private String studentName;
  /** 培训内容 */
  private String trainContent;
  /** 培训综合评分 */
  private String pxzhPf;

  /** 培训教师评价详情 */
  private List<MesSwSafeTrainTeacherEvaluateDetail> teacherEvaluateDetail;
}
