package com.yhd.admin.bms.domain.query.sw.exam;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户考试试卷题目信息
 *
 * <AUTHOR>
 * @date 2023/10/10 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwExamPaperQuestionParam extends QueryParam implements Serializable {
  private static final long serialVersionUID = 7906102024019954165L;
  private Long id;

  /** 考试计划表主键id */
  private Long examPlanId;
  /** 计划考试时长 */
  private Long examTimeLength;
  /** 考试试卷标题 */
  private String paperTitle;
  /** 试卷序列号 */
  private String serialNum;
  /** 考试试卷分类 */
  private String paperClassify;
  /** 试卷题型：单选SINGLE_CHOICE、多选MULTI_CHOICE、判断JUDGE_CHOICE */
  private String questionType;
  /** 试题题目内容 */
  private String questionContent;
  /** 试题难度：1-低，2-中，3-高 */
  private String difficultyType;
  /** 试题标题 */
  private String questionTitle;
  /** 试题题目答案选项 */
  private String questionAnswerOption;
  /** 试题题目正确答案 */
  private String questionRightAnswer;
  /** 试题题目附件 */
  private String fileUrl;
  /** 附件类型 */
  private String fileType;
  /** 出题人账号 */
  private String questionSetter;
  /** 出题人姓名 */
  private String questionSetterName;
  /** 相关培训内容 */
  private String trainingContent;
  /** 试题题目分数 */
  private Integer questionScore;
  /** 答题正确人数 */
  private String rightCount;
  /** 已完成答题人数 */
  private Integer finishCount;
}
