package com.yhd.admin.scada.domain.dto;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/25 16:17
 */

@lombok.Data
@lombok.ToString
public class PLCFilterDTO implements java.io.Serializable, Cloneable {
    private static final long serialVersionUID = -8571018850789477052L;
    /**
     * 主键
     */
    private Long id;
    /**
     * PLC主键
     */
    private String plcId;
    /**
     * 最小
     */
    private Double rangeMin;
    /**
     * 最大
     */
    private Double rangeMax;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private java.time.LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private java.time.LocalDateTime updatedTime;


}
