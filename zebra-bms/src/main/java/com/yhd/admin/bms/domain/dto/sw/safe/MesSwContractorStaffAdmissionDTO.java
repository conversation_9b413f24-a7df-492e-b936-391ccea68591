package com.yhd.admin.bms.domain.dto.sw.safe;

import com.yhd.admin.bms.domain.dto.sys.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 承包商员工入场管理
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwContractorStaffAdmissionDTO extends BaseDTO implements Cloneable, Serializable {
  /** 承包商id */
  private Long contractorId;
  /** 承包商名称 */
  private String contractorName;
  /** 承包商负责人 */
  private String chargeAccount;
  /** 承包商负责人名称 */
  private String chargeName;
  /** 发起时间 */
  private String admissionTime;
  /** 入场人数 */
  private Integer admissionQuantity;
  /** 流程状态code */
  private String statusCode;
  /** 流程状态name */
  private String statusName;
  /** 流程实例ID */
  private String processInstanceId;
  /** 任务ID */
  private String taskId;
  /** 当前节点的下一节点处理部门 */
  private List<String> nextDepartmentList;
  /** 签字内容的集合 */
  private List<MesSwContractorStaffAdmissionSignatureDTO> signatureList;
}
