package com.yhd.admin.bms.service.sw.impl.exam;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.exam.MesSwExamPlanDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafetyTrainingPlanConvert;
import com.yhd.admin.bms.domain.convert.sw.exam.MesSwExamPlanConvert;
import com.yhd.admin.bms.domain.convert.sys.UserAccountConvert;
import com.yhd.admin.bms.domain.dto.sw.MesCcmBbiDTO;
import com.yhd.admin.bms.domain.dto.sw.MesOrgDTO;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafeExamPaperDTO;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafetyTrainingPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPlanDTO;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafeExamPaper;
import com.yhd.admin.bms.domain.entity.sw.MesSwSafetyTrainingPlan;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwExamPlan;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwUserExam;
import com.yhd.admin.bms.domain.entity.sys.SysOrg;
import com.yhd.admin.bms.domain.entity.sys.SysUserAccount;
import com.yhd.admin.bms.domain.enums.BMSRedisKeyEnum;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.enums.safe.ExamStatusEnum;
import com.yhd.admin.bms.domain.query.sw.MesSwSafeExamPaperParam;
import com.yhd.admin.bms.domain.query.sw.MesSwSafetyTrainingPlanParam;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwExamPlanParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.exam.MesInAndOutDeptVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.MesCcmBbiService;
import com.yhd.admin.bms.service.sw.MesSwSafeExamPaperService;
import com.yhd.admin.bms.service.sw.MesSwSafetyTrainingPlanService;
import com.yhd.admin.bms.service.sw.exam.MesSwExamPlanService;
import com.yhd.admin.bms.service.sw.exam.MesSwUserExamService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.OrgService;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.common.domain.dto.UserAccountDTO;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 安全考试计划表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-01
 */
@Service
public class MesSwExamPlanServiceImpl extends ServiceImpl<MesSwExamPlanDao, MesSwExamPlan>
    implements MesSwExamPlanService {
    private static final int PLAN_STATUS = 1;

    @Resource
    private MesSwExamPlanConvert convert;

    @Resource
    private MesSwSafetyTrainingPlanService swSafetyTrainingPlanService;

    @Resource
    private MesSwSafetyTrainingPlanConvert swSafetyTrainingPlanConvert;

    @Resource
    private OrgService orgService;

    @Resource
    private MesCcmBbiService ccmBbiService;

    @Resource
    private DicService dicService;

    @Resource
    private UserAccountService userAccountService;

    @Resource
    private UserAccountConvert userAccountConvert;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private MesSwUserExamService examService;

    @Resource
    private MesSwSafeExamPaperService  paperService;

    @Override
    public IPage<MesSwExamPlanDTO> pagingQuery(MesSwExamPlanParam queryParam) {
        UserDTO userInfo = UserContextHolder.getUserInfo();
        if (Objects.isNull(userInfo) || StringUtils.isEmpty(userInfo.getAccountName())) {
            throw new BMSException(ExceptionEnum.USER_NOT_LOGIN);
        }
        Page<MesSwExamPlan> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwExamPlan> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        // 06.30修改，查询条件增加培训内容
        if (!Objects.isNull(queryParam.getTrainingContent())) {
            queryChain.eq(MesSwExamPlan::getTrainingContent, queryParam.getTrainingContent());
        }
        // 考试日期区间
        if (Objects.nonNull(queryParam.getStartDate()) && Objects.nonNull(queryParam.getEndDate()))  {
            queryChain.between(MesSwExamPlan::getExamStartDate, queryParam.getStartDate(), queryParam.getEndDate())
                .or().between(MesSwExamPlan::getExamEndDate, queryParam.getStartDate(), queryParam.getEndDate());
        }
        if (!StringUtils.isEmpty(queryParam.getExamTitle())) {
            queryChain.like(MesSwExamPlan::getExamTitle, queryParam.getExamTitle());
        }
        if (!StringUtils.isEmpty(queryParam.getExamType())) {
            queryChain.like(MesSwExamPlan::getExamType, queryParam.getExamType());
        }
        if (!StringUtils.isEmpty(queryParam.getAuthor())) {
            queryChain.like(MesSwExamPlan::getAuthor, queryParam.getAuthor());
        }
        if (!StringUtils.isEmpty(queryParam.getPlanStatus())) {
            queryChain.eq(MesSwExamPlan::getPlanStatus, queryParam.getPlanStatus());
        }
        if (!StringUtils.isEmpty(queryParam.getPlanStatusName())) {
            queryChain.eq(MesSwExamPlan::getPlanStatusName, queryParam.getPlanStatusName());
        }
//        if (!StringUtils.isEmpty(queryParam.getExamDate())) {
//            queryChain.eq(MesSwExamPlan::getExamDate, queryParam.getExamDate());
//        }
        queryChain.orderByDesc(MesSwExamPlan::getCreatedTime);
        // 安监员有抽考权限
        List<UserAccountDTO> ajyList = userAccountService.getAJYList();
        List<String> ajyUserList =
            ajyList.stream().map(UserAccountDTO::getUsername).collect(Collectors.toList());

        IPage<MesSwExamPlan> iPage = queryChain.page(page);
        iPage
            .getRecords()
            .forEach(
                v -> {
                    // 查询计划考试考生是否全部完成，全部完成才可以进行抽考
                    List<MesSwUserExam> userExamList = examService.queryListByUserPlanId(v.getId());
                    List<MesSwUserExam> finishExamList =
                        userExamList.stream()
                            .filter(
                                exam -> ExamStatusEnum.FINISHED.getCode().equals(exam.getExamStatus()))
                            .collect(Collectors.toList());
                    v.setExtractShow(
                        ajyUserList.contains(userInfo.getAccountName())
                            && !v.getExtract()
                            && (!CollectionUtil.isEmpty(userExamList)
                            && userExamList.size() == finishExamList.size()));
                });

        return iPage.convert(convert::toDTO);
    }

    @Override
    public Boolean add(MesSwExamPlanParam param) {
        String jsonString = JSON.toJSONString(param.getDepartmentIdList());
        String jsonStringName = JSON.toJSONString(param.getDepartmentNameList());
        param.setDepartmentId(jsonString);
        param.setDepartmentName(jsonStringName);
        // 06.30版本修改
        String account = JSON.toJSONString(param.getExamStudentAccountList());
        param.setExamStudentAccount(account);
        String name = JSON.toJSONString(param.getExamStudentNameList());
        param.setExamStudentName(name);
        // 已制定状态
        param.setPlanStatusName(dicService.transform("EXAM_PLAN", "0"));
        param.setPlanStatus("0");
        MesSwExamPlan entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MesSwExamPlanParam param) {
        String jsonString = JSON.toJSONString(param.getDepartmentIdList());
        String jsonStringName = JSON.toJSONString(param.getDepartmentNameList());
        param.setDepartmentName(jsonStringName);
        param.setDepartmentId(jsonString);
        // 06.30版本修改
        String account = JSON.toJSONString(param.getExamStudentAccountList());
        param.setExamStudentAccount(account);
        String name = JSON.toJSONString(param.getExamStudentNameList());
        param.setExamStudentName(name);
        // 已制定状态
        param.setPlanStatusName(dicService.transform("EXAM_PLAN", "0"));
        param.setPlanStatus("0");
        MesSwExamPlan entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MesSwExamPlanDTO getCurrentDetail(MesSwExamPlanParam param) {
        MesSwExamPlan byId = super.getById(param.getId());
        byId.setDepartmentIdList(JSON.parseArray(byId.getDepartmentId(), String.class));
        byId.setDepartmentNameList(JSON.parseArray(byId.getDepartmentName(), String.class));
        // 06.30版本修改
        byId.setExamStudentAccountList(JSON.parseArray(byId.getExamStudentAccount(), String.class));
        byId.setExamStudentNameList(JSON.parseArray(byId.getExamStudentName(), String.class));
        MesSwExamPlanDTO dto = convert.toDTO(byId);
        //获取总分
        MesSwSafeExamPaperParam paperParam = new MesSwSafeExamPaperParam();
        paperParam.setId(byId.getExamPaperId());
        MesSwSafeExamPaperDTO paperDTO = paperService.getCurrentDetail(paperParam);
        if (Objects.nonNull(paperDTO)) {
            dto.setPaperScore(paperDTO.getPaperScore());
        }
        return dto;
    }

    /**
     * @return @Description:查询安全培训计划
     */
    @Override
    public List<MesSwSafetyTrainingPlanDTO> getBySafePlanList() {
        LambdaQueryWrapper<MesSwSafetyTrainingPlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 安全审批通过状态
        lambdaQueryWrapper.eq(MesSwSafetyTrainingPlan::getSignOutStatus, PLAN_STATUS);
        List<MesSwSafetyTrainingPlan> list = swSafetyTrainingPlanService.list(lambdaQueryWrapper);
        return swSafetyTrainingPlanConvert.toDTOList(list);
    }

    @Override
    public MesSwSafetyTrainingPlanDTO getByExamieesList(
        MesSwSafetyTrainingPlanParam mesSwSafetyTrainingPlanParam) {
        Assert.hasText(mesSwSafetyTrainingPlanParam.getTrainingContent(), "安全培训计划不能为空");
        LambdaQueryWrapper<MesSwSafetyTrainingPlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        MesSwSafetyTrainingPlan list = null;
        // 培训内容不为空查询安全培训计划的考试来源(部门)
        if (!StringUtils.isEmpty(mesSwSafetyTrainingPlanParam.getTrainingContent())) {
            lambdaQueryWrapper.eq(
                MesSwSafetyTrainingPlan::getTrainingContent,
                mesSwSafetyTrainingPlanParam.getTrainingContent());
            list = swSafetyTrainingPlanService.getOne(lambdaQueryWrapper);
        }
        MesSwSafetyTrainingPlanDTO dtoList = swSafetyTrainingPlanConvert.toDTO(list);
        dtoList.setDepartmentList(JSON.parseArray(dtoList.getDepartment(), MesOrgDTO.class));
//    dtoList.setDepartmentIdList(JSON.parseArray(dtoList.getDepartmentId(), String.class));
        return dtoList;
    }

    @Override
    public List<MesInAndOutDeptVO> getByInAndOutDeptList() {
        List<MesInAndOutDeptVO> voArrayList = Lists.newArrayList();
        // 查询厂内部（部门）
        LambdaQueryWrapper<SysOrg> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orgLambdaQueryWrapper.eq(SysOrg::getStatus, Boolean.TRUE);
        orgLambdaQueryWrapper.ne(SysOrg::getParentId, 0);
        List<SysOrg> list = orgService.list(orgLambdaQueryWrapper);
        MesInAndOutDeptVO mesInAndOutDeptVO = null;
        for (SysOrg sysOrg : list) {
            mesInAndOutDeptVO = new MesInAndOutDeptVO();
            mesInAndOutDeptVO.setDeptId(sysOrg.getId());
            mesInAndOutDeptVO.setDepartment(sysOrg.getOrgName());
            voArrayList.add(mesInAndOutDeptVO);
        }

        // 承包商
        List<MesCcmBbiDTO> listCompanyNames = ccmBbiService.getListCompanyNames();
        for (MesCcmBbiDTO listCompanyName : listCompanyNames) {
            mesInAndOutDeptVO = new MesInAndOutDeptVO();
            mesInAndOutDeptVO.setDeptId(listCompanyName.getId());
            mesInAndOutDeptVO.setDepartment(listCompanyName.getCompanyName());
            voArrayList.add(mesInAndOutDeptVO);
        }

        return voArrayList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishExam(MesSwExamPlanParam param) {
        param.setPlanStatusName(dicService.transform("EXAM_PLAN", "1"));
        param.setPlanStatus("1");
        // 校验参数必填
        if (param.getId() == null) {
            throw new BMSException("error", "主键不能为空");
        }
        MesSwExamPlanDTO mesSwExamPlanDTO = this.getCurrentDetail(param);
        // 06.30修改为根据用户账号查询
        LambdaQueryChainWrapper<SysUserAccount> lambdaQueryWrapper =
            userAccountService
                .lambdaQuery()
                .eq(SysUserAccount::getIsEnable, true)
                .in(SysUserAccount::getUsername, mesSwExamPlanDTO.getExamStudentAccountList());
//        // 根据部门集合查询
//        LambdaQueryChainWrapper<SysUserAccount> lambdaQueryWrapper =
//            userAccountService
//                .lambdaQuery()
//                .eq(SysUserAccount::getIsEnable, true)
//                .in(SysUserAccount::getDepartmentId, mesSwExamPlanDTO.getDepartmentIdList());
        // 存缓存
        HashOperations<String, String, List<UserAccountDTO>> hashOperations =
            redisTemplate.opsForHash();
        hashOperations.put(
            BMSRedisKeyEnum.PLAN_EXAM_USER.getKey(),
            mesSwExamPlanDTO.getId().toString(),
            lambdaQueryWrapper.list().stream()
                .map(userAccountConvert::toDTO)
                .collect(Collectors.toList()));
        super.updateById(convert.toEntity(param));
        return examService.initUserExam(param);
    }

    private void checkParamster(MesSwExamPlanParam param) {
        Assert.hasText(param.getExamTitle(), "试卷标题不能为空");
        Assert.hasText(param.getExamType(), "试卷分类不能为空");
        Assert.hasText(param.getTrainingContent(), "安全培训计划不能为空");
        Assert.hasText(param.getDepartmentId(), "部门ID不能为空");
//        if (StringUtils.isEmpty(param.getExamDate())) {
//            throw new BMSException("error", "考试日期不能空");
//        }
        if (StringUtils.isEmpty(param.getExamDuration())) {
            throw new BMSException("error", "考试时长不能空");
        }
        if (StringUtils.isEmpty(param.getQuestionDisorder())) {
            throw new BMSException("error", "题目乱序不能为空空");
        }
        if (StringUtils.isEmpty(param.getAnswerDisorder())) {
            throw new BMSException("error", "答案乱序不能为空空");
        }
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }
}
