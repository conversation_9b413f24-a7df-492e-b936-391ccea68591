package com.yhd.admin.bms.service.sw.impl.goods;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.goods.MesSwGoodsPlanDetailDao;
import com.yhd.admin.bms.domain.convert.sw.goods.MesSwGoodsPlanDetailConvert;
import com.yhd.admin.bms.domain.dto.sw.goods.MesSwGoodsPlanDetailDTO;
import com.yhd.admin.bms.domain.entity.sw.goods.MesSwGoodsPlanDetail;
import com.yhd.admin.bms.domain.query.sw.goods.MesSwGoodsPlanDetailParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.service.sw.goods.MesSwGoodsPlanDetailService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 筛板物资计划详情-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/26 18:42
 */
@Service
public class MesSwGoodsPlanDetailServiceImpl
    extends ServiceImpl<MesSwGoodsPlanDetailDao, MesSwGoodsPlanDetail>
    implements MesSwGoodsPlanDetailService {

  private final MesSwGoodsPlanDetailConvert planDetailConvert;

  public MesSwGoodsPlanDetailServiceImpl(MesSwGoodsPlanDetailConvert planDetailConvert) {
    this.planDetailConvert = planDetailConvert;
  }

  @Override
  public IPage<MesSwGoodsPlanDetailDTO> pagingQuery(MesSwGoodsPlanDetailParam param) {
    Page<MesSwGoodsPlanDetail> iPage = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MesSwGoodsPlanDetail> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 计划id
    queryChain.eq(
        Objects.nonNull(param.getPlanId()), MesSwGoodsPlanDetail::getPlanId, param.getPlanId());

    // 排序
    queryChain.orderByAsc(MesSwGoodsPlanDetail::getId);

    IPage<MesSwGoodsPlanDetail> page = queryChain.page(iPage);

    return page.convert(planDetailConvert::toDTO);
  }

  @Override
  public MesSwGoodsPlanDetailDTO getCurrentDetail(MesSwGoodsPlanDetailParam param) {
    return planDetailConvert.toDTO(super.getById(param.getId()));
  }

  @Override
  public List<MesSwGoodsPlanDetail> queryList(MesSwGoodsPlanDetailParam param) {
    LambdaQueryWrapper<MesSwGoodsPlanDetail> wrapper = new LambdaQueryWrapper<>();
    // 计划id
    wrapper.eq(
        Objects.nonNull(param.getPlanId()), MesSwGoodsPlanDetail::getPlanId, param.getPlanId());

    wrapper.orderByAsc(MesSwGoodsPlanDetail::getId);

    return baseMapper.selectList(wrapper);
  }

  @Override
  public Boolean add(MesSwGoodsPlanDetailParam param) {
    return super.save(planDetailConvert.toEntity(param));
  }

  @Override
  public Boolean modify(MesSwGoodsPlanDetailParam param) {
    return super.updateById(planDetailConvert.toEntity(param));
  }

  @Override
  public Boolean removeBatch(BatchParam param) {
    return super.removeByIds(param.getId());
  }
}
