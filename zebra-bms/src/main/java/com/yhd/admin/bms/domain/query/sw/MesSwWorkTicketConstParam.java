package com.yhd.admin.bms.domain.query.sw;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/29 9:02
 * @Version 1.0
 *
 * 施工工作票--Param
 */

@EqualsAndHashCode(callSuper = false)
@Data
public class MesSwWorkTicketConstParam extends QueryParam{
    /**
     * 施工地点
     */
    private String sgLocation;
    /**
     * 作业内容
     */
    private String sgContent;
    /**
     * 计划作业开始时间
     */
    private LocalDateTime sgStartTime;
    /**
     * 计划作业结束时间
     */
    private LocalDateTime sgEndTime;
    /**
     * 工作票提交人code
     */
    private String tjUserCode;
    /**
     * 工作票提交人name
     */
    private String tjUserName;
    /**
     * 施工总负责人code
     */
    private String dwUserCode;
    /**
     * 施工总负责人name
     */
    private String dwUserName;
    /**
     * 施工技术负责人code
     */
    private String jsUserCode;
    /**
     * 施工技术负责人name
     */
    private String jsUserName;
    /**
     * 施工安全负责人code
     */
    private String aqUserCode;
    /**
     * 施工安全负责人name
     */
    private String aqUserName;
    /**
     * 现场施工总负责人code
     */
    private String xcsgUserCode;
    /**
     * 现场施工总负责人name
     */
    private String xcsgUserName;
    /**
     * 现场施工技术负责人code
     */
    private String xcjsUserCode;
    /**
     * 现场施工技术负责人name
     */
    private String xcjsUserName;
    /**
     * 现场施工安全负责人code
     */
    private String xcaqUserCode;
    /**
     * 现场施工安全负责人name
     */
    private String xcaqUserName;
    /**
     * 工作票状态code
     */
    private String statusCode;
    /**
     * 工作票状态name
     */
    private String statusName;

    /**
     * 列表页查询计划作业开始时间
     */
    private LocalDateTime querySgStartTime;
    /**
     * 列表页查询计划作业结束时间
     */
    private LocalDateTime querySgEndTime;
    /** 工作票审批用户列表 */
    private List<MesSwWorkTicketConstSignUserParam> userSpTypeList;
    /** 工作票贯彻签字用户列表 */
    private List<MesSwWorkTicketConstSignUserParam> userGcTypeList;
    /** 工作票其他人员用户列表 */
    private List<MesSwWorkTicketConstSignUserParam> userQtTypeList;


}
