package com.yhd.admin.bms.service.sw.impl.consumption;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.consumption.MesConsumptionWaterDao;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionWaterDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionCoal;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionWater;
import com.yhd.admin.bms.domain.enums.produce.KHConsumptionPointEnum;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionWaterParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionCoalService;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionWaterService;
import com.yhd.admin.bms.service.sw.dashboard.KHPointService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 能耗管理-水耗
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
@Service
@Slf4j
public class MesConsumptionWaterImpl
    extends ServiceImpl<MesConsumptionWaterDao, MesConsumptionWater>
    implements MesConsumptionWaterService {

  @Resource private MesConsumptionCoalService consumptionCoalService;
  @Resource private KHPointService khPointService;

  @Override
  public List<MesConsumptionWaterDTO> queryList(MesConsumptionWaterParam param) {
    String year = param.getYear();
    if (StringUtils.isBlank(year)) {
      year = String.valueOf(LocalDate.now().getYear());
    }
    List<MesConsumptionWaterDTO> result = Lists.newArrayList();
    // 水耗数据
    LambdaQueryChainWrapper<MesConsumptionWater> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.like(MesConsumptionWater::getDate, year);
    List<MesConsumptionWater> waterList = wrapper.list();
    // 表里没有当天数据，需要去查询今天的数据
    MesConsumptionWater waterToday = queryTodayData();
    if (waterToday.getWater1() != null
        || waterToday.getWater2() != null
        || waterToday.getWater3() != null) {
      // 只要有一项不为null，即为有效数据
      waterList.add(waterToday);
    }
    if (CollectionUtil.isEmpty(waterList)) {
      return result;
    }
    // 分组累加
    Map<String, MesConsumptionWater> waterMap =
        waterList.stream()
            .collect(
                Collectors.groupingBy(
                    bean ->
                        bean.getDate().getYear()
                            + "-"
                            + String.format("%02d", bean.getDate().getMonthValue()),
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                          BigDecimal water1 =
                              list.stream()
                                  .map(MesConsumptionWater::getWater1)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          BigDecimal water2 =
                              list.stream()
                                  .map(MesConsumptionWater::getWater2)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          BigDecimal water3 =
                              list.stream()
                                  .map(MesConsumptionWater::getWater3)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          MesConsumptionWater sums = new MesConsumptionWater();
                          sums.setWater1(water1);
                          sums.setWater2(water2);
                          sums.setWater3(water3);
                          return sums;
                        })));
    // 煤产量数据
    LambdaQueryWrapper<MesConsumptionCoal> coalWrapper = new LambdaQueryWrapper<>();
    coalWrapper.like(MesConsumptionCoal::getDate, year);
    List<MesConsumptionCoal> coalList = consumptionCoalService.list(coalWrapper);
    // 补查当天数据，表里没有当天数据
    MesConsumptionCoal coalPO = queryTodayCoal();
    if (coalPO.getRawCon201() != null || coalPO.getRawCon208() != null) {
      // 只要有一项不为null，即为有效数据
      coalList.add(coalPO);
    }

    Map<String, MesConsumptionCoal> coalMap = new HashMap<>();
    // 分组累加
    if (CollectionUtil.isNotEmpty(coalList)) {
      coalMap =
          coalList.stream()
              .collect(
                  Collectors.groupingBy(
                      bean ->
                          bean.getDate().getYear()
                              + "-"
                              + String.format("%02d", bean.getDate().getMonthValue()),
                      Collectors.collectingAndThen(
                          Collectors.toList(),
                          list -> {
                            BigDecimal rawCon201 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon201)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal rawCon208 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon208)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            MesConsumptionCoal sums = new MesConsumptionCoal();
                            sums.setRawCon201(rawCon201);
                            sums.setRawCon208(rawCon208);
                            return sums;
                          })));
    }

    for (Map.Entry<String, MesConsumptionWater> entry : waterMap.entrySet()) {
      String month = entry.getKey();
      MesConsumptionWater total = entry.getValue();
      // 生成返回数据，水耗
      MesConsumptionWaterDTO item = new MesConsumptionWaterDTO();
      item.setMonth(month);
      BigDecimal water1 = total.getWater1();
      item.setWater1(water1);
      BigDecimal water2 = total.getWater2();
      item.setWater2(water2);
      BigDecimal water3 = total.getWater3();
      item.setWater3(water3);
      // 总流量=水表1+水表2+水表3
      BigDecimal totalWater = addBigDecimal(water1, water2, water3);
      item.setTotalWater(totalWater);
      // 生成返回数据，原煤产量=201胶带机+218胶带机
      MesConsumptionCoal coalTotal = coalMap.get(month);
      if (coalTotal != null) {
        BigDecimal rowCoal = addBigDecimal(coalTotal.getRawCon201(), coalTotal.getRawCon208());
        item.setRowCoal(rowCoal);
        if (totalWater != null && rowCoal != null && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 吨煤水耗=总水耗/原煤产量（四舍五入保留两位小数）
          BigDecimal waterConsumptionDm =
              NumberUtil.div(totalWater, rowCoal, 2, RoundingMode.HALF_UP);
          item.setWaterConsumptionDm(waterConsumptionDm);
        }
      }
      result.add(item);
    }
    result.sort((b1, b2) -> b2.getMonth().compareTo(b1.getMonth()));
    return result;
  }

  /**
   * 查询当天的煤产量
   *
   * @return
   */
  private MesConsumptionCoal queryTodayCoal() {
    // 获取煤产量对应的点位
    List<String> pointCoalList = new ArrayList<>();
    pointCoalList.add(KHConsumptionPointEnum.XMSW_Raw_CON201_Info_ProductToday.getKey());
    pointCoalList.add(KHConsumptionPointEnum.XMSW_Raw_CON218_Info_ProductToday.getKey());
    Map<String, BigDecimal> coalDataMap =
        khPointService.getCoalProductionByParams(LocalDate.now().toString(), pointCoalList);
    // 煤产量，生成对应的数据
    // 能耗管理-煤产量
    MesConsumptionCoal coalPO = new MesConsumptionCoal();
    coalPO.setDate(LocalDate.now());
    if (CollUtil.isNotEmpty(coalDataMap)) {
      coalDataMap.forEach(
          (key, value) -> {
            if (KHConsumptionPointEnum.XMSW_Raw_CON201_Info_ProductToday.getKey().equals(key)) {
              coalPO.setRawCon201(value);
            } else if (KHConsumptionPointEnum.XMSW_Raw_CON218_Info_ProductToday.getKey()
                .equals(key)) {
              coalPO.setRawCon208(value);
            }
          });
    }
    return coalPO;
  }

  /**
   * 查询水耗当天的消耗量
   *
   * @return
   */
  private MesConsumptionWater queryTodayData() {
    // 补查当天数据，表里没有当天数据
    // 水耗点位
    List<String> pointKeyList = new ArrayList<>();
    pointKeyList.add(KHConsumptionPointEnum.XMSW_Lump_WTRmeter_WaterUsageQSBF1.getKey());
    pointKeyList.add(KHConsumptionPointEnum.XMSW_Lump_WTRmeter_WaterUsageQSBF2.getKey());
    pointKeyList.add(KHConsumptionPointEnum.XMSW_Lump_WTRmeter_WaterUsageQSBF3.getKey());
    Map<String, BigDecimal> pointDataMap =
        khPointService.getConsumptionByParams(LocalDate.now().toString(), pointKeyList);
    // 能耗管理-水耗
    MesConsumptionWater waterPO = new MesConsumptionWater();
    waterPO.setDate(LocalDate.now());
    if (CollUtil.isNotEmpty(pointDataMap)) {
      pointDataMap.forEach(
          (key, value) -> {
            if (KHConsumptionPointEnum.XMSW_Lump_WTRmeter_WaterUsageQSBF1.getKey().equals(key)) {
              waterPO.setWater1(value);
            } else if (KHConsumptionPointEnum.XMSW_Lump_WTRmeter_WaterUsageQSBF2.getKey()
                .equals(key)) {
              waterPO.setWater2(value);
            } else if (KHConsumptionPointEnum.XMSW_Lump_WTRmeter_WaterUsageQSBF3.getKey()
                .equals(key)) {
              waterPO.setWater3(value);
            }
          });
    }
    return waterPO;
  }

  @Override
  public List<MesConsumptionWaterDTO> getCurrentDetail(MesConsumptionWaterParam param) {
    String month = param.getMonth();
    if (StringUtils.isBlank(month)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询参数为空，请检查！");
    }
    List<MesConsumptionWaterDTO> result = Lists.newArrayList();
    // 水耗数据
    LambdaQueryChainWrapper<MesConsumptionWater> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.like(MesConsumptionWater::getDate, month);
    List<MesConsumptionWater> waterList = wrapper.list();
    // 表里没有当天数据，需要去查询今天的数据
    MesConsumptionWater waterToday = queryTodayData();
    if (waterToday.getWater1() != null
        || waterToday.getWater2() != null
        || waterToday.getWater3() != null) {
      // 只要有一项不为null，即为有效数据
      waterList.add(waterToday);
    }

    if (CollectionUtil.isEmpty(waterList)) {
      return result;
    }

    // 分组累加
    Map<LocalDate, List<MesConsumptionWater>> waterMap =
        CollStreamUtil.groupByKey(waterList, MesConsumptionWater::getDate);

    // 月合计
    BigDecimal monthWater1 = null;
    BigDecimal monthWater2 = null;
    BigDecimal monthWater3 = null;

    for (Map.Entry<LocalDate, List<MesConsumptionWater>> entry : waterMap.entrySet()) {
      LocalDate date = entry.getKey();
      List<MesConsumptionWater> dayConsumption = entry.getValue();
      // 生成返回数据，水耗
      MesConsumptionWaterDTO item = new MesConsumptionWaterDTO();
      item.setDate(date);
      item.setDateStr(date.toString());
      if (CollectionUtil.isNotEmpty(dayConsumption)) {
        MesConsumptionWater totalItem = dayConsumption.get(0);
        BigDecimal water1 = totalItem.getWater1();
        item.setWater1(water1);
        BigDecimal water2 = totalItem.getWater2();
        item.setWater2(water2);
        BigDecimal water3 = totalItem.getWater3();
        item.setWater3(water3);
        // 总流量=水表1+水表2+水表3
        BigDecimal totalWater = addBigDecimal(water1, water2, water3);
        item.setTotalWater(totalWater);

        // 月合计，累加水耗
        monthWater1 = addBigDecimal(monthWater1, water1);
        monthWater2 = addBigDecimal(monthWater2, water2);
        monthWater3 = addBigDecimal(monthWater3, water3);
      }
      result.add(item);
    }
    result.sort(Comparator.comparing(MesConsumptionWaterDTO::getDate));
    if (CollectionUtil.isNotEmpty(result)) {
      // 结果不为空，将合计也放入
      MesConsumptionWaterDTO monthTotal = new MesConsumptionWaterDTO();
      monthTotal.setDateStr("合计");
      monthTotal.setWater1(monthWater1);
      monthTotal.setWater2(monthWater2);
      monthTotal.setWater3(monthWater3);
      monthTotal.setTotalWater(addBigDecimal(monthWater1, monthWater2, monthWater3));
      result.add(monthTotal);
    }

    return result;
  }

  @Override
  public List<MesConsumptionWaterDTO> queryIntervalList(LocalDate start, LocalDate end) {
    List<MesConsumptionWaterDTO> result = Lists.newArrayList();
    // 水耗数据
    LambdaQueryChainWrapper<MesConsumptionWater> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.between(MesConsumptionWater::getDate, start, end);
    List<MesConsumptionWater> waterList = wrapper.list();
    // 核心逻辑：包含边界（>= start 且 <= end）
    LocalDate now = LocalDate.now();
    boolean isBetween = !now.isBefore(start) && !now.isAfter(end);
    if (isBetween) {
      // 表里没有当天数据，需要去查询今天的数据
      MesConsumptionWater waterToday = queryTodayData();
      if (waterToday.getWater1() != null
          || waterToday.getWater2() != null
          || waterToday.getWater3() != null) {
        // 只要有一项不为null，即为有效数据
        waterList.add(waterToday);
      }
    }
    if (CollectionUtil.isEmpty(waterList)) {
      return result;
    }

    // 分组累加
    Map<LocalDate, List<MesConsumptionWater>> waterMap =
        CollStreamUtil.groupByKey(waterList, MesConsumptionWater::getDate);
    for (Map.Entry<LocalDate, List<MesConsumptionWater>> entry : waterMap.entrySet()) {
      LocalDate date = entry.getKey();
      List<MesConsumptionWater> dayConsumption = entry.getValue();
      // 生成返回数据，水耗
      MesConsumptionWaterDTO item = new MesConsumptionWaterDTO();
      item.setDate(date);
      item.setDateStr(date.toString());
      if (CollectionUtil.isNotEmpty(dayConsumption)) {
        MesConsumptionWater totalItem = dayConsumption.get(0);
        BigDecimal water1 = totalItem.getWater1();
        item.setWater1(water1);
        BigDecimal water2 = totalItem.getWater2();
        item.setWater2(water2);
        BigDecimal water3 = totalItem.getWater3();
        item.setWater3(water3);
        // 总流量=水表1+水表2+水表3
        BigDecimal totalWater = addBigDecimal(water1, water2, water3);
        item.setTotalWater(totalWater);
      }
      result.add(item);
    }
    result.sort(Comparator.comparing(MesConsumptionWaterDTO::getDate));
    return result;
  }

  private static BigDecimal addBigDecimal(BigDecimal... values) {
    if (values != null && values.length > 0 && values[0] == null) {
      values[0] = BigDecimal.ZERO;
    }
    return NumberUtil.add(values);
  }
}
