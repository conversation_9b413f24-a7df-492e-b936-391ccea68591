package com.yhd.admin.bms.domain.vo.sw.exam;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 安全考试汇总  考试成绩单
 *
 * @since 1.0.0 2024-03-06
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesSwExamResultTranscriptVO implements Serializable {

    /** 部门ID */
    private Long departmentId;
    /** 部门名称 */
    private String department;
    /** 部门下人员成绩单 */
    private List<MesSwUserExamVO> resultList;
}
