package com.yhd.admin.bms.domain.query.sw;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.dto.sw.MesOrgDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSafetyTrainingExpense;
import com.yhd.admin.bms.domain.entity.sw.TrainingSchedule;
import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/4 13:33
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafetyTrainingPlanParam extends QueryParam implements Cloneable, Serializable {
    List<Long> ids;
    /**
     * 培训内容
     */
    private String trainingContent;
    /**
     * 培训形式
     */
    private String trainingForm;
    /**
     * 培训日期
     */
    private String trainingDate;

    private List<String> trainingDateList;
    /**
     * 培训时间
     */
    private String trainingSchedule;

    private List<TrainingSchedule> trainingSchedules;
    /**
     * 培训地点
     */
    private String trainingLocation;
    /**
     * 培训教师account
     */
    private String trainingTeacherAccount;

    private List<String> trainingTeacherAccountList;
    /**
     * 培训教师name
     */
    private String trainingTeacherName;

    private List<String> trainingTeacherNameList;
    /**
     * 培训类型code
     */
    private String trainingTypeCode;
    /**
     * 培训类型name
     */
    private String trainingTypeName;
    /**
     * 学员来源id
     */
    private String departmentId;

    private List<String> departmentIdList;
    /**
     * 学员来源
     */
    private String department;

    private List<MesOrgDTO> departmentList;
    /**
     * 学员人数
     */
    private String studentNumber;
    /**
     * 学员account列表
     */
    private String studentId;

    private List<String> studentIdList;
    /**
     * 学员name列表
     */
    private String student;

    private List<String> studentList;
    /**
     * 培训费用
     */
    private String trainingExpense;
    /**
     * 培训费用列表
     */
    private List<MesSafetyTrainingExpense> trainingExpensesList;
    /**
     * 课时
     */
    private Integer classHour;
    /**
     * 课时费
     */
    private Double classHourCost;
    /**
     * 教师授课费
     */
    private Double teacherCost;
    /**
     * 培训教材费
     */
    private Double trainingCost;
    /**
     * 交通费及其他
     */
    private Double trafficCost;
    /**
     * 合计
     */
    private Double amount;
    /**
     * 分管领导code
     */
    private String trainingLeaderNo;
    /**
     * 分管领导name
     */
    private String trainingLeaderName;
    /**
     * 主要负责人code
     */
    private String trainingHeadNo;
    /**
     * 主要负责人name
     */
    private String trainingHeadName;
    /**
     * 能否删除（0：签名未完成，可以删除 1:签名已完成，不可以删除）
     */
    private Integer signOutStatus;

    /**
     * 培训教材列表
     */
    private List<JSONObject> trainingTextbook;
    /**
     * 培训主管签字图片url
     */
    private String trainingManagerSignature;
    /**
     * 分管领导签字图片url
     */
    private String trainingLeaderSignature;
    /**
     * 主要负责人签字图片url
     */
    private String trainingHeadSignature;
}
