plugins {
    id 'java'
    id 'war'
}

group = 'com.yhd'
version = '0.0.1-SNAPSHOT'

dependencies {
    implementation project(':zebra-common')
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation rootProject.ext.dependencies['commons-lang3']
    implementation rootProject.ext.dependencies['commons-pool2']
    implementation rootProject.ext.dependencies['mybatis-plus-boot-starter']
    implementation rootProject.ext.dependencies['mapstruct']
    implementation rootProject.ext.dependencies['guava']
    implementation rootProject.ext.dependencies['cas-client']
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.cloud:spring-cloud-starter-oauth2'
    implementation 'org.springframework.cloud:spring-cloud-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    runtimeOnly 'mysql:mysql-connector-java'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor rootProject.ext.dependencies['mapstruct-processor']
    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.springframework.security:spring-security-test'
    testAnnotationProcessor rootProject.ext.dependencies['mapstruct-processor']
    testImplementation rootProject.ext.dependencies['junit-jupiter-api']
    testImplementation rootProject.ext.dependencies['junit-jupiter-params']
    testImplementation rootProject.ext.dependencies['junit-jupiter-engine']
    testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'

}


test {
    useJUnitPlatform()
}
