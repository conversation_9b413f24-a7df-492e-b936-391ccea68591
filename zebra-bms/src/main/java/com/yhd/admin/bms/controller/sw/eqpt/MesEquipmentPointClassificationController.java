package com.yhd.admin.bms.controller.sw.eqpt;

import com.yhd.admin.bms.domain.convert.sw.eqpt.MesEquipmentPointClassificationConvert;
import com.yhd.admin.bms.domain.query.sw.eqpt.MesEquipmentPointClassificationParam;
import com.yhd.admin.bms.service.sw.eqpt.MesEquipmentPointClassificationService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备健康中心-设备点位分类管理
 *
 * <AUTHOR>
 * @date 2025/4/23 10:22
 */
@RestController
@RequestMapping(value = "/equipment/pointClassification")
public class MesEquipmentPointClassificationController {

  @Resource private MesEquipmentPointClassificationService mesEquipmentPointClassificationService;
  @Resource private MesEquipmentPointClassificationConvert mesEquipmentPointClassificationConvert;



  @PostMapping(
      value = "/getTree",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getTree(
      @RequestBody MesEquipmentPointClassificationParam param) {
    try {
      return RespJson.buildSuccessResponse(mesEquipmentPointClassificationConvert.toVOS(mesEquipmentPointClassificationService.getTree(param)));
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }

    @PostMapping(
        value = "/getListOfProtect",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getListOfProtect(
        @RequestBody MesEquipmentPointClassificationParam param) {
        try {
            return RespJson.buildSuccessResponse(mesEquipmentPointClassificationConvert.toVOS(mesEquipmentPointClassificationService.getListOfProtect(param)));
        } catch (Exception e) {
            return RespJson.buildFailureResponse(e.getMessage());
        }
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesEquipmentPointClassificationParam param) {
        return RespJson.buildSuccessResponse(mesEquipmentPointClassificationConvert.toVO(mesEquipmentPointClassificationService.getCurrentDetail(param)));
    }

  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> add(@RequestBody MesEquipmentPointClassificationParam param) {
    try {
      Boolean retVal = mesEquipmentPointClassificationService.addOrModify(param);
      return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    } catch (Exception e) {
      return RespJson.buildFailureResponse(e.getMessage());
    }
  }
}
