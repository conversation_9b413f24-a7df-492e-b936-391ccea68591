package com.yhd.admin.bms.domain.entity.sw.ticket;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 吊装作业许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-28
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesSwWorkTicketLift extends BaseEntity implements Cloneable, Serializable {

	/**
	* 许可证编号
	*/
	private String ticketNo;

	/**
	* 申请单位
	*/
	private String applyUnit;

	/**
	* 编制日期
	*/
	private LocalDate orgDate;

	/**
	* 作业地点
	*/
	private String workLocation;

	/**
	* 作业内容
	*/
	private String workContent;

	/**
	* 许可证有效日期-开始日期
	*/
	private LocalDateTime ticketStartTime;

	/**
	* 许可证有效日期-结束日期
	*/
	private LocalDateTime ticketEndTime;

	/**
	* 起吊总重量
	*/
	private String ticketWeight;

	/**
	* 吊重作业类型
	*/
	private String ticketType;

	/**
	* 可能产生的危害
	*/
	private String ticketHarm;

	/**
	* 涉及其他特种作业
	*/
	private String ticketSpecial;

	/**
	* 安全措施落实情况
	*/
	private String ticketSafeMeasure;

	/**
	* 其他安全检查补充事项
	*/
	private String otherSafeCheck;

	/**
	* 操作人员签字
	*/
	private String operateSign;

	/**
	* 监护人员签字
	*/
	private String custodySign;

	/**
	* 指挥人员签字
	*/
	private String commandSign;

	/**
	* 作业负责人签字
	*/
	private String dutySign;

    /**
     * 作业人员确认签字
     */
    private String dutyConfirmSign;

    /**
     * 车间验收人签字
     */
    private String workshopConfirmSign;

    /**
     * 厂部验收人签字
     */
    private String factoryConfirmSign;

    /**
     * 许可证状态code
     */
    private String statusCode;

    /**
     * 许可证状态name
     */
    private String statusName;

}
