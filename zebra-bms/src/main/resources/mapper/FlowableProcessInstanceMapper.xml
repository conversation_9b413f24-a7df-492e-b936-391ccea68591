<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yhd.admin.bms.dao.flowable.IFlowableProcessInstanceDao">
    <select id="getPagerModel" parameterType="com.yhd.admin.bms.domain.vo.flowable.ProcessInstanceQueryVo"
            resultType="com.yhd.admin.bms.domain.vo.flowable.ProcessInstanceVo">
        SELECT DISTINCT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ as processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t2.NAME_ AS taskName,
        t1.START_USER_ID_ AS approver
        FROM
        ACT_HI_PROCINST t1
        LEFT JOIN ACT_RU_TASK t2 ON t1.ID_ = t2.PROC_INST_ID_
        <where>
            <if test="params.userCode != null">and t1.START_USER_ID_ = #{params.userCode}</if>
            <if test="params.formName != null">and t1.NAME_ like CONCAT('%',#{params.formName},'%')</if>
            <if test="params.processInstanceId != null">and t1.ID_ = #{params.processInstanceId}</if>
            <if test="params.startDate != null">
                <!-- 开始时间检索 -->
                AND date_format(t1.START_TIME_
                , '%y%m%d') &gt;= date_format(#{params.startDate}
                , '%y%m%d')
            </if>

            <if test="params.endDate != null">
                <!-- 结束时间检索 -->
                AND date_format(t1.START_TIME_
                , '%y%m%d') &lt;= date_format(#{params.endDate}
                , '%y%m%d')
            </if>
        </where>
        ORDER BY t1.START_TIME_ DESC
    </select>
</mapper>
