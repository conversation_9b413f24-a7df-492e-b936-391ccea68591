package com.yhd.admin.bms.domain.entity.sw.goods;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/** 筛板物资计划 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwGoodsPlan extends BaseEntity implements Serializable {
  private static final long serialVersionUID = 5935271974454162646L;
  /** 计划月份 */
  private String month;
  /** 名称 */
  private String name;
  /** 计划物资种类数量 */
  private Integer cateNum;

  /** 详情列表 */
  @TableField(exist = false)
  private List<MesSwGoodsPlanDetail> detailList;
}
