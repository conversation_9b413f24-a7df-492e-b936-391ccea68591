<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.passport.dao.RoleDao">


  <select id="selectRoleListByUser"
    parameterType="com.yhd.admin.passport.domain.query.UserAccountParam"
    resultType="com.yhd.admin.passport.domain.entity.SysRole">
    select role.id,
           role.is_enable,
           role.role_code,
           role.role_name,
           role.created_by,
           role.created_time,
           role.updated_by,
           role.updated_time
    from tb_sys_user_role urole
           inner join tb_sys_role role on urole.role_id = role.id
           inner join tb_sys_user_account acount on acount.id = urole.account_id
    where acount.username = #{userName}
  </select>
  <select id="selectMenuByRole" parameterType="java.util.List"
    resultType="java.lang.Long">
    select menu.id
    from tb_sys_role_menu mr
    inner join tb_sys_role role on mr.role_id = role.id
    inner join tb_sys_menu menu on menu.id = mr.menu_id
    where mr.role_id in
    <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="selectAuthorityById" parameterType="java.util.List"
    resultType="java.lang.String">
    WITH recursive r AS (
    SELECT id,
    parent_id,
    `key`,
    path,
    NAME,
    locale,
    icon,
    hide_in_menu,
    hide_children_in_menu,
    authority,
    type,
    order_num,
    LEVEL,
    created_by,
    created_time,
    updated_by,
    updated_time
    FROM tb_sys_menu
    WHERE id in
    <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
      #{id}
    </foreach>
    UNION ALL
    SELECT c.id,
    c.parent_id,
    c.`key`,
    c.path,
    c.NAME,
    c.locale,
    c.icon,
    c.hide_in_menu,
    c.hide_children_in_menu,
    c.authority,
    c.type,
    c.order_num,
    c.LEVEL,
    c.created_by,
    c.created_time,
    c.updated_by,
    c.updated_time
    FROM tb_sys_menu c,
    r
    WHERE c.id = r.parent_id
    )
    SELECT r.authority
    FROM r
    ORDER BY id;
  </select>

</mapper>
