package com.yhd.admin.scada.domain.dto;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 @Description PLC 点表
 * @createTime 2021/6/5 16:38
 */
@Data
@ToString
public class PLCPointDTO implements java.io.Serializable, Cloneable {

  private static final long serialVersionUID = 1850390152446173476L;
  private String id;
  /** 设备ID */
  private String deviceNo;
  /** 设备名称 */
  private String deviceName;
  /** 点位地址 */
  private String pointAddress;
  /** 点位名称 */
  private String pointName;
  /** 所属系统 */
  private String sys;

  /** 链接方式 */
  private String connect;
  /** 类型 */
  private String types;
  /** 类型名称 */
  private String typesName;
  /** KEEPSERVER地址 */
  private String kwpAddress;
  /** 优先级 */
  private Integer priority;
  /** 订阅 */
  private Boolean onSubscription;
  /** 启用 */
  private Boolean enable;
  /** 是否推送kafka */
  private Boolean kafka;
  /** 是否推送websocket */
  private Boolean websocket;
  /** 是否入库 */
  private Boolean db;
  /** 是否入msql */
  private Boolean mysql;
  /** 是否过滤 */
  private Boolean filter;
  /** 缩放 */
  private Double zoom;
  /** 单位 */
  private String unit;
  /** 是否存入消息表 */
  private Boolean notice;
  /** 创建人 */
  private String createdBy;
  /** 创建时间 */
  private java.time.LocalDateTime createdTime;
  /** 更新人 */
  private String updatedBy;
  /** 更新时间 */
  private java.time.LocalDateTime updatedTime;

  /** webSocket 队列 */
  private List<PLCWsDTO> wsTopics;

  /** kafka 队列 */
  private List<PLCKafkaDTO> kafkaTopics;

  /** 过滤区间 */
  private List<PLCFilterDTO> filters;
}
