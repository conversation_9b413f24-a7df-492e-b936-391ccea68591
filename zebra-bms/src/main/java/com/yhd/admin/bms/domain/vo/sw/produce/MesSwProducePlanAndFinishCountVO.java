package com.yhd.admin.bms.domain.vo.sw.produce;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 生产计划产量及完成产量统计
 *
 * <AUTHOR>
 * @date 2025/1/4 10:19
 */
@Data
public class MesSwProducePlanAndFinishCountVO implements Serializable {
  private static final long serialVersionUID = -1429747868223567832L;

  /** 日计划：当月总计划量/当月天数，四舍五入保留整数 */
  private BigDecimal planDayCount;
  /** 日完成：装车数据+电厂皮带 */
  private BigDecimal finishDayCount;
  /** 日完成占比：完成数/计划数*100，四舍五入保留两位小数 */
  private BigDecimal finishDayRatio;

  /** 月计划 */
  private BigDecimal planMonthCount;
  /** 月完成：装车数据+电厂皮带 */
  private BigDecimal finishMonthCount;
  /** 月完成占比：完成数/计划数*100，四舍五入保留两位小数 */
  private BigDecimal finishMonthRatio;

  /** 年计划 */
  private BigDecimal planYearCount;
  /** 年完成：装车数据+电厂皮带 */
  private BigDecimal finishYearCount;
  /** 年完成占比：完成数/计划数*100，四舍五入保留两位小数 */
  private BigDecimal finishYearRatio;
}
