package com.yhd.admin.bms.service.sw.consumption;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionAntifreezeDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionAntifreeze;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionAntifreezeParam;
import java.time.LocalDate;
import java.util.List;

/**
 * 能耗管理-防冻液
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
public interface MesConsumptionAntifreezeService extends IService<MesConsumptionAntifreeze> {
  /**
   * 查询。
   *
   * @param param
   * @return
   */
  List<MesConsumptionAntifreezeDTO> queryList(MesConsumptionAntifreezeParam param);

  /**
   * 根据月份查询详情
   *
   * @param param
   * @return
   */
  List<MesConsumptionAntifreezeDTO> getCurrentDetail(MesConsumptionAntifreezeParam param);

  /**
   * 根据时间段，查询防冻液列表
   *
   * @param start, end，年月日，最多是今天，不能给未来某天
   * @return 防冻液统计列表
   */
  List<MesConsumptionAntifreezeDTO> queryIntervalList(LocalDate start, LocalDate end);
}
