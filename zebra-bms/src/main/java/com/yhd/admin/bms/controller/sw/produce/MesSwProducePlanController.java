package com.yhd.admin.bms.controller.sw.produce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.produce.MesSwProducePlanConvert;
import com.yhd.admin.bms.domain.dto.sw.produce.MesSwProducePlanDTO;
import com.yhd.admin.bms.domain.entity.sw.produce.MesSwProducePlan;
import com.yhd.admin.bms.domain.query.sw.produce.MesSwProducePlanParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProducePlanVO;
import com.yhd.admin.bms.service.sw.produce.MesSwProducePlanService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 生产计划-控制层
 *
 * <AUTHOR>
 * @date 2024/10/27 8:28
 */
@RestController
@RequestMapping(value = "/pe/plan")
public class MesSwProducePlanController {
  private static final Logger logger = LoggerFactory.getLogger(MesSwProducePlanController.class);

  private final MesSwProducePlanService planService;
  private final MesSwProducePlanConvert planConvert;

  public MesSwProducePlanController(
      MesSwProducePlanService planService, MesSwProducePlanConvert planConvert) {
    this.planService = planService;
    this.planConvert = planConvert;
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MesSwProducePlanVO> pagingQuery(@RequestBody MesSwProducePlanParam param) {
    IPage<MesSwProducePlanDTO> page = planService.pagingQuery(param);
    return new PageRespJson<>(page.convert(planConvert::toVO));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MesSwProducePlanVO> getCurrentDetail(@RequestBody MesSwProducePlanParam param) {
    return RespJson.buildSuccessResponse(planConvert.toVO(planService.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> add(@RequestBody MesSwProducePlanParam param) {
    return RespJson.buildSuccessResponse(planService.add(param));
  }

  @PostMapping(
      value = "/modify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> modify(@RequestBody MesSwProducePlanParam param) {
    return RespJson.buildSuccessResponse(planService.modify(param));
  }

  @PostMapping(
      value = "/removeBatch",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> removeBatch(@RequestBody BatchParam param) {
    return RespJson.buildSuccessResponse(planService.removeBatch(param));
  }

  /** 校验计划时间数据是否已存在，存在true，不存在false */
  @PostMapping(
      value = "/isExist",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> isExist(@RequestBody MesSwProducePlanParam param) {
    boolean result = false;
    // 新增判断是否已存在
    if (null == param.getId()) {
      MesSwProducePlanParam planParam = new MesSwProducePlanParam();
      planParam.setPlanTimeStr(param.getPlanTimeStr());
      List<MesSwProducePlan> planList = planService.queryList(param);
      if (CollectionUtils.isNotEmpty(planList)) {
        logger.debug("新增生产计划数据， 计划时间：{}，已存在！", param.getPlanTimeStr());
        result = true;
      }
    } else {
      // 编辑判断是否存在
      MesSwProducePlanDTO detail = planService.getCurrentDetail(param);
      if (!param.getPlanTimeStr().equals(detail.getPlanTimeStr())) {
        MesSwProducePlanParam planParam = new MesSwProducePlanParam();
        planParam.setPlanTimeStr(param.getPlanTimeStr());
        List<MesSwProducePlan> planList = planService.queryList(param);
        if (CollectionUtils.isNotEmpty(planList)) {
          logger.debug("编辑生产计划数据， 计划时间：{}，已存在！", param.getPlanTimeStr());
          result = true;
        }
      }
    }

    return RespJson.buildSuccessResponse(result);
  }
}
