package com.yhd.admin.bms.service.sw.safe;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainOrgGroupStudentSignDTO;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwSafeTrainStudentSignDTO;
import com.yhd.admin.bms.domain.entity.sw.safe.MesSwSafeTrainStudentSign;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwSafeTrainStudentSignParam;

import java.util.List;

/**
 * 安全培训学员签到记录-业务层接口
 *
 * <AUTHOR>
 * @date 2024/1/4 17:07
 */
public interface MesSwSafeTrainStudentSignService extends IService<MesSwSafeTrainStudentSign> {

  /**
   * 根据条件查询学员签到列表
   *
   * @param param 参数
   * @return 学员签到列表
   */
  List<MesSwSafeTrainStudentSignDTO> queryList(MesSwSafeTrainStudentSignParam param);

  /**
   * 根据课程ID查询学员签到列表
   *
   * @param courseId 课程ID
   * @return 学员签到列表
   */
  List<MesSwSafeTrainStudentSignDTO> queryListByCourseId(Long courseId);

  /**
   * 根据课程ID查询组织分组后学员签到列表
   *
   * @param courseId 课程ID
   * @return 组织分组后学员签到列表
   */
  List<MesSwSafeTrainOrgGroupStudentSignDTO> queryOrgGroupListByCourseId(Long courseId);

  /**
   * 查询学员签到详情信息
   *
   * @param param 参数
   * @return 学员签到详情信息
   */
  MesSwSafeTrainStudentSignDTO getCurrentDetail(MesSwSafeTrainStudentSignParam param);

  /**
   * 查询学员签到详情信息
   *
   * @param id 主键id
   * @return 学员签到详情信息
   */
  MesSwSafeTrainStudentSignDTO getCurrentDetail(Long id);

  MesSwSafeTrainStudentSignDTO getCurrentDetail(Long courseId, String studentCode);

    /**
   * 根据课程id查询学员签到人数
   *
   * @param courseId 课程id
   * @return 学员签到列表
   */
  Integer getStudentSignCount(Long courseId);
}
