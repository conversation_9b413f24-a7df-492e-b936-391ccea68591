package com.yhd.admin.bms.service.sys.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sys.UserAccountDao;
import com.yhd.admin.bms.dao.sys.UserDao;
import com.yhd.admin.bms.domain.convert.sys.UserConvert;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sys.SysUser;
import com.yhd.admin.bms.domain.entity.sys.SysUserAccount;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.query.sys.UserParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sys.UserAccountService;
import com.yhd.admin.bms.service.sys.UserService;
import liquibase.util.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserServiceImpl.java @Description TODO
 * @createTime 2020年04月01日 14:50:00
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserDao, SysUser> implements UserService {

  private final UserConvert convert;

  private final UserAccountDao accountDao;

  private final UserAccountService userAccountService;

  public UserServiceImpl(
      UserConvert convert, UserAccountDao accountDao, UserAccountService userAccountService) {
    this.convert = convert;
    this.accountDao = accountDao;
    this.userAccountService = userAccountService;
  }

  @Override
  public UserDTO getUser(UserParam param) {
    if (StringUtils.isBlank(param.getAccountName()) && param.getId() == null) {
      throw new BMSException(
          ExceptionEnum.USER_NAME_ID_NULL.getCode(), ExceptionEnum.USER_NAME_ID_NULL.getDesc());
    }
    SysUserAccount userAccount =
        userAccountService
            .lambdaQuery()
            .eq(
                StringUtils.isNotBlank(param.getAccountName()),
                SysUserAccount::getUsername,
                param.getAccountName())
            .eq(param.getId() != null, SysUserAccount::getId, param.getId())
            .one();
    Objects.requireNonNull(userAccount, "用户不存在");
    SysUser sysUser = new SysUser();
    // 转换实体
    covertUserAccount(sysUser, userAccount);
    return convert.toDTO(sysUser);
  }

  // 辅助方法
  private void covertUserAccount(SysUser sysUser, SysUserAccount userAccount) {
    sysUser.setId(userAccount.getId());
    sysUser.setAccountName(userAccount.getUsername());
    sysUser.setEmail(userAccount.getEmail());
    sysUser.setPhone(userAccount.getPhone());
    sysUser.setOrgId(userAccount.getDepartmentId());
    sysUser.setIsEnable(userAccount.getIsEnable());
    sysUser.setSignature(userAccount.getSignature());
    sysUser.setName(userAccount.getName());
    sysUser.setTsdOperatePassword(userAccount.getTsdOperatePassword());
    sysUser.setCreatedBy(userAccount.getCreatedBy());
    sysUser.setUpdatedBy(userAccount.getUpdatedBy());
    sysUser.setCreatedTime(userAccount.getCreatedTime());
    sysUser.setUpdatedTime(userAccount.getUpdatedTime());
  }

  @Override
  public IPage<UserDTO> pagingQuery(UserParam queryParam) {
    IPage<SysUser> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
    LambdaQueryChainWrapper<SysUser> userQuery = new LambdaQueryChainWrapper<>(baseMapper);
    userQuery
        .eq(
            StringUtils.isNoneBlank(queryParam.getAccountName()),
            SysUser::getAccountName,
            queryParam.getAccountName())
        .like(
            StringUtils.isNotBlank(queryParam.getEmail()), SysUser::getEmail, queryParam.getEmail())
        .like(
            StringUtils.isNotBlank(queryParam.getTitle()), SysUser::getTitle, queryParam.getTitle())
        .likeLeft(
            StringUtils.isNotBlank(queryParam.getPhone()), SysUser::getPhone, queryParam.getPhone())
        .likeLeft(
            StringUtils.isNotBlank(queryParam.getTags()), SysUser::getTags, queryParam.getTags())
        .like(StringUtils.isNotBlank(queryParam.getName()), SysUser::getName, queryParam.getName())
        .like(
            StringUtils.isNotBlank(queryParam.getAddress()),
            SysUser::getAddress,
            queryParam.getAddress())
        .eq(queryParam.getOrgId() != null, SysUser::getOrgId, queryParam.getOrgId())
        .orderByDesc(SysUser::getCreatedTime);
    return userQuery.page(iPage).convert(convert::toDTO);
  }

  @Override
  public Boolean add(UserParam userParam) {
    LambdaUpdateChainWrapper<SysUserAccount> accountUpdateChain =
        new LambdaUpdateChainWrapper<>(accountDao);
    accountUpdateChain
        .eq(
            StringUtils.isNotBlank(userParam.getAccountName()),
            SysUserAccount::getUsername,
            userParam.getAccountName())
        .set(SysUserAccount::getIsBind, Boolean.TRUE)
        .update();
    return save(convert.toEntity(userParam));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean modify(UserParam userParam) {
    if (StringUtils.isBlank(userParam.getAccountName())
        || StringUtils.isBlank(userParam.getName())
        || StringUtils.isBlank(userParam.getPhone())
        || StringUtils.isBlank(userParam.getEmail())) {
      throw new BMSException(
          ExceptionEnum.REQUIRED_ERROR.getCode(), ExceptionEnum.REQUIRED_ERROR.getDesc());
    }
    if (!StringUtils.equals(userParam.getAccountName(), userParam.getOldAccountName())) {
      // 绑定新用户
      LambdaUpdateChainWrapper<SysUserAccount> bindingUpdate =
          new LambdaUpdateChainWrapper<>(accountDao);
      bindingUpdate
          .eq(
              StringUtils.isNotBlank(userParam.getAccountName()),
              SysUserAccount::getUsername,
              userParam.getAccountName())
          .set(SysUserAccount::getIsBind, Boolean.TRUE)
          .set(SysUserAccount::getName, userParam.getName())
          .set(SysUserAccount::getEmail, userParam.getEmail())
          .set(SysUserAccount::getPhone, userParam.getPhone())
          .update();
      // 解绑旧账户
      LambdaUpdateChainWrapper<SysUserAccount> unBindingUpdate =
          new LambdaUpdateChainWrapper<>(accountDao);
      unBindingUpdate
          .eq(
              StringUtils.isNotBlank(userParam.getOldAccountName()),
              SysUserAccount::getUsername,
              userParam.getOldAccountName())
          .set(SysUserAccount::getIsBind, Boolean.FALSE)
          .update();
    }
    // 查询未变更前用户信息
    UserDTO oldUser = this.getUser(userParam);
    // 绑定新用户
    LambdaUpdateChainWrapper<SysUserAccount> updateChainWrapper =
        new LambdaUpdateChainWrapper<>(accountDao);
    updateChainWrapper
        .eq(SysUserAccount::getUsername, userParam.getAccountName())
        .set(SysUserAccount::getName, userParam.getName())
        .set(SysUserAccount::getEmail, userParam.getEmail())
        .set(SysUserAccount::getPhone, userParam.getPhone())
        .set(
            !userParam.getTsdOperatePassword().equals(oldUser.getTsdOperatePassword()),
            SysUserAccount::getTsdOperatePassword,
            MD5Util.computeMD5(MD5Util.computeMD5(userParam.getTsdOperatePassword())))
        .update();
    return new LambdaUpdateChainWrapper<>(baseMapper)
        .eq(SysUser::getId, userParam.getId())
        .update(convert.toEntity(userParam));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean removeBatch(BatchParam batchParam) {
    if (!CollectionUtils.isEmpty(batchParam.getId())) {
      LambdaUpdateChainWrapper<SysUserAccount> accountUpdateChain =
          new LambdaUpdateChainWrapper<>(accountDao);
      batchParam.getId().stream()
          .map(this::getById)
          .map(SysUser::getAccountName)
          .filter(StringUtils::isNotBlank)
          .forEach(
              accountName ->
                  accountUpdateChain
                      .eq(SysUserAccount::getUsername, accountName)
                      .set(SysUserAccount::getIsBind, Boolean.FALSE)
                      .update());
    }
    return removeByIds(batchParam.getId());
  }

  @Override
  public UserDTO obtainCurrentDetail(UserParam queryParam) {
    return convert.toDTO(getById(queryParam.getId()));
  }

  @Override
  public List<UserDTO> queryUser(UserParam queryParam) {
    LambdaQueryChainWrapper<SysUser> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
    if (StringUtils.isNotBlank(queryParam.getType())) {
      LambdaUpdateChainWrapper<SysUserAccount> accountUpdateChain =
          new LambdaUpdateChainWrapper<>(accountDao)
              .eq(SysUserAccount::getType, queryParam.getType());
      List<SysUserAccount> list = accountDao.selectList(accountUpdateChain);
      List<String> accoutList =
          list.stream().map(SysUserAccount::getUsername).collect(Collectors.toList());
      queryChainWrapper
          .like(
              StringUtils.isNotBlank(queryParam.getName()), SysUser::getName, queryParam.getName())
          .in(SysUser::getAccountName, accoutList)
          .isNotNull(SysUser::getAccountName)
          .eq(queryParam.getOrgId() != null, SysUser::getOrgId, queryParam.getOrgId());
      return queryChainWrapper.list().stream()
          .filter(o -> StringUtils.isNotBlank(o.getAccountName()))
          .map(convert::toDTO)
          .collect(Collectors.toList());
    }

    queryChainWrapper
        .like(StringUtils.isNotBlank(queryParam.getName()), SysUser::getName, queryParam.getName())
        .isNotNull(SysUser::getAccountName)
        .eq(queryParam.getOrgId() != null, SysUser::getOrgId, queryParam.getOrgId());
    return queryChainWrapper.list().stream()
        .filter(o -> StringUtils.isNotBlank(o.getAccountName()))
        .map(convert::toDTO)
        .collect(Collectors.toList());
  }
}
