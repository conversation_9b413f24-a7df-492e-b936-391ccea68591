<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yhd.admin.bms.dao.flowable.IFlowableTaskDao">
    <!-- 我的待办-->
    <select id="getApplyingTasks" parameterType="com.yhd.admin.bms.domain.vo.flowable.TaskQueryVo"
            resultType="com.yhd.admin.bms.domain.vo.flowable.TaskVo">
        SELECT DISTINCT t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.NAME_ AS formName,
        t2.TENANT_ID_ AS systemSn,
        t2.BUSINESS_KEY_ AS businessKey,
        t2.PROC_INST_ID_ AS processInstanceId,
        t1.CREATE_TIME_ AS startTime,
        t2.START_USER_ID_ AS startUserName,
        t4.form_process_status AS formProcessStatus
        FROM ACT_RU_TASK t1
        INNER JOIN ACT_RU_EXECUTION t2
        ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN ACT_RU_IDENTITYLINK t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN tb_form_instance t4 ON t4.form_process_instance_id = t1.PROC_INST_ID_
        <where>
            t2.BUSINESS_KEY_ IS NOT NULL AND t4.form_process_status IS NULL
            <if test="params.startDate != null">
                <!-- 开始时间检索 -->
                AND date_format(t1.CREATE_TIME_
                , '%y%m%d') &gt;= date_format(#{params.startDate}
                , '%y%m%d')
            </if>

            <if test="params.endDate != null">
                <!-- 结束时间检索 -->
                AND date_format(t1.CREATE_TIME_
                , '%y%m%d') &lt;= date_format(#{params.endDate}
                , '%y%m%d')
            </if>
        </where>
        AND (
        t1.ASSIGNEE_ = #{params.userCode}
        OR (t1.ASSIGNEE_ IN (SELECT G.group_id_ FROM ACT_ID_MEMBERSHIP G WHERE G.user_id_ = #{params.userCode}
        ))
        OR (
        (t1.ASSIGNEE_ IS NULL
        OR t1.ASSIGNEE_ = '')
        AND (t3.USER_ID_ = #{params.userCode}
        OR t3.GROUP_ID_ IN
        (SELECT g.group_id_ FROM ACT_ID_MEMBERSHIP g WHERE g.user_id_ = #{params.userCode}
        ))
        )
        )
        ORDER BY t1.CREATE_TIME_ DESC
    </select>
    <!-- 已办任务-->
    <select id="getApplyedTasks" parameterType="com.yhd.admin.bms.domain.vo.flowable.TaskQueryVo"
            resultType="com.yhd.admin.bms.domain.vo.flowable.TaskVo">
        SELECT DISTINCT t1.ID_ AS taskId,
        t1.NAME_ AS taskName,
        t2.account_name AS approver,
        t3.NAME_ AS formName,
        t3.BUSINESS_KEY_ AS businessKey,
        t3.PROC_INST_ID_ AS processInstanceId,
        t3.TENANT_ID_ AS systemSn,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t3.START_USER_ID_ AS startUserName
        FROM ACT_HI_TASKINST t1
        LEFT JOIN tb_sys_user t2 ON t1.ASSIGNEE_ = t2.account_name
        LEFT JOIN ACT_HI_PROCINST t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        <where>
            t1.END_TIME_ IS NOT NULL
            <if test="params.startDate != null">
                <!-- 开始时间检索 -->
                AND date_format(t1.START_TIME_
                , '%y%m%d') &gt;= date_format(#{params.startDate}
                , '%y%m%d')
            </if>
            <if test="params.endDate != null">
                <!-- 结束时间检索 -->
                AND date_format(t1.START_TIME_
                , '%y%m%d') &lt;= date_format(#{params.endDate}
                , '%y%m%d')
            </if>
        </where>
        AND t1.ASSIGNEE_ = #{params.userCode}
        ORDER BY t1.END_TIME_ DESC
    </select>
    <!-- 根据流程实例id到act_ru_task 表查询下一步审批人和taskId-->
    <select id="getTaskByProcessInstanceId" parameterType="java.lang.String"
            resultType="com.yhd.admin.bms.domain.vo.flowable.TaskVo">
        SELECT t1.ID_       AS taskId,
               t1.ASSIGNEE_ AS approver

        FROM ACT_RU_TASK t1
        WHERE t1.PROC_INST_ID_ = #{processInstanceId}
    </select>
    <select id="getApplyTotal" parameterType="com.yhd.admin.bms.domain.vo.flowable.TaskQueryVo"
            resultType="com.yhd.admin.bms.domain.vo.flowable.TaskVo">
        SELECT DISTINCT t1.ID_                 AS taskId,
                        t1.NAME_               AS taskName,
                        t2.NAME_               AS formName,
                        t2.TENANT_ID_          AS systemSn,
                        t2.BUSINESS_KEY_       AS businessKey,
                        t2.PROC_INST_ID_       AS processInstanceId,
                        t1.CREATE_TIME_        AS startTime,
                        t2.START_USER_ID_      AS startUserName,
                        t4.form_process_status AS formProcessStatus
        FROM ACT_RU_TASK t1
                 INNER JOIN ACT_RU_EXECUTION t2
                            ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
                 LEFT JOIN ACT_HI_IDENTITYLINK t3 ON t3.TASK_ID_ = t1.ID_
                 LEFT JOIN tb_form_instance t4 ON t4.form_process_instance_id = t1.PROC_INST_ID_
        WHERE t2.BUSINESS_KEY_ IS NOT NULL
          AND (
                    t1.ASSIGNEE_ = #{params.userCode}
                OR (t1.ASSIGNEE_ IN (SELECT G.group_id_ FROM ACT_ID_MEMBERSHIP G WHERE G.user_id_ = #{params.userCode}))
                OR (
                            (t1.ASSIGNEE_ IS NULL
                                OR t1.ASSIGNEE_ = '')
                            AND (t3.USER_ID_ = #{params.userCode}
                            OR t3.GROUP_ID_ IN
                               (SELECT g.group_id_ FROM ACT_ID_MEMBERSHIP g WHERE g.user_id_ = #{params.userCode}))
                        )
            )
        ORDER BY t1.CREATE_TIME_ DESC
    </select>
</mapper>
