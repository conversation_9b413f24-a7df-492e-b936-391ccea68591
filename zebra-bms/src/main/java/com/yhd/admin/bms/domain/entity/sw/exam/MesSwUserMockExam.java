package com.yhd.admin.bms.domain.entity.sw.exam;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwMockExamPaperQuestionUserAnswerTypeGroupDTO;
import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户模拟考试信息
 *
 * <AUTHOR>
 * @date 2023/10/31 10:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwUserMockExam extends BaseEntity implements Serializable {
  private static final long serialVersionUID = 7041798856203505680L;

  /** 用户账号 */
  private String accountName;
  /** 用户姓名 */
  private String realName;
  /** 考试试卷(组卷)主键id */
  private Long examPaperId;
  /** 考试试卷标题 */
  private String paperTitle;
  /** 考试试卷分类 */
  private String paperClassify;
  /** 考试状态：0未开始、1进行中、2已完成 */
  private String examStatus;

  @TableField(exist = false)
  private String examStatusName;
  /** 考试开始时间 */
  private LocalDateTime examStartTime;
  /** 考试结束时间 */
  private LocalDateTime examEndTime;
  /** 试卷总分 */
  private Integer paperScore;
  /** 用户最终得分 */
  private Integer userScore;
  /** 试卷题目数量 */
  private Integer questionCount;
  /** 做题时间(秒) */
  private Long doTime;

  @TableField(exist = false)
  private List<MesSwMockExamPaperQuestionUserAnswer> paperQuestionList;
  /** 试卷题目题型分组列表 */
  @TableField(exist = false)
  private List<MesSwMockExamPaperQuestionUserAnswerTypeGroupDTO> typeGroupQuestionList;
}
