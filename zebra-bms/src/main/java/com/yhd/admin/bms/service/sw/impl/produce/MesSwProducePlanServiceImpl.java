package com.yhd.admin.bms.service.sw.impl.produce;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.bms.dao.sw.produce.MesSwProducePlanDao;
import com.yhd.admin.bms.domain.convert.sw.produce.MesSwProducePlanConvert;
import com.yhd.admin.bms.domain.dto.sw.produce.MesSwProducePlanDTO;
import com.yhd.admin.bms.domain.entity.sw.load.LoadFinishMonthCount;
import com.yhd.admin.bms.domain.entity.sw.load.LoadSumcoaltype;
import com.yhd.admin.bms.domain.entity.sw.produce.MesProduceDcpd;
import com.yhd.admin.bms.domain.entity.sw.produce.MesSwProducePlan;
import com.yhd.admin.bms.domain.query.sw.produce.MesSwProducePlanParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.load.LoadFinishMonthCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProduceCoalTypeCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProduceFinishDayCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProduceFinishMonthCountVO;
import com.yhd.admin.bms.domain.vo.sw.produce.MesSwProducePlanAndFinishCountVO;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.load.LoadFinishMonthCountService;
import com.yhd.admin.bms.service.sw.load.LoadSumcoaltypeService;
import com.yhd.admin.bms.service.sw.load.ZcTrainloadService;
import com.yhd.admin.bms.service.sw.produce.MesProduceDcpdService;
import com.yhd.admin.bms.service.sw.produce.MesSwProducePlanService;
import com.yhd.admin.common.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 生产计划-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/1/4 10:26
 */
@Service
public class MesSwProducePlanServiceImpl extends ServiceImpl<MesSwProducePlanDao, MesSwProducePlan>
    implements MesSwProducePlanService {

    private final MesSwProducePlanConvert planConvert;

    private final ZcTrainloadService trainloadService;
    private final MesProduceDcpdService produce901Service;
    private final LoadFinishMonthCountService loadFinishMonthCountService;
    private final LoadSumcoaltypeService sumcoaltypeService;

    public MesSwProducePlanServiceImpl(
        MesSwProducePlanConvert planConvert,
        ZcTrainloadService trainloadService,
        MesProduceDcpdService produce901Service,
        LoadFinishMonthCountService loadFinishMonthCountService,
        LoadSumcoaltypeService sumcoaltypeService) {
        this.planConvert = planConvert;
        this.trainloadService = trainloadService;
        this.produce901Service = produce901Service;
        this.loadFinishMonthCountService = loadFinishMonthCountService;
        this.sumcoaltypeService = sumcoaltypeService;
    }

    @Override
    public IPage<MesSwProducePlanDTO> pagingQuery(MesSwProducePlanParam param) {
        Page<MesSwProducePlan> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MesSwProducePlan> queryChain =
            new LambdaQueryChainWrapper<>(baseMapper);
        // 计划类型
        queryChain.eq(StringUtils.isNotBlank(param.getPlanType()),
            MesSwProducePlan::getPlanType,
            param.getPlanType());
        /**
         * 原代码
         */
//        // 计划年份
//        queryChain.like(
//            StringUtils.isNotBlank(param.getPlanTimeStr()),
//            MesSwProducePlan::getPlanTimeStr,
//            param.getPlanTimeStr());
        // 计划年份
        queryChain.between(
            StringUtils.isNotBlank(param.getStartYear()) && StringUtils.isNotBlank(param.getEndYear()),
            MesSwProducePlan::getPlanTimeStr,
            param.getStartYear(),
            param.getEndYear());
        // 计划月份
        queryChain.between(
            StringUtils.isNotBlank(param.getStartMonth()) && StringUtils.isNotBlank(param.getEndMonth()),
            MesSwProducePlan::getPlanTimeStr,
            param.getStartMonth(),
            param.getEndMonth());
        //计划日期
        queryChain.between(
            Objects.nonNull(param.getStartDate()) && Objects.nonNull(param.getEndDate()),
            MesSwProducePlan::getPlanTimeStr,
            param.getStartDate(),
            param.getEndDate());
        // 排序
        queryChain.orderByAsc(MesSwProducePlan::getPlanTimeStr);

        IPage<MesSwProducePlan> page = queryChain.page(iPage);

        return page.convert(planConvert::toDTO);
    }

    @Override
    public List<MesSwProducePlan> queryList(MesSwProducePlanParam param) {
        LambdaQueryWrapper<MesSwProducePlan> wrapper = new LambdaQueryWrapper<>();
        // 计划时间
        wrapper.eq(
            StringUtils.isNotBlank(param.getPlanTimeStr()),
            MesSwProducePlan::getPlanTimeStr,
            param.getPlanTimeStr());

        return baseMapper.selectList(wrapper);
    }

    @Override
    public MesSwProducePlanDTO getCurrentDetail(MesSwProducePlanParam param) {
        return planConvert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(MesSwProducePlanParam param) {
        MesSwProducePlan entity = planConvert.toEntity(param);
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modify(MesSwProducePlanParam param) {
        MesSwProducePlanDTO detail = this.getCurrentDetail(param);
        if (!param.getPlanType().equals(detail.getPlanType())) {
            throw new BMSException("error", "计划类型不允许修改");
        }
        return super.updateById(planConvert.toEntity(param));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

    @Override
    public MesSwProducePlanAndFinishCountVO getProducePlanAndFinishCount() {
        MesSwProducePlanAndFinishCountVO result = new MesSwProducePlanAndFinishCountVO();
        // 日产量情况
        MesSwProduceFinishDayCountVO dayCount = this.getProduceFinishDayCount(LocalDate.now());
        // 日完成
        result.setFinishDayCount(dayCount.getFinishCount());
        // 日计划
        result.setPlanDayCount(dayCount.getPlanCount());
        // 日完成占比
        result.setFinishDayRatio(dayCount.getFinishRatio());
        // 月计划情况
        // 查询当月生产计划
        MesSwProducePlanParam monthPlanParam = new MesSwProducePlanParam();
        monthPlanParam.setPlanTimeStr(DateUtil.getSysYearMonth());
        List<MesSwProducePlan> planList = this.queryList(monthPlanParam);
        if (CollectionUtils.isNotEmpty(planList)) {
            MesSwProducePlan producePlan = planList.get(0);
            // 月计划
            result.setPlanMonthCount(
                BigDecimal.valueOf(producePlan.getTotal()).multiply(BigDecimal.valueOf(10000)));
        }
        // 月完成产量
        BigDecimal finishMonthCount = BigDecimal.ZERO;
        // 查询当月装车数据
        LambdaQueryWrapper<LoadFinishMonthCount> loadMonthWrapper = new LambdaQueryWrapper<>();
        loadMonthWrapper.eq(LoadFinishMonthCount::getMonthstr, DateUtil.getSysYearMonth());
        List<LoadFinishMonthCount> finishMonthCounts =
            loadFinishMonthCountService.list(loadMonthWrapper);
        if (CollectionUtils.isNotEmpty(finishMonthCounts)) {
            LoadFinishMonthCount monthCount = finishMonthCounts.get(0);
            finishMonthCount = monthCount.getFinishTon();
        }
        // 统计当月电厂皮带产量
        LambdaQueryWrapper<MesProduceDcpd> pMonthWrapper = new LambdaQueryWrapper<>();
        pMonthWrapper.like(MesProduceDcpd::getStatsDate, DateUtil.getSysYearMonth());
        List<MesProduceDcpd> month901List = produce901Service.list(pMonthWrapper);
        if (CollectionUtils.isNotEmpty(month901List)) {
            double monthCount =
                month901List.stream()
                    .mapToDouble(data -> null == data.getQuantity() ? 0 : data.getQuantity())
                    .sum();
            finishMonthCount = finishMonthCount.add(BigDecimal.valueOf(monthCount));
        }
        result.setFinishMonthCount(BigDecimal.valueOf(Math.ceil(finishMonthCount.doubleValue())));

        // 月完成占比
        if (result.getPlanMonthCount() != null
            && result.getPlanMonthCount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal finishMonthRatio =
                result
                    .getFinishMonthCount()
                    .divide(result.getPlanMonthCount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            result.setFinishMonthRatio(finishMonthRatio);
        }

        // 年计划
        // 查询当年生产计划
        MesSwProducePlanParam yearPlanParam = new MesSwProducePlanParam();
        String yearMonth = DateUtil.getSysYearMonth();
        String[] split = yearMonth.split("-");
        yearPlanParam.setPlanTimeStr(split[0]);
        List<MesSwProducePlan> planYearList = this.queryList(yearPlanParam);
        if (CollectionUtils.isNotEmpty(planYearList)) {
            MesSwProducePlan producePlan = planYearList.get(0);
            // 年计划
            result.setPlanYearCount(
                BigDecimal.valueOf(producePlan.getTotal()).multiply(BigDecimal.valueOf(10000)));
        }
        // 查询年装车完成净重
        BigDecimal sumYearWeight = trainloadService.sumWeight(split[0], "year");
        // 年完成产量
        BigDecimal finishYearCount = BigDecimal.ZERO;
        if (sumYearWeight != null) {
            finishYearCount = sumYearWeight;
        }
        // 查询电厂皮带生产数据
        LambdaQueryWrapper<MesProduceDcpd> pYearWrapper = new LambdaQueryWrapper<>();
        pYearWrapper.like(MesProduceDcpd::getStatsDate, split[0]);
        List<MesProduceDcpd> produceYear901List = produce901Service.list(pYearWrapper);
        if (CollectionUtils.isNotEmpty(produceYear901List)) {
            double yearProduceCount =
                produceYear901List.stream()
                    .mapToDouble(data -> null == data.getQuantity() ? 0 : data.getQuantity())
                    .sum();
            finishYearCount = finishYearCount.add(BigDecimal.valueOf(yearProduceCount));
        }
        result.setFinishYearCount(BigDecimal.valueOf(Math.ceil(finishYearCount.doubleValue())));
        // 年完成占比
        if (result.getPlanYearCount() != null
            && result.getPlanYearCount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal finishYearRatio =
                result
                    .getFinishYearCount()
                    .divide(result.getPlanYearCount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            result.setFinishYearRatio(finishYearRatio);
        }

        return result;
    }

    @Override
    public LoadFinishMonthCountVO getLoadFinishMonthCount() {
        LoadFinishMonthCountVO result = new LoadFinishMonthCountVO();
        List<LoadFinishMonthCount> monthCounts = loadFinishMonthCountService.list();
        if (CollectionUtils.isNotEmpty(monthCounts)) {
            LoadFinishMonthCount monthCount = monthCounts.get(0);
            result.setMonthFinish(monthCount.getFinishTon());
            List<LoadFinishMonthCount> limitList =
                monthCounts.stream().limit(6).collect(Collectors.toList());
            result.setMonthCountList(
                limitList.stream()
                    .sorted(Comparator.comparing(LoadFinishMonthCount::getMonthstr))
                    .collect(Collectors.toList()));
        }
        // 查询当月生产计划数据
        MesSwProducePlanParam planParam = new MesSwProducePlanParam();
        planParam.setPlanTimeStr(DateUtil.getSysYearMonth());
        List<MesSwProducePlan> planList = this.queryList(planParam);
        if (CollectionUtils.isNotEmpty(planList)) {
            MesSwProducePlan producePlan = planList.get(0);
            result.setMonthPlan(producePlan.getYthwy() * 10000);
        }
        if (result.getMonthPlan() != null && result.getMonthFinish() != null) {
            // 均衡超欠 =月完成-月计划，无计划展示--
            BigDecimal jhcq = result.getMonthFinish().subtract(new BigDecimal(result.getMonthPlan()));
            result.setMonthJhcq(jhcq);
            // 获取剩余天数
            Integer days = DateUtil.remainingDaysInMonth();
            if (days > 0) {
                BigDecimal syrj = jhcq.divide(new BigDecimal(days), 0, RoundingMode.HALF_UP);
                result.setSyrj(syrj);
            }
        }

        return result;
    }

    @Override
    public MesSwProduceFinishDayCountVO getProduceFinishDayCount(LocalDate date) {
        if (Objects.isNull(date)) {
            return null;
        }
        MesSwProduceFinishDayCountVO result = new MesSwProduceFinishDayCountVO();
        // 查询当月生产计划
        MesSwProducePlanParam monthPlanParam = new MesSwProducePlanParam();
        monthPlanParam.setPlanTimeStr(date.toString().substring(0, 7));
        List<MesSwProducePlan> planList = this.queryList(monthPlanParam);
        // 当月天数
        int monthDays = date.lengthOfMonth();
        // 日计划
        if (CollectionUtils.isNotEmpty(planList)) {
            MesSwProducePlan monthPlan = planList.get(0);
            // 月计划->吨
            BigDecimal monthPlanTotal =
                BigDecimal.valueOf(monthPlan.getTotal()).multiply(BigDecimal.valueOf(10000));
            BigDecimal planDayCount =
                monthPlanTotal.divide(new BigDecimal(monthDays), 0, RoundingMode.HALF_UP);
            result.setPlanCount(planDayCount);
        }
        // 查询日装车完成净重
        BigDecimal sumDayWeight = trainloadService.sumWeight(date.toString(), "day");
        // 日完成产量
        BigDecimal finishDayCount = BigDecimal.ZERO;
        if (sumDayWeight != null) {
            finishDayCount = sumDayWeight;
        }
        // 查询电厂皮带生产数据
        if (date.equals(LocalDate.now())) { // 当天电厂皮带数据
            MesProduceDcpd dcpdToday = produce901Service.getDcpdQuantityToday();
            if (dcpdToday != null && dcpdToday.getQuantity() != null) {
                finishDayCount = finishDayCount.add(BigDecimal.valueOf(dcpdToday.getQuantity()));
            }
        } else {
            LambdaQueryWrapper<MesProduceDcpd> pDayWrapper = new LambdaQueryWrapper<>();
            pDayWrapper.eq(MesProduceDcpd::getStatsDate, date);
            List<MesProduceDcpd> produce901List = produce901Service.list(pDayWrapper);
            if (CollectionUtils.isNotEmpty(produce901List)) {
                MesProduceDcpd produce901 = produce901List.get(0);
                finishDayCount = finishDayCount.add(BigDecimal.valueOf(produce901.getQuantity()));
            }
        }
        result.setFinishCount(BigDecimal.valueOf(Math.ceil(finishDayCount.doubleValue())));
        // 日完成占比
        if (result.getPlanCount() != null && result.getPlanCount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal finishDayRatio =
                result
                    .getFinishCount()
                    .divide(result.getPlanCount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            result.setFinishRatio(finishDayRatio);
        }
        // 日超欠：日完成-日计划
        if (result.getPlanCount() != null && result.getFinishCount() != null) {
            result.setOverdue(result.getFinishCount().subtract(result.getPlanCount()));
        }

        return result;
    }

    @Override
    public List<MesSwProduceFinishDayCountVO> getProduceFinishDayCountList(Integer days) {
        List<MesSwProduceFinishDayCountVO> result = Lists.newArrayList();
        for (int i = 1; i <= days; i++) {
            LocalDate localDate = LocalDate.now().minusDays(i);
            MesSwProduceFinishDayCountVO dayCount = this.getProduceFinishDayCount(localDate);
            dayCount.setDate(localDate);
            if (dayCount.getFinishCount() == null) {
                dayCount.setFinishCount(BigDecimal.ZERO);
            }
            result.add(dayCount);
        }

        return result.stream()
            .sorted(Comparator.comparing(MesSwProduceFinishDayCountVO::getDate))
            .collect(Collectors.toList());
    }

    @Override
    public MesSwProduceFinishMonthCountVO getProduceFinishMonthCount(String month) {
        if (StringUtils.isBlank(month)) {
            return null;
        }
        MesSwProduceFinishMonthCountVO result = new MesSwProduceFinishMonthCountVO();
        // 查询当月生产计划
        MesSwProducePlanParam monthPlanParam = new MesSwProducePlanParam();
        monthPlanParam.setPlanTimeStr(month);
        List<MesSwProducePlan> planList = this.queryList(monthPlanParam);
        if (CollectionUtils.isNotEmpty(planList)) {
            MesSwProducePlan producePlan = planList.get(0);
            // 月计划
            result.setPlanCount(BigDecimal.valueOf(producePlan.getTotal()));
        }

        // 月完成产量
        BigDecimal finishMonthCount = BigDecimal.ZERO;
        // 查询当月装车数据
        LambdaQueryWrapper<LoadFinishMonthCount> loadMonthWrapper = new LambdaQueryWrapper<>();
        loadMonthWrapper.eq(LoadFinishMonthCount::getMonthstr, month);
        List<LoadFinishMonthCount> finishMonthCounts =
            loadFinishMonthCountService.list(loadMonthWrapper);
        if (CollectionUtils.isNotEmpty(finishMonthCounts)) {
            LoadFinishMonthCount monthCount = finishMonthCounts.get(0);
            finishMonthCount = monthCount.getFinishTon();
        }
        // 统计当月电厂皮带产量
        LambdaQueryWrapper<MesProduceDcpd> pMonthWrapper = new LambdaQueryWrapper<>();
        pMonthWrapper.like(MesProduceDcpd::getStatsDate, month);
        List<MesProduceDcpd> month901List = produce901Service.list(pMonthWrapper);
        if (CollectionUtils.isNotEmpty(month901List)) {
            double monthCount =
                month901List.stream()
                    .mapToDouble(data -> null == data.getQuantity() ? 0 : data.getQuantity())
                    .sum();
            finishMonthCount = finishMonthCount.add(BigDecimal.valueOf(monthCount));
        }
        // 单位吨转万吨
        result.setFinishCount(
            BigDecimal.valueOf(Math.ceil(finishMonthCount.doubleValue()))
                .divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP));
        // 月完成占比
        if (result.getPlanCount() != null && result.getPlanCount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal finishMonthRatio =
                result
                    .getFinishCount()
                    .divide(result.getPlanCount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            result.setFinishRatio(finishMonthRatio);
        }
        if (result.getPlanCount() != null && result.getFinishCount() != null) {
            // 均衡超欠 =月完成-月计划，无计划展示--
            BigDecimal jhcq = result.getFinishCount().subtract(result.getPlanCount());
            result.setJhcq(jhcq);
            // 剩余日均，当月计算
            if (month.equals(DateUtil.getSysYearMonth())) {
                // 获取剩余天数
                Integer days = DateUtil.remainingDaysInMonth();
                if (days > 0) {
                    BigDecimal syrj = jhcq.divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
                    result.setSyrj(syrj);
                }
            }
        }

        return result;
    }

    @Override
    public List<MesSwProduceFinishMonthCountVO> getProduceFinishMonthCountList(Integer months) {
        List<MesSwProduceFinishMonthCountVO> result = Lists.newArrayList();
        // 获取近半年所有月份
        List<String> monthList =
            DateUtil.dateMonthBetween(DateUtil.minusMonth(months), DateUtil.getSysYearMonth());
        monthList.forEach(
            month -> {
                MesSwProduceFinishMonthCountVO countVO = new MesSwProduceFinishMonthCountVO();
                MesSwProduceFinishMonthCountVO monthCount = this.getProduceFinishMonthCount(month);
                countVO.setMonth(month);
                countVO.setPlanCount(
                    monthCount.getPlanCount() == null ? BigDecimal.ZERO : monthCount.getPlanCount());
                countVO.setFinishCount(
                    monthCount.getFinishCount() == null ? BigDecimal.ZERO : monthCount.getFinishCount());
                countVO.setJhcq(monthCount.getJhcq());
                countVO.setSyrj(monthCount.getSyrj());
                result.add(countVO);
            });

        return result.stream()
            .sorted(Comparator.comparing(MesSwProduceFinishMonthCountVO::getMonth))
            .collect(Collectors.toList());
    }

    @Override
    public MesSwProduceFinishMonthCountVO getProduceFinishYearCount() {
        MesSwProduceFinishMonthCountVO result = new MesSwProduceFinishMonthCountVO();
        // 查询当年生产计划
        MesSwProducePlanParam yearPlanParam = new MesSwProducePlanParam();
        String yearMonth = DateUtil.getSysYearMonth();
        String[] split = yearMonth.split("-");
        yearPlanParam.setPlanTimeStr(split[0]);
        List<MesSwProducePlan> planYearList = this.queryList(yearPlanParam);
        if (CollectionUtils.isNotEmpty(planYearList)) {
            MesSwProducePlan producePlan = planYearList.get(0);
            // 年计划
            result.setPlanCount(BigDecimal.valueOf(producePlan.getTotal()));
        }
        // 查询年装车完成净重
        BigDecimal sumYearWeight = trainloadService.sumWeight(split[0], "year");
        // 年完成产量
        BigDecimal finishYearCount = BigDecimal.ZERO;
        if (sumYearWeight != null) {
            finishYearCount = sumYearWeight;
        }
        // 查询电厂皮带生产数据
        LambdaQueryWrapper<MesProduceDcpd> pYearWrapper = new LambdaQueryWrapper<>();
        pYearWrapper.like(MesProduceDcpd::getStatsDate, split[0]);
        List<MesProduceDcpd> produceYear901List = produce901Service.list(pYearWrapper);
        if (CollectionUtils.isNotEmpty(produceYear901List)) {
            double yearProduceCount =
                produceYear901List.stream()
                    .mapToDouble(data -> null == data.getQuantity() ? 0 : data.getQuantity())
                    .sum();
            finishYearCount = finishYearCount.add(BigDecimal.valueOf(yearProduceCount));
        }
        result.setFinishCount(
            finishYearCount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP));
        // 年完成占比
        if (result.getPlanCount() != null && result.getPlanCount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal finishYearRatio =
                result
                    .getFinishCount()
                    .divide(result.getPlanCount(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            result.setFinishRatio(finishYearRatio);
        }
        if (result.getPlanCount() != null && result.getFinishCount() != null) {
            // 均衡超欠 =完成-计划，无计划展示--
            BigDecimal jhcq = result.getFinishCount().subtract(result.getPlanCount());
            result.setJhcq(jhcq);
            // 剩余日均，当年计算
            // 获取剩余天数
            Integer days = DateUtil.remainingDaysInYear();
            BigDecimal syrj = jhcq.divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
            result.setSyrj(syrj);
        }

        return result;
    }

    @Override
    public List<MesSwProduceCoalTypeCountVO> getProduceCoalTypeCount() {
        List<MesSwProduceCoalTypeCountVO> result = Lists.newArrayList();
        // 查询装车煤种数据列表
        List<LoadSumcoaltype> loadCoalTypes = sumcoaltypeService.list();
        if (CollectionUtils.isNotEmpty(loadCoalTypes)) {
            loadCoalTypes.forEach(
                v -> {
                    MesSwProduceCoalTypeCountVO countVO = new MesSwProduceCoalTypeCountVO();
                    countVO.setType(v.getTllCoaltypedesc());
                    countVO.setValue(v.getTllTotalnum());
                    result.add(countVO);
                });
        }
        // 查询电厂皮带-电厂混煤
        // 查询电厂皮带生产数据
        String yearMonth = DateUtil.getSysYearMonth();
        String[] split = yearMonth.split("-");
        LambdaQueryWrapper<MesProduceDcpd> pYearWrapper = new LambdaQueryWrapper<>();
        pYearWrapper.like(MesProduceDcpd::getStatsDate, split[0]);
        List<MesProduceDcpd> produceYear901List = produce901Service.list(pYearWrapper);
        if (CollectionUtils.isNotEmpty(produceYear901List)) {
            double yearProduceCount =
                produceYear901List.stream()
                    .mapToDouble(data -> null == data.getQuantity() ? 0 : data.getQuantity())
                    .sum();
            MesSwProduceCoalTypeCountVO countVO = new MesSwProduceCoalTypeCountVO();
            countVO.setType("电厂混煤");
            countVO.setValue(
                BigDecimal.valueOf(yearProduceCount)
                    .divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP));
            result.add(countVO);
        }

        return result;
    }

    @Override
    public List<MesSwProduceFinishMonthCountVO> getLoadFinishMonthCountList(Integer months) {
        List<MesSwProduceFinishMonthCountVO> result = Lists.newArrayList();
        // 获取近半年所有月份
        // 查询当月装车数据
        LambdaQueryWrapper<LoadFinishMonthCount> loadMonthWrapper = new LambdaQueryWrapper<>();
        List<LoadFinishMonthCount> loadCountList = loadFinishMonthCountService.list(loadMonthWrapper);
        List<LoadFinishMonthCount> halfLoadCountList =
            loadCountList.stream()
                .limit(months)
                .sorted(Comparator.comparing(LoadFinishMonthCount::getMonthstr))
                .collect(Collectors.toList());
        halfLoadCountList.forEach(
            v -> {
                MesSwProduceFinishMonthCountVO countVO = new MesSwProduceFinishMonthCountVO();
                countVO.setMonth(v.getMonthstr());
                // 装车完成产量，万吨
                countVO.setFinishCount(
                    v.getFinishTon().divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP));
                // 装车计划，万吨
                BigDecimal planCount = BigDecimal.ZERO;
                // 查询当月生产计划
                MesSwProducePlanParam monthPlanParam = new MesSwProducePlanParam();
                monthPlanParam.setPlanTimeStr(v.getMonthstr());
                List<MesSwProducePlan> planList = this.queryList(monthPlanParam);
                if (CollectionUtils.isNotEmpty(planList)) {
                    MesSwProducePlan producePlan = planList.get(0);
                    planCount = BigDecimal.valueOf(producePlan.getYthwy());
                    // 当月，统计均衡超欠，剩余日均
                    if (v.getMonthstr().equals(DateUtil.getSysYearMonth())) {
                        // 均衡超欠
                        BigDecimal jhcq = countVO.getFinishCount().subtract(planCount);
                        countVO.setJhcq(jhcq);
                        // 剩余日均
                        // 获取剩余天数
                        Integer days = DateUtil.remainingDaysInMonth();
                        if (days > 0) {
                            BigDecimal syrj = jhcq.divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
                            countVO.setSyrj(syrj);
                        }
                    }
                }
                countVO.setPlanCount(planCount);

                result.add(countVO);
            });

        return result;
    }
}
