package com.yhd.admin.bms.service.flowable;

import com.yhd.admin.bms.domain.vo.flowable.*;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.flowable.engine.runtime.ProcessInstance;

import java.io.InputStream;
import java.util.List;

/** 流程实例 */
public interface IFlowableProcessInstanceService {
  RespJson<ProcessInstance> startProcessInstanceByKey(
      StartProcessInstanceVo startProcessInstanceVo);

  RespJson<String> revokeProcess(RevokeProcessVo params);

  InputStream createImage(ProcessInstanceQueryVo param);

  PageRespJson<ProcessInstanceVo> getMyProcessInstances(ProcessInstanceQueryVo params);

  /**
   * 终止流程
   *
   * @param endVo 参数
   * @return
   */
  public RespJson<String> stopProcessInstanceById(EndProcessVo endVo);

  /**
   * 删除流程实例
   *
   * @param processInstanceId 流程实例id
   * @return
   */
  public RespJson<String> deleteProcessInstanceById(String processInstanceId);

  void deleteProcessInstanceByIdS(List<String> processInstanceIdS);
}
