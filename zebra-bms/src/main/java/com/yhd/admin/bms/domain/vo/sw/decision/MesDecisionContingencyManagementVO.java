package com.yhd.admin.bms.domain.vo.sw.decision;

import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.bms.domain.dto.sw.decision.MesDecisionContingencyManagementDTO;
import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 智能决策中心-应急管理政策
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-22
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MesDecisionContingencyManagementVO extends BaseVO implements Cloneable, Serializable {

	/**
	* 父类id,一级分类为0
	*/
	private Long parentId;

	/**
	* 政策名称
	*/
	private String policyName;

	/**
	* 是否有二级目录（0：否/1:是）
	*/
	private Integer ifSecondList;

	/**
	* 文件类型
	*/
	private String fileType;

	/**
	* 文件类型name
	*/
	private String fileTypeName;

	/**
	* 富文本
	*/
	private String richText;

    /**
     * 附件
     */
    private List<JSONObject> annex;

    /**
     * 子集
     */
    private List<MesDecisionContingencyManagementDTO> childList;

}
