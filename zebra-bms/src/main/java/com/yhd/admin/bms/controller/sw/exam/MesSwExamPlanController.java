package com.yhd.admin.bms.controller.sw.exam;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwSafetyTrainingPlanConvert;
import com.yhd.admin.bms.domain.convert.sw.exam.MesSwExamPlanConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwSafetyTrainingPlanDTO;
import com.yhd.admin.bms.domain.dto.sw.exam.MesSwExamPlanDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwSafetyTrainingPlanParam;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwExamPlanParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwSafetyTrainingPlanVO;
import com.yhd.admin.bms.domain.vo.sw.exam.MesInAndOutDeptVO;
import com.yhd.admin.bms.domain.vo.sw.exam.MesSwExamPlanVO;
import com.yhd.admin.bms.service.sw.exam.MesSwExamPlanService;
import com.yhd.admin.common.annotation.SysLogs;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.eums.BusinessType;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 安全考试计划
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-01
 */
@RestController
@RequestMapping("/exam/plan")
public class MesSwExamPlanController {


    @Resource
    private MesSwExamPlanConvert convert;

    @Resource
    private MesSwExamPlanService service;

    @Resource
    private MesSwSafetyTrainingPlanConvert swSafetyTrainingPlanConvert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwExamPlanVO> pagingQuery(@RequestBody MesSwExamPlanParam queryParam) {
        IPage<MesSwExamPlanDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MesSwExamPlanParam param) {
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
    }

    @SysLogs(title = "安全考试计划表新增", businessType = BusinessType.INSERT)
    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MesSwExamPlanParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "安全考试计划表修改", businessType = BusinessType.UPDATE)
    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MesSwExamPlanParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    @SysLogs(title = "安全考试计划表删除", businessType = BusinessType.DELETE)
    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
    }

    /**
     * @return
     * @Description:查询安全培训计划
     */
    @PostMapping(
        value = "/getBySafePlanList"
    )
    public RespJson<List<MesSwSafetyTrainingPlanVO>> getBySafePlanList() {
        List<MesSwSafetyTrainingPlanDTO> mesSwSafetyTrainingPlanVOList = service.getBySafePlanList();
        return RespJson.buildSuccessResponse(swSafetyTrainingPlanConvert.toVOList(mesSwSafetyTrainingPlanVOList));
    }

    /**
     * @return
     * @Description:根据培训计划查询考生来源
     */
    @PostMapping(
        value = "/getByExamineeList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public RespJson<?> getByExamineeList(@RequestBody MesSwSafetyTrainingPlanParam mesSwSafetyTrainingPlanParam) {
        MesSwSafetyTrainingPlanDTO mesSwSafetyTrainingPlanVOList = service.getByExamieesList(mesSwSafetyTrainingPlanParam);
        MesSwSafetyTrainingPlanVO voList = swSafetyTrainingPlanConvert.toVO(mesSwSafetyTrainingPlanVOList);
        return RespJson.buildSuccessResponse(voList);
    }

    /**
     * @return
     * @Description:查询厂部人员和承包商（外部）
     */
    @PostMapping(
        value = "/getByInAndOutDeptList"
    )
    public RespJson<?> getByInAndOutDeptList() {
        List<MesInAndOutDeptVO> mesInAndOutDeptVOS = service.getByInAndOutDeptList();
        return RespJson.buildSuccessResponse(mesInAndOutDeptVOS);
    }


    /**
     * @return
     * @Description:发布考试接口
     */
    @PostMapping(
        value = "/publishExam",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public RespJson<?> publishExam(@RequestBody MesSwExamPlanParam param) {
        return RespJson.buildSuccessResponse(service.publishExam(param));
    }

}
