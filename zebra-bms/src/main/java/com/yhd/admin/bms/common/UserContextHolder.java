package com.yhd.admin.bms.common;

import com.yhd.admin.bms.config.userdetails.AdminUserDetail;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public class UserContextHolder {

  public static AdminUserDetail getUserDetail() {

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    return (AdminUserDetail) authentication.getPrincipal();
  }

  public static UserDTO getUserInfo() {
    return getUserDetail().getUserInfo();
  }
}
