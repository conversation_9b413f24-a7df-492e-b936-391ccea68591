package com.yhd.admin.bms.domain.convert.sw;

import com.yhd.admin.bms.domain.dto.sw.MesSwEarlyMeetingDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwEarlyMeeting;
import com.yhd.admin.bms.domain.query.sw.MesSwEarlyMeetingParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwEarlyMeetingVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description : TODO
 * @Date 2023/10/10 09:56
 **/

@Mapper(componentModel = "spring")
public interface MesSwEarlyMeetingConvert {
  MesSwEarlyMeeting toEntity(MesSwEarlyMeetingParam param);

  MesSwEarlyMeetingDTO toDTO(MesSwEarlyMeeting earlyMeeting);

  MesSwEarlyMeetingVO toVO(MesSwEarlyMeetingDTO earlyMeetingDTO);

}
