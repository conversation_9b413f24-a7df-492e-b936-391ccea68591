package com.yhd.admin.bms.domain.vo.sw.safe;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全培训课程信息
 *
 * <AUTHOR>
 * @date 2024/1/4 16:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwSafeTrainCourseVO extends BaseVO implements Serializable {
  private static final long serialVersionUID = 2268496237374331138L;

  /** 培训计划主键id */
  private Long planId;
  /** 培训教师，多个逗号分割 */
  private String trainTeacher;
  /** 培训教师姓名，多个逗号分割 */
  private String trainTeacherName;
  /** 培训日期(yyyy-MM-dd) */
  private LocalDate trainDate;
  /** 培训内容 */
  private String trainContent;
  /** 培训学员类型：厂内人员、承包商人员 */
  private String trainStudentType;
  /** 培训学员组织ID，多个逗号分割 */
  private String trainDepartment;
  /** 培训学员ID，多个逗号分割 */
  private String trainStudentId;
  /** 学员人数 */
  private Integer studentNumber;
  /** 签到人数 */
  private Integer signNumber;
  /** 签到率 */
  private BigDecimal signRate;
  /** 课程时间(mm:ss),时分字符串 */
  private String courseTime;
  /** 发起签到时间 */
  private LocalDateTime startSignTime;
  /** 停止签到时间 */
  private LocalDateTime endSignTime;
  /** 签到教师账号 */
  private String signTeacherCode;
  /** 签到教师姓名 */
  private String signTeacherName;
  /** 签到教师签名url */
  private String signTeacherUrl;
  /** 教师签到时间 */
  private LocalDateTime teacherSignTime;
  /** 课程状态,默认课程未签到：0课程未签到、1课程已签到、2开启签到、3停止签到。签到详情页面=2显示停止按钮，其他显示停止按钮 */
  private String courseStatus;

  private String courseStatusName;
  /** 学员是否完成签到,默认未完成：0未完成、1已完成 */
  private String signFinish;
  /** 学员签到状态（移动端）,默认未完成：0未完成、1已完成 */
  private String studentSignStatus;


  private String signFinishName;

  /** 当前用户能否点击教师签到按钮：true可以，false不可以 */
  private boolean teacherSignClick = false;
  /** 当前用户能否点击发起/停止签到按钮：true可以，false不可以 */
  private boolean startEndSignClick = false;
  /** 学员签到信息列表 ,组织分组 */
  private List<MesSwSafeTrainOrgGroupStudentSignVO> orgGroupSignList;
}
