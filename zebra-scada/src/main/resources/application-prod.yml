spring:
    datasource:
        dynamic:
            primary: mysql
            strict: false
            datasource:
                mysql:
                    driver-class-name: com.mysql.cj.jdbc.Driver
                    url: ****************************************************************************************************
                    username: sw_admin
                    password: XLvgt_2v*Xh
                    hikari:
                        minimum-idle: 5
                        # 空闲连接存活最大时间，默认600000（10分钟）
                        idle-timeout: 180000
                        # 连接池最大连接数，默认是10
                        maximum-pool-size: 10
                        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
                        is-auto-commit: true
                        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
                        max-lifetime: 30000
                        # 数据库连接超时时间,默认30秒，即30000
                        connection-timeout: 30000
                        connection-test-query: SELECT 1
                pgsql:
                    url: *****************************************************************************************************************************
                    username: postgres
                    password: password
                    driver-class-name: org.postgresql.Driver
                    hikari:
                        minimum-idle: 5
                        # 空闲连接存活最大时间，默认600000（10分钟）
                        idle-timeout: 180000
                        # 连接池最大连接数，默认是10
                        maximum-pool-size: 10
                        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
                        is-auto-commit: true
                        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
                        max-lifetime: 30000
                        # 数据库连接超时时间,默认30秒，即30000
                        connection-timeout: 30000
                        connection-test-query: SELECT 1
    redis:
        host: ************
        port: 6379
        timeout: 60s
        password: Yhd@2024
        lettuce:
            pool:
                max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
                max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-idle: 10      # 连接池中的最大空闲连接
                min-idle: 5       # 连接池中的最小空闲连接
        database: 1
        #kafka:
        #bootstrap-servers: ***********:9092,***********:9092,***********:9092,***********:9092

server:
    port: 9093
security:
    oauth2:
        resource:
            id: scada
            token-info-uri: http://************:9115/passport/oauth/check_token
        client:
            access-token-uri: http://************:9115/passport/oauth/token
            client-id: bms
            client-secret: 123456
            grant-type: authorization_code,password,refresh_token
            scope: all

oss:
  server:
    endpoint: http://************
    port: 5000
    access_key: admin
    secret_key: yhd@123456
    bucket_name: avatar

opc:
  ua:
    end_point_url: opc.tcp://10.248.56.206:37800

wenzhen:
  opcua:
    end_point_url: opc.tcp://10.248.56.201:4842/sdpyopcua/server/

bms:
  url: http://************:9114/bms

