package com.yhd.admin.bms.domain.vo.sw.ticket;

import com.yhd.admin.bms.domain.vo.sys.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 登高作业工作票签字用户信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesSwWorkTicketAscentSignUserVO extends BaseVO implements Cloneable, Serializable {


    /**
     * 承包商施工工作票主键id
     */
    private Long ticketId;

    /**
     * 类型
     */
    private String type;

    /**
     * 签字用户类型
     */
    private String userType;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 签字时间(yyyy-MM-dd hh:mm:ss)
     */
    private LocalDateTime signTime;

    /**
     * 签字图片url
     */
    private String signUrl;

    /**
     * 审批意见
     */
    private String spOpinion;

    /**
     * 是否完成签字：0false,1true
     */
    private Boolean isSign;

    /**
     * 是否有权限审批
     */
    private Boolean isAuthority;
}
