package com.yhd.admin.bms.service.sw.impl.decision;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.bms.dao.sw.decision.MesDecisionContingencyManagementDao;
import com.yhd.admin.bms.domain.convert.sw.decision.MesDecisionContingencyManagementConvert;
import com.yhd.admin.bms.domain.dto.sw.decision.MesDecisionContingencyManagementDTO;
import com.yhd.admin.bms.domain.entity.sw.decision.MesDecisionContingencyManagement;
import com.yhd.admin.bms.domain.query.sw.decision.MesDecisionContingencyManagementParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.decision.MesDecisionContingencyManagementService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.bms.service.sys.FileService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;


/**
 * 智能决策中心-应急管理政策
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-22
 */
@Service
public class MesDecisionContingencyManagementServiceImpl extends ServiceImpl<MesDecisionContingencyManagementDao, MesDecisionContingencyManagement> implements MesDecisionContingencyManagementService {

    @Resource
    private MesDecisionContingencyManagementConvert convert;

    @Resource
    private FileService fileService;

    @Resource
    private DicService dicService;

    private static final String BUSINESS_TYPE = "应急管理政策";
    private static final String FILE_TYPE = "附件";
    // 文件类型字典项
    private static final String CONTINGENCY_FILE_TYPE = "CONTINGENCY_FILE_TYPE";

    @Override
    public Boolean add(MesDecisionContingencyManagementParam param) {
        if (Objects.isNull(param.getPolicyName())){
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "缺少参数，请检查！");
        }
        if (Objects.isNull(param.getParentId())){
            param.setParentId(0L);
        }
        if (StringUtils.isNotBlank(param.getFileType())){
            param.setFileTypeName(dicService.transform(CONTINGENCY_FILE_TYPE, param.getFileType()));
        }
        MesDecisionContingencyManagement entity = convert.toEntity(param);
        Boolean save = this.save(entity);
        if (save){
            if (Objects.nonNull(param.getFileType()) && "pdf文件".equals(param.getFileType())){
                fileService.insertFile(entity.getId(), BUSINESS_TYPE, FILE_TYPE, param.getAnnex());
            }
        }
        return save;
    }

    @Override
    public Boolean modify(MesDecisionContingencyManagementParam param) {
        if (Objects.isNull(param.getId())){
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "缺少参数，请检查！");
        }
        if (!CollectionUtil.isEmpty(param.getAnnex())){
            //保存附件
            fileService.insertFile(param.getId(), BUSINESS_TYPE, FILE_TYPE, param.getAnnex());
        } else {
            //如果为空，删除附件
            fileService.removeFile(param.getId(), BUSINESS_TYPE, FILE_TYPE);
        }
        MesDecisionContingencyManagement entity = convert.toEntity(param);
        return this.updateById(entity);
    }

    @Override
    public List<MesDecisionContingencyManagementDTO> getTree(MesDecisionContingencyManagementParam param) {
        LambdaQueryWrapper<MesDecisionContingencyManagement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesDecisionContingencyManagement::getParentId,0L);
        List<MesDecisionContingencyManagementDTO> dtos = convert.toDTOS(this.list(wrapper));
        if (!CollectionUtils.isEmpty(dtos)){
            dtos.forEach(dto -> {
                if ("pdf文件".equals(dto.getFileType())){
                    List<JSONObject> fileList = fileService.getFileList(dto.getId(), BUSINESS_TYPE, FILE_TYPE);
                    dto.setAnnex(fileList);
                }
                dto.setChildList(getChildren(dto.getId()));
            });
        }
        return dtos;
    }

    public List<MesDecisionContingencyManagementDTO> getChildren(Long id) {
        LambdaQueryWrapper<MesDecisionContingencyManagement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesDecisionContingencyManagement::getParentId,id);
        List<MesDecisionContingencyManagementDTO> dtos = convert.toDTOS(this.list(wrapper));
        dtos.forEach(dto -> {
            if ("pdf文件".equals(dto.getFileType())){
                List<JSONObject> fileList = fileService.getFileList(dto.getId(), BUSINESS_TYPE, FILE_TYPE);
                dto.setAnnex(fileList);
            }
        });
        return dtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        param.getId().forEach(id -> {
            MesDecisionContingencyManagement byId = this.getById(id);
            //有附件的要删除
            if ("pdf文件".equals(byId.getFileType())){
                fileService.removeFile(id, BUSINESS_TYPE);
            }
            //一级目录下子目录删除
            if (byId.getParentId() == 0){
                LambdaQueryWrapper<MesDecisionContingencyManagement> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MesDecisionContingencyManagement::getParentId, id);
                this.remove(wrapper);
            }
        });
        return this.removeByIds(param.getId());
    }

}
