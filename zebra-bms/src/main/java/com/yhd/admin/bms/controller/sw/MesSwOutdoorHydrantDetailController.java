package com.yhd.admin.bms.controller.sw;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.MesSwOutdoorHydrantDetailConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwOutdoorHydrantDetailDTO;
import com.yhd.admin.bms.domain.query.sw.MesSwOutdoorHydrantDetailParam;
import com.yhd.admin.bms.domain.vo.sw.MesSwOutdoorHydrantDetailVO;
import com.yhd.admin.bms.service.sw.MesSwOutdoorHydrantDetailService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <p>
 * 室外消火栓试验维护记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@RestController
@RequestMapping(value = "/outdoorHydrantDetail")
public class MesSwOutdoorHydrantDetailController {

    /**
     * service
     */
    @Resource
    private MesSwOutdoorHydrantDetailService service;

    @Resource
    private MesSwOutdoorHydrantDetailConvert convert;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesSwOutdoorHydrantDetailVO> pagingQuery(@RequestBody MesSwOutdoorHydrantDetailParam queryParam) {
        IPage<MesSwOutdoorHydrantDetailDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MesSwOutdoorHydrantDetailParam queryParam, OAuth2Authentication authentication) {
//    queryParam.setCreatedBy(authentication.getName());
    service.add(queryParam);
    return RespJson.buildSuccessResponse();
    }

    @PostMapping(
          value = "/getCurrentDetail",
          consumes = MediaType.APPLICATION_JSON_VALUE,
          produces = MediaType.APPLICATION_JSON_VALUE)
      public RespJson getCurrentDetail(@RequestBody MesSwOutdoorHydrantDetailParam queryParam) {
        service.getCurrentDetail(queryParam);
        return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(queryParam)));
      }
}
