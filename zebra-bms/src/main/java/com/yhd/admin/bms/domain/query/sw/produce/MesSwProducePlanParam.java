package com.yhd.admin.bms.domain.query.sw.produce;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 生产计划
 *
 * <AUTHOR>
 * @date 2025/1/4 10:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwProducePlanParam extends QueryParam implements Serializable {
    private static final long serialVersionUID = -1429747868223567832L;

    /**
     * 计划类型：月计划、年计划
     */
    private String planType;
    /**
     * 计划时间
     */
    private String planTimeStr;
    /**
     * 一体化外运
     */
    private Long ythwy;
    /**
     * 直供用户
     */
    private Long zgyh;
    /**
     * 总计划
     */
    private Long total;

    private LocalDate startDate;
    private LocalDate endDate;

    private String startMonth;
    private String endMonth;

    private String startYear;
    private String endYear;
}
