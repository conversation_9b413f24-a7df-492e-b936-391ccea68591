package com.yhd.admin.bms.domain.convert.sw.consumption;

import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionElectricArtificialDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionElectricArtificial;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionElectricArtificialParam;
import com.yhd.admin.bms.domain.vo.sw.consumption.MesConsumptionElectricArtificialVO;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesConsumptionElectricArtificialConvert {

  MesConsumptionElectricArtificial toEntity(MesConsumptionElectricArtificialParam param);

  MesConsumptionElectricArtificialDTO toDTO(MesConsumptionElectricArtificial entity);

  List<MesConsumptionElectricArtificialDTO> toListDTO(
      List<MesConsumptionElectricArtificial> entity);

  MesConsumptionElectricArtificialVO toVO(MesConsumptionElectricArtificialDTO entity);

  List<MesConsumptionElectricArtificialVO> toListVO(
      List<MesConsumptionElectricArtificialDTO> entity);
}
