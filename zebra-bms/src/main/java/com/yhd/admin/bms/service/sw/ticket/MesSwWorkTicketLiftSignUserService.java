package com.yhd.admin.bms.service.sw.ticket;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketLiftSignUser;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketLiftSignUserParam;

import java.util.List;

/**
 * 吊装作业许可证-签字表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-28
 */
public interface MesSwWorkTicketLiftSignUserService extends IService<MesSwWorkTicketLiftSignUser> {

    Boolean approval(MesSwWorkTicketLiftSignUserParam param);
}
