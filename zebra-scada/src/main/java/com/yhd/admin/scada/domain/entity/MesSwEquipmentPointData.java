package com.yhd.admin.scada.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备资料-温振报警点位数据
 *
 * <AUTHOR>
 * @date 2025/2/07 10:22
 */
@Data
public class MesSwEquipmentPointData implements Cloneable, Serializable {
  @TableId(type = IdType.AUTO) // 自增类型
  private Long id;

  private Long pointId;
  /** 点位 */
  private String pointAddress;

  private LocalDateTime valueTime;
  /** 点位值 */
  private String pointValue;
  /** 缺陷Id */
  private Long flawId;
  /** 报警状态：warn_no，不报警；warn_low，低于报警；warn_high，高于报警； */
  private String warnStatus;
  /** 处理状态：为空，没有处理，ok，已处理 */
  private String handlerStatus;

  /** 创建人 */
  @TableField(fill = FieldFill.INSERT)
  private String createdBy;
  /** 创建时间 */
  @TableField(updateStrategy = FieldStrategy.NEVER, insertStrategy = FieldStrategy.NEVER)
  private LocalDateTime createdTime;
  /** 更新人 */
  @TableField(fill = FieldFill.UPDATE)
  private String updatedBy;
  /** 更新时间 */
  @TableField(updateStrategy = FieldStrategy.NEVER, insertStrategy = FieldStrategy.NEVER)
  private LocalDateTime updatedTime;
}
