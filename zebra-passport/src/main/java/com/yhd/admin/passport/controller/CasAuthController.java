package com.yhd.admin.passport.controller;

import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.passport.auth.config.properties.CasAuthProperties;
import com.yhd.admin.passport.auth.token.CustomTokenServices;
import com.yhd.admin.passport.service.UserAccountService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * cas统一授权身份认证
 *
 * <AUTHOR>
 * @date 2024/4/7 9:31
 */
@RestController
@RequestMapping("/oauth/cas")
public class CasAuthController {
    private static final Logger logger = LoggerFactory.getLogger(CasAuthController.class);

    @Resource
    @Qualifier("customTokenServices")
    private CustomTokenServices customTokenServices;
    @Autowired
    private CasAuthProperties casAuthProperties;
    @Autowired
    private UserAccountService userAccountService;

    @GetMapping(value = "/login")
    public void authLogin(HttpServletRequest request, HttpServletResponse response)
        throws IOException {

        String jobNum = request.getRemoteUser();
        if (StringUtils.isBlank(jobNum)) {
            throw new UsernameNotFoundException("用户不存在");
        }
        logger.debug("用户工号：{}，cas认证登录成功,跳转到一体化管控平台", jobNum);
        //查询系统是否存在此工号
        Optional<UserAccountDTO> accountOptional = userAccountService.getUserAccount(jobNum);
        if (accountOptional.isEmpty()) {
            throw new UsernameNotFoundException("用户不存在");
        }
        UserAccountDTO userAccount = accountOptional.get();
        // 生成新的token
        OAuth2AccessToken accessToken = customTokenServices.createTokenByUsername(userAccount.getUsername(), "bms");
        logger.debug("--->>>>>用户：工号{}，用户名{}，一体化管控平台oauth2认证成功，生成token：{}", jobNum, userAccount.getUsername(),
            accessToken);

        // 跳转页面处理，拼接token
        StringBuffer sb = new StringBuffer();
        sb.append(casAuthProperties.getAppRedirectUrl()).append("?token=").append(accessToken.getValue());
        logger.debug("跳转页面地址：{}", sb);

        // 免登录处理,携带token重定向到一体化管控平台，前端页面
        response.sendRedirect(sb.toString());
    }

    @GetMapping(value = {"/logout"}, produces = {"application/json;charset=UTF-8"})
    public void logout(@RequestParam String token, HttpServletRequest request,
                       HttpServletResponse response) throws IOException {
        if (casAuthProperties.isOpen()) {
            logger.debug("退出登录，销毁会话开始");

            // 1. 销毁OAuth2 token
            boolean revokeToken = customTokenServices.revokeToken(token);
            logger.debug("退出登录，销毁oauth2结果：{}", revokeToken);

            // 2. 清除安全上下文
            SecurityContextHolder.clearContext();

            // 3. 销毁当前会话
            if (request.getSession(false) != null) {
                request.getSession().invalidate();
            }

            // 4. 清除所有cookies
            if (request.getCookies() != null) {
                for (javax.servlet.http.Cookie cookie : request.getCookies()) {
                    cookie.setValue("");
                    cookie.setPath("/");
                    cookie.setMaxAge(0);
                    response.addCookie(cookie);
                }
            }

            // 5. 构建CAS登出URL并重定向
            StringBuilder logoutUrl = new StringBuilder();
            logoutUrl.append(casAuthProperties.getServerLogoutUrl())
                    .append(casAuthProperties.getAppUrl())
                    .append("/passport")
                    .append(casAuthProperties.getAppLoginUrl());
            logger.debug("退出登录跳转地址：{}", logoutUrl);
//            response.sendRedirect(logoutUrl.toString());
        }
    }

    @GetMapping("/cas-login")
    public void startCasLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String serviceUrl = "http://localhost:9091/passport/oauth/cas/login";
        String loginUrl = "https://sdzk.shendong.vip/sdzksso/proxy.login" + "?service=" + URLEncoder.encode(serviceUrl, StandardCharsets.UTF_8);
        response.sendRedirect(loginUrl.toString());
    }

    @GetMapping("/cas-logout")
    public void startCasLogout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String serviceUrl = "http://localhost:9091/passport/oauth/cas/login";
        String loginUrl = "https://sdzk.shendong.vip/sdzksso/proxy.login" + "?service=" + URLEncoder.encode(casAuthProperties.getAppRedirectUrl(), StandardCharsets.UTF_8);
        response.sendRedirect(loginUrl.toString());
    }
}
