package com.yhd.admin.bms.domain.convert.sw.exam;

import com.yhd.admin.bms.domain.dto.sw.exam.MesSwUserMockExamDTO;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwUserMockExam;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwUserMockExamParam;
import com.yhd.admin.bms.domain.vo.sw.exam.MesSwUserMockExamVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesSwUserMockExamConvert {
  MesSwUserMockExam toEntity(MesSwUserMockExamParam param);

  MesSwUserMockExam toEntity(MesSwUserMockExamDTO dto);

  MesSwUserMockExamDTO toDTO(MesSwUserMockExam entity);

  MesSwUserMockExamVO toVO(MesSwUserMockExamDTO dto);

  MesSwUserMockExamVO entityToVO(MesSwUserMockExam entity);
}
