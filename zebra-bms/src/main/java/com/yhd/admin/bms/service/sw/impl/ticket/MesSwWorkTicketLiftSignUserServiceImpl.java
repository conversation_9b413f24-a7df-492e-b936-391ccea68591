package com.yhd.admin.bms.service.sw.impl.ticket;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.bms.common.UserContextHolder;
import com.yhd.admin.bms.dao.sw.ticket.MesSwWorkTicketLiftSignUserDao;
import com.yhd.admin.bms.domain.convert.sw.ticket.MesSwWorkTicketLiftSignUserConvert;
import com.yhd.admin.bms.domain.dto.sys.UserDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConst;
import com.yhd.admin.bms.domain.entity.sw.MesSwWorkTicketConstSignUser;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketLift;
import com.yhd.admin.bms.domain.entity.sw.ticket.MesSwWorkTicketLiftSignUser;
import com.yhd.admin.bms.domain.enums.safe.WorkTicketStatusEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketConstGcEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketConstSpEnum;
import com.yhd.admin.bms.domain.enums.ticket.WorkTicketLiftSpEnum;
import com.yhd.admin.bms.domain.query.sw.ticket.MesSwWorkTicketLiftSignUserParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketLiftService;
import com.yhd.admin.bms.service.sw.ticket.MesSwWorkTicketLiftSignUserService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import com.yhd.admin.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 吊装作业许可证-签字表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-28
 */
@Service
public class MesSwWorkTicketLiftSignUserServiceImpl extends ServiceImpl<MesSwWorkTicketLiftSignUserDao, MesSwWorkTicketLiftSignUser> implements MesSwWorkTicketLiftSignUserService {

    @Resource
    private MesSwWorkTicketLiftSignUserConvert signUserConvert;

    @Resource
    private MesSwWorkTicketLiftService liftService;

    @Resource
    private DicService dicService;

    //审批状态
    private static final String DZ_TICKET_STATUS = "DZ_TICKET_STATUS";

    @Override
    public Boolean approval(MesSwWorkTicketLiftSignUserParam param) {
        if (Objects.isNull(param.getId())
            || StringUtils.isBlank(param.getSpOpinion())
            || StringUtils.isBlank(param.getSignUrl())) {
            throw new BMSException(ResultStateEnum.FAIL.getCode(), "请检查参数是否符合规范");
        }
        MesSwWorkTicketLiftSignUser entity = signUserConvert.toEntity(param);
        if (StringUtils.isNotBlank(param.getSignUrl())){
            entity.setIsSign(true);
        }
        Boolean result = this.updateById(entity);
        // 查询审批用户签字记录
        MesSwWorkTicketLiftSignUser signUser = this.getById(param.getId());
        LambdaQueryWrapper<MesSwWorkTicketLiftSignUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MesSwWorkTicketLiftSignUser::getTicketId,signUser.getTicketId());
        List<MesSwWorkTicketLiftSignUser> signUsers = this.list(wrapper);
        // 审批用户全部签字
        Long spCount = signUsers.stream().filter(v -> v.getIsSign() == false).count();
        if (spCount == 0){
            // 全部签字 修改状态为作业结束签字
            MesSwWorkTicketLift ticketLift = new MesSwWorkTicketLift();
            ticketLift.setId(signUser.getTicketId());
            ticketLift.setStatusCode("2");
            ticketLift.setStatusName(dicService.transform(DZ_TICKET_STATUS, ticketLift.getStatusCode()));
            liftService.updateById(ticketLift);
        }
        return result;
    }
}
