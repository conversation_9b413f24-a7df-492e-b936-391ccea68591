package com.yhd.admin.scada;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/3 09:21
 */

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = RANDOM_PORT)
@Slf4j
@ActiveProfiles("dev")
public class TestScadaApplication {


    private final DataSource iotDataSource;

    private final SqlSessionFactory sqlSessionFactory;

    public TestScadaApplication(@Qualifier("iotDataSource") DataSource iotDataSource, @Qualifier("iotSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        this.iotDataSource = iotDataSource;
        this.sqlSessionFactory = sqlSessionFactory;
    }

    @Test
    public void testIOTDB() throws SQLException {

        ResultSet resultSet = iotDataSource.getConnection().createStatement().executeQuery("select * from root.sz.s7-1500.MD1220");
        log.debug(">>>>>>>>>>>>>>{}", resultSet.getMetaData().getColumnName(1));
        log.debug(">>>>>>>>>>>>>>{}", resultSet.getMetaData().getColumnName(2));
        log.debug(">>>>>>>>>>>>>>{}", resultSet.getMetaData().getColumnName(3));
        while (resultSet.next()) {
            log.debug(">>>>>>{},{},{}", resultSet.getString(1), resultSet.getString(2), resultSet.getString(3));
        }

    }

    @Test
    public void testMybatis() {

    }

}
