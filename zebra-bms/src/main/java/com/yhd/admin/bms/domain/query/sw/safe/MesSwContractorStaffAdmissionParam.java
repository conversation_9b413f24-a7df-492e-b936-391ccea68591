package com.yhd.admin.bms.domain.query.sw.safe;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 承包商员工入场管理
 *
 * <AUTHOR>
 * @date 2024/01/05 18:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwContractorStaffAdmissionParam extends QueryParam
    implements Cloneable, Serializable {
  /** 承包商id */
  private Long contractorId;
  /** 承包商名称 */
  private String contractorName;
  /** 承包商负责人 */
  private String chargeAccount;
  /** 承包商负责人名称 */
  private String chargeName;
  /** 发起时间 */
  private String admissionTime;
  /** 入场人数 */
  private Integer admissionQuantity;
  /** 流程状态code */
  private String statusCode;
  /** 流程状态name */
  private String statusName;
  /** 流程实例ID */
  private String processInstanceId;
  /** 任务ID */
  private String taskId;
  /** 签字表主键id */
  private Long signatureId;
  /** 签字的url */
  private String signatureUrl;
  /** 签字的url */
  private String signatureDate;
  /** 处理部门 */
  private String nextDepartment;

  /** 发起时间-开始 */
  private String startTime;
  /** 发起时间-开始 */
  private String endTime;

  /** 模板编辑保存的数据 */
  private List<MesSwContractorStaffAdmissionTemplateParam> templateList;
}
