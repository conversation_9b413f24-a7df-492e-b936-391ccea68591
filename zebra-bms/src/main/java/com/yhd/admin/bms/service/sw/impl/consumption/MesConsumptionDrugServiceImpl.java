package com.yhd.admin.bms.service.sw.impl.consumption;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.constant.DicConstant;
import com.yhd.admin.bms.dao.sw.consumption.MesConsumptionDrugDao;
import com.yhd.admin.bms.domain.convert.sw.consumption.MesConsumptionDrugConvert;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionDrugDTO;
import com.yhd.admin.bms.domain.dto.sw.consumption.MesConsumptionDrugStatisticsDTO;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionCoal;
import com.yhd.admin.bms.domain.entity.sw.consumption.MesConsumptionDrug;
import com.yhd.admin.bms.domain.enums.ExceptionEnum;
import com.yhd.admin.bms.domain.enums.produce.KHConsumptionPointEnum;
import com.yhd.admin.bms.domain.query.sw.consumption.MesConsumptionDrugParam;
import com.yhd.admin.bms.exception.BMSException;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionCoalService;
import com.yhd.admin.bms.service.sw.consumption.MesConsumptionDrugService;
import com.yhd.admin.bms.service.sw.dashboard.KHPointService;
import com.yhd.admin.bms.service.sys.DicService;
import com.yhd.admin.common.domain.enums.ResultStateEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 能耗管理-药耗
 *
 * <AUTHOR>
 * @date 2025/07/02 10:22
 */
@Service
public class MesConsumptionDrugServiceImpl
    extends ServiceImpl<MesConsumptionDrugDao, MesConsumptionDrug>
    implements MesConsumptionDrugService {

  @Resource private MesConsumptionDrugConvert convert;
  @Resource private DicService dicService;
  @Resource private MesConsumptionCoalService consumptionCoalService;
  @Resource private KHPointService khPointService;

  private static BigDecimal addBigDecimal(BigDecimal... values) {
    if (values != null && values.length > 0 && values[0] == null) {
      values[0] = BigDecimal.ZERO;
    }
    return NumberUtil.add(values);
  }

  @Override
  public IPage<MesConsumptionDrugDTO> pagingQuery(MesConsumptionDrugParam param) {
    Page<MesConsumptionDrug> iPage = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MesConsumptionDrug> queryChain =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 查询参数
    if (!Objects.isNull(param.getStartDate()) && !Objects.isNull(param.getEndDate())) {
      queryChain.between(MesConsumptionDrug::getDate, param.getStartDate(), param.getEndDate());
    }
    // 排序
    queryChain.orderByDesc(MesConsumptionDrug::getDate);
    IPage<MesConsumptionDrug> page = queryChain.page(iPage);
    return page.convert(convert::toDTO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addOrModify(MesConsumptionDrugParam queryParam) {
    if (StringUtils.isBlank(queryParam.getClasses())
        || queryParam.getDate() == null
        || queryParam.getAnionNum() == null
        || queryParam.getAnionWeight() == null
        || queryParam.getCationWeight() == null
        || queryParam.getCationNum() == null) {
      throw new BMSException(
          ExceptionEnum.REQUIRED_ERROR.getCode(), ExceptionEnum.REQUIRED_ERROR.getDesc());
    }

    Long id = queryParam.getId();
    Integer count =
        this.lambdaQuery()
            .ne(id != null, MesConsumptionDrug::getId, id)
            .eq(MesConsumptionDrug::getDate, queryParam.getDate())
            .eq(MesConsumptionDrug::getClasses, queryParam.getClasses())
            .count();
    if (count != null && count > 0) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "该日期+班次已经存在，不能重复添加，请检查！");
    }
    MesConsumptionDrug entity = convert.toEntity(queryParam);

    String classesName = dicService.transform(DicConstant.CLASS, queryParam.getClasses());
    entity.setClassesName(classesName);

    BigDecimal cationTotal =
        NumberUtil.mul(queryParam.getCationNum(), queryParam.getCationWeight());
    entity.setCationTotal(cationTotal);

    BigDecimal anionTotal = NumberUtil.mul(queryParam.getAnionNum(), queryParam.getAnionWeight());
    entity.setAnionTotal(anionTotal);
    return saveOrUpdate(entity);
  }

  @Override
  public Boolean removeBatch(MesConsumptionDrugParam param) {
    return super.removeById(param.getId());
  }

  @Override
  public MesConsumptionDrugDTO getCurrentDetail(MesConsumptionDrugParam queryParam) {
    return convert.toDTO(super.getById(queryParam.getId()));
  }

  @Override
  public List<MesConsumptionDrugStatisticsDTO> queryStatistics(MesConsumptionDrugParam queryParam) {
    String year = queryParam.getYear();
    if (StringUtils.isBlank(year)) {
      year = String.valueOf(LocalDate.now().getYear());
    }
    if (StringUtils.isNotBlank(queryParam.getMonth())) {
      year = queryParam.getMonth();
    }
    List<MesConsumptionDrugStatisticsDTO> result = Lists.newArrayList();
    // 药耗数据
    LambdaQueryChainWrapper<MesConsumptionDrug> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.like(MesConsumptionDrug::getDate, year);
    List<MesConsumptionDrug> drugList = wrapper.list();
    if (CollectionUtil.isEmpty(drugList)) {
      return result;
    }
    // 分组累加
    Map<String, MesConsumptionDrugStatisticsDTO> waterMap =
        drugList.stream()
            .collect(
                Collectors.groupingBy(
                    bean ->
                        bean.getDate().getYear()
                            + "-"
                            + String.format("%02d", bean.getDate().getMonthValue()),
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                          BigDecimal anionTotal =
                              list.stream()
                                  .map(MesConsumptionDrug::getAnionTotal)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          BigDecimal cationTotal =
                              list.stream()
                                  .map(MesConsumptionDrug::getCationTotal)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          MesConsumptionDrugStatisticsDTO sums =
                              new MesConsumptionDrugStatisticsDTO();
                          sums.setAnionTotal(anionTotal);
                          sums.setCationTotal(cationTotal);
                          return sums;
                        })));
    // 煤产量数据
    LambdaQueryWrapper<MesConsumptionCoal> coalWrapper = new LambdaQueryWrapper<>();
    coalWrapper.like(MesConsumptionCoal::getDate, year);
    List<MesConsumptionCoal> coalList = consumptionCoalService.list(coalWrapper);

    // 补查当天数据，表里没有当天数据
    MesConsumptionCoal coalPO = queryTodayCoal();
    if (coalPO.getRawCon201() != null || coalPO.getRawCon208() != null) {
      // 只要有一项不为null，即为有效数据
      coalList.add(coalPO);
    }
    Map<String, MesConsumptionCoal> coalMap = new HashMap<>();
    // 分组累加
    if (CollectionUtil.isNotEmpty(coalList)) {
      coalMap =
          coalList.stream()
              .collect(
                  Collectors.groupingBy(
                      bean ->
                          bean.getDate().getYear()
                              + "-"
                              + String.format("%02d", bean.getDate().getMonthValue()),
                      Collectors.collectingAndThen(
                          Collectors.toList(),
                          list -> {
                            BigDecimal rawCon201 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon201)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal rawCon208 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon208)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            MesConsumptionCoal sums = new MesConsumptionCoal();
                            sums.setRawCon201(rawCon201);
                            sums.setRawCon208(rawCon208);
                            return sums;
                          })));
    }
    BigDecimal thousand = new BigDecimal("1000");
    for (Map.Entry<String, MesConsumptionDrugStatisticsDTO> entry : waterMap.entrySet()) {
      String month = entry.getKey();
      MesConsumptionDrugStatisticsDTO item = entry.getValue();
      item.setMonth(month);
      // 生成返回数据，原煤产量=201胶带机+218胶带机
      MesConsumptionCoal coalTotal = coalMap.get(month);
      if (coalTotal != null) {
        BigDecimal rowCoal = addBigDecimal(coalTotal.getRawCon201(), coalTotal.getRawCon208());
        item.setRowCoal(rowCoal);
        if (item.getAnionTotal() != null
            && rowCoal != null
            && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 阴离子总重量/原煤产量*1000（四舍五入保留两位小数）
          BigDecimal anionConsumptionDm =
              NumberUtil.div(item.getAnionTotal(), rowCoal)
                  .multiply(thousand)
                  .setScale(2, RoundingMode.HALF_UP);
          item.setAnionConsumptionDm(anionConsumptionDm);
        }
        if (item.getCationTotal() != null
            && rowCoal != null
            && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 阳离子总重量/原煤产量*1000（四舍五入保留两位小数）
          BigDecimal cationConsumptionDm =
              NumberUtil.div(item.getCationTotal(), rowCoal)
                  .multiply(thousand)
                  .setScale(2, RoundingMode.HALF_UP);
          item.setCationConsumptionDm(cationConsumptionDm);
        }
      }
      result.add(item);
    }
    result.sort((b1, b2) -> b2.getMonth().compareTo(b1.getMonth()));
    return result;
  }

  @Override
  public List<MesConsumptionDrugStatisticsDTO> getStatisticsCurrentDetail(
      MesConsumptionDrugParam param) {
    String month = param.getMonth();
    if (StringUtils.isBlank(month)) {
      throw new BMSException(ResultStateEnum.FAIL.getCode(), "查询参数为空，请检查！");
    }
    List<MesConsumptionDrugStatisticsDTO> result = Lists.newArrayList();
    LambdaQueryChainWrapper<MesConsumptionDrug> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.like(MesConsumptionDrug::getDate, month);
    List<MesConsumptionDrug> drugList = wrapper.list();
    if (CollectionUtil.isEmpty(drugList)) {
      return result;
    }
    // 分组累加
    Map<String, MesConsumptionDrugStatisticsDTO> waterMap =
        drugList.stream()
            .collect(
                Collectors.groupingBy(
                    bean -> bean.getDate().toString(),
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                          BigDecimal anionTotal =
                              list.stream()
                                  .map(MesConsumptionDrug::getAnionTotal)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          BigDecimal cationTotal =
                              list.stream()
                                  .map(MesConsumptionDrug::getCationTotal)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          MesConsumptionDrugStatisticsDTO sums =
                              new MesConsumptionDrugStatisticsDTO();
                          sums.setAnionTotal(anionTotal);
                          sums.setCationTotal(cationTotal);
                          return sums;
                        })));
    // 煤产量数据
    LambdaQueryWrapper<MesConsumptionCoal> coalWrapper = new LambdaQueryWrapper<>();
    wrapper.like(MesConsumptionDrug::getDate, month);
    List<MesConsumptionCoal> coalList = consumptionCoalService.list(coalWrapper);
    Map<String, MesConsumptionCoal> coalMap = new HashMap<>();
    // 分组累加
    if (CollectionUtil.isNotEmpty(coalList)) {
      coalMap =
          coalList.stream()
              .collect(
                  Collectors.groupingBy(
                      bean -> bean.getDate().toString(),
                      Collectors.collectingAndThen(
                          Collectors.toList(),
                          list -> {
                            BigDecimal rawCon201 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon201)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal rawCon208 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon208)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            MesConsumptionCoal sums = new MesConsumptionCoal();
                            sums.setRawCon201(rawCon201);
                            sums.setRawCon208(rawCon208);
                            return sums;
                          })));
    }
    BigDecimal thousand = new BigDecimal("1000");
    // 月合计
    BigDecimal anionTotal = null;
    BigDecimal cationTotal = null;
    BigDecimal rowCoalTotal = null;

    for (Map.Entry<String, MesConsumptionDrugStatisticsDTO> entry : waterMap.entrySet()) {
      String key = entry.getKey();
      MesConsumptionDrugStatisticsDTO item = entry.getValue();
      item.setDateStr(key);
      // 生成返回数据，原煤产量=201胶带机+218胶带机
      MesConsumptionCoal coalTotal = coalMap.get(key);
      if (coalTotal != null) {
        BigDecimal rowCoal = addBigDecimal(coalTotal.getRawCon201(), coalTotal.getRawCon208());
        item.setRowCoal(rowCoal);
        if (item.getAnionTotal() != null
            && rowCoal != null
            && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 阴离子总重量/原煤产量*1000（四舍五入保留两位小数）
          BigDecimal anionConsumptionDm =
              NumberUtil.div(item.getAnionTotal(), rowCoal)
                  .multiply(thousand)
                  .setScale(2, RoundingMode.HALF_UP);
          item.setAnionConsumptionDm(anionConsumptionDm);
        }
        if (item.getCationTotal() != null
            && rowCoal != null
            && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 阳离子总重量/原煤产量*1000（四舍五入保留两位小数）
          BigDecimal cationConsumptionDm =
              NumberUtil.div(item.getCationTotal(), rowCoal)
                  .multiply(thousand)
                  .setScale(2, RoundingMode.HALF_UP);
          item.setCationConsumptionDm(cationConsumptionDm);
        }

        // 月合计
        anionTotal = addBigDecimal(anionTotal, item.getAnionTotal());
        cationTotal = addBigDecimal(cationTotal, item.getCationTotal());
        rowCoalTotal = addBigDecimal(rowCoalTotal, rowCoal);
      }
      result.add(item);
    }
    result.sort((b1, b2) -> b2.getDateStr().compareTo(b1.getDateStr()));
    if (CollectionUtil.isNotEmpty(result)) {
      // 结果不为空，将合计也放入
      MesConsumptionDrugStatisticsDTO monthTotal = new MesConsumptionDrugStatisticsDTO();
      monthTotal.setDateStr("合计");
      monthTotal.setAnionTotal(anionTotal);
      monthTotal.setCationTotal(cationTotal);
      monthTotal.setRowCoal(rowCoalTotal);
      if (anionTotal != null
          && rowCoalTotal != null
          && rowCoalTotal.compareTo(BigDecimal.ZERO) != 0) {
        // 阴离子总重量/原煤产量*1000（四舍五入保留两位小数）
        BigDecimal anionConsumptionDm =
            NumberUtil.div(anionTotal, rowCoalTotal)
                .multiply(thousand)
                .setScale(2, RoundingMode.HALF_UP);
        monthTotal.setAnionConsumptionDm(anionConsumptionDm);
      }
      if (cationTotal != null
          && rowCoalTotal != null
          && rowCoalTotal.compareTo(BigDecimal.ZERO) != 0) {
        // 阳离子总重量/原煤产量*1000（四舍五入保留两位小数）
        BigDecimal cationConsumptionDm =
            NumberUtil.div(cationTotal, rowCoalTotal)
                .multiply(thousand)
                .setScale(2, RoundingMode.HALF_UP);
        monthTotal.setCationConsumptionDm(cationConsumptionDm);
      }

      result.add(monthTotal);
    }
    return result;
  }

  @Override
  public List<MesConsumptionDrugStatisticsDTO> queryIntervalList(LocalDate start, LocalDate end) {
    List<MesConsumptionDrugStatisticsDTO> result = Lists.newArrayList();
    LambdaQueryChainWrapper<MesConsumptionDrug> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    wrapper.between(MesConsumptionDrug::getDate, start, end);
    List<MesConsumptionDrug> drugList = wrapper.list();
    if (CollectionUtil.isEmpty(drugList)) {
      return result;
    }
    // 分组累加
    Map<String, MesConsumptionDrugStatisticsDTO> waterMap =
        drugList.stream()
            .collect(
                Collectors.groupingBy(
                    bean -> bean.getDate().toString(),
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                          BigDecimal anionTotal =
                              list.stream()
                                  .map(MesConsumptionDrug::getAnionTotal)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          BigDecimal cationTotal =
                              list.stream()
                                  .map(MesConsumptionDrug::getCationTotal)
                                  .filter(Objects::nonNull)
                                  .reduce(BigDecimal.ZERO, BigDecimal::add);
                          MesConsumptionDrugStatisticsDTO sums =
                              new MesConsumptionDrugStatisticsDTO();
                          sums.setAnionTotal(anionTotal);
                          sums.setCationTotal(cationTotal);
                          return sums;
                        })));
    // 煤产量数据
    LambdaQueryWrapper<MesConsumptionCoal> coalWrapper = new LambdaQueryWrapper<>();
    wrapper.between(MesConsumptionDrug::getDate, start, end);
    List<MesConsumptionCoal> coalList = consumptionCoalService.list(coalWrapper);
    Map<String, MesConsumptionCoal> coalMap = new HashMap<>();
    // 分组累加
    if (CollectionUtil.isNotEmpty(coalList)) {
      coalMap =
          coalList.stream()
              .collect(
                  Collectors.groupingBy(
                      bean -> bean.getDate().toString(),
                      Collectors.collectingAndThen(
                          Collectors.toList(),
                          list -> {
                            BigDecimal rawCon201 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon201)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal rawCon208 =
                                list.stream()
                                    .map(MesConsumptionCoal::getRawCon208)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            MesConsumptionCoal sums = new MesConsumptionCoal();
                            sums.setRawCon201(rawCon201);
                            sums.setRawCon208(rawCon208);
                            return sums;
                          })));
    }
    BigDecimal thousand = new BigDecimal("1000");
    for (Map.Entry<String, MesConsumptionDrugStatisticsDTO> entry : waterMap.entrySet()) {
      String key = entry.getKey();
      MesConsumptionDrugStatisticsDTO item = entry.getValue();
      item.setDateStr(key);
      // 生成返回数据，原煤产量=201胶带机+218胶带机
      MesConsumptionCoal coalTotal = coalMap.get(key);
      if (coalTotal != null) {
        BigDecimal rowCoal = addBigDecimal(coalTotal.getRawCon201(), coalTotal.getRawCon208());
        item.setRowCoal(rowCoal);
        if (item.getAnionTotal() != null
            && rowCoal != null
            && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 阴离子总重量/原煤产量*1000（四舍五入保留两位小数）
          BigDecimal anionConsumptionDm =
              NumberUtil.div(item.getAnionTotal(), rowCoal)
                  .multiply(thousand)
                  .setScale(2, RoundingMode.HALF_UP);
          item.setAnionConsumptionDm(anionConsumptionDm);
        }
        if (item.getCationTotal() != null
            && rowCoal != null
            && rowCoal.compareTo(BigDecimal.ZERO) != 0) {
          // 阳离子总重量/原煤产量*1000（四舍五入保留两位小数）
          BigDecimal cationConsumptionDm =
              NumberUtil.div(item.getCationTotal(), rowCoal)
                  .multiply(thousand)
                  .setScale(2, RoundingMode.HALF_UP);
          item.setCationConsumptionDm(cationConsumptionDm);
        }
      }
      result.add(item);
    }
    result.sort((b1, b2) -> b2.getDateStr().compareTo(b1.getDateStr()));
    return result;
  }

  /**
   * 查询当天的煤产量
   *
   * @return 原煤产量
   */
  private MesConsumptionCoal queryTodayCoal() {
    // 获取煤产量对应的点位
    List<String> pointCoalList = new ArrayList<>();
    pointCoalList.add(KHConsumptionPointEnum.XMSW_Raw_CON201_Info_ProductToday.getKey());
    pointCoalList.add(KHConsumptionPointEnum.XMSW_Raw_CON218_Info_ProductToday.getKey());
    Map<String, BigDecimal> coalDataMap =
        khPointService.getCoalProductionByParams(LocalDate.now().toString(), pointCoalList);
    // 煤产量，生成对应的数据
    // 能耗管理-煤产量
    MesConsumptionCoal coalPO = new MesConsumptionCoal();
    coalPO.setDate(LocalDate.now());
    if (CollUtil.isNotEmpty(coalDataMap)) {
      coalDataMap.forEach(
          (key, value) -> {
            if (KHConsumptionPointEnum.XMSW_Raw_CON201_Info_ProductToday.getKey().equals(key)) {
              coalPO.setRawCon201(value);
            } else if (KHConsumptionPointEnum.XMSW_Raw_CON218_Info_ProductToday.getKey()
                .equals(key)) {
              coalPO.setRawCon208(value);
            }
          });
    }
    return coalPO;
  }
}
