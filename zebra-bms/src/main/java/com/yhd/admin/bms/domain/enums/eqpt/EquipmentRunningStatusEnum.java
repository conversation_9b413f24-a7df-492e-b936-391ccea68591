package com.yhd.admin.bms.domain.enums.eqpt;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备运行状态管理-亚控点位key枚举类
 *
 * <AUTHOR>
 * @date 2025/01/09 10:22
 */
@Getter
public enum EquipmentRunningStatusEnum {
  XMSW_Raw_CON218_RunReturn("XMSW_Raw_CON218_RunReturn", "南筛分"),
  XMSW_Lump_CON302_RunReturn("XMSW_Lump_CON302_RunReturn", "主洗B系统"),
  XMSW_Lump_CON301_RunReturn("XMSW_Lump_CON301_RunReturn", "主洗A系统"),
  /** 装车系统 */
  XMSW_Load_CON771_MOT1_RunReturn("XMSW_Load_CON771_MOT1_RunReturn", "771胶带机1号电机运行"),
  XMSW_Load_CON771_MOT2_RunReturn("XMSW_Load_CON771_MOT2_RunReturn", "771胶带机2号电机运行"),
  /** 北筛分 */
  XMSW_Raw_CON201_RunState1("XMSW_Raw_CON201_RunState1", "201胶带机1号电机运行"),
  XMSW_Raw_CON201_RunState2("XMSW_Raw_CON201_RunState2", "201胶带机2号电机运行"),
  ;

  private final String key;
  private final String desc;

  EquipmentRunningStatusEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }

  public static List<String> getKeyList() {
    EquipmentRunningStatusEnum[] values = EquipmentRunningStatusEnum.values();

    return Arrays.stream(values)
        .map(EquipmentRunningStatusEnum::getKey)
        .collect(Collectors.toList());
  }

  public static EquipmentRunningStatusEnum getEnumByKey(String key) {
    if (StringUtils.isBlank(key)) {
      return null;
    }
    EquipmentRunningStatusEnum[] values = EquipmentRunningStatusEnum.values();
    for (EquipmentRunningStatusEnum value : values) {
      if (value.getKey().equals(key)) {
        return value;
      }
    }
    return null;
  }
}
