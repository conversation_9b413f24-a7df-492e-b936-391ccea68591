package com.yhd.admin.passport.auth.provider;

import com.yhd.admin.common.domain.dto.UserAccountDTO;
import com.yhd.admin.passport.service.UserAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class AuthenticationManagerProvider extends WebSecurityConfigurerAdapter {

  @Autowired UserDetailsService userDetailsService;
  @Autowired PasswordEncoder passwordEncoder;

  @Resource private RedisTemplate redisTemplate;

  @Resource private UserAccountService userAccountService;

  @Bean
  @Override
  public AuthenticationManager authenticationManagerBean() throws Exception {

    return super.authenticationManagerBean();
  }

  @Override
  protected void configure(AuthenticationManagerBuilder auth) {
    auth.authenticationProvider(daoAuthenticationProvider());
  }

  @Bean(name = "daoAuthenticationProvider")
  public AuthenticationProvider daoAuthenticationProvider() {
    CustomDaoAuthenticationProvider daoAuthenticationProvider =
        new CustomDaoAuthenticationProvider();
    daoAuthenticationProvider.setUserDetailsService(userDetailsService);
    daoAuthenticationProvider.setHideUserNotFoundExceptions(false);
    daoAuthenticationProvider.setPasswordEncoder(passwordEncoder);
    daoAuthenticationProvider.setRedisTemplate(redisTemplate);
    daoAuthenticationProvider.setUserAccountService(userAccountService);
    return daoAuthenticationProvider;
  }
}
