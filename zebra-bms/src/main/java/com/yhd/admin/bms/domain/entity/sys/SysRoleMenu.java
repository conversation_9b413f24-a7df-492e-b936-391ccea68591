package com.yhd.admin.bms.domain.entity.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysRoleMenu.java
 * @Description TODO
 * @createTime 2020年03月30日 14:49:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleMenu extends BaseEntity implements Serializable, Cloneable {

    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 菜单ID
     */
    private Long menuId;
}
