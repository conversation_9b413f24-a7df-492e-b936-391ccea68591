package com.yhd.admin.bms.controller.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketContractorConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketContractorDTO;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketContractorParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketContractorSignUserParam;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketContractorService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 承包商施工工作票-控制层
 *
 * <AUTHOR>
 * @date 2024/5/28 22:58
 */
@RestController
@RequestMapping(value = "/work/ticket/contractor")
public class MesSwWorkTicketContractorController {
  private final MesSwWorkTicketContractorService workTicketContractorService;
  private final MesSwWorkTicketContractorConvert workTicketContractorConvert;

  public MesSwWorkTicketContractorController(
      MesSwWorkTicketContractorService workTicketContractorService,
      MesSwWorkTicketContractorConvert workTicketContractorConvert) {
    this.workTicketContractorService = workTicketContractorService;
    this.workTicketContractorConvert = workTicketContractorConvert;
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<?> pagingQuery(@RequestBody MesSwWorkTicketContractorParam param) {
    IPage<MesSwWorkTicketContractorDTO> page = workTicketContractorService.pagingQuery(param);
    return new PageRespJson<>(page.convert(workTicketContractorConvert::toVO));
  }

  @PostMapping(
      value = "/queryList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> queryList(@RequestBody MesSwWorkTicketContractorParam param) {
    List<MesSwWorkTicketContractorDTO> dtoList = workTicketContractorService.queryList(param);
    return RespJson.buildSuccessResponse(
        dtoList.stream().map(workTicketContractorConvert::toVO).collect(Collectors.toList()));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getCurrentDetail(@RequestBody MesSwWorkTicketContractorParam param) {
    return RespJson.buildSuccessResponse(
        workTicketContractorConvert.toVO(workTicketContractorService.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> add(@RequestBody MesSwWorkTicketContractorParam param) {
    Boolean retVal = workTicketContractorService.add(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/zf",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> zf(@RequestBody MesSwWorkTicketContractorParam param) {
    Boolean retVal = workTicketContractorService.zf(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/sp",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> sp(@RequestBody MesSwWorkTicketContractorSignUserParam param) {
    Boolean retVal = workTicketContractorService.sp(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/getSgNameList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getSgNameList() {
    return RespJson.buildSuccessResponse(workTicketContractorService.getSgNameList());
  }

  @PostMapping(
      value = "/getSgLocationList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getSgLocationList() {
    return RespJson.buildSuccessResponse(workTicketContractorService.getSgLocationList());
  }

  @PostMapping(
      value = "/getSgItemList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getSgItemList() {
    return RespJson.buildSuccessResponse(workTicketContractorService.getSgItemList());
  }
}
