package com.yhd.admin.bms.domain.entity.sw;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/4 17:11
 * @Version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class MesSwSafeQuestion extends BaseEntity implements Cloneable, Serializable {
    /**
     *出题人
     */
    private String questionSetter;
    /**
     *出题人名称
     */
    private String questionSetterName;
    /**
     *试题分组id
     */
    private Long titleId;
    /**
     *试题标题
     */
    private String title;
    /**
     *试题分类（考试试题/模拟试题）
     */
    private String questionSort;
    /**
     *试题状态：0禁用，1启用
     */
    private Boolean status;
    /**
     *相关培训
     */
    private Long trainingId;
    /**
     *相关培训内容
     */
    private String trainingContent;
    /**
     *试题题型：JUDGE_CHOICE-判断题，SINGLE_CHOICE-单选题，MULTI_CHOICE-多选题
     */
    private String questionType;
    /**
     *试题题型名称
     */
    private String questionName;
    /**
     *试题分值
     */
    private Integer score;
    /**
     *试题难度：1-低，2-中，3-高
     */
    private String difficultyType;
    /**
     *试题难度名称
     */
    private String difficultyName;
    /**
     *试题内容
     */
    private String questionContent;
    /**
     *试题类型
     */
    private String fileType;
    /**
     *试题附件：用@&&@分隔
     */
    private String fileUrl;
    /**
     *试题答案：用@&&@分隔
     */
    private String questionAnswer;
    /**
     *正确答案：用@&&@分隔
     */
    private String correctAnswer;
}
