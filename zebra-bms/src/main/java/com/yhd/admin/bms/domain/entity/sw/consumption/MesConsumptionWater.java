package com.yhd.admin.bms.domain.entity.sw.consumption;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 能耗管理-水耗
 *
 * <AUTHOR>
 * @date 2025/01/05 10:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesConsumptionWater extends BaseEntity implements Cloneable, Serializable {
  /** 日期 */
  private LocalDate date;
  /** 水表1 m³ */
  private BigDecimal water1;
  /** 水表2 m³ */
  private BigDecimal water2;
  /** 水表3 m³ */
  private BigDecimal water3;
}
