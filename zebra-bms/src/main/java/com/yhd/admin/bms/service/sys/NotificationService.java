package com.yhd.admin.bms.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sys.SysNotification;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NotificationService.java
 * @Description 系统通知
 */
public interface NotificationService extends IService<SysNotification> {

    Object queryList(String userName);

    Boolean read(SysNotification notify);

    Boolean readall(String userName);

    Boolean readtype(String type, String userName);

    Boolean add(SysNotification notification);
}
