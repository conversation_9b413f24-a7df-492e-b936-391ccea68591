package com.yhd.admin.bms.service.sw.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwMockExamPaperQuestion;

import java.util.List;

public interface MesSwMockExamPaperQuestionService extends IService<MesSwMockExamPaperQuestion> {

  /**
   * 根据用户模拟考试表主键id查询模拟试卷题目列表
   *
   * @param userMockId 用户模拟考试表主键id
   * @return 模拟试卷题目列表
   */
  List<MesSwMockExamPaperQuestion> queryListByUserMockId(Long userMockId);
}
