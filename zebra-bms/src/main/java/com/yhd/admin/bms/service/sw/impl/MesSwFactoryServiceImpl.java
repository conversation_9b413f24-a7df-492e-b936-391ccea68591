package com.yhd.admin.bms.service.sw.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.bms.dao.sw.MesSwFactoryDao;
import com.yhd.admin.bms.domain.convert.sw.MesSwFactoryConvert;
import com.yhd.admin.bms.domain.dto.sw.MesSwFactoryDTO;
import com.yhd.admin.bms.domain.entity.sw.MesSwFactory;
import com.yhd.admin.bms.domain.query.sw.MesSwFactoryParam;
import com.yhd.admin.bms.domain.query.sys.BatchParam;
import com.yhd.admin.bms.service.sw.MesSwFactoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 工业厂区草坪
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-10-10
 */
@Service
public class MesSwFactoryServiceImpl extends ServiceImpl<MesSwFactoryDao, MesSwFactory> implements MesSwFactoryService {

    @Resource
    private MesSwFactoryConvert convert;

    @Resource
    private MesSwFactoryService service;

    @Override
    public IPage<MesSwFactoryDTO> pagingQuery(MesSwFactoryParam queryParam) {
        Page<MesSwFactory> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesSwFactory> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        if (queryParam.getStartDate() != null && queryParam.getEndDate() != null) {
            queryChain.between(
                MesSwFactory::getCheckTime,
                queryParam.getStartDate(),
                queryParam.getEndDate());
        }
        if (!StringUtils.isEmpty(queryParam.getChecker())) {
            queryChain.like(MesSwFactory::getChecker, queryParam.getChecker());
        }
        if (!StringUtils.isEmpty(queryParam.getWorkShop())) {
            queryChain.like(MesSwFactory::getWorkShop, queryParam.getWorkShop());
        }
        if (!StringUtils.isEmpty(queryParam.getReviewer())) {
            queryChain.like(MesSwFactory::getReviewer, queryParam.getReviewer());
        }
        queryChain.orderByDesc(MesSwFactory::getCheckTime, MesSwFactory::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }


    @Override
    public Boolean add(MesSwFactoryParam param) {
        MesSwFactory entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MesSwFactoryParam param) {
        MesSwFactory entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MesSwFactoryDTO getCurrentDetail(MesSwFactoryParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
