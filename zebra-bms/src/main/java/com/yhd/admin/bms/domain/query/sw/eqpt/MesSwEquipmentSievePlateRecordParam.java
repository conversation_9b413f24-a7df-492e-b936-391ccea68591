package com.yhd.admin.bms.domain.query.sw.eqpt;

import com.yhd.admin.bms.domain.query.sys.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备资料-筛板管理
 *
 * <AUTHOR>
 * @date 2024/11/08 10:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MesSwEquipmentSievePlateRecordParam extends QueryParam
    implements Cloneable, Serializable {
    /**
     * 筛板id
     */
    private String sievePlateId;

    /**
     * 设备资料id
     */
    private Long equipmentDataId;
    /**
     * 更换日期
     */
    private LocalDate date;
    /**
     * 报废时间
     */
    private LocalDateTime scrapTime;
    /**
     * 是否为盲板
     */
    private String blindPlate;
    /**
     * 原材质及尺寸编码
     */
    private String originalMaterialCode;
    /**
     * 原材质及尺寸名称
     */
    private String originalMaterialName;
    /**
     * 材质及尺寸编码
     */
    private String materialCode;
    /**
     * 材质及尺寸名称
     */
    private String materialName;
    /**
     * 更换原因code
     */
    private String changeReasonCode;
    /**
     * 更换原因名称
     */
    private String changeReasonName;
    /**
     * 使用天数
     */
    private Integer day;
    /**
     * 更换人account
     */
    private String changerAccount;
    /**
     * 更换人名称
     */
    private String changerName;

    /**
     * 列号
     */
    private Integer column;
    /**
     * 行号
     */
    private Integer row;
    /**
     * 筛板名称
     */
    private String name;

    private LocalDate startDate;
    private LocalDate endDate;

    private LocalDate scrapStartDate;
    private LocalDate scrapEndDate;

    /**
     * 材质
     */
    private Long categoryCode;
    /**
     * 孔径
     */
    private String apertureCode;
    /**
     * 设备编码
     */
    private String equipmentNo;
    /**
     * 是否正在使用
     */
    private String ifUse;

    private List<MesSwEquipmentSievePlateRecordItemParam> locationList;
}
