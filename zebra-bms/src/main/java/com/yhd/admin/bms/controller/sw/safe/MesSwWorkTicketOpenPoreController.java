package com.yhd.admin.bms.controller.sw.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.bms.domain.convert.sw.safe.MesSwWorkTicketOpenPoreConvert;
import com.yhd.admin.bms.domain.dto.sw.safe.MesSwWorkTicketOpenPoreDTO;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketOpenPoreParam;
import com.yhd.admin.bms.domain.query.sw.safe.MesSwWorkTicketOpenPoreSignParam;
import com.yhd.admin.bms.service.sw.safe.MesSwWorkTicketOpenPoreService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 开孔作业许可票表-签字表
 *
 * <AUTHOR>
 * @date 2024/05/29 18:22
 */
@RestController
@RequestMapping(value = "/ticket/openpore")
public class MesSwWorkTicketOpenPoreController {
  @Resource private MesSwWorkTicketOpenPoreService service;
  @Resource private MesSwWorkTicketOpenPoreConvert convert;

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<?> pagingQuery(@RequestBody MesSwWorkTicketOpenPoreParam param) {
    IPage<MesSwWorkTicketOpenPoreDTO> page = service.pagingQuery(param);
    return new PageRespJson<>(page.convert(convert::toVO));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> getCurrentDetail(@RequestBody MesSwWorkTicketOpenPoreParam param) {
    return RespJson.buildSuccessResponse(convert.toVO(service.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> add(@RequestBody MesSwWorkTicketOpenPoreParam param) {
    Boolean retVal = service.add(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/cancel",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> zf(@RequestBody MesSwWorkTicketOpenPoreParam param) {
    Boolean retVal = service.cancel(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/approve",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> approve(@RequestBody MesSwWorkTicketOpenPoreSignParam param) {
    Boolean retVal = service.approve(param);
    return retVal ? RespJson.buildSuccessResponse() : RespJson.buildFailureResponse("");
  }

  @PostMapping(
      value = "/select",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<?> select(@RequestBody MesSwWorkTicketOpenPoreParam param) {
    return RespJson.buildSuccessResponse(service.select(param));
  }
}
