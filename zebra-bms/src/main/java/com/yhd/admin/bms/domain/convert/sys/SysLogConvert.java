package com.yhd.admin.bms.domain.convert.sys;

import com.yhd.admin.bms.domain.dto.sys.SysLogDTO;
import com.yhd.admin.bms.domain.entity.sys.SysLog;
import com.yhd.admin.bms.domain.query.sys.SysLogParam;
import com.yhd.admin.bms.domain.vo.sys.SysLogVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface SysLogConvert {

  SysLogDTO toDTO(SysLog sysLog);

  SysLogVO toVO(SysLogDTO LogDTO);

  SysLog toEntity(SysLogParam LogParam);
}
