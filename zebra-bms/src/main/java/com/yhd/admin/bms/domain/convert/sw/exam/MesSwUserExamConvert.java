package com.yhd.admin.bms.domain.convert.sw.exam;

import com.yhd.admin.bms.domain.dto.sw.exam.MesSwUserExamDTO;
import com.yhd.admin.bms.domain.entity.sw.exam.MesSwUserExam;
import com.yhd.admin.bms.domain.query.sw.exam.MesSwUserExamParam;
import com.yhd.admin.bms.domain.vo.sw.exam.MesSwUserExamVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MesSwUserExamConvert {
    MesSwUserExam toEntity(MesSwUserExamParam param);

    MesSwUserExam toEntity(MesSwUserExamDTO dto);

    MesSwUserExamDTO toDTO(MesSwUserExam entity);

    MesSwUserExamVO toVO(MesSwUserExamDTO dto);

    MesSwUserExamVO entityToVO(MesSwUserExam entity);

    List<MesSwUserExamDTO> toDTOList(List<MesSwUserExam> entity);

    List<MesSwUserExamVO> toVO(List<MesSwUserExamDTO> dto);
}
