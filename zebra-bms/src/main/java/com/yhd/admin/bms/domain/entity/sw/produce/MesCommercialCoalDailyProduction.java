package com.yhd.admin.bms.domain.entity.sw.produce;

import com.yhd.admin.bms.domain.entity.sys.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * 商品煤日信息统计
 * <AUTHOR>
 * @Date 2025/3/4 18:01 @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MesCommercialCoalDailyProduction extends BaseEntity {
    /** 日期 */
    private LocalDate date;
    /** 类型 */
    private String coalType;
    /** 计划量 */
    private BigDecimal planNum;
    /** 完成量 */
    private BigDecimal completeNum;
    /** 超欠量 */
    private BigDecimal overdueNum;







}
